//---------------------------------------------------------------------------
// Sword2 Object Pool System (c) 2024
//
// File:	ObjectPool.h
// Date:	2024.07.02
// Code:	AI Assistant
// Desc:	High-performance object pool for game entities
//---------------------------------------------------------------------------
#ifndef OBJECT_POOL_H
#define OBJECT_POOL_H

#include <windows.h>
#include <vector>
#include <queue>

// 对象池配置
struct ObjectPoolConfig
{
    DWORD dwInitialSize;        // 初始对象数量
    DWORD dwMaxSize;            // 最大对象数量
    DWORD dwGrowthSize;         // 增长步长
    BOOL bAutoShrink;           // 是否自动收缩
    DWORD dwShrinkThreshold;    // 收缩阈值
};

// 对象池统计信息
struct ObjectPoolStats
{
    DWORD dwTotalCreated;       // 总创建数量
    DWORD dwTotalDestroyed;     // 总销毁数量
    DWORD dwCurrentActive;      // 当前活跃数量
    DWORD dwCurrentPooled;      // 当前池中数量
    DWORD dwPeakActive;         // 峰值活跃数量
    DWORD dwPoolHits;           // 池命中次数
    DWORD dwPoolMisses;         // 池未命中次数
    DWORD dwGrowthCount;        // 扩容次数
    DWORD dwShrinkCount;        // 收缩次数
};

// 池化对象基类
class IPooledObject
{
public:
    virtual ~IPooledObject() {}
    virtual void OnPoolCreate() {}      // 从池中创建时调用
    virtual void OnPoolDestroy() {}     // 返回池中时调用
    virtual void OnPoolReset() {}       // 重置对象状态
    virtual BOOL IsPoolable() const { return TRUE; } // 是否可以池化
};

// 模板对象池类
template<typename T>
class CObjectPool
{
public:
    CObjectPool();
    ~CObjectPool();

    BOOL Initialize(const ObjectPoolConfig& config);
    void Cleanup();

    // 对象获取和归还
    T* Acquire();
    BOOL Release(T* pObject);

    // 批量操作
    BOOL AcquireArray(T** ppArray, DWORD dwCount);
    BOOL ReleaseArray(T** ppArray, DWORD dwCount);

    // 池管理
    void Grow(DWORD dwCount = 0);
    void Shrink();
    void Clear();

    // 统计信息
    void GetStats(ObjectPoolStats* pStats);
    void ResetStats();

    // 配置
    void SetConfig(const ObjectPoolConfig& config) { m_Config = config; }
    const ObjectPoolConfig& GetConfig() const { return m_Config; }

private:
    ObjectPoolConfig m_Config;          // 配置信息
    ObjectPoolStats m_Stats;            // 统计信息
    std::queue<T*> m_FreeObjects;       // 空闲对象队列
    std::vector<T*> m_AllObjects;       // 所有对象列表
    CRITICAL_SECTION m_CriticalSection; // 线程安全
    BOOL m_bInitialized;

    T* CreateNewObject();
    void DestroyObject(T* pObject);
    BOOL ShouldShrink() const;
};

// 智能对象指针 - 自动归还到池
template<typename T>
class CPooledPtr
{
public:
    CPooledPtr() : m_pObject(NULL), m_pPool(NULL) {}
    CPooledPtr(T* pObject, CObjectPool<T>* pPool) : m_pObject(pObject), m_pPool(pPool) {}
    ~CPooledPtr() { Reset(); }

    T* Get() const { return m_pObject; }
    T* operator->() const { return m_pObject; }
    T& operator*() const { return *m_pObject; }
    operator T*() const { return m_pObject; }

    void Reset()
    {
        if (m_pObject && m_pPool)
        {
            m_pPool->Release(m_pObject);
            m_pObject = NULL;
            m_pPool = NULL;
        }
    }

    T* Release()
    {
        T* pTemp = m_pObject;
        m_pObject = NULL;
        m_pPool = NULL;
        return pTemp;
    }

private:
    T* m_pObject;
    CObjectPool<T>* m_pPool;

    // 禁止拷贝
    CPooledPtr(const CPooledPtr&);
    CPooledPtr& operator=(const CPooledPtr&);
};

// 对象池管理器
class CObjectPoolManager
{
public:
    CObjectPoolManager();
    ~CObjectPoolManager();

    BOOL Initialize();
    void Cleanup();

    // 注册对象池
    template<typename T>
    BOOL RegisterPool(const char* pName, const ObjectPoolConfig& config);

    // 获取对象池
    template<typename T>
    CObjectPool<T>* GetPool(const char* pName);

    // 统计信息
    void LogAllStats();
    void ResetAllStats();

    // 内存整理
    void DefragmentAll();
    void ShrinkAll();

private:
    struct PoolInfo
    {
        void* pPool;
        const char* pName;
        const std::type_info* pTypeInfo;
    };

    std::vector<PoolInfo> m_Pools;
    CRITICAL_SECTION m_CriticalSection;
    BOOL m_bInitialized;
};

// 全局对象池管理器
extern CObjectPoolManager g_ObjectPoolManager;

// 便捷宏定义
#define REGISTER_OBJECT_POOL(type, name, config) \
    g_ObjectPoolManager.RegisterPool<type>(name, config)

#define GET_OBJECT_POOL(type, name) \
    g_ObjectPoolManager.GetPool<type>(name)

#define ACQUIRE_OBJECT(type, name) \
    GET_OBJECT_POOL(type, name)->Acquire()

#define RELEASE_OBJECT(type, name, obj) \
    GET_OBJECT_POOL(type, name)->Release(obj)

// 默认配置
inline ObjectPoolConfig GetDefaultPoolConfig()
{
    ObjectPoolConfig config;
    config.dwInitialSize = 32;
    config.dwMaxSize = 1024;
    config.dwGrowthSize = 16;
    config.bAutoShrink = TRUE;
    config.dwShrinkThreshold = 75; // 75%空闲时收缩
    return config;
}

//===========================================================================
// 模板实现
//===========================================================================

template<typename T>
CObjectPool<T>::CObjectPool()
{
    memset(&m_Stats, 0, sizeof(m_Stats));
    InitializeCriticalSection(&m_CriticalSection);
    m_bInitialized = FALSE;
}

template<typename T>
CObjectPool<T>::~CObjectPool()
{
    Cleanup();
    DeleteCriticalSection(&m_CriticalSection);
}

template<typename T>
BOOL CObjectPool<T>::Initialize(const ObjectPoolConfig& config)
{
    if (m_bInitialized)
        return TRUE;

    EnterCriticalSection(&m_CriticalSection);

    m_Config = config;

    // 创建初始对象
    for (DWORD i = 0; i < m_Config.dwInitialSize; i++)
    {
        T* pObject = CreateNewObject();
        if (pObject)
        {
            m_FreeObjects.push(pObject);
        }
    }

    m_bInitialized = TRUE;
    LeaveCriticalSection(&m_CriticalSection);

    return TRUE;
}

template<typename T>
void CObjectPool<T>::Cleanup()
{
    if (!m_bInitialized)
        return;

    EnterCriticalSection(&m_CriticalSection);

    // 销毁所有对象
    for (size_t i = 0; i < m_AllObjects.size(); i++)
    {
        DestroyObject(m_AllObjects[i]);
    }

    m_AllObjects.clear();
    while (!m_FreeObjects.empty())
        m_FreeObjects.pop();

    m_bInitialized = FALSE;
    LeaveCriticalSection(&m_CriticalSection);
}

template<typename T>
T* CObjectPool<T>::Acquire()
{
    EnterCriticalSection(&m_CriticalSection);

    T* pObject = NULL;

    if (!m_FreeObjects.empty())
    {
        // 从池中获取
        pObject = m_FreeObjects.front();
        m_FreeObjects.pop();
        m_Stats.dwPoolHits++;
    }
    else if (m_AllObjects.size() < m_Config.dwMaxSize)
    {
        // 创建新对象
        pObject = CreateNewObject();
        if (pObject)
        {
            m_Stats.dwPoolMisses++;
        }
    }

    if (pObject)
    {
        m_Stats.dwCurrentActive++;
        m_Stats.dwCurrentPooled = (DWORD)m_FreeObjects.size();

        if (m_Stats.dwCurrentActive > m_Stats.dwPeakActive)
            m_Stats.dwPeakActive = m_Stats.dwCurrentActive;

        // 调用池化对象接口
        if (IPooledObject* pPooled = dynamic_cast<IPooledObject*>(pObject))
        {
            pPooled->OnPoolCreate();
        }
    }

    LeaveCriticalSection(&m_CriticalSection);
    return pObject;
}

template<typename T>
BOOL CObjectPool<T>::Release(T* pObject)
{
    if (!pObject)
        return FALSE;

    EnterCriticalSection(&m_CriticalSection);

    // 检查对象是否可以池化
    IPooledObject* pPooled = dynamic_cast<IPooledObject*>(pObject);
    if (pPooled && !pPooled->IsPoolable())
    {
        // 不可池化，直接销毁
        DestroyObject(pObject);
        LeaveCriticalSection(&m_CriticalSection);
        return TRUE;
    }

    // 重置对象状态
    if (pPooled)
    {
        pPooled->OnPoolReset();
        pPooled->OnPoolDestroy();
    }

    // 归还到池中
    m_FreeObjects.push(pObject);
    m_Stats.dwCurrentActive--;
    m_Stats.dwCurrentPooled = (DWORD)m_FreeObjects.size();

    // 检查是否需要收缩
    if (m_Config.bAutoShrink && ShouldShrink())
    {
        Shrink();
    }

    LeaveCriticalSection(&m_CriticalSection);
    return TRUE;
}

template<typename T>
T* CObjectPool<T>::CreateNewObject()
{
    T* pObject = new T();
    if (pObject)
    {
        m_AllObjects.push_back(pObject);
        m_Stats.dwTotalCreated++;
    }
    return pObject;
}

template<typename T>
void CObjectPool<T>::DestroyObject(T* pObject)
{
    if (pObject)
    {
        delete pObject;
        m_Stats.dwTotalDestroyed++;
    }
}

template<typename T>
BOOL CObjectPool<T>::ShouldShrink() const
{
    if (m_FreeObjects.size() == 0)
        return FALSE;

    DWORD dwTotalObjects = (DWORD)m_AllObjects.size();
    DWORD dwFreePercent = (DWORD)m_FreeObjects.size() * 100 / dwTotalObjects;

    return dwFreePercent >= m_Config.dwShrinkThreshold;
}

#endif // OBJECT_POOL_H
