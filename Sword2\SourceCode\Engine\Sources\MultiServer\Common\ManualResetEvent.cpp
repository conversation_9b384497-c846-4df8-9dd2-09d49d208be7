#include "stdafx.h"
#include "ManualResetEvent.h"

/*
 * namespace OnlineGameLib::Win32
 */

namespace OnlineGameLib {
namespace Win32 {

CManualResetEvent::CManualResetEvent( bool initialState /* = false */ )
   : CEvent( 0, true, initialState )
{
	// 手动重置事件构造完成
	// 基类CEvent已经完成了所有必要的初始化
}

CManualResetEvent::CManualResetEvent( const char *pName, bool initialState /* = false */ )
   : CEvent( 0, true, initialState, pName )
{
   
}

} // End of namespace OnlineGameLib
} // End of namespace Win32