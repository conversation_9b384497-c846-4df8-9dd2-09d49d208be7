//---------------------------------------------------------------------------
// Sword2 Modern Configuration Manager (c) 2024
//
// File:	ModernConfigManager.h
// Date:	2024.07.02
// Code:	AI Assistant
// Desc:	Modern C++ configuration management with type safety and validation
//---------------------------------------------------------------------------
#ifndef MODERN_CONFIG_MANAGER_H
#define MODERN_CONFIG_MANAGER_H

#include "ModernCpp.h"
#include <string>
#include <unordered_map>
#include <variant>
#include <optional>
#include <fstream>
#include <sstream>
#include <regex>
#include <functional>

namespace sword2 {

// 配置值类型
using ConfigValue = std::variant<
    bool,
    int32_t,
    uint32_t,
    int64_t,
    uint64_t,
    float,
    double,
    std::string
>;

// 配置验证器
using ConfigValidator = std::function<bool(const ConfigValue&)>;

// 配置变更回调
using ConfigChangeCallback = std::function<void(const std::string& key, const ConfigValue& oldValue, const ConfigValue& newValue)>;

// 配置项描述
struct ConfigDescriptor
{
    std::string key;
    ConfigValue defaultValue;
    std::string description;
    ConfigValidator validator;
    bool isRequired = false;
    bool isReadOnly = false;
    
    ConfigDescriptor(const std::string& k, ConfigValue def, const std::string& desc = "")
        : key(k), defaultValue(std::move(def)), description(desc) {}
    
    ConfigDescriptor& SetValidator(ConfigValidator val)
    {
        validator = std::move(val);
        return *this;
    }
    
    ConfigDescriptor& SetRequired(bool required = true)
    {
        isRequired = required;
        return *this;
    }
    
    ConfigDescriptor& SetReadOnly(bool readOnly = true)
    {
        isReadOnly = readOnly;
        return *this;
    }
};

// 现代化的配置管理器
class ModernConfigManager : public Singleton<ModernConfigManager>
{
public:
    ModernConfigManager() = default;
    ~ModernConfigManager() = default;

    // 配置文件操作
    Result<bool, std::string> LoadFromFile(const std::string& filename);
    Result<bool, std::string> SaveToFile(const std::string& filename) const;
    Result<bool, std::string> LoadFromString(const std::string& content);
    std::string SaveToString() const;

    // 配置项注册
    void RegisterConfig(const ConfigDescriptor& descriptor);
    void RegisterConfigs(const std::vector<ConfigDescriptor>& descriptors);

    // 类型安全的配置访问
    template<typename T>
    std::optional<T> Get(const std::string& key) const
    {
        auto it = m_configs.find(key);
        if (it != m_configs.end())
        {
            if (std::holds_alternative<T>(it->second))
            {
                return std::get<T>(it->second);
            }
        }
        
        // 尝试从默认值获取
        auto descIt = m_descriptors.find(key);
        if (descIt != m_descriptors.end())
        {
            if (std::holds_alternative<T>(descIt->second.defaultValue))
            {
                return std::get<T>(descIt->second.defaultValue);
            }
        }
        
        return std::nullopt;
    }

    template<typename T>
    T GetOrDefault(const std::string& key, const T& defaultValue) const
    {
        auto value = Get<T>(key);
        return value.value_or(defaultValue);
    }

    // 配置设置
    template<typename T>
    Result<bool, std::string> Set(const std::string& key, const T& value)
    {
        // 检查是否为只读
        auto descIt = m_descriptors.find(key);
        if (descIt != m_descriptors.end() && descIt->second.isReadOnly)
        {
            return Result<bool, std::string>::failure("Configuration key '" + key + "' is read-only");
        }

        ConfigValue newValue = value;
        
        // 验证值
        if (descIt != m_descriptors.end() && descIt->second.validator)
        {
            if (!descIt->second.validator(newValue))
            {
                return Result<bool, std::string>::failure("Validation failed for key '" + key + "'");
            }
        }

        // 获取旧值用于回调
        ConfigValue oldValue;
        auto it = m_configs.find(key);
        if (it != m_configs.end())
        {
            oldValue = it->second;
        }
        else if (descIt != m_descriptors.end())
        {
            oldValue = descIt->second.defaultValue;
        }

        // 设置新值
        m_configs[key] = newValue;

        // 触发变更回调
        for (const auto& callback : m_changeCallbacks)
        {
            try
            {
                callback(key, oldValue, newValue);
            }
            catch (const std::exception& e)
            {
                OutputDebugStringA(("[CONFIG] Exception in change callback: " + std::string(e.what()) + "\n").c_str());
            }
        }

        return Result<bool, std::string>::success(true);
    }

    // 批量操作
    Result<bool, std::string> SetMultiple(const std::unordered_map<std::string, ConfigValue>& configs);
    std::unordered_map<std::string, ConfigValue> GetAll() const;

    // 配置验证
    Result<bool, std::string> ValidateAll() const;
    std::vector<std::string> GetMissingRequiredKeys() const;

    // 回调管理
    void AddChangeCallback(ConfigChangeCallback callback);
    void ClearChangeCallbacks();

    // 配置查询
    bool HasKey(const std::string& key) const;
    std::vector<std::string> GetAllKeys() const;
    std::vector<std::string> GetKeysWithPrefix(const std::string& prefix) const;

    // 配置重置
    void ResetToDefaults();
    void ResetKey(const std::string& key);

    // 配置导出
    std::string ExportToJSON() const;
    std::string ExportToXML() const;
    Result<bool, std::string> ImportFromJSON(const std::string& json);

    // 环境变量支持
    void LoadFromEnvironment(const std::string& prefix = "SWORD2_");
    
    // 命令行参数支持
    void LoadFromCommandLine(int argc, char* argv[]);

    // 配置监控
    void StartFileWatcher(const std::string& filename);
    void StopFileWatcher();

private:
    std::unordered_map<std::string, ConfigValue> m_configs;
    std::unordered_map<std::string, ConfigDescriptor> m_descriptors;
    std::vector<ConfigChangeCallback> m_changeCallbacks;
    
    // 文件监控
    std::string m_watchedFile;
    std::atomic<bool> m_fileWatcherRunning{false};
    std::thread m_fileWatcherThread;

    // 内部方法
    ConfigValue ParseValue(const std::string& str, const ConfigValue& defaultValue) const;
    std::string ValueToString(const ConfigValue& value) const;
    Result<bool, std::string> ParseINILine(const std::string& line);
    void FileWatcherLoop();
};

// 配置节管理器
class ConfigSection
{
public:
    explicit ConfigSection(const std::string& sectionName)
        : m_sectionName(sectionName) {}

    template<typename T>
    std::optional<T> Get(const std::string& key) const
    {
        return ModernConfigManager::getInstance().Get<T>(m_sectionName + "." + key);
    }

    template<typename T>
    T GetOrDefault(const std::string& key, const T& defaultValue) const
    {
        return ModernConfigManager::getInstance().GetOrDefault(m_sectionName + "." + key, defaultValue);
    }

    template<typename T>
    Result<bool, std::string> Set(const std::string& key, const T& value)
    {
        return ModernConfigManager::getInstance().Set(m_sectionName + "." + key, value);
    }

    void RegisterConfig(const std::string& key, ConfigValue defaultValue, const std::string& description = "")
    {
        ConfigDescriptor desc(m_sectionName + "." + key, std::move(defaultValue), description);
        ModernConfigManager::getInstance().RegisterConfig(desc);
    }

private:
    std::string m_sectionName;
};

// 常用验证器
namespace Validators {

inline ConfigValidator Range(double min, double max)
{
    return [min, max](const ConfigValue& value) -> bool {
        return std::visit([min, max](auto&& arg) -> bool {
            using T = std::decay_t<decltype(arg)>;
            if constexpr (std::is_arithmetic_v<T>)
            {
                double val = static_cast<double>(arg);
                return val >= min && val <= max;
            }
            return false;
        }, value);
    };
}

inline ConfigValidator MinLength(size_t minLen)
{
    return [minLen](const ConfigValue& value) -> bool {
        if (std::holds_alternative<std::string>(value))
        {
            return std::get<std::string>(value).length() >= minLen;
        }
        return false;
    };
}

inline ConfigValidator Regex(const std::string& pattern)
{
    return [pattern](const ConfigValue& value) -> bool {
        if (std::holds_alternative<std::string>(value))
        {
            std::regex regex(pattern);
            return std::regex_match(std::get<std::string>(value), regex);
        }
        return false;
    };
}

inline ConfigValidator OneOf(const std::vector<ConfigValue>& allowedValues)
{
    return [allowedValues](const ConfigValue& value) -> bool {
        return std::find(allowedValues.begin(), allowedValues.end(), value) != allowedValues.end();
    };
}

} // namespace Validators

} // namespace sword2

// 全局配置管理器访问
#define CONFIG() sword2::ModernConfigManager::getInstance()

// 便捷宏
#define CONFIG_GET(type, key) CONFIG().Get<type>(key)
#define CONFIG_GET_OR_DEFAULT(type, key, default) CONFIG().GetOrDefault<type>(key, default)
#define CONFIG_SET(key, value) CONFIG().Set(key, value)
#define CONFIG_LOAD(filename) CONFIG().LoadFromFile(filename)
#define CONFIG_SAVE(filename) CONFIG().SaveToFile(filename)

// 配置节宏
#define CONFIG_SECTION(name) sword2::ConfigSection(name)

#endif // MODERN_CONFIG_MANAGER_H
