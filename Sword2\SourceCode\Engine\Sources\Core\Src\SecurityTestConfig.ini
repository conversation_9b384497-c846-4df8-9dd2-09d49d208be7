[SecurityTestFramework]
; 安全测试框架配置
EnableSecurityTesting=1
TestTimeout=30000
VerboseMode=1
StopOnFirstFailure=0
GenerateDetailedReports=1
SaveTestResults=1

; 测试输出配置
ReportPath=./SecurityTestReports/
LogLevel=2
; 0=Error, 1=Warning, 2=Info, 3=Debug

[BufferOverflowTests]
; 缓冲区溢出测试配置
EnableBufferOverflowTests=1
TestBasicOverflow=1
TestStackOverflow=1
TestHeapOverflow=1
TestFormatStringAttack=1
TestStringFunctions=1
TestBoundaryConditions=1
TestNullPointerHandling=1
TestIntegerOverflow=1

; 测试参数
MaxTestStringLength=10000
OverflowTestIterations=100
StackDepthLimit=1000

[SQLInjectionTests]
; SQL注入测试配置
EnableSQLInjectionTests=1
TestBasicInjection=1
TestUnionInjection=1
TestBlindInjection=1
TestTimeBasedInjection=1
TestFilterBypass=1
TestEncodingBypass=1
TestStoredProcedureInjection=1

; SQL注入载荷文件
PayloadFile=./TestData/sql_payloads.txt
MaxPayloadLength=1000
TestIterationsPerPayload=10

[XSSAttackTests]
; XSS攻击测试配置
EnableXSSTests=1
TestReflectedXSS=1
TestStoredXSS=1
TestDOMBasedXSS=1
TestFilterEvasion=1
TestEncodingEvasion=1
TestEventHandlerXSS=1
TestJavaScriptInjection=1

; XSS载荷配置
XSSPayloadFile=./TestData/xss_payloads.txt
MaxXSSPayloadLength=500
TestAllEncodings=1

[NetworkAttackTests]
; 网络攻击测试配置
EnableNetworkAttackTests=1
TestMalformedPackets=1
TestOversizedPackets=1
TestPacketFlooding=1
TestReplayAttack=1
TestProtocolViolation=1
TestCRCManipulation=1
TestConnectionFlooding=1

; 网络测试参数
MaxPacketSize=65536
FloodingPacketCount=1000
FloodingDuration=10000
TestTargetIP=127.0.0.1
TestTargetPort=8888

[InputValidationTests]
; 输入验证测试配置
EnableInputValidationTests=1
TestUsernameValidation=1
TestPasswordValidation=1
TestEmailValidation=1
TestChatMessageValidation=1
TestLengthLimits=1
TestSpecialCharacters=1
TestUnicodeHandling=1
TestMaliciousInput=1
TestPathTraversal=1
TestCommandInjection=1

; 输入测试参数
MaxUsernameLength=50
MaxPasswordLength=100
MaxChatMessageLength=500
MaliciousInputFile=./TestData/malicious_inputs.txt

[AccessControlTests]
; 访问控制测试配置
EnableAccessControlTests=1
TestUnauthorizedAccess=1
TestPrivilegeEscalation=1
TestRoleBypass=1
TestSessionHijacking=1
TestSessionFixation=1
TestBruteForceAttack=1
TestCredentialStuffing=1

; 访问控制测试参数
TestUserID=12345
TestAdminUserID=1
MaxLoginAttempts=100
BruteForceDelay=100

[AntiCheatTests]
; 反外挂测试配置
EnableAntiCheatTests=1
TestMemoryModification=1
TestCodeInjection=1
TestDLLInjection=1
TestSpeedHack=1
TestTeleportHack=1
TestItemDuplication=1
TestDebuggerDetection=1
TestVirtualMachineDetection=1

; 反外挂测试参数
SpeedHackMultiplier=10.0
TeleportDistance=1000.0
ItemDuplicationCount=999

[PenetrationTests]
; 渗透测试配置
EnablePenetrationTests=1
EnableReconnaissance=1
EnableVulnerabilityScanning=1
EnableExploitation=1
EnablePostExploitation=1
EnableSocialEngineering=0

; 渗透测试参数
TargetNetworkRange=***********/24
ScanPortRange=1-1000
MaxExploitAttempts=10
StealthMode=1
TimeLimit=3600000

; 渗透测试目标
TestTargets=127.0.0.1,*************
ExcludeTargets=***********

[PerformanceSecurityTests]
; 性能安全测试配置
EnablePerformanceTests=1
EnableBaselineTests=1
EnableOverheadTests=1
EnableStressTests=1
EnableLoadTests=1
EnableEnduranceTests=1

; 性能测试参数
TestDuration=300000
MaxConcurrentUsers=1000
AcceptableOverhead=20.0
StressTestDuration=600000
EnduranceTestDuration=3600000

; 性能阈值
CPUThreshold=80.0
MemoryThreshold=80.0
NetworkThreshold=80.0
ResponseTimeThreshold=1000

[SecurityBenchmarks]
; 安全基准测试配置
EnableBenchmarkTests=1
BenchmarkLevel=2
; 0=Basic, 1=Standard, 2=Advanced, 3=Expert

ComplianceStandard=0
; 0=OWASP_TOP10, 1=ISO27001, 2=NIST_CSF, 3=PCI_DSS, 4=GDPR

EnableContinuousMonitoring=1
BenchmarkCheckInterval=3600000

; 基准测试模块
TestAuthentication=1
TestAuthorization=1
TestInputValidation=1
TestOutputEncoding=1
TestCryptography=1
TestErrorHandling=1
TestLoggingMonitoring=1
TestDataProtection=1
TestCommunicationSecurity=1
TestSystemConfiguration=1

; 合规性检查
CheckOWASPCompliance=1
CheckISO27001Compliance=1
CheckNISTCSFCompliance=1
CheckPCIDSSCompliance=0
CheckGDPRCompliance=1

[TestReporting]
; 测试报告配置
GenerateHTMLReport=1
GenerateJSONReport=1
GenerateXMLReport=1
GeneratePDFReport=1
GenerateCSVReport=1

; 报告内容
IncludeExecutiveSummary=1
IncludeTechnicalDetails=1
IncludeRecommendations=1
IncludeComplianceStatus=1
IncludeRiskAssessment=1
IncludeRemediationPlan=1

; 报告分发
EmailReports=0
EmailRecipients=<EMAIL>,<EMAIL>
ReportRetentionDays=90

[TestData]
; 测试数据配置
TestDataPath=./TestData/
SQLPayloadFile=sql_payloads.txt
XSSPayloadFile=xss_payloads.txt
MaliciousInputFile=malicious_inputs.txt
NetworkPayloadFile=network_payloads.txt
BenchmarkDataFile=benchmark_data.xml

; 测试数据生成
GenerateRandomTestData=1
RandomDataSeed=12345
TestDataSize=1000

[Advanced]
; 高级测试配置
EnableFuzzing=1
EnableMutationTesting=1
EnableRegressionTesting=1
EnableAutomatedTesting=1

; 模糊测试配置
FuzzingIterations=10000
FuzzingTimeout=60000
MutationRate=0.1

; 自动化测试
ScheduledTestingEnabled=1
TestSchedule=0 2 * * *
; Cron格式：每天凌晨2点运行

; 集成测试
EnableCIIntegration=1
CIWebhookURL=http://ci.example.com/webhook
FailBuildOnSecurityIssues=1

[Logging]
; 测试日志配置
EnableTestLogging=1
LogPath=./SecurityTestLogs/
MaxLogFileSize=10485760
MaxLogFiles=10
LogRotationEnabled=1

; 日志级别
LogSecurityEvents=1
LogPerformanceMetrics=1
LogTestResults=1
LogErrorDetails=1
LogDebugInfo=1
