#include "StdAfx.h"
#include "StatSys.h"
#include "../Include/KRelaySvrProtocol.h"
#include "Global.h"

VariableServer g_VariableServer;

bool VariableServer::Initialize()
{
	return g_VariableLogManager.Initialize(&m_callBack, g_Config.m_cfgVariableLog.szServiceIp, g_Config.m_cfgVariableLog.UserName, g_Config.m_cfgVariableLog.Password, g_Config.m_cfgVariableLog.DataBaseName);
	
}

void VariableServer::Terminate()
{
	// 清理变量日志管理器
	g_VariableLogManager.Terminate();

	// 清理回调对象
	memset(&m_callBack, 0, sizeof(m_callBack));

	// 记录终止日志
	printf("VariableServer terminated successfully\n");
}

int VariableLogManagerCallBack::SendPackageToGs( void* pData, unsigned int nSize, unsigned long uConnectId )
{
	CNetConnect* pConn = g_HostServer.GetConnection(uConnectId);
	if (pConn)
		return pConn->SendPackage(pData, nSize);
	return 0;
}