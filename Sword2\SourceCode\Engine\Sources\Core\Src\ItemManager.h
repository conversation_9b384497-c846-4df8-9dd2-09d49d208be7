//---------------------------------------------------------------------------
// Sword2 Item Manager (c) 2024
//
// File:	ItemManager.h
// Date:	2024.07.02
// Code:	AI Assistant
// Desc:	Comprehensive item management system
//---------------------------------------------------------------------------
#ifndef ITEM_MANAGER_H
#define ITEM_MANAGER_H

#include "ItemSystem.h"
#include "GameDataSystem.h"
#include "LuaScriptEngine.h"
#include "UnifiedLoggingSystem.h"
#include "BusinessMonitor.h"
#include <unordered_map>
#include <memory>
#include <mutex>
#include <shared_mutex>
#include <thread>
#include <atomic>
#include <queue>
#include <functional>

namespace sword2 {

// 物品操作结果
enum class ItemOperationResult : uint8_t
{
    Success = 0,        // 成功
    Failed,             // 失败
    NotFound,           // 未找到
    NoSpace,            // 空间不足
    WeightLimit,        // 重量限制
    NotOwner,           // 非拥有者
    CannotTrade,        // 不可交易
    CannotDrop,         // 不可丢弃
    CannotSell,         // 不可出售
    RequirementNotMet,  // 需求不满足
    AlreadyEquipped,    // 已装备
    NotEquipped,        // 未装备
    Expired,            // 已过期
    Locked              // 已锁定
};

// 物品事件类型
enum class ItemEvent : uint8_t
{
    Create = 0,         // 创建物品
    Destroy,            // 销毁物品
    Move,               // 移动物品
    Use,                // 使用物品
    Equip,              // 装备物品
    Unequip,            // 卸下物品
    Enhance,            // 强化物品
    Socket,             // 镶嵌宝石
    Trade,              // 交易物品
    Drop,               // 丢弃物品
    Expire              // 物品过期
};

// 物品管理器
class ItemManager : public Singleton<ItemManager>
{
public:
    ItemManager()
        : m_running(false), m_nextInstanceId(1), m_updateInterval(std::chrono::seconds(10)) {}
    
    ~ItemManager()
    {
        Stop();
    }
    
    // 启动物品管理器
    bool Start()
    {
        if (m_running) return true;
        
        // 加载物品数据
        if (!LoadItemData())
        {
            LOG_ERROR("ITEM_MGR", "Failed to load item data");
            return false;
        }
        
        m_running = true;
        m_updateThread = std::thread(&ItemManager::UpdateLoop, this);
        
        LOG_INFO("ITEM_MGR", "Item manager started with " + std::to_string(m_itemTemplates.size()) + " item templates");
        return true;
    }
    
    // 停止物品管理器
    void Stop()
    {
        if (!m_running) return;
        
        m_running = false;
        if (m_updateThread.joinable())
        {
            m_updateThread.join();
        }
        
        // 清理数据
        {
            std::unique_lock<std::shared_mutex> lock(m_templatesMutex);
            m_itemTemplates.clear();
        }
        
        {
            std::unique_lock<std::shared_mutex> lock(m_instancesMutex);
            m_itemInstances.clear();
        }
        
        LOG_INFO("ITEM_MGR", "Item manager stopped");
    }
    
    // 注册物品模板
    bool RegisterItemTemplate(const ItemTemplate& itemTemplate)
    {
        std::unique_lock<std::shared_mutex> lock(m_templatesMutex);
        
        if (m_itemTemplates.find(itemTemplate.itemId) != m_itemTemplates.end())
        {
            LOG_WARNING("ITEM_MGR", "Item template already exists: " + std::to_string(itemTemplate.itemId));
            return false;
        }
        
        m_itemTemplates[itemTemplate.itemId] = std::make_shared<ItemTemplate>(itemTemplate);
        
        LOG_DEBUG("ITEM_MGR", "Registered item template: " + itemTemplate.name + " (ID: " + std::to_string(itemTemplate.itemId) + ")");
        return true;
    }
    
    // 获取物品模板
    std::shared_ptr<ItemTemplate> GetItemTemplate(uint32_t itemId)
    {
        std::shared_lock<std::shared_mutex> lock(m_templatesMutex);
        auto it = m_itemTemplates.find(itemId);
        return (it != m_itemTemplates.end()) ? it->second : nullptr;
    }
    
    // 创建物品实例
    uint32_t CreateItemInstance(uint32_t templateId, uint32_t ownerId, uint32_t stackCount = 1)
    {
        auto itemTemplate = GetItemTemplate(templateId);
        if (!itemTemplate)
        {
            LOG_WARNING("ITEM_MGR", "Item template not found: " + std::to_string(templateId));
            return 0;
        }
        
        uint32_t instanceId = m_nextInstanceId++;
        auto instance = std::make_unique<ItemInstance>(instanceId, templateId, ownerId);
        instance->stackCount = std::min(stackCount, itemTemplate->maxStack);
        instance->durability = itemTemplate->maxDurability;
        
        // 生成随机属性
        GenerateRandomAttributes(*instance, *itemTemplate);
        
        {
            std::unique_lock<std::shared_mutex> lock(m_instancesMutex);
            m_itemInstances[instanceId] = std::move(instance);
        }
        
        // 触发物品创建事件
        TriggerItemEvent(ItemEvent::Create, instanceId, ownerId, 0);
        
        LOG_INFO("ITEM_MGR", "Created item instance " + std::to_string(instanceId) + 
                " for template " + std::to_string(templateId) + " owner " + std::to_string(ownerId));
        return instanceId;
    }
    
    // 销毁物品实例
    bool DestroyItemInstance(uint32_t instanceId)
    {
        std::unique_lock<std::shared_mutex> lock(m_instancesMutex);
        
        auto it = m_itemInstances.find(instanceId);
        if (it == m_itemInstances.end())
        {
            LOG_WARNING("ITEM_MGR", "Item instance not found: " + std::to_string(instanceId));
            return false;
        }
        
        uint32_t ownerId = it->second->ownerId;
        m_itemInstances.erase(it);
        
        // 触发物品销毁事件
        TriggerItemEvent(ItemEvent::Destroy, instanceId, ownerId, 0);
        
        LOG_INFO("ITEM_MGR", "Destroyed item instance " + std::to_string(instanceId));
        return true;
    }
    
    // 获取物品实例
    std::shared_ptr<ItemInstance> GetItemInstance(uint32_t instanceId)
    {
        std::shared_lock<std::shared_mutex> lock(m_instancesMutex);
        auto it = m_itemInstances.find(instanceId);
        return (it != m_itemInstances.end()) ? it->second : nullptr;
    }
    
    // 移动物品
    ItemOperationResult MoveItem(uint32_t instanceId, ItemPosition newPosition, uint32_t x = 0, uint32_t y = 0)
    {
        auto instance = GetItemInstance(instanceId);
        if (!instance)
            return ItemOperationResult::NotFound;
        
        if (instance->isLocked)
            return ItemOperationResult::Locked;
        
        ItemPosition oldPosition = instance->position;
        instance->position = newPosition;
        
        // 触发物品移动事件
        TriggerItemEvent(ItemEvent::Move, instanceId, instance->ownerId, 0);
        
        LOG_DEBUG("ITEM_MGR", "Moved item " + std::to_string(instanceId) + 
                 " from position " + std::to_string(static_cast<int>(oldPosition)) + 
                 " to " + std::to_string(static_cast<int>(newPosition)));
        
        return ItemOperationResult::Success;
    }
    
    // 使用物品
    ItemOperationResult UseItem(uint32_t instanceId, uint32_t playerId)
    {
        auto instance = GetItemInstance(instanceId);
        if (!instance)
            return ItemOperationResult::NotFound;
        
        if (instance->ownerId != playerId)
            return ItemOperationResult::NotOwner;
        
        if (instance->IsExpired())
            return ItemOperationResult::Expired;
        
        auto itemTemplate = GetItemTemplate(instance->templateId);
        if (!itemTemplate)
            return ItemOperationResult::NotFound;
        
        // 执行物品脚本
        if (!itemTemplate->scriptPath.empty())
        {
            ExecuteItemScript(itemTemplate->scriptPath, instanceId, playerId);
        }
        
        // 触发物品使用事件
        TriggerItemEvent(ItemEvent::Use, instanceId, playerId, 0);
        
        LOG_INFO("ITEM_MGR", "Player " + std::to_string(playerId) + " used item " + std::to_string(instanceId));
        return ItemOperationResult::Success;
    }
    
    // 装备物品
    ItemOperationResult EquipItem(uint32_t instanceId, uint32_t playerId)
    {
        auto instance = GetItemInstance(instanceId);
        if (!instance)
            return ItemOperationResult::NotFound;
        
        if (instance->ownerId != playerId)
            return ItemOperationResult::NotOwner;
        
        auto itemTemplate = GetItemTemplate(instance->templateId);
        if (!itemTemplate)
            return ItemOperationResult::NotFound;
        
        if (itemTemplate->genre != ItemGenre::Equipment)
            return ItemOperationResult::Failed;
        
        // 这里应该检查玩家是否满足装备需求
        // 暂时简化处理
        
        instance->position = ItemPosition::Equip;
        
        // 触发装备事件
        TriggerItemEvent(ItemEvent::Equip, instanceId, playerId, 0);
        
        LOG_INFO("ITEM_MGR", "Player " + std::to_string(playerId) + " equipped item " + std::to_string(instanceId));
        return ItemOperationResult::Success;
    }
    
    // 卸下装备
    ItemOperationResult UnequipItem(uint32_t instanceId, uint32_t playerId)
    {
        auto instance = GetItemInstance(instanceId);
        if (!instance)
            return ItemOperationResult::NotFound;
        
        if (instance->ownerId != playerId)
            return ItemOperationResult::NotOwner;
        
        if (instance->position != ItemPosition::Equip)
            return ItemOperationResult::NotEquipped;
        
        instance->position = ItemPosition::EquipRoom;
        
        // 触发卸装事件
        TriggerItemEvent(ItemEvent::Unequip, instanceId, playerId, 0);
        
        LOG_INFO("ITEM_MGR", "Player " + std::to_string(playerId) + " unequipped item " + std::to_string(instanceId));
        return ItemOperationResult::Success;
    }
    
    // 强化物品
    ItemOperationResult EnhanceItem(uint32_t instanceId, uint32_t playerId)
    {
        auto instance = GetItemInstance(instanceId);
        if (!instance)
            return ItemOperationResult::NotFound;
        
        if (instance->ownerId != playerId)
            return ItemOperationResult::NotOwner;
        
        if (!instance->Enhance())
            return ItemOperationResult::Failed;
        
        // 触发强化事件
        TriggerItemEvent(ItemEvent::Enhance, instanceId, playerId, 0);
        
        LOG_INFO("ITEM_MGR", "Player " + std::to_string(playerId) + " enhanced item " + std::to_string(instanceId) + 
                " to level " + std::to_string(instance->enhanceLevel));
        return ItemOperationResult::Success;
    }
    
    // 镶嵌宝石
    ItemOperationResult SocketGem(uint32_t instanceId, uint32_t gemId, uint32_t playerId)
    {
        auto instance = GetItemInstance(instanceId);
        if (!instance)
            return ItemOperationResult::NotFound;
        
        if (instance->ownerId != playerId)
            return ItemOperationResult::NotOwner;
        
        if (!instance->SocketGem(gemId))
            return ItemOperationResult::Failed;
        
        // 触发镶嵌事件
        TriggerItemEvent(ItemEvent::Socket, instanceId, playerId, gemId);
        
        LOG_INFO("ITEM_MGR", "Player " + std::to_string(playerId) + " socketed gem " + std::to_string(gemId) + 
                " into item " + std::to_string(instanceId));
        return ItemOperationResult::Success;
    }
    
    // 交易物品
    ItemOperationResult TradeItem(uint32_t instanceId, uint32_t fromPlayerId, uint32_t toPlayerId)
    {
        auto instance = GetItemInstance(instanceId);
        if (!instance)
            return ItemOperationResult::NotFound;
        
        if (instance->ownerId != fromPlayerId)
            return ItemOperationResult::NotOwner;
        
        if (!instance->canTrade)
            return ItemOperationResult::CannotTrade;
        
        if (instance->isBound)
            return ItemOperationResult::CannotTrade;
        
        instance->ownerId = toPlayerId;
        
        // 触发交易事件
        TriggerItemEvent(ItemEvent::Trade, instanceId, fromPlayerId, toPlayerId);
        
        LOG_INFO("ITEM_MGR", "Traded item " + std::to_string(instanceId) + 
                " from player " + std::to_string(fromPlayerId) + " to " + std::to_string(toPlayerId));
        return ItemOperationResult::Success;
    }
    
    // 丢弃物品
    ItemOperationResult DropItem(uint32_t instanceId, uint32_t playerId)
    {
        auto instance = GetItemInstance(instanceId);
        if (!instance)
            return ItemOperationResult::NotFound;
        
        if (instance->ownerId != playerId)
            return ItemOperationResult::NotOwner;
        
        if (!instance->canDrop)
            return ItemOperationResult::CannotDrop;
        
        // 触发丢弃事件
        TriggerItemEvent(ItemEvent::Drop, instanceId, playerId, 0);
        
        // 销毁物品
        DestroyItemInstance(instanceId);
        
        LOG_INFO("ITEM_MGR", "Player " + std::to_string(playerId) + " dropped item " + std::to_string(instanceId));
        return ItemOperationResult::Success;
    }
    
    // 获取玩家物品列表
    std::vector<uint32_t> GetPlayerItems(uint32_t playerId, ItemPosition position = ItemPosition::Hand)
    {
        std::vector<uint32_t> items;
        
        std::shared_lock<std::shared_mutex> lock(m_instancesMutex);
        for (const auto& [instanceId, instance] : m_itemInstances)
        {
            if (instance && instance->ownerId == playerId)
            {
                if (position == ItemPosition::Hand || instance->position == position)
                {
                    items.push_back(instanceId);
                }
            }
        }
        
        return items;
    }
    
    // 获取物品统计信息
    struct ItemStatistics
    {
        size_t totalTemplates = 0;
        size_t totalInstances = 0;
        std::unordered_map<ItemGenre, size_t> itemsByGenre;
        std::unordered_map<EquipDetailType, size_t> equipmentByType;
        std::unordered_map<EquipNature, size_t> equipmentByNature;
        std::unordered_map<uint32_t, size_t> itemsByOwner;
    };
    
    ItemStatistics GetStatistics()
    {
        std::shared_lock<std::shared_mutex> templateLock(m_templatesMutex);
        std::shared_lock<std::shared_mutex> instanceLock(m_instancesMutex);
        
        ItemStatistics stats;
        stats.totalTemplates = m_itemTemplates.size();
        stats.totalInstances = m_itemInstances.size();
        
        for (const auto& [templateId, itemTemplate] : m_itemTemplates)
        {
            if (!itemTemplate) continue;
            
            stats.itemsByGenre[itemTemplate->genre]++;
            if (itemTemplate->genre == ItemGenre::Equipment)
            {
                stats.equipmentByType[itemTemplate->detailType]++;
                stats.equipmentByNature[itemTemplate->nature]++;
            }
        }
        
        for (const auto& [instanceId, instance] : m_itemInstances)
        {
            if (!instance) continue;
            
            stats.itemsByOwner[instance->ownerId]++;
        }
        
        return stats;
    }
    
    // 设置更新间隔
    void SetUpdateInterval(std::chrono::seconds interval)
    {
        m_updateInterval = interval;
        LOG_INFO("ITEM_MGR", "Item update interval set to " + std::to_string(interval.count()) + " seconds");
    }

private:
    std::atomic<bool> m_running;
    std::thread m_updateThread;
    std::chrono::seconds m_updateInterval;
    
    // 物品数据
    mutable std::shared_mutex m_templatesMutex;
    std::unordered_map<uint32_t, std::shared_ptr<ItemTemplate>> m_itemTemplates;
    
    mutable std::shared_mutex m_instancesMutex;
    std::unordered_map<uint32_t, std::shared_ptr<ItemInstance>> m_itemInstances;
    
    std::atomic<uint32_t> m_nextInstanceId;
    
    // 更新循环
    void UpdateLoop()
    {
        while (m_running)
        {
            try
            {
                UpdateExpiredItems();
                std::this_thread::sleep_for(m_updateInterval);
            }
            catch (const std::exception& e)
            {
                LOG_ERROR("ITEM_MGR", std::string("Error in update loop: ") + e.what());
            }
        }
    }
    
    void UpdateExpiredItems()
    {
        std::vector<uint32_t> expiredItems;
        
        {
            std::shared_lock<std::shared_mutex> lock(m_instancesMutex);
            for (const auto& [instanceId, instance] : m_itemInstances)
            {
                if (instance && instance->IsExpired())
                {
                    expiredItems.push_back(instanceId);
                }
            }
        }
        
        // 处理过期物品
        for (uint32_t instanceId : expiredItems)
        {
            auto instance = GetItemInstance(instanceId);
            if (instance)
            {
                TriggerItemEvent(ItemEvent::Expire, instanceId, instance->ownerId, 0);
                DestroyItemInstance(instanceId);
                
                LOG_INFO("ITEM_MGR", "Item " + std::to_string(instanceId) + " expired and was destroyed");
            }
        }
    }
    
    bool LoadItemData()
    {
        LOG_INFO("ITEM_MGR", "Loading item data...");
        
        // 从GameDataSystem加载物品数据
        // 这里应该加载各种装备配置文件
        // 暂时创建一些示例物品
        
        CreateExampleItems();
        
        LOG_INFO("ITEM_MGR", "Loaded " + std::to_string(m_itemTemplates.size()) + " item templates");
        return true;
    }
    
    void CreateExampleItems()
    {
        std::unique_lock<std::shared_mutex> lock(m_templatesMutex);
        
        // 创建武器示例
        {
            ItemTemplate item(10001, "新手剑", ItemGenre::Equipment);
            item.description = "新手专用的基础剑";
            item.detailType = EquipDetailType::MeleeWeapon;
            item.equipPart = ItemPart::Weapon;
            item.nature = EquipNature::Normal;
            item.level = 1;
            item.price = 100;
            item.weight = 5;
            item.maxDurability = 100;
            item.requirement.requiredLevel = 1;
            item.baseAttributes.emplace_back(ItemAttributeType::Damage, 10, 15);
            item.scriptPath = "items/weapon_basic_sword.lua";
            
            m_itemTemplates[item.itemId] = std::make_shared<ItemTemplate>(item);
        }
        
        // 创建护甲示例
        {
            ItemTemplate item(20001, "布衣", ItemGenre::Equipment);
            item.description = "简单的布制衣服";
            item.detailType = EquipDetailType::Armor;
            item.equipPart = ItemPart::Body;
            item.nature = EquipNature::Normal;
            item.level = 1;
            item.price = 50;
            item.weight = 3;
            item.maxDurability = 80;
            item.requirement.requiredLevel = 1;
            item.baseAttributes.emplace_back(ItemAttributeType::Defense, 5, 8);
            item.scriptPath = "items/armor_basic_cloth.lua";
            
            m_itemTemplates[item.itemId] = std::make_shared<ItemTemplate>(item);
        }
        
        // 创建药品示例
        {
            ItemTemplate item(30001, "小还丹", ItemGenre::Medicine);
            item.description = "恢复少量生命值的丹药";
            item.level = 1;
            item.price = 10;
            item.weight = 1;
            item.maxStack = 100;
            item.baseAttributes.emplace_back(ItemAttributeType::Health, 50);
            item.scriptPath = "items/medicine_small_heal.lua";
            
            m_itemTemplates[item.itemId] = std::make_shared<ItemTemplate>(item);
        }
        
        // 创建材料示例
        {
            ItemTemplate item(40001, "铁矿石", ItemGenre::Materials);
            item.description = "用于锻造的基础材料";
            item.level = 1;
            item.price = 5;
            item.weight = 2;
            item.maxStack = 200;
            item.scriptPath = "items/material_iron_ore.lua";
            
            m_itemTemplates[item.itemId] = std::make_shared<ItemTemplate>(item);
        }
    }
    
    void GenerateRandomAttributes(ItemInstance& instance, const ItemTemplate& itemTemplate)
    {
        // 这里可以根据物品品质生成随机属性
        // 暂时简化处理
        if (itemTemplate.nature == EquipNature::Violet)
        {
            // 紫色装备有额外属性
            instance.randomAttributes.emplace_back(ItemAttributeType::Lucky, 1, 3);
        }
        else if (itemTemplate.nature == EquipNature::Gold)
        {
            // 金色装备有更多属性
            instance.randomAttributes.emplace_back(ItemAttributeType::Lucky, 3, 5);
            instance.randomAttributes.emplace_back(ItemAttributeType::CriticalRate, 1, 2, true);
        }
    }
    
    void ExecuteItemScript(const std::string& scriptPath, uint32_t instanceId, uint32_t playerId)
    {
        // 这里应该执行物品脚本
        // 暂时简化处理
        LOG_DEBUG("ITEM_MGR", "Executing item script: " + scriptPath + 
                 " for item " + std::to_string(instanceId) + " player " + std::to_string(playerId));
    }
    
    void TriggerItemEvent(ItemEvent event, uint32_t instanceId, uint32_t playerId, uint32_t extraData)
    {
        // 这里可以触发脚本事件或其他处理
        std::string eventName = GetEventName(event);
        LOG_DEBUG("ITEM_MGR", "Item event triggered: " + eventName + " for item " + std::to_string(instanceId) + 
                 ", player " + std::to_string(playerId));
        
        // 可以在这里执行脚本或其他逻辑
    }
    
    std::string GetEventName(ItemEvent event)
    {
        switch (event)
        {
        case ItemEvent::Create: return "Create";
        case ItemEvent::Destroy: return "Destroy";
        case ItemEvent::Move: return "Move";
        case ItemEvent::Use: return "Use";
        case ItemEvent::Equip: return "Equip";
        case ItemEvent::Unequip: return "Unequip";
        case ItemEvent::Enhance: return "Enhance";
        case ItemEvent::Socket: return "Socket";
        case ItemEvent::Trade: return "Trade";
        case ItemEvent::Drop: return "Drop";
        case ItemEvent::Expire: return "Expire";
        default: return "Unknown";
        }
    }
};

} // namespace sword2

// 全局物品管理器访问
#define ITEM_MANAGER() sword2::ItemManager::getInstance()

// 便捷宏定义
#define START_ITEM_SYSTEM() ITEM_MANAGER().Start()
#define STOP_ITEM_SYSTEM() ITEM_MANAGER().Stop()

#define REGISTER_ITEM_TEMPLATE(itemTemplate) ITEM_MANAGER().RegisterItemTemplate(itemTemplate)
#define GET_ITEM_TEMPLATE(itemId) ITEM_MANAGER().GetItemTemplate(itemId)

#define CREATE_ITEM_INSTANCE(templateId, ownerId, stackCount) ITEM_MANAGER().CreateItemInstance(templateId, ownerId, stackCount)
#define DESTROY_ITEM_INSTANCE(instanceId) ITEM_MANAGER().DestroyItemInstance(instanceId)
#define GET_ITEM_INSTANCE(instanceId) ITEM_MANAGER().GetItemInstance(instanceId)

#define MOVE_ITEM(instanceId, position, x, y) ITEM_MANAGER().MoveItem(instanceId, position, x, y)
#define USE_ITEM(instanceId, playerId) ITEM_MANAGER().UseItem(instanceId, playerId)
#define EQUIP_ITEM(instanceId, playerId) ITEM_MANAGER().EquipItem(instanceId, playerId)
#define UNEQUIP_ITEM(instanceId, playerId) ITEM_MANAGER().UnequipItem(instanceId, playerId)

#define ENHANCE_ITEM(instanceId, playerId) ITEM_MANAGER().EnhanceItem(instanceId, playerId)
#define SOCKET_GEM(instanceId, gemId, playerId) ITEM_MANAGER().SocketGem(instanceId, gemId, playerId)

#define TRADE_ITEM(instanceId, fromPlayerId, toPlayerId) ITEM_MANAGER().TradeItem(instanceId, fromPlayerId, toPlayerId)
#define DROP_ITEM(instanceId, playerId) ITEM_MANAGER().DropItem(instanceId, playerId)

#define GET_PLAYER_ITEMS(playerId, position) ITEM_MANAGER().GetPlayerItems(playerId, position)

#endif // ITEM_MANAGER_H
