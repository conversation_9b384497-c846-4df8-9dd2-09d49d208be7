//---------------------------------------------------------------------------
// Sword2 Map Data Parser (c) 2024
//
// File:	MapDataParser.h
// Date:	2024.07.02
// Code:	AI Assistant
// Desc:	Specialized parser for worldset and map configuration files
//---------------------------------------------------------------------------
#ifndef MAP_DATA_PARSER_H
#define MAP_DATA_PARSER_H

#include "GameDataSystem.h"
#include <regex>

namespace sword2 {

// 地图类型枚举
enum class MapType : uint8_t
{
    Normal = 0,         // 普通地图
    City = 1,           // 城镇
    Dungeon = 2,        // 副本
    PvP = 3,            // PVP地图
    Raid = 4,           // 团队副本
    Special = 5         // 特殊地图
};

// 地图状态
enum class MapStatus : uint8_t
{
    Disabled = 0,       // 禁用
    Enabled = 1,        // 启用
    Maintenance = 2     // 维护中
};

// 增强的地图数据结构
struct EnhancedMapData : public MapData
{
    MapType mapType = MapType::Normal;
    MapStatus status = MapStatus::Enabled;
    
    // 地图属性
    uint32_t minLevel = 1;          // 最低等级要求
    uint32_t maxLevel = 999;        // 最高等级要求
    uint32_t recommendLevel = 1;    // 推荐等级
    
    // 地图限制
    uint32_t timeLimit = 0;         // 时间限制（秒）
    uint32_t entryLimit = 0;        // 进入次数限制
    bool requiresParty = false;     // 是否需要组队
    
    // 地图区域
    struct MapRegion
    {
        std::string name;           // 区域名称
        int x1, y1, x2, y2;        // 区域坐标
        std::string type;           // 区域类型
        std::unordered_map<std::string, std::string> properties;
    };
    
    std::vector<MapRegion> regions; // 地图区域
    
    // NPC和怪物
    struct NPCSpawn
    {
        uint32_t npcId;            // NPC ID
        std::string npcName;       // NPC名称
        int x, y;                  // 坐标
        uint32_t respawnTime = 0;  // 重生时间
        std::string scriptFile;    // 脚本文件
    };
    
    std::vector<NPCSpawn> npcs;     // NPC列表
    std::vector<NPCSpawn> monsters; // 怪物列表
    
    // 传送点
    struct TeleportPoint
    {
        uint32_t id;               // 传送点ID
        std::string name;          // 传送点名称
        int x, y;                  // 坐标
        uint32_t targetMapId = 0;  // 目标地图ID
        int targetX = 0, targetY = 0; // 目标坐标
        uint32_t requiredLevel = 1; // 需求等级
        uint32_t cost = 0;         // 传送费用
    };
    
    std::vector<TeleportPoint> teleports; // 传送点列表
    
    // 资源点
    struct ResourcePoint
    {
        uint32_t id;               // 资源点ID
        std::string type;          // 资源类型
        int x, y;                  // 坐标
        uint32_t respawnTime = 300; // 重生时间
        uint32_t requiredSkill = 0; // 需求技能
        std::vector<uint32_t> dropItems; // 掉落物品
    };
    
    std::vector<ResourcePoint> resources; // 资源点列表
    
    EnhancedMapData() = default;
    EnhancedMapData(uint32_t id, const std::string& name) : MapData(id, name) {}
    
    // 检查玩家是否可以进入地图
    bool CanPlayerEnter(uint32_t playerLevel, bool hasParty = false) const
    {
        if (status != MapStatus::Enabled)
            return false;
        
        if (playerLevel < minLevel || playerLevel > maxLevel)
            return false;
        
        if (requiresParty && !hasParty)
            return false;
        
        return true;
    }
    
    // 获取地图类型描述
    std::string GetMapTypeDescription() const
    {
        switch (mapType)
        {
        case MapType::Normal: return "普通地图";
        case MapType::City: return "城镇";
        case MapType::Dungeon: return "副本";
        case MapType::PvP: return "PVP地图";
        case MapType::Raid: return "团队副本";
        case MapType::Special: return "特殊地图";
        default: return "未知";
        }
    }
    
    // 查找最近的传送点
    const TeleportPoint* FindNearestTeleport(int x, int y) const
    {
        const TeleportPoint* nearest = nullptr;
        int minDistance = INT_MAX;
        
        for (const auto& teleport : teleports)
        {
            int distance = abs(teleport.x - x) + abs(teleport.y - y);
            if (distance < minDistance)
            {
                minDistance = distance;
                nearest = &teleport;
            }
        }
        
        return nearest;
    }
};

// 地图数据解析器
class MapDataParser : public IDataFileParser
{
public:
    bool ParseFile(const std::string& filePath, std::vector<GameDataItem>& items) override
    {
        std::vector<EnhancedMapData> maps;
        
        // 根据文件名确定解析方式
        if (filePath.find("worldset") != std::string::npos)
        {
            if (!ParseWorldSetFile(filePath, maps))
            {
                LOG_ERROR("MAP", "Failed to parse worldset file: " + filePath);
                return false;
            }
        }
        else
        {
            if (!ParseMapConfigFile(filePath, maps))
            {
                LOG_ERROR("MAP", "Failed to parse map config file: " + filePath);
                return false;
            }
        }
        
        // 转换为基础数据项
        items.clear();
        items.reserve(maps.size());
        
        for (const auto& map : maps)
        {
            GameDataItem item;
            item.id = map.id;
            item.name = map.name;
            item.description = map.description;
            
            // 复制所有属性
            item.properties = map.properties;
            
            // 添加地图特有属性
            item.SetProperty("map_type", static_cast<int>(map.mapType));
            item.SetProperty("status", static_cast<int>(map.status));
            item.SetProperty("world_id", map.worldId);
            item.SetProperty("max_players", map.maxPlayers);
            item.SetProperty("pvp_enabled", map.isPvpEnabled);
            item.SetProperty("min_level", map.minLevel);
            item.SetProperty("max_level", map.maxLevel);
            item.SetProperty("recommend_level", map.recommendLevel);
            item.SetProperty("time_limit", map.timeLimit);
            item.SetProperty("entry_limit", map.entryLimit);
            item.SetProperty("requires_party", map.requiresParty);
            item.SetProperty("map_file", map.mapFile);
            
            // 统计信息
            item.SetProperty("npc_count", static_cast<int>(map.npcs.size()));
            item.SetProperty("monster_count", static_cast<int>(map.monsters.size()));
            item.SetProperty("teleport_count", static_cast<int>(map.teleports.size()));
            item.SetProperty("resource_count", static_cast<int>(map.resources.size()));
            
            items.push_back(std::move(item));
        }
        
        LOG_INFO("MAP", "Successfully parsed " + std::to_string(items.size()) + " maps from " + filePath);
        return true;
    }
    
    DataFileFormat GetSupportedFormat() const override
    {
        return DataFileFormat::INI;
    }

private:
    bool ParseWorldSetFile(const std::string& filePath, std::vector<EnhancedMapData>& maps)
    {
        std::ifstream file(filePath);
        if (!file.is_open())
        {
            LOG_ERROR("MAP", "Cannot open worldset file: " + filePath);
            return false;
        }
        
        std::string line;
        std::string currentSection;
        
        while (std::getline(file, line))
        {
            line = TrimString(line);
            
            // 跳过空行和注释
            if (line.empty() || line[0] == ';' || line[0] == '#')
                continue;
            
            // 检查是否是节
            if (line[0] == '[' && line.back() == ']')
            {
                currentSection = line.substr(1, line.length() - 2);
                continue;
            }
            
            // 解析世界列表
            if (currentSection == "WorldList")
            {
                size_t equalPos = line.find('=');
                if (equalPos != std::string::npos)
                {
                    std::string worldKey = TrimString(line.substr(0, equalPos));
                    std::string worldValue = TrimString(line.substr(equalPos + 1));
                    
                    // 解析世界ID
                    if (worldKey.substr(0, 6) == "World_")
                    {
                        try
                        {
                            uint32_t worldId = std::stoul(worldKey.substr(6));
                            int status = std::stoi(worldValue);
                            
                            if (status == 1) // 只加载启用的世界
                            {
                                EnhancedMapData map;
                                map.id = worldId;
                                map.worldId = worldId;
                                map.name = "World_" + std::to_string(worldId);
                                map.status = MapStatus::Enabled;
                                map.mapType = DetermineMapType(worldId);
                                
                                // 设置默认属性
                                SetDefaultMapProperties(map);
                                
                                maps.push_back(std::move(map));
                            }
                        }
                        catch (const std::exception& e)
                        {
                            LOG_WARNING("MAP", "Failed to parse world entry: " + worldKey + " = " + worldValue);
                        }
                    }
                }
            }
        }
        
        LOG_INFO("MAP", "Parsed " + std::to_string(maps.size()) + " worlds from worldset");
        return true;
    }
    
    bool ParseMapConfigFile(const std::string& filePath, std::vector<EnhancedMapData>& maps)
    {
        // 这里可以解析其他地图配置文件
        // 例如具体的地图属性、NPC位置等
        LOG_INFO("MAP", "Parsing map config file: " + filePath);
        return true;
    }
    
    MapType DetermineMapType(uint32_t worldId)
    {
        // 根据世界ID范围确定地图类型
        if (worldId >= 100 && worldId <= 199)
            return MapType::City;           // 城镇地图
        else if (worldId >= 200 && worldId <= 299)
            return MapType::Normal;         // 普通地图
        else if (worldId >= 300 && worldId <= 399)
            return MapType::Dungeon;        // 副本地图
        else if (worldId >= 400 && worldId <= 499)
            return MapType::PvP;            // PVP地图
        else if (worldId >= 500 && worldId <= 599)
            return MapType::Raid;           // 团队副本
        else
            return MapType::Special;        // 特殊地图
    }
    
    void SetDefaultMapProperties(EnhancedMapData& map)
    {
        // 根据地图类型设置默认属性
        switch (map.mapType)
        {
        case MapType::City:
            map.maxPlayers = 200;
            map.isPvpEnabled = false;
            map.minLevel = 1;
            map.maxLevel = 999;
            break;
            
        case MapType::Normal:
            map.maxPlayers = 100;
            map.isPvpEnabled = true;
            map.minLevel = 1;
            map.maxLevel = 999;
            break;
            
        case MapType::Dungeon:
            map.maxPlayers = 10;
            map.isPvpEnabled = false;
            map.requiresParty = true;
            map.timeLimit = 3600; // 1小时
            map.entryLimit = 3;   // 每日3次
            break;
            
        case MapType::PvP:
            map.maxPlayers = 50;
            map.isPvpEnabled = true;
            map.timeLimit = 1800; // 30分钟
            break;
            
        case MapType::Raid:
            map.maxPlayers = 20;
            map.isPvpEnabled = false;
            map.requiresParty = true;
            map.timeLimit = 7200; // 2小时
            map.entryLimit = 1;   // 每日1次
            break;
            
        default:
            map.maxPlayers = 100;
            map.isPvpEnabled = false;
            break;
        }
        
        // 根据世界ID设置推荐等级
        if (map.worldId >= 100 && map.worldId <= 120)
            map.recommendLevel = 1;
        else if (map.worldId >= 121 && map.worldId <= 150)
            map.recommendLevel = 20;
        else if (map.worldId >= 200 && map.worldId <= 250)
            map.recommendLevel = 40;
        else if (map.worldId >= 300 && map.worldId <= 350)
            map.recommendLevel = 60;
        else if (map.worldId >= 400 && map.worldId <= 450)
            map.recommendLevel = 80;
        else
            map.recommendLevel = 100;
        
        // 设置等级范围
        map.minLevel = std::max(1u, map.recommendLevel - 10);
        map.maxLevel = map.recommendLevel + 20;
        
        // 设置地图文件路径
        map.mapFile = "maps/world_" + std::to_string(map.worldId) + ".map";
        
        // 添加到属性中
        map.properties["world_id"] = std::to_string(map.worldId);
        map.properties["map_type"] = std::to_string(static_cast<int>(map.mapType));
        map.properties["recommend_level"] = std::to_string(map.recommendLevel);
    }
    
    std::string TrimString(const std::string& str)
    {
        size_t start = str.find_first_not_of(" \t\r\n");
        if (start == std::string::npos)
            return "";
        
        size_t end = str.find_last_not_of(" \t\r\n");
        return str.substr(start, end - start + 1);
    }
};

} // namespace sword2

#endif // MAP_DATA_PARSER_H
