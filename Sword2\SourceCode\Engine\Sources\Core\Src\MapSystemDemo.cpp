//---------------------------------------------------------------------------
// Sword2 Map System Demo (c) 2024
//
// File:	MapSystemDemo.cpp
// Date:	2024.07.02
// Code:	AI Assistant
// Desc:	Comprehensive demonstration of the map and scene system
//---------------------------------------------------------------------------

#include "KCore.h"
#include "MapSystem.h"
#include "MapManager.h"
#include "MapScriptIntegration.h"
#include "PlayerSystem.h"
#include "PlayerManager.h"
#include "NPCSystem.h"
#include "NPCManager.h"
#include "LuaScriptEngine.h"
#include "UnifiedLoggingSystem.h"

namespace sword2 {

// 地图系统演示类
class MapSystemDemo
{
public:
    MapSystemDemo()
    {
        m_initialized = false;
    }
    
    bool Initialize()
    {
        if (m_initialized) return true;
        
        printf("[MAP_DEMO] Initializing map system demo...\n");
        
        // 初始化日志系统
        LOGGER().Initialize();
        LOGGER().AddConsoleAppender(LogLevel::Debug);
        
        // 启动玩家管理器
        if (!START_PLAYER_SYSTEM())
        {
            printf("[MAP_DEMO] Failed to start player system\n");
            return false;
        }
        
        // 启动NPC管理器
        if (!START_NPC_SYSTEM())
        {
            printf("[MAP_DEMO] Failed to start NPC system\n");
            return false;
        }
        
        // 初始化脚本引擎
        if (!INIT_SCRIPT_ENGINE("../../../Server/script"))
        {
            printf("[MAP_DEMO] Failed to initialize script engine\n");
            return false;
        }
        
        // 启动地图管理器
        if (!START_MAP_SYSTEM())
        {
            printf("[MAP_DEMO] Failed to start map system\n");
            return false;
        }
        
        // 初始化地图脚本集成
        if (!INIT_MAP_SCRIPT_INTEGRATION())
        {
            printf("[MAP_DEMO] Failed to initialize map script integration\n");
            return false;
        }
        
        m_initialized = true;
        LOG_INFO("MAP_DEMO", "Map system demo initialized successfully");
        
        return true;
    }
    
    void RunDemo()
    {
        if (!m_initialized)
        {
            printf("[MAP_DEMO] System not initialized\n");
            return;
        }
        
        LOG_INFO("MAP_DEMO", "Starting map system demonstration...");
        
        // 演示各个功能
        DemoMapInstanceCreation();
        DemoSceneObjectCreation();
        DemoPlayerMapInteraction();
        DemoTriggerSystem();
        DemoPortalSystem();
        DemoResourceSystem();
        DemoMapScripting();
        DemoMapStatistics();
        
        LOG_INFO("MAP_DEMO", "Map system demonstration completed");
    }
    
    void Cleanup()
    {
        if (!m_initialized) return;
        
        LOG_INFO("MAP_DEMO", "Cleaning up map system demo...");
        
        // 清理创建的地图实例
        for (uint32_t instanceId : m_createdInstances)
        {
            DESTROY_MAP_INSTANCE(instanceId);
        }
        m_createdInstances.clear();
        
        // 停止系统
        STOP_MAP_SYSTEM();
        STOP_NPC_SYSTEM();
        SHUTDOWN_SCRIPT_ENGINE();
        STOP_PLAYER_SYSTEM();
        
        m_initialized = false;
        LOG_INFO("MAP_DEMO", "Map system demo cleanup completed");
    }

private:
    bool m_initialized;
    std::vector<uint32_t> m_createdInstances;
    std::vector<uint32_t> m_createdPlayers;
    
    void DemoMapInstanceCreation()
    {
        LOG_INFO("MAP_DEMO", "=== Map Instance Creation Demo ===");
        
        // 创建不同类型的地图实例
        std::vector<uint32_t> testMaps = {100, 101, 102, 103, 104}; // 假设这些地图ID存在
        
        for (uint32_t mapId : testMaps)
        {
            uint32_t instanceId = CREATE_MAP_INSTANCE(mapId, false);
            if (instanceId != 0)
            {
                m_createdInstances.push_back(instanceId);
                LOG_INFO("MAP_DEMO", "Created map instance " + std::to_string(instanceId) + 
                        " for map " + std::to_string(mapId));
                
                // 获取实例信息
                auto instance = GET_MAP_INSTANCE(instanceId);
                if (instance)
                {
                    LOG_INFO("MAP_DEMO", "Instance info - Name: " + instance->mapName + 
                            ", Max Players: " + std::to_string(instance->maxPlayers) + 
                            ", Time Limit: " + std::to_string(instance->timeLimit));
                }
            }
            else
            {
                LOG_WARNING("MAP_DEMO", "Failed to create instance for map " + std::to_string(mapId));
            }
        }
        
        LOG_INFO("MAP_DEMO", "Created " + std::to_string(m_createdInstances.size()) + " map instances");
    }
    
    void DemoSceneObjectCreation()
    {
        LOG_INFO("MAP_DEMO", "=== Scene Object Creation Demo ===");
        
        if (m_createdInstances.empty()) return;
        
        uint32_t instanceId = m_createdInstances[0];
        
        // 创建各种类型的场景对象
        
        // 创建触发器
        uint32_t triggerId = CREATE_SCENE_OBJECT(instanceId, SceneObjectType::Trigger, 
                                                 "测试触发器", 1000, 1000, "trigger_test.lua");
        if (triggerId != 0)
        {
            LOG_INFO("MAP_DEMO", "Created trigger object with ID: " + std::to_string(triggerId));
        }
        
        // 创建传送门
        uint32_t portalId = CREATE_SCENE_OBJECT(instanceId, SceneObjectType::Portal, 
                                               "测试传送门", 1100, 1000, "portal_test.lua");
        if (portalId != 0)
        {
            LOG_INFO("MAP_DEMO", "Created portal object with ID: " + std::to_string(portalId));
            
            // 设置传送门目标
            auto instance = GET_MAP_INSTANCE(instanceId);
            if (instance)
            {
                auto portal = std::dynamic_pointer_cast<PortalObject>(instance->GetSceneObject(portalId));
                if (portal)
                {
                    portal->targetMapId = 101;
                    portal->targetX = 2000;
                    portal->targetY = 2000;
                    portal->requiredLevel = 10;
                    portal->cost = 100;
                    LOG_INFO("MAP_DEMO", "Configured portal to map 101 (2000, 2000)");
                }
            }
        }
        
        // 创建资源点
        uint32_t resourceId = CREATE_SCENE_OBJECT(instanceId, SceneObjectType::Resource, 
                                                 "铁矿石", 1200, 1000, "resource_iron.lua");
        if (resourceId != 0)
        {
            LOG_INFO("MAP_DEMO", "Created resource object with ID: " + std::to_string(resourceId));
            
            // 配置资源点
            auto instance = GET_MAP_INSTANCE(instanceId);
            if (instance)
            {
                auto resource = std::dynamic_pointer_cast<ResourceObject>(instance->GetSceneObject(resourceId));
                if (resource)
                {
                    resource->resourceType = "iron_ore";
                    resource->maxQuantity = 100;
                    resource->currentQuantity = 100;
                    resource->respawnTime = 300;
                    resource->requiredSkill = 1; // 采矿技能
                    resource->requiredLevel = 5;
                    
                    // 设置掉落物品
                    resource->dropItems.emplace_back(5001, 1); // 铁矿石
                    resource->dropItems.emplace_back(5002, 1); // 精铁
                    
                    LOG_INFO("MAP_DEMO", "Configured iron ore resource");
                }
            }
        }
        
        // 创建装饰物
        uint32_t decorationId = CREATE_SCENE_OBJECT(instanceId, SceneObjectType::Decoration, 
                                                   "古树", 1300, 1000, "");
        if (decorationId != 0)
        {
            LOG_INFO("MAP_DEMO", "Created decoration object with ID: " + std::to_string(decorationId));
        }
        
        // 创建障碍物
        uint32_t barrierId = CREATE_SCENE_OBJECT(instanceId, SceneObjectType::Barrier, 
                                                "石墙", 1400, 1000, "");
        if (barrierId != 0)
        {
            LOG_INFO("MAP_DEMO", "Created barrier object with ID: " + std::to_string(barrierId));
        }
    }
    
    void DemoPlayerMapInteraction()
    {
        LOG_INFO("MAP_DEMO", "=== Player Map Interaction Demo ===");
        
        if (m_createdInstances.empty()) return;
        
        // 创建测试玩家
        PlayerCreationParams params1("地图测试玩家1", "map_test1");
        params1.series = PlayerSeries::Wudang;
        uint32_t playerId1 = CREATE_PLAYER(params1);
        
        PlayerCreationParams params2("地图测试玩家2", "map_test2");
        params2.series = PlayerSeries::Shaolin;
        uint32_t playerId2 = CREATE_PLAYER(params2);
        
        if (playerId1 != 0 && playerId2 != 0)
        {
            m_createdPlayers.push_back(playerId1);
            m_createdPlayers.push_back(playerId2);
            
            // 玩家登录
            uint32_t sessionId1, sessionId2;
            PLAYER_LOGIN(playerId1, "127.0.0.1", 2001, sessionId1);
            PLAYER_LOGIN(playerId2, "127.0.0.1", 2002, sessionId2);
            
            // 玩家进入地图
            uint32_t instanceId = m_createdInstances[0];
            bool result1 = PLAYER_ENTER_MAP(playerId1, 100, 1000, 1000);
            bool result2 = PLAYER_ENTER_MAP(playerId2, 100, 1050, 1000);
            
            if (result1 && result2)
            {
                LOG_INFO("MAP_DEMO", "Both players entered the map successfully");
                
                // 检查地图中的玩家
                auto instance = GET_MAP_INSTANCE(instanceId);
                if (instance)
                {
                    LOG_INFO("MAP_DEMO", "Map instance now has " + 
                            std::to_string(instance->GetPlayerCount()) + " players");
                    
                    // 模拟玩家移动和触发器检测
                    CHECK_TRIGGERS(instanceId, playerId1, 1000, 1000);
                    CHECK_TRIGGERS(instanceId, playerId2, 1050, 1000);
                }
                
                // 玩家离开地图
                PLAYER_LEAVE_MAP(playerId1, instanceId);
                PLAYER_LEAVE_MAP(playerId2, instanceId);
                
                LOG_INFO("MAP_DEMO", "Players left the map");
            }
            
            // 玩家登出
            PLAYER_LOGOUT(playerId1);
            PLAYER_LOGOUT(playerId2);
        }
    }
    
    void DemoTriggerSystem()
    {
        LOG_INFO("MAP_DEMO", "=== Trigger System Demo ===");
        
        if (m_createdInstances.empty()) return;
        
        uint32_t instanceId = m_createdInstances[0];
        auto instance = GET_MAP_INSTANCE(instanceId);
        if (!instance) return;
        
        // 创建不同类型的触发器
        
        // 进入触发器
        auto enterTrigger = std::make_shared<TriggerObject>(1001, "进入触发器", TriggerType::OnEnter);
        enterTrigger->SetPosition(instance->mapId, 1500, 1000);
        enterTrigger->triggerRange = 100;
        enterTrigger->condition = "level >= 5";
        enterTrigger->effectScript = "trigger_enter_effect";
        enterTrigger->rewardExp = 100;
        enterTrigger->rewardMoney = 50;
        instance->AddSceneObject(enterTrigger);
        
        LOG_INFO("MAP_DEMO", "Created enter trigger at (1500, 1000)");
        
        // 定时触发器
        auto timerTrigger = std::make_shared<TriggerObject>(1002, "定时触发器", TriggerType::OnTimer);
        timerTrigger->SetPosition(instance->mapId, 1600, 1000);
        timerTrigger->triggerRange = 200;
        timerTrigger->cooldownTime = 30000; // 30秒冷却
        timerTrigger->effectScript = "trigger_timer_effect";
        instance->AddSceneObject(timerTrigger);
        
        LOG_INFO("MAP_DEMO", "Created timer trigger at (1600, 1000)");
        
        // 条件触发器
        auto conditionTrigger = std::make_shared<TriggerObject>(1003, "条件触发器", TriggerType::OnCondition);
        conditionTrigger->SetPosition(instance->mapId, 1700, 1000);
        conditionTrigger->triggerRange = 80;
        conditionTrigger->condition = "hasItem(2001) and level >= 10";
        conditionTrigger->effectScript = "trigger_condition_effect";
        conditionTrigger->rewardItems.push_back(3001); // 奖励物品
        instance->AddSceneObject(conditionTrigger);
        
        LOG_INFO("MAP_DEMO", "Created condition trigger at (1700, 1000)");
        
        // 模拟触发器激活
        if (!m_createdPlayers.empty())
        {
            uint32_t playerId = m_createdPlayers[0];
            
            // 模拟玩家进入触发器范围
            for (const auto& [triggerId, trigger] : instance->triggers)
            {
                if (trigger->triggerType == TriggerType::OnEnter)
                {
                    bool triggered = trigger->Trigger(playerId);
                    if (triggered)
                    {
                        LOG_INFO("MAP_DEMO", "Trigger " + trigger->name + " activated by player " + std::to_string(playerId));
                    }
                }
            }
        }
    }
    
    void DemoPortalSystem()
    {
        LOG_INFO("MAP_DEMO", "=== Portal System Demo ===");
        
        if (m_createdInstances.size() < 2) return;
        
        uint32_t sourceInstanceId = m_createdInstances[0];
        uint32_t targetInstanceId = m_createdInstances[1];
        
        auto sourceInstance = GET_MAP_INSTANCE(sourceInstanceId);
        auto targetInstance = GET_MAP_INSTANCE(targetInstanceId);
        
        if (!sourceInstance || !targetInstance) return;
        
        // 在源地图创建传送门
        auto portal = std::make_shared<PortalObject>(2001, "传送门到地图2", targetInstance->mapId, 2000, 2000);
        portal->SetPosition(sourceInstance->mapId, 1800, 1000);
        portal->requiredLevel = 1;
        portal->cost = 0;
        portal->isActive = true;
        sourceInstance->AddSceneObject(portal);
        
        LOG_INFO("MAP_DEMO", "Created portal from map " + std::to_string(sourceInstance->mapId) + 
                " to map " + std::to_string(targetInstance->mapId));
        
        // 在目标地图创建返回传送门
        auto returnPortal = std::make_shared<PortalObject>(2002, "返回传送门", sourceInstance->mapId, 1800, 1000);
        returnPortal->SetPosition(targetInstance->mapId, 2000, 2000);
        returnPortal->requiredLevel = 1;
        returnPortal->cost = 0;
        returnPortal->isActive = true;
        targetInstance->AddSceneObject(returnPortal);
        
        LOG_INFO("MAP_DEMO", "Created return portal from map " + std::to_string(targetInstance->mapId) + 
                " to map " + std::to_string(sourceInstance->mapId));
        
        // 模拟玩家使用传送门
        if (!m_createdPlayers.empty())
        {
            uint32_t playerId = m_createdPlayers[0];
            
            // 玩家进入源地图
            PLAYER_ENTER_MAP(playerId, sourceInstance->mapId, 1800, 1000);
            
            // 模拟使用传送门
            auto player = GET_ONLINE_PLAYER(playerId);
            if (player && portal->CanUse(*player))
            {
                bool success = portal->Use(*player);
                if (success)
                {
                    LOG_INFO("MAP_DEMO", "Player successfully used portal");
                    
                    // 玩家应该出现在目标地图
                    PLAYER_ENTER_MAP(playerId, targetInstance->mapId, 2000, 2000);
                }
            }
            
            PLAYER_LOGOUT(playerId);
        }
    }
    
    void DemoResourceSystem()
    {
        LOG_INFO("MAP_DEMO", "=== Resource System Demo ===");
        
        if (m_createdInstances.empty()) return;
        
        uint32_t instanceId = m_createdInstances[0];
        auto instance = GET_MAP_INSTANCE(instanceId);
        if (!instance) return;
        
        // 创建不同类型的资源点
        
        // 铁矿资源
        auto ironOre = std::make_shared<ResourceObject>(3001, "铁矿脉", "iron_ore");
        ironOre->SetPosition(instance->mapId, 1900, 1000);
        ironOre->maxQuantity = 50;
        ironOre->currentQuantity = 50;
        ironOre->respawnTime = 300;
        ironOre->requiredSkill = 1; // 采矿技能
        ironOre->requiredLevel = 5;
        ironOre->dropItems.emplace_back(5001, 2); // 铁矿石 x2
        ironOre->dropItems.emplace_back(5002, 1); // 精铁 x1
        instance->AddSceneObject(ironOre);
        
        LOG_INFO("MAP_DEMO", "Created iron ore resource at (1900, 1000)");
        
        // 草药资源
        auto herb = std::make_shared<ResourceObject>(3002, "灵芝", "herb");
        herb->SetPosition(instance->mapId, 2000, 1000);
        herb->maxQuantity = 10;
        herb->currentQuantity = 10;
        herb->respawnTime = 600;
        herb->requiredSkill = 2; // 采药技能
        herb->requiredLevel = 10;
        herb->dropItems.emplace_back(6001, 1); // 灵芝 x1
        herb->dropItems.emplace_back(6002, 1); // 灵芝精华 x1
        instance->AddSceneObject(herb);
        
        LOG_INFO("MAP_DEMO", "Created herb resource at (2000, 1000)");
        
        // 木材资源
        auto wood = std::make_shared<ResourceObject>(3003, "古木", "wood");
        wood->SetPosition(instance->mapId, 2100, 1000);
        wood->maxQuantity = 20;
        wood->currentQuantity = 20;
        wood->respawnTime = 180;
        wood->requiredSkill = 3; // 伐木技能
        wood->requiredLevel = 3;
        wood->dropItems.emplace_back(7001, 3); // 木材 x3
        wood->dropItems.emplace_back(7002, 1); // 硬木 x1
        instance->AddSceneObject(wood);
        
        LOG_INFO("MAP_DEMO", "Created wood resource at (2100, 1000)");
        
        // 模拟资源采集
        if (!m_createdPlayers.empty())
        {
            uint32_t playerId = m_createdPlayers[0];
            auto player = GET_ONLINE_PLAYER(playerId);
            
            if (player)
            {
                // 模拟采集铁矿
                if (ironOre->CanHarvest(*player))
                {
                    auto harvestedItems = ironOre->Harvest(*player);
                    LOG_INFO("MAP_DEMO", "Player harvested " + std::to_string(harvestedItems.size()) + 
                            " types of items from iron ore");
                    
                    for (const auto& [itemId, count] : harvestedItems)
                    {
                        LOG_INFO("MAP_DEMO", "  Item " + std::to_string(itemId) + " x" + std::to_string(count));
                    }
                }
                
                // 模拟资源状态更新
                ironOre->Update();
                herb->Update();
                wood->Update();
                
                LOG_INFO("MAP_DEMO", "Resource status after harvesting:");
                LOG_INFO("MAP_DEMO", "  Iron ore: " + std::to_string(ironOre->currentQuantity) + 
                        "/" + std::to_string(ironOre->maxQuantity));
                LOG_INFO("MAP_DEMO", "  Herb: " + std::to_string(herb->currentQuantity) + 
                        "/" + std::to_string(herb->maxQuantity));
                LOG_INFO("MAP_DEMO", "  Wood: " + std::to_string(wood->currentQuantity) + 
                        "/" + std::to_string(wood->maxQuantity));
            }
        }
    }
    
    void DemoMapScripting()
    {
        LOG_INFO("MAP_DEMO", "=== Map Scripting Demo ===");
        
        // 创建测试脚本
        std::string testScript = R"(
            function map_test_function(instanceId, playerId, objectId)
                WriteLog("Map script function called!")
                WriteLog("Instance ID: " .. instanceId)
                WriteLog("Player ID: " .. playerId)
                WriteLog("Object ID: " .. objectId)
                
                local mapName = GetMapName(instanceId)
                local playerCount = GetMapPlayerCount(instanceId)
                local maxPlayers = GetMapMaxPlayers(instanceId)
                
                WriteLog("Map Name: " .. mapName)
                WriteLog("Players: " .. playerCount .. "/" .. maxPlayers)
                
                -- 创建场景对象
                local objectId = CreateSceneObject(instanceId, 1, "脚本创建的触发器", 3000, 3000, "")
                if objectId > 0 then
                    WriteLog("Created scene object with ID: " .. objectId)
                end
                
                -- 播放地图效果
                local effectId = PlayMapEffect("explosion", instanceId, 3000, 3000, 5000)
                if effectId > 0 then
                    WriteLog("Playing map effect with ID: " .. effectId)
                end
                
                return true
            end
            
            function map_player_enter(instanceId, playerId)
                WriteLog("Player " .. playerId .. " entered map instance " .. instanceId)
                
                local playerName = GetPlayerName(playerId)
                ShowMapMessage(instanceId, "欢迎 " .. playerName .. " 来到这个地图！", 3000)
                
                return true
            end
            
            function map_player_leave(instanceId, playerId)
                WriteLog("Player " .. playerId .. " left map instance " .. instanceId)
                
                local playerName = GetPlayerName(playerId)
                WriteLog("Goodbye " .. playerName .. "!")
                
                return true
            end
            
            function trigger_activate(instanceId, playerId, triggerId)
                WriteLog("Trigger " .. triggerId .. " activated by player " .. playerId)
                
                -- 给玩家奖励
                AddPlayerExp(playerId, 100)
                SetPlayerMoney(playerId, GetPlayerMoney(playerId) + 50)
                
                WriteLog("Player received rewards!")
                return true
            end
        )";
        
        // 执行测试脚本
        auto context = std::make_unique<LuaScriptContext>();
        context->Initialize();
        
        ScriptResult result = context->ExecuteString(testScript);
        if (result == ScriptResult::Success)
        {
            LOG_INFO("MAP_DEMO", "Successfully loaded map test script");
            
            if (!m_createdInstances.empty())
            {
                // 测试脚本函数调用
                uint32_t instanceId = m_createdInstances[0];
                uint32_t playerId = !m_createdPlayers.empty() ? m_createdPlayers[0] : 1001;
                
                std::vector<LuaValue> args = {
                    LuaValue(static_cast<double>(instanceId)),
                    LuaValue(static_cast<double>(playerId)),
                    LuaValue(static_cast<double>(0))
                };
                
                result = context->CallFunction("map_test_function", args);
                if (result == ScriptResult::Success)
                {
                    LOG_INFO("MAP_DEMO", "Successfully executed map script function");
                }
                else
                {
                    LOG_ERROR("MAP_DEMO", "Failed to execute map script function");
                }
                
                // 注册事件处理器
                REGISTER_MAP_EVENT_HANDLER(MapEvent::PlayerEnter, "map_player_enter", "", 100);
                REGISTER_MAP_EVENT_HANDLER(MapEvent::PlayerLeave, "map_player_leave", "", 100);
                REGISTER_MAP_EVENT_HANDLER(MapEvent::TriggerActivate, "trigger_activate", "", 100);
                
                // 触发事件测试
                TRIGGER_MAP_EVENT(MapEvent::PlayerEnter, instanceId, playerId, 0);
                TRIGGER_MAP_EVENT(MapEvent::TriggerActivate, instanceId, playerId, 1001);
                TRIGGER_MAP_EVENT(MapEvent::PlayerLeave, instanceId, playerId, 0);
            }
        }
        else
        {
            LOG_ERROR("MAP_DEMO", "Failed to load map test script");
        }
    }
    
    void DemoMapStatistics()
    {
        LOG_INFO("MAP_DEMO", "=== Map Statistics Demo ===");
        
        auto stats = MAP_MANAGER().GetStatistics();
        
        LOG_INFO("MAP_DEMO", "Map system statistics:");
        LOG_INFO("MAP_DEMO", "  Total maps: " + std::to_string(stats.totalMaps));
        LOG_INFO("MAP_DEMO", "  Total instances: " + std::to_string(stats.totalInstances));
        LOG_INFO("MAP_DEMO", "  Total players: " + std::to_string(stats.totalPlayers));
        LOG_INFO("MAP_DEMO", "  Total objects: " + std::to_string(stats.totalObjects));
        
        LOG_INFO("MAP_DEMO", "Maps by type:");
        for (const auto& [type, count] : stats.mapsByType)
        {
            std::string typeName;
            switch (type)
            {
            case MapType::Normal: typeName = "Normal"; break;
            case MapType::Instance: typeName = "Instance"; break;
            case MapType::PvP: typeName = "PvP"; break;
            case MapType::Raid: typeName = "Raid"; break;
            default: typeName = "Unknown"; break;
            }
            
            LOG_INFO("MAP_DEMO", "  " + typeName + ": " + std::to_string(count));
        }
        
        LOG_INFO("MAP_DEMO", "Players per map:");
        for (const auto& [mapId, count] : stats.playersPerMap)
        {
            LOG_INFO("MAP_DEMO", "  Map " + std::to_string(mapId) + ": " + std::to_string(count) + " players");
        }
        
        LOG_INFO("MAP_DEMO", "Instances per map:");
        for (const auto& [mapId, count] : stats.instancesPerMap)
        {
            LOG_INFO("MAP_DEMO", "  Map " + std::to_string(mapId) + ": " + std::to_string(count) + " instances");
        }
    }
};

} // namespace sword2

// 全局地图系统演示实例
sword2::MapSystemDemo g_MapDemo;

// 初始化地图系统
bool InitializeMapSystem()
{
    return g_MapDemo.Initialize();
}

// 运行地图系统演示
void RunMapSystemDemo()
{
    g_MapDemo.RunDemo();
}

// 清理地图系统
void CleanupMapSystem()
{
    g_MapDemo.Cleanup();
}
