//Microsoft Developer Studio generated resource script.
//
#include "resource.h"

#define APSTUDIO_READONLY_SYMBOLS
/////////////////////////////////////////////////////////////////////////////
//
// Generated from the TEXTINCLUDE 2 resource.
//
#define APSTUDIO_HIDDEN_SYMBOLS
#include "windows.h"
#undef APSTUDIO_HIDDEN_SYMBOLS
#include "resource.h"

/////////////////////////////////////////////////////////////////////////////
#undef APSTUDIO_READONLY_SYMBOLS

/////////////////////////////////////////////////////////////////////////////
// Chinese (P.R.C.) resources

#if !defined(AFX_RESOURCE_DLL) || defined(AFX_TARG_CHS)
#ifdef _WIN32
LANGUAGE LANG_CHINESE, SUBLANG_CHINESE_SIMPLIFIED
#pragma code_page(936)
#endif //_WIN32

/////////////////////////////////////////////////////////////////////////////
//
// Icon
//

// Icon with lowest ID value placed first to ensure application icon
// remains consistent on all systems.
IDI_S3RELAY             ICON    DISCARDABLE     "S3Relay.ICO"
IDI_SMALL               ICON    DISCARDABLE     "SMALL.ICO"

/////////////////////////////////////////////////////////////////////////////
//
// Menu
//

IDC_S3RELAY MENU DISCARDABLE 
BEGIN
    POPUP "&File"
    BEGIN
        POPUP "S&ystem", GRAYED
        BEGIN
            MENUITEM "Start&up",                    IDM_STARTUP, GRAYED
            MENUITEM "Shut&down",                   IDM_SHUTDOWN, GRAYED
        END
        MENUITEM SEPARATOR
        POPUP "&Control"
        BEGIN
            MENUITEM "U&pdate Trace",               IDM_UPDATE_TRACE
        END
        MENUITEM "&Clear Trace",                IDM_CLEAR_TRACE
        MENUITEM SEPARATOR
        POPUP "&Server Info"
        BEGIN
            MENUITEM "Trace &Host",                 IDM_TRACE_HOSTSERVER
            MENUITEM "Trace &Chat",                 IDM_TRACE_CHATSERVER
            MENUITEM "Trace &Tong",                 IDM_TRACE_TONGSERVER
            MENUITEM "Trace &Relay",                IDM_TRACE_RELAYSERVER
        END
        POPUP "&Center Info"
        BEGIN
            MENUITEM "Trace &Root",                 IDM_TRACE_ROOTCENTER
            MENUITEM "Trace &Gateway",              IDM_TRACE_GATEWAYCENTER
            MENUITEM "Trace &DBRole",               IDM_TRACE_DBROLECENTER
            MENUITEM "Trace &Relay",                IDM_TRACE_RELAYCENTER
        END
        MENUITEM SEPARATOR
        POPUP "S&ock Info"
        BEGIN
            MENUITEM "Trace &Status",               IDM_TRACE_STATUS
            MENUITEM "Trace S&ockThread (Debug)",   IDM_TRACE_SOCKTHREAD
            MENUITEM "Trace &DataSize (Debug)",     IDM_TRACE_DATASIZE
        END
        MENUITEM SEPARATOR
        POPUP "&Player Info"
        BEGIN
            MENUITEM "Trace &Player",               IDM_TRACE_PLAYER
            MENUITEM "Trace Player &Detail",        IDM_TRACE_PLAYER_DETAIL
        END
        POPUP "&Friend Info"
        BEGIN
            MENUITEM "Trace &Player",               IDM_TRACE_FRIENDPLAYER
            MENUITEM "Trace &AFQS",                 IDM_TRACE_FRIENDAFQS
        END
        MENUITEM SEPARATOR
        POPUP "&Netcard Info"
        BEGIN
            MENUITEM "Trace &IP",                   IDM_TRACE_IP
            MENUITEM "Trace &Mac",                  IDM_TRACE_MAC
        END
        MENUITEM SEPARATOR
        MENUITEM "E&xit",                       IDM_EXIT
    END
    POPUP "&Help"
    BEGIN
        MENUITEM "&About ...",                  IDM_ABOUT
    END
END


/////////////////////////////////////////////////////////////////////////////
//
// Accelerator
//

IDC_S3RELAY ACCELERATORS MOVEABLE PURE 
BEGIN
    "?",            IDM_ABOUT,              ASCII,  ALT
    "/",            IDM_ABOUT,              ASCII,  ALT
END


/////////////////////////////////////////////////////////////////////////////
//
// Dialog
//

IDD_ABOUTBOX DIALOG DISCARDABLE  22, 17, 230, 75
STYLE DS_MODALFRAME | WS_CAPTION | WS_SYSMENU
CAPTION "About"
FONT 8, "System"
BEGIN
    ICON            IDI_S3RELAY,IDC_MYICON,14,9,16,16
    LTEXT           "S3Relay Version 1.0",IDC_STATIC,49,10,119,8,SS_NOPREFIX
    LTEXT           "Copyright (C) 2003",IDC_STATIC,49,20,119,8
    DEFPUSHBUTTON   "OK",IDOK,195,6,30,11,WS_GROUP
END

IDD_DIALOG_ROOTLOGIN DIALOG DISCARDABLE  22, 17, 187, 74
STYLE DS_MODALFRAME | WS_CAPTION | WS_SYSMENU
CAPTION "login root"
FONT 8, "System"
BEGIN
    LTEXT           "&Name: ",IDC_STATIC,7,9,24,8
    EDITTEXT        IDC_EDIT_NAME,45,7,135,12,ES_AUTOHSCROLL
    LTEXT           "&Pswd: ",IDC_STATIC,7,25,23,8
    EDITTEXT        IDC_EDIT_PSWD,45,23,135,12,ES_PASSWORD | ES_AUTOHSCROLL
    DEFPUSHBUTTON   "OK",IDOK,27,53,50,14
    PUSHBUTTON      "Cancel",IDCANCEL,109,53,50,14
END


#ifdef APSTUDIO_INVOKED
/////////////////////////////////////////////////////////////////////////////
//
// TEXTINCLUDE
//

2 TEXTINCLUDE DISCARDABLE 
BEGIN
    "#define APSTUDIO_HIDDEN_SYMBOLS\r\n"
    "#include ""windows.h""\r\n"
    "#undef APSTUDIO_HIDDEN_SYMBOLS\r\n"
    "#include ""resource.h""\r\n"
    "\0"
END

3 TEXTINCLUDE DISCARDABLE 
BEGIN
    "\r\n"
    "\0"
END

1 TEXTINCLUDE DISCARDABLE 
BEGIN
    "resource.h\0"
END

#endif    // APSTUDIO_INVOKED


/////////////////////////////////////////////////////////////////////////////
//
// DESIGNINFO
//

#ifdef APSTUDIO_INVOKED
GUIDELINES DESIGNINFO DISCARDABLE 
BEGIN
    IDD_DIALOG_ROOTLOGIN, DIALOG
    BEGIN
        LEFTMARGIN, 7
        RIGHTMARGIN, 180
        TOPMARGIN, 7
        BOTTOMMARGIN, 67
    END
END
#endif    // APSTUDIO_INVOKED


/////////////////////////////////////////////////////////////////////////////
//
// String Table
//

STRINGTABLE DISCARDABLE 
BEGIN
    IDS_APP_TITLE           "S3Relay"
    IDS_HELLO               "Hello World!"
    IDC_S3RELAY             "S3RELAY"
END

#endif    // Chinese (P.R.C.) resources
/////////////////////////////////////////////////////////////////////////////



#ifndef APSTUDIO_INVOKED
/////////////////////////////////////////////////////////////////////////////
//
// Generated from the TEXTINCLUDE 3 resource.
//


/////////////////////////////////////////////////////////////////////////////
#endif    // not APSTUDIO_INVOKED

