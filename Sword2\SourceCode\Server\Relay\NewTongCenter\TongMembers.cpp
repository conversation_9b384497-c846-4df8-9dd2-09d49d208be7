// TongMembers.cpp: implementation of the CTongMembers class.
// All member's info of the tong
// by <PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON>@263.net 2004.09.22
//////////////////////////////////////////////////////////////////////

#include "stdafx.h"
#include "TongMembers.h"
#include <algorithm>

//////////////////////////////////////////////////////////////////////
// Construction/Destruction
//////////////////////////////////////////////////////////////////////

CTongMembers::CTongMembers()
{
	// 初始化成员变量
	memset(m_Members, 0, sizeof(m_Members));
	m_nMemberCount = 0;
	m_bInitialized = false;
}

CTongMembers::~CTongMembers()
{
	UnInit();
}

BOOL CTongMembers::Init()
{
	if(!m_clsFreeIndex.Init(MAXNUM_TONG_TOTAL))
	{
		return FALSE;
	}

	for(int i = MAXNUM_TONG_TOTAL - 1; i >= 0; i--)
	{
		m_clsFreeIndex.Push(i);
	}

	return TRUE;
}

void CTongMembers::UnInit()
{
	m_mapRoleName2Index.clear();
	m_mapRoleId.clear();
}

int CTongMembers::GetMemberCount()
{
	return m_mapRoleName2Index.size();
}

int CTongMembers::Add(const TONGMEMBERBASE &tagBase, 
					   const TONGDUTYMININFO &tagDutyMin)
{
	int nIndex = m_clsFreeIndex.Pop();
	if(nIndex == -1)
	{
		return -1;
	}

	std::string strRole = tagBase.szRoleName;
	std::pair< ROLENAME2INDEXMAP::iterator, bool > ret = 
		m_mapRoleName2Index.insert(ROLENAME2INDEXMAP::value_type(strRole, nIndex));

	if(!ret.second)
	{
		m_clsFreeIndex.Push(nIndex);

		printf("Add tong member error!\n");
		return -1;
	}

	m_szTongMember[nIndex].tagBase = tagBase;
	m_szTongMember[nIndex].listDuty.push_back(tagDutyMin);

	TONGPROTOCOLCOMMON new_id;
	new_id.dwRoleID = tagBase.dwRoleID;
	new_id.nPlayerIndex = tagBase.nPlayerIndex;
	m_mapRoleId.insert(make_pair(new_id, tagBase.szRoleName));

	return nIndex;
}

BOOL CTongMembers::Del(LPCSTR pRoleName, 
					   FUNC_DELAGENTDUTYCALLBACK pCallBack, 
					   FUNC_DELAGENTROLECALLBACK pAgentCallBack)
{
	if(pRoleName == NULL)
	{
		return FALSE;
	}

	std::string strRole = pRoleName;
	ROLENAME2INDEXMAP::iterator it = m_mapRoleName2Index.find(pRoleName);
	if(it == m_mapRoleName2Index.end())
	{
		return FALSE;
	}

	// Remove all agent
	RemoveAllAgent(it->second, 
		pCallBack);

	// Remove all agent duty
	RemoveAgentDutys(it->second, 
		pAgentCallBack);

	int nIndex = it->second;
	TONGMEMBER *pMember = &m_szTongMember[nIndex];
	pMember->listAgent.clear();
	pMember->listDuty.clear();
	m_clsFreeIndex.Push(nIndex);

	TONGPROTOCOLCOMMON id;
	id.dwRoleID = pMember->tagBase.dwRoleID;
	id.nPlayerIndex = pMember->tagBase.nPlayerIndex;
	m_mapRoleId.erase(id);

	m_mapRoleName2Index.erase(it);

	return TRUE;
}

BOOL CTongMembers::UpdateMainDuty(int nIndex, 
								  const TONGDUTYMININFO &tagDutyMin, 
								  FUNC_DELAGENTDUTYCALLBACK pCallBack)
{
	if(nIndex < 0 || nIndex >= MAXNUM_TONG_TOTAL)
	{
		return FALSE;
	}

	// Remove all agent
	RemoveAllAgent(nIndex, pCallBack);

	m_szTongMember[nIndex].listDuty[0] = tagDutyMin;

	return TRUE;
}

BOOL CTongMembers::AddAgentDuty(int nIndex, 
								const TONGDUTYMININFO &tagDutyMin, 
								FUNC_ADDAGENTDUTYCALLBACK pCallBack, 
								BOOL bRegisterMode)
{
	if(nIndex < 0 || nIndex >= MAXNUM_TONG_TOTAL)
	{
		return FALSE;
	}

	if(m_szTongMember[nIndex].listDuty.size() > MAXNUM_AGENTDUTY + 1)
	{
		// Reach max agent duty limit
		return FALSE;
	}

	if(NULL != pCallBack)
	{
		// Call back function
		pCallBack(m_szTongMember[nIndex].tagBase.dwServerID, 
			m_szTongMember[nIndex].tagBase.dwRoleID, 
			m_szTongMember[nIndex].tagBase.nPlayerIndex, 
			tagDutyMin.tagComInfo.dwDutyID, 
			tagDutyMin.dwRightValue, 
			tagDutyMin.szRoleName);
	}

	if(!bRegisterMode)
	{
		GetAgentList(tagDutyMin.szRoleName)->push_back(nIndex);
	}

	m_szTongMember[nIndex].listDuty.push_back(tagDutyMin);
	
	return TRUE;
}

int CTongMembers::GetMemberIndex(LPCSTR pRoleName)
{
	if(pRoleName == NULL)
	{
		return -1;
	}

	std::string strRole = pRoleName;
	ROLENAME2INDEXMAP::iterator it = m_mapRoleName2Index.find(pRoleName);
	if(it == m_mapRoleName2Index.end())
	{
		return -1;
	}

	return it->second;
}

BOOL CTongMembers::RemoveAgentDuty(int nIndex, DWORD dwDutyID, 
								   LPCSTR pcDutyRoleName/* Duty's owner */, 
								   FUNC_DELAGENTDUTYCALLBACK pCallBack, 
								   BOOL bNoRemoveInAgentList)
{
	if(nIndex < 0 || nIndex >= MAXNUM_TONG_TOTAL || pcDutyRoleName == NULL)
	{
		return FALSE;
	}

	TONGDUTYVECTOR::iterator it = m_szTongMember[nIndex].listDuty.begin();

	// Main duty can't be delete
	it ++;
	while(it != m_szTongMember[nIndex].listDuty.end())
	{
		if(it->tagComInfo.dwDutyID == dwDutyID && strcmp(it->szRoleName, pcDutyRoleName) == 0)
		{
			if(NULL != pCallBack)
			{
				// Call back function
				pCallBack(m_szTongMember[nIndex].tagBase.dwServerID, 
					m_szTongMember[nIndex].tagBase.dwRoleID, 
					m_szTongMember[nIndex].tagBase.nPlayerIndex, 
					dwDutyID, 
					pcDutyRoleName);
			}

			m_szTongMember[nIndex].listDuty.erase(it);

			if(!bNoRemoveInAgentList)
			{
				TONGAGENTVECTOR *pAgentList = GetAgentList(pcDutyRoleName);
				TONGAGENTVECTOR::iterator ita = std::find(pAgentList->begin(), 
														  pAgentList->end(), 
														  nIndex);
				if(ita != pAgentList->end())
				{
					pAgentList->erase(ita);
				}
			}
			
			break;
		}

		it ++;
	}

	return TRUE;
}

BOOL CTongMembers::GetDutyValue(int nIndex, DWORD &dwRightValue)
{
	if(nIndex < 0 || nIndex >= MAXNUM_TONG_TOTAL)
	{
		return FALSE;
	}

	dwRightValue = 0;
	TONGDUTYVECTOR::iterator it = 
		m_szTongMember[nIndex].listDuty.begin();
	while(it != m_szTongMember[nIndex].listDuty.end())
	{
		dwRightValue |= it->dwRightValue;
		
		it ++;
	}

	return TRUE;
}

BOOL CTongMembers::GetDutyValueSet(int nIndex, DWORD &dwRightValueSet)
{
	if(nIndex < 0 || nIndex >= MAXNUM_TONG_TOTAL)
	{
		return FALSE;
	}

	dwRightValueSet = 0;
	TONGDUTYVECTOR::iterator it = 
		m_szTongMember[nIndex].listDuty.begin();
	while(it != m_szTongMember[nIndex].listDuty.end())
	{
		dwRightValueSet |= it->tagComInfo.dwRightValueSet;

		it ++;
	}

	return TRUE;
}

// Remove all member agent
BOOL CTongMembers::RemoveAllAgent(int nIndex, 
								  FUNC_DELAGENTDUTYCALLBACK pCallBack)
{
	if(nIndex < 0 || nIndex >= MAXNUM_TONG_TOTAL)
	{
		return FALSE;
	}
	
	DWORD dwMainDutyID = GetMainDutyID(nIndex);
	TONGAGENTVECTOR::iterator it = 
		m_szTongMember[nIndex].listAgent.begin();
	while(it != m_szTongMember[nIndex].listAgent.end())
	{
		RemoveAgentDuty(*it, dwMainDutyID, 
			m_szTongMember[nIndex].tagBase.szRoleName, pCallBack, TRUE);

		it ++;
	}

	return TRUE;
}

// Remove all agent duty
BOOL CTongMembers::RemoveAgentDutys(int nIndex, FUNC_DELAGENTROLECALLBACK pCallBack)
{
	if(nIndex < 0 || nIndex >= MAXNUM_TONG_TOTAL)
	{
		return FALSE;
	}

	TONGDUTYVECTOR &listDuty = m_szTongMember[nIndex].listDuty;
	for(int i = 1; i < (int)listDuty.size(); i++)
	{
		int nParentIndex = GetMemberIndex(listDuty[i].szRoleName);
		TONGAGENTVECTOR *pAgentList = GetAgentList(nParentIndex);
		TONGMEMBERBASE *pBaseInfo = (TONGMEMBERBASE *)Get(nParentIndex);
		TONGAGENTVECTOR::iterator it = std::find(pAgentList->begin(), 
												 pAgentList->end(), 
												 nIndex);
		if(pBaseInfo && pAgentList && it != pAgentList->end())
		{
			pAgentList->erase(it);

			if(pCallBack != NULL)
			{
				pCallBack(pBaseInfo->dwServerID, 
					pBaseInfo->dwRoleID, pBaseInfo->nPlayerIndex);
			}
		}
	}
	
	return TRUE;
}

LPCSTR CTongMembers::GetDefaultTitle(DWORD dwTargetDutyID)
{
	switch (dwTargetDutyID) 
	{
	case DUTY_ID_MEMBER:
		return G_TONG_SOLDIER;
		break;
	case DUTY_ID_SERGEANT:
		return G_TONG_SERGEANT;
		break;
	case DUTY_ID_CAPTAIN:
		return G_TONG_CAPTAIN;
		break;
	case DUTY_ID_GENERAL:
		return G_TONG_GENERAL;
		break;
	case DUTY_ID_QUEEN:
		return G_TONG_QUEEN;
		break;
	case DUTY_ID_KING:
		return G_TONG_MARSHAL;
		break;
	}
	return "";
}

LPCSTR CTongMembers::GetRoleName(const TONGPROTOCOLCOMMON *pId)
{
	MAP_ROLEID::const_iterator it = m_mapRoleId.find(*pId);
	if (it != m_mapRoleId.end())
		return it->second.c_str();
	return NULL;
}

