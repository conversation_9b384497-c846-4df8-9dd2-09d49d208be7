//---------------------------------------------------------------------------
// Sword2 Auction Manager (c) 2024
//
// File:	AuctionManager.h
// Date:	2024.07.02
// Code:	AI Assistant
// Desc:	Comprehensive auction house management system
//---------------------------------------------------------------------------
#ifndef AUCTION_MANAGER_H
#define AUCTION_MANAGER_H

#include "AuctionSystem.h"
#include "PlayerManager.h"
#include "ItemManager.h"
#include "InventoryManager.h"
#include "LuaScriptEngine.h"
#include "UnifiedLoggingSystem.h"
#include <unordered_map>
#include <memory>
#include <mutex>
#include <shared_mutex>
#include <thread>
#include <atomic>
#include <queue>
#include <functional>
#include <algorithm>

namespace sword2 {

// 拍卖操作结果
enum class AuctionResult : uint8_t
{
    Success = 0,        // 成功
    Failed,             // 失败
    NotFound,           // 未找到
    InsufficientMoney,  // 金钱不足
    InsufficientSpace,  // 背包空间不足
    InvalidPrice,       // 价格无效
    InvalidDuration,    // 持续时间无效
    CannotTrade,        // 无法交易
    AlreadyBid,         // 已经竞价
    AuctionEnded,       // 拍卖已结束
    PermissionDenied,   // 权限不足
    TooManyAuctions,    // 拍卖数量过多
    BidTooLow,          // 竞价过低
    SelfBid             // 自己竞价
};

// 拍卖行管理器
class AuctionManager : public Singleton<AuctionManager>
{
public:
    AuctionManager()
        : m_running(false), m_nextAuctionId(1), m_nextBidId(1), m_nextMailId(1),
          m_updateInterval(std::chrono::minutes(1)) {}
    
    ~AuctionManager()
    {
        Stop();
    }
    
    // 启动拍卖行管理器
    bool Start()
    {
        if (m_running) return true;
        
        m_running = true;
        m_updateThread = std::thread(&AuctionManager::UpdateLoop, this);
        
        LOG_INFO("AUCTION_MGR", "Auction manager started");
        return true;
    }
    
    // 停止拍卖行管理器
    void Stop()
    {
        if (!m_running) return;
        
        m_running = false;
        if (m_updateThread.joinable())
        {
            m_updateThread.join();
        }
        
        // 清理数据
        {
            std::unique_lock<std::shared_mutex> lock(m_auctionsMutex);
            m_auctions.clear();
        }
        
        {
            std::unique_lock<std::shared_mutex> lock(m_mailsMutex);
            m_mails.clear();
        }
        
        LOG_INFO("AUCTION_MGR", "Auction manager stopped");
    }
    
    // 创建拍卖
    AuctionResult CreateAuction(uint32_t sellerId, uint32_t itemInstanceId, uint32_t startPrice,
                               uint32_t buyoutPrice, AuctionType type, uint32_t duration)
    {
        auto seller = GET_ONLINE_PLAYER(sellerId);
        if (!seller)
        {
            return AuctionResult::NotFound;
        }
        
        // 检查物品是否存在
        auto itemInstance = GET_ITEM_INSTANCE(itemInstanceId);
        if (!itemInstance || itemInstance->ownerId != sellerId)
        {
            return AuctionResult::NotFound;
        }
        
        // 检查是否可以交易
        if (!itemInstance->canTrade || itemInstance->isBound)
        {
            return AuctionResult::CannotTrade;
        }
        
        // 检查价格
        if (startPrice == 0 || (buyoutPrice > 0 && buyoutPrice <= startPrice))
        {
            return AuctionResult::InvalidPrice;
        }
        
        // 检查持续时间
        if (duration < 3600 || duration > 259200) // 1小时到72小时
        {
            return AuctionResult::InvalidDuration;
        }
        
        // 检查拍卖数量限制
        if (GetPlayerAuctionCount(sellerId) >= GetMaxAuctionsPerPlayer(sellerId))
        {
            return AuctionResult::TooManyAuctions;
        }
        
        // 计算上架费用
        uint32_t listingFee = CalculateListingFee(startPrice, duration);
        if (seller->money < listingFee)
        {
            return AuctionResult::InsufficientMoney;
        }
        
        // 扣除上架费用
        seller->money -= listingFee;
        
        // 从玩家背包移除物品
        if (!REMOVE_ITEM_FROM_INVENTORY(sellerId, itemInstanceId, itemInstance->stackCount))
        {
            // 回退费用
            seller->money += listingFee;
            return AuctionResult::Failed;
        }
        
        // 创建拍卖
        std::unique_lock<std::shared_mutex> lock(m_auctionsMutex);
        
        uint32_t auctionId = m_nextAuctionId++;
        AuctionItem auction(sellerId, itemInstanceId, itemInstance->templateId, 
                          startPrice, type, duration);
        auction.auctionId = auctionId;
        auction.sellerName = seller->name;
        auction.buyoutPrice = buyoutPrice;
        auction.listingFee = listingFee;
        auction.quantity = itemInstance->stackCount;
        // auction.itemName = GetItemName(itemInstance->templateId); // 需要从物品系统获取
        
        m_auctions[auctionId] = auction;
        
        LOG_INFO("AUCTION_MGR", "Created auction " + std::to_string(auctionId) + 
                " by player " + std::to_string(sellerId) + 
                " for item " + std::to_string(itemInstance->templateId));
        
        return AuctionResult::Success;
    }
    
    // 竞价
    AuctionResult PlaceBid(uint32_t bidderId, uint32_t auctionId, uint32_t bidAmount)
    {
        auto bidder = GET_ONLINE_PLAYER(bidderId);
        if (!bidder)
        {
            return AuctionResult::NotFound;
        }
        
        std::unique_lock<std::shared_mutex> lock(m_auctionsMutex);
        
        auto it = m_auctions.find(auctionId);
        if (it == m_auctions.end())
        {
            return AuctionResult::NotFound;
        }
        
        auto& auction = it->second;
        
        // 检查拍卖状态
        if (!auction.CanBid())
        {
            return AuctionResult::AuctionEnded;
        }
        
        // 检查是否为卖家自己
        if (bidderId == auction.sellerId)
        {
            return AuctionResult::SelfBid;
        }
        
        // 检查竞价金额
        if (bidAmount < auction.GetNextBidAmount())
        {
            return AuctionResult::BidTooLow;
        }
        
        // 检查玩家金钱
        if (bidder->money < bidAmount)
        {
            return AuctionResult::InsufficientMoney;
        }
        
        // 退还之前竞价者的金钱
        if (auction.highestBidderId != 0)
        {
            RefundBidder(auction.highestBidderId, auction.currentPrice);
        }
        
        // 扣除竞价金额
        bidder->money -= bidAmount;
        
        // 添加竞价记录
        uint32_t bidId = m_nextBidId++;
        if (!auction.AddBid(bidderId, bidder->name, bidAmount))
        {
            // 回退金钱
            bidder->money += bidAmount;
            return AuctionResult::Failed;
        }
        
        LOG_INFO("AUCTION_MGR", "Player " + std::to_string(bidderId) + 
                " bid " + std::to_string(bidAmount) + " on auction " + std::to_string(auctionId));
        
        // 如果拍卖完成，处理结算
        if (auction.state == AuctionState::Completed)
        {
            ProcessAuctionCompletion(auction);
        }
        
        return AuctionResult::Success;
    }
    
    // 一口价购买
    AuctionResult Buyout(uint32_t buyerId, uint32_t auctionId)
    {
        auto buyer = GET_ONLINE_PLAYER(buyerId);
        if (!buyer)
        {
            return AuctionResult::NotFound;
        }
        
        std::unique_lock<std::shared_mutex> lock(m_auctionsMutex);
        
        auto it = m_auctions.find(auctionId);
        if (it == m_auctions.end())
        {
            return AuctionResult::NotFound;
        }
        
        auto& auction = it->second;
        
        // 检查是否可以一口价购买
        if (!auction.CanBuyout())
        {
            return AuctionResult::AuctionEnded;
        }
        
        // 检查是否为卖家自己
        if (buyerId == auction.sellerId)
        {
            return AuctionResult::SelfBid;
        }
        
        // 检查玩家金钱
        if (buyer->money < auction.buyoutPrice)
        {
            return AuctionResult::InsufficientMoney;
        }
        
        // 检查背包空间
        if (!HasEnoughSpace(*buyer, auction.itemTemplateId, auction.quantity))
        {
            return AuctionResult::InsufficientSpace;
        }
        
        // 扣除金钱
        buyer->money -= auction.buyoutPrice;
        
        // 退还之前竞价者的金钱
        if (auction.highestBidderId != 0)
        {
            RefundBidder(auction.highestBidderId, auction.currentPrice);
        }
        
        // 设置为一口价购买
        auction.currentPrice = auction.buyoutPrice;
        auction.highestBidderId = buyerId;
        auction.highestBidderName = buyer->name;
        auction.state = AuctionState::Completed;
        
        // 处理结算
        ProcessAuctionCompletion(auction);
        
        LOG_INFO("AUCTION_MGR", "Player " + std::to_string(buyerId) + 
                " bought out auction " + std::to_string(auctionId) + 
                " for " + std::to_string(auction.buyoutPrice));
        
        return AuctionResult::Success;
    }
    
    // 取消拍卖
    AuctionResult CancelAuction(uint32_t sellerId, uint32_t auctionId)
    {
        std::unique_lock<std::shared_mutex> lock(m_auctionsMutex);
        
        auto it = m_auctions.find(auctionId);
        if (it == m_auctions.end())
        {
            return AuctionResult::NotFound;
        }
        
        auto& auction = it->second;
        
        // 检查权限
        if (auction.sellerId != sellerId)
        {
            return AuctionResult::PermissionDenied;
        }
        
        // 检查是否可以取消
        if (auction.state != AuctionState::Active || auction.totalBids > 0)
        {
            return AuctionResult::AuctionEnded;
        }
        
        // 设置为已取消
        auction.state = AuctionState::Cancelled;
        
        // 退还物品给卖家
        SendItemMail(sellerId, auction.auctionId, auction.itemInstanceId, auction.quantity,
                    "拍卖取消", "您的拍卖已被取消，物品已退还。");
        
        LOG_INFO("AUCTION_MGR", "Auction " + std::to_string(auctionId) + " cancelled by seller");
        
        return AuctionResult::Success;
    }
    
    // 搜索拍卖
    std::vector<AuctionItem> SearchAuctions(const AuctionSearchCriteria& criteria)
    {
        std::shared_lock<std::shared_mutex> lock(m_auctionsMutex);
        std::vector<AuctionItem> results;
        
        for (const auto& [auctionId, auction] : m_auctions)
        {
            // 应用搜索条件
            if (criteria.activeOnly && auction.state != AuctionState::Active)
                continue;
            
            if (criteria.itemTemplateId != 0 && auction.itemTemplateId != criteria.itemTemplateId)
                continue;
            
            if (auction.currentPrice < criteria.minPrice || auction.currentPrice > criteria.maxPrice)
                continue;
            
            if (criteria.sellerId != 0 && auction.sellerId != criteria.sellerId)
                continue;
            
            if (criteria.type != auction.type)
                continue;
            
            // 物品名称搜索
            if (!criteria.itemName.empty())
            {
                // 这里需要从物品系统获取物品名称进行匹配
                // if (auction.itemName.find(criteria.itemName) == std::string::npos)
                //     continue;
            }
            
            results.push_back(auction);
        }
        
        // 排序
        std::sort(results.begin(), results.end(), [&criteria](const AuctionItem& a, const AuctionItem& b) {
            switch (criteria.sortBy)
            {
            case AuctionSearchCriteria::SortBy::TimeLeft:
                return criteria.ascending ? a.GetRemainingTime() < b.GetRemainingTime() 
                                         : a.GetRemainingTime() > b.GetRemainingTime();
            case AuctionSearchCriteria::SortBy::Price:
                return criteria.ascending ? a.currentPrice < b.currentPrice 
                                         : a.currentPrice > b.currentPrice;
            case AuctionSearchCriteria::SortBy::BidCount:
                return criteria.ascending ? a.totalBids < b.totalBids 
                                         : a.totalBids > b.totalBids;
            default:
                return false;
            }
        });
        
        // 分页
        uint32_t startIndex = criteria.pageIndex * criteria.pageSize;
        uint32_t endIndex = std::min(startIndex + criteria.pageSize, static_cast<uint32_t>(results.size()));
        
        if (startIndex >= results.size())
        {
            return {};
        }
        
        return std::vector<AuctionItem>(results.begin() + startIndex, results.begin() + endIndex);
    }
    
    // 获取拍卖详情
    AuctionItem* GetAuction(uint32_t auctionId)
    {
        std::shared_lock<std::shared_mutex> lock(m_auctionsMutex);
        auto it = m_auctions.find(auctionId);
        if (it != m_auctions.end())
        {
            // 增加查看次数
            const_cast<AuctionItem&>(it->second).IncrementView();
            return &const_cast<AuctionItem&>(it->second);
        }
        return nullptr;
    }
    
    // 获取玩家的拍卖
    std::vector<AuctionItem> GetPlayerAuctions(uint32_t playerId, bool activeOnly = false)
    {
        std::shared_lock<std::shared_mutex> lock(m_auctionsMutex);
        std::vector<AuctionItem> playerAuctions;
        
        for (const auto& [auctionId, auction] : m_auctions)
        {
            if (auction.sellerId == playerId)
            {
                if (!activeOnly || auction.state == AuctionState::Active)
                {
                    playerAuctions.push_back(auction);
                }
            }
        }
        
        return playerAuctions;
    }
    
    // 获取玩家的竞价
    std::vector<AuctionItem> GetPlayerBids(uint32_t playerId, bool activeOnly = false)
    {
        std::shared_lock<std::shared_mutex> lock(m_auctionsMutex);
        std::vector<AuctionItem> playerBids;
        
        for (const auto& [auctionId, auction] : m_auctions)
        {
            // 检查是否有该玩家的竞价
            bool hasBid = false;
            for (const auto& bid : auction.bidHistory)
            {
                if (bid.bidderId == playerId)
                {
                    hasBid = true;
                    break;
                }
            }
            
            if (hasBid)
            {
                if (!activeOnly || auction.state == AuctionState::Active)
                {
                    playerBids.push_back(auction);
                }
            }
        }
        
        return playerBids;
    }
    
    // 获取拍卖统计
    AuctionStatistics GetStatistics()
    {
        std::shared_lock<std::shared_mutex> lock(m_auctionsMutex);
        
        AuctionStatistics stats;
        stats.totalAuctions = m_auctions.size();
        
        auto now = std::chrono::system_clock::now();
        auto dayStart = now - std::chrono::hours(24);
        
        std::unordered_map<uint32_t, uint32_t> itemCounts;
        
        for (const auto& [auctionId, auction] : m_auctions)
        {
            switch (auction.state)
            {
            case AuctionState::Active:
                stats.activeAuctions++;
                break;
            case AuctionState::Completed:
                stats.completedAuctions++;
                stats.totalVolume += auction.currentPrice;
                stats.totalCommission += auction.CalculateCommission();
                
                if (auction.endTime >= dayStart)
                {
                    stats.dailyVolume += auction.currentPrice;
                    stats.dailyCommission += auction.CalculateCommission();
                }
                break;
            default:
                break;
            }
            
            // 统计热门物品
            itemCounts[auction.itemTemplateId]++;
        }
        
        // 排序热门物品
        for (const auto& [itemId, count] : itemCounts)
        {
            stats.popularItems.emplace_back(itemId, count);
        }
        
        std::sort(stats.popularItems.begin(), stats.popularItems.end(),
                 [](const auto& a, const auto& b) { return a.second > b.second; });
        
        // 只保留前10个
        if (stats.popularItems.size() > 10)
        {
            stats.popularItems.resize(10);
        }
        
        stats.lastUpdate = now;
        return stats;
    }

private:
    std::atomic<bool> m_running;
    std::thread m_updateThread;
    std::chrono::minutes m_updateInterval;
    
    // 拍卖数据
    mutable std::shared_mutex m_auctionsMutex;
    std::unordered_map<uint32_t, AuctionItem> m_auctions;
    std::atomic<uint32_t> m_nextAuctionId;
    std::atomic<uint32_t> m_nextBidId;
    
    // 邮件数据
    mutable std::shared_mutex m_mailsMutex;
    std::unordered_map<uint32_t, AuctionMail> m_mails;
    std::atomic<uint32_t> m_nextMailId;
    
    // 更新循环
    void UpdateLoop()
    {
        while (m_running)
        {
            try
            {
                ProcessExpiredAuctions();
                CleanupOldData();
                std::this_thread::sleep_for(m_updateInterval);
            }
            catch (const std::exception& e)
            {
                LOG_ERROR("AUCTION_MGR", std::string("Error in update loop: ") + e.what());
            }
        }
    }
    
    void ProcessExpiredAuctions();
    void ProcessAuctionCompletion(AuctionItem& auction);
    void CleanupOldData();
    
    // 辅助方法
    uint32_t GetPlayerAuctionCount(uint32_t playerId);
    uint32_t GetMaxAuctionsPerPlayer(uint32_t playerId);
    uint32_t CalculateListingFee(uint32_t startPrice, uint32_t duration);
    bool HasEnoughSpace(const Player& player, uint32_t itemId, uint32_t quantity);
    void RefundBidder(uint32_t bidderId, uint32_t amount);
    void SendMoneyMail(uint32_t recipientId, uint32_t auctionId, uint32_t amount, 
                      const std::string& subject, const std::string& content);
    void SendItemMail(uint32_t recipientId, uint32_t auctionId, uint32_t itemId, uint32_t quantity,
                     const std::string& subject, const std::string& content);
};

} // namespace sword2

// 全局拍卖行管理器访问
#define AUCTION_MANAGER() sword2::AuctionManager::getInstance()

// 便捷宏定义
#define START_AUCTION_SYSTEM() AUCTION_MANAGER().Start()
#define STOP_AUCTION_SYSTEM() AUCTION_MANAGER().Stop()

#define CREATE_AUCTION(sellerId, itemId, startPrice, buyoutPrice, type, duration) \
    AUCTION_MANAGER().CreateAuction(sellerId, itemId, startPrice, buyoutPrice, type, duration)
#define PLACE_BID(bidderId, auctionId, amount) AUCTION_MANAGER().PlaceBid(bidderId, auctionId, amount)
#define BUYOUT_AUCTION(buyerId, auctionId) AUCTION_MANAGER().Buyout(buyerId, auctionId)
#define CANCEL_AUCTION(sellerId, auctionId) AUCTION_MANAGER().CancelAuction(sellerId, auctionId)

#define SEARCH_AUCTIONS(criteria) AUCTION_MANAGER().SearchAuctions(criteria)
#define GET_AUCTION(auctionId) AUCTION_MANAGER().GetAuction(auctionId)
#define GET_PLAYER_AUCTIONS(playerId, activeOnly) AUCTION_MANAGER().GetPlayerAuctions(playerId, activeOnly)
#define GET_PLAYER_BIDS(playerId, activeOnly) AUCTION_MANAGER().GetPlayerBids(playerId, activeOnly)

#define GET_AUCTION_STATISTICS() AUCTION_MANAGER().GetStatistics()

#endif // AUCTION_MANAGER_H
