﻿<?xml version="1.0" encoding="utf-8"?>
<Project DefaultTargets="Build" ToolsVersion="4.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <ItemGroup Label="ProjectConfigurations">
    <ProjectConfiguration Include="Debug|Win32">
      <Configuration>Debug</Configuration>
      <Platform>Win32</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Release|Win32">
      <Configuration>Release</Configuration>
      <Platform>Win32</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Template|Win32">
      <Configuration>Template</Configuration>
      <Platform>Win32</Platform>
    </ProjectConfiguration>
  </ItemGroup>
  <PropertyGroup Label="Globals">
    <SccProjectName>"$/Library/LuaLib/LuaLibDll", IUBAAAAA</SccProjectName>
    <SccLocalPath>.</SccLocalPath>
    <ProjectGuid>{FCB5799E-D6F2-E39D-C0A5-88DC5787835C}</ProjectGuid>
    <WindowsTargetPlatformVersion>10.0</WindowsTargetPlatformVersion>
  </PropertyGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.Default.props" />
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Template|Win32'" Label="Configuration">
    <ConfigurationType>Application</ConfigurationType>
    <PlatformToolset>v143</PlatformToolset>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|Win32'" Label="Configuration">
    <ConfigurationType>DynamicLibrary</ConfigurationType>
    <UseOfMfc>false</UseOfMfc>
    <CharacterSet>MultiByte</CharacterSet>
    <PlatformToolset>v143</PlatformToolset>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'" Label="Configuration">
    <ConfigurationType>DynamicLibrary</ConfigurationType>
    <UseOfMfc>false</UseOfMfc>
    <CharacterSet>MultiByte</CharacterSet>
    <PlatformToolset>v143</PlatformToolset>
  </PropertyGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.props" />
  <ImportGroup Label="ExtensionSettings">
  </ImportGroup>
  <ImportGroup Label="PropertySheets" Condition="'$(Configuration)|$(Platform)'=='Template|Win32'">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  <ImportGroup Condition="'$(Configuration)|$(Platform)'=='Release|Win32'" Label="PropertySheets">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
    <Import Project="$(VCTargetsPath)Microsoft.Cpp.UpgradeFromVC60.props" />
  </ImportGroup>
  <ImportGroup Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'" Label="PropertySheets">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
    <Import Project="$(VCTargetsPath)Microsoft.Cpp.UpgradeFromVC60.props" />
  </ImportGroup>
  <PropertyGroup Label="UserMacros" />
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">
    <OutDir>.\Debug\</OutDir>
    <IntDir>.\Debug\</IntDir>
    <LinkIncremental>true</LinkIncremental>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|Win32'">
    <OutDir>.\Release\</OutDir>
    <IntDir>.\Release\</IntDir>
    <LinkIncremental>false</LinkIncremental>
  </PropertyGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">
    <ClCompile>
      <RuntimeLibrary>MultiThreadedDebugDLL</RuntimeLibrary>
      <InlineFunctionExpansion>Default</InlineFunctionExpansion>
      <FunctionLevelLinking>false</FunctionLevelLinking>
      <Optimization>Disabled</Optimization>
      <SuppressStartupBanner>true</SuppressStartupBanner>
      <WarningLevel>Level3</WarningLevel>
      <MinimalRebuild>true</MinimalRebuild>
      <DebugInformationFormat>None</DebugInformationFormat>
      <AdditionalIncludeDirectories>.\src;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <PreprocessorDefinitions>WIN32;_DEBUG;_WINDOWS;_USRDLL;LUALIBDLL_EXPORTS;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <AssemblerListingLocation>.\Debug\</AssemblerListingLocation>
      <PrecompiledHeaderOutputFile>.\Debug\LuaLibDll.pch</PrecompiledHeaderOutputFile>
      <ObjectFileName>.\Debug\</ObjectFileName>
      <ProgramDataBaseFileName>.\Debug\</ProgramDataBaseFileName>
      <BasicRuntimeChecks>EnableFastChecks</BasicRuntimeChecks>
      <MultiProcessorCompilation>true</MultiProcessorCompilation>
    </ClCompile>
    <PostBuildEvent>
      <Command>copy debug\lualibdll.lib ..\..\..\Lib\lualibdll.lib
copy debug\lualibdll.dll ..\..\..\Lib\lualibdll.dll</Command>
    </PostBuildEvent>
    <Midl>
      <SuppressStartupBanner>true</SuppressStartupBanner>
      <PreprocessorDefinitions>_DEBUG;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <TypeLibraryName>.\Debug\LuaLibDll.tlb</TypeLibraryName>
      <MkTypLibCompatible>true</MkTypLibCompatible>
      <TargetEnvironment>Win32</TargetEnvironment>
    </Midl>
    <ResourceCompile>
      <Culture>0x0804</Culture>
      <PreprocessorDefinitions>_DEBUG;%(PreprocessorDefinitions)</PreprocessorDefinitions>
    </ResourceCompile>
    <Bscmake>
      <SuppressStartupBanner>true</SuppressStartupBanner>
      <OutputFile>.\Debug\LuaLibDll.bsc</OutputFile>
    </Bscmake>
    <Link>
      <SuppressStartupBanner>true</SuppressStartupBanner>
      <LinkDLL>true</LinkDLL>
      <GenerateDebugInformation>true</GenerateDebugInformation>
      <SubSystem>Console</SubSystem>
      <OutputFile>.\Debug\LuaLibDll.dll</OutputFile>
      <ImportLibrary>.\Debug\LuaLibDll.lib</ImportLibrary>
      <AdditionalDependencies>odbc32.lib;odbccp32.lib;%(AdditionalDependencies)</AdditionalDependencies>
    </Link>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Release|Win32'">
    <ClCompile>
      <RuntimeLibrary>MultiThreadedDLL</RuntimeLibrary>
      <InlineFunctionExpansion>OnlyExplicitInline</InlineFunctionExpansion>
      <StringPooling>true</StringPooling>
      <FunctionLevelLinking>true</FunctionLevelLinking>
      <Optimization>MaxSpeed</Optimization>
      <SuppressStartupBanner>true</SuppressStartupBanner>
      <WarningLevel>Level3</WarningLevel>
      <AdditionalIncludeDirectories>.\LuaLibDll\src;.\src;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <PreprocessorDefinitions>WIN32;NDEBUG;_WINDOWS;_USRDLL;LUALIBDLL_EXPORTS;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <AssemblerListingLocation>.\Release\</AssemblerListingLocation>
      <PrecompiledHeaderOutputFile>.\Release\LuaLibDll.pch</PrecompiledHeaderOutputFile>
      <ObjectFileName>.\Release\</ObjectFileName>
      <ProgramDataBaseFileName>.\Release\</ProgramDataBaseFileName>
    </ClCompile>
    <PostBuildEvent>
      <Command>copy Release\lualibdll.lib ..\..\..\Lib\lualibdll.lib
copy Release\lualibdll.dll ..\..\..\Lib\lualibdll.dll</Command>
    </PostBuildEvent>
    <Midl>
      <SuppressStartupBanner>true</SuppressStartupBanner>
      <PreprocessorDefinitions>NDEBUG;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <TypeLibraryName>.\Release\LuaLibDll.tlb</TypeLibraryName>
      <MkTypLibCompatible>true</MkTypLibCompatible>
      <TargetEnvironment>Win32</TargetEnvironment>
    </Midl>
    <ResourceCompile>
      <Culture>0x0804</Culture>
      <PreprocessorDefinitions>NDEBUG;%(PreprocessorDefinitions)</PreprocessorDefinitions>
    </ResourceCompile>
    <Bscmake>
      <SuppressStartupBanner>true</SuppressStartupBanner>
      <OutputFile>.\Release\LuaLibDll.bsc</OutputFile>
    </Bscmake>
    <Link>
      <SuppressStartupBanner>true</SuppressStartupBanner>
      <LinkDLL>true</LinkDLL>
      <SubSystem>Console</SubSystem>
      <OutputFile>.\Release\LuaLibDll.dll</OutputFile>
      <ImportLibrary>.\Release\LuaLibDll.lib</ImportLibrary>
      <AdditionalDependencies>odbc32.lib;odbccp32.lib;%(AdditionalDependencies)</AdditionalDependencies>
    </Link>
  </ItemDefinitionGroup>
  <ItemGroup>
    <ClCompile Include="src\lapi.c" />
    <ClCompile Include="src\lcode.c" />
    <ClCompile Include="src\ldebug.c" />
    <ClCompile Include="src\ldo.c" />
    <ClCompile Include="src\lfunc.c" />
    <ClCompile Include="src\lgc.c" />
    <ClCompile Include="src\llex.c" />
    <ClCompile Include="src\lmem.c" />
    <ClCompile Include="src\lobject.c" />
    <ClCompile Include="src\lparser.c" />
    <ClCompile Include="src\lstate.c" />
    <ClCompile Include="src\lstring.c" />
    <ClCompile Include="src\ltable.c" />
    <ClCompile Include="src\ltests.c" />
    <ClCompile Include="src\ltm.c" />
    <ClCompile Include="src\lua.c" />
    <ClCompile Include="src\LuaExtend.c" />
    <ClCompile Include="src\lundump.c" />
    <ClCompile Include="src\lvm.c" />
    <ClCompile Include="src\lzio.c" />
    <ClCompile Include="src\baselib\lauxlib.c" />
    <ClCompile Include="src\baselib\lbaselib.c" />
    <ClCompile Include="src\baselib\ldblib.c" />
    <ClCompile Include="src\baselib\liolib.c" />
    <ClCompile Include="src\baselib\lmathlib.c" />
    <ClCompile Include="src\baselib\lstrlib.c" />
  </ItemGroup>
  <ItemGroup>
    <ClInclude Include="src\lapi.h" />
    <ClInclude Include="src\lauxlib.h" />
    <ClInclude Include="src\lcode.h" />
    <ClInclude Include="src\ldebug.h" />
    <ClInclude Include="src\ldo.h" />
    <ClInclude Include="src\lfunc.h" />
    <ClInclude Include="src\lgc.h" />
    <ClInclude Include="src\llex.h" />
    <ClInclude Include="src\llimits.h" />
    <ClInclude Include="src\lmem.h" />
    <ClInclude Include="src\lobject.h" />
    <ClInclude Include="src\lopcodes.h" />
    <ClInclude Include="src\lparser.h" />
    <ClInclude Include="src\lstate.h" />
    <ClInclude Include="src\lstring.h" />
    <ClInclude Include="src\ltable.h" />
    <ClInclude Include="src\ltm.h" />
    <ClInclude Include="src\lua.h" />
    <ClInclude Include="src\luadebug.h" />
    <ClInclude Include="src\LuaDef.h" />
    <ClInclude Include="src\lualib.h" />
    <ClInclude Include="src\lundump.h" />
    <ClInclude Include="src\lvm.h" />
    <ClInclude Include="src\lzio.h" />
  </ItemGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.targets" />
  <ImportGroup Label="ExtensionTargets">
  </ImportGroup>
</Project>