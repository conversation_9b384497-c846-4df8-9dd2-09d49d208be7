//---------------------------------------------------------------------------
// Sword2 Social Manager (c) 2024
//
// File:	SocialManager.h
// Date:	2024.07.02
// Code:	AI Assistant
// Desc:	Comprehensive social management system
//---------------------------------------------------------------------------
#ifndef SOCIAL_MANAGER_H
#define SOCIAL_MANAGER_H

#include "SocialSystem.h"
#include "PlayerManager.h"
#include "LuaScriptEngine.h"
#include "UnifiedLoggingSystem.h"
#include <unordered_map>
#include <memory>
#include <mutex>
#include <shared_mutex>
#include <thread>
#include <atomic>
#include <queue>
#include <functional>
#include <algorithm>

namespace sword2 {

// 好友管理器
class FriendManager
{
public:
    FriendManager() = default;
    ~FriendManager() = default;
    
    // 添加好友
    SocialResult AddFriend(uint32_t playerId, uint32_t friendId, const std::string& groupName = "默认分组")
    {
        if (playerId == friendId)
            return SocialResult::SelfOperation;
        
        std::unique_lock<std::shared_mutex> lock(m_friendsMutex);
        
        auto& playerFriends = m_playerFriends[playerId];
        
        // 检查是否已经是好友
        auto it = std::find_if(playerFriends.begin(), playerFriends.end(),
            [friendId](const FriendInfo& info) { return info.playerId == friendId; });
        
        if (it != playerFriends.end())
        {
            if (it->relationship == FriendshipType::Blacklist)
                return SocialResult::Blocked;
            if (it->relationship == FriendshipType::Friend)
                return SocialResult::AlreadyExists;
        }
        
        // 获取好友信息
        auto friendPlayer = GET_ONLINE_PLAYER(friendId);
        if (!friendPlayer)
            return SocialResult::NotFound;
        
        // 创建好友信息
        FriendInfo friendInfo(friendId, friendPlayer->name);
        friendInfo.groupName = groupName;
        friendInfo.level = friendPlayer->level;
        friendInfo.series = friendPlayer->series;
        friendInfo.isOnline = true;
        
        // 如果已存在，更新关系
        if (it != playerFriends.end())
        {
            *it = friendInfo;
        }
        else
        {
            playerFriends.push_back(friendInfo);
        }
        
        LOG_INFO("SOCIAL_MGR", "Player " + std::to_string(playerId) + " added friend " + std::to_string(friendId));
        return SocialResult::Success;
    }
    
    // 删除好友
    SocialResult RemoveFriend(uint32_t playerId, uint32_t friendId)
    {
        std::unique_lock<std::shared_mutex> lock(m_friendsMutex);
        
        auto it = m_playerFriends.find(playerId);
        if (it == m_playerFriends.end())
            return SocialResult::NotFound;
        
        auto& friendList = it->second;
        auto friendIt = std::find_if(friendList.begin(), friendList.end(),
            [friendId](const FriendInfo& info) { return info.playerId == friendId; });
        
        if (friendIt == friendList.end())
            return SocialResult::NotFound;
        
        friendList.erase(friendIt);
        
        LOG_INFO("SOCIAL_MGR", "Player " + std::to_string(playerId) + " removed friend " + std::to_string(friendId));
        return SocialResult::Success;
    }
    
    // 添加到黑名单
    SocialResult AddToBlacklist(uint32_t playerId, uint32_t targetId)
    {
        if (playerId == targetId)
            return SocialResult::SelfOperation;
        
        std::unique_lock<std::shared_mutex> lock(m_friendsMutex);
        
        auto& playerFriends = m_playerFriends[playerId];
        
        // 查找是否已存在
        auto it = std::find_if(playerFriends.begin(), playerFriends.end(),
            [targetId](const FriendInfo& info) { return info.playerId == targetId; });
        
        auto targetPlayer = GET_ONLINE_PLAYER(targetId);
        std::string targetName = targetPlayer ? targetPlayer->name : "Unknown";
        
        if (it != playerFriends.end())
        {
            // 更新关系为黑名单
            it->relationship = FriendshipType::Blacklist;
            it->groupName = "黑名单";
        }
        else
        {
            // 创建新的黑名单条目
            FriendInfo blacklistInfo(targetId, targetName, FriendshipType::Blacklist);
            blacklistInfo.groupName = "黑名单";
            if (targetPlayer)
            {
                blacklistInfo.level = targetPlayer->level;
                blacklistInfo.series = targetPlayer->series;
                blacklistInfo.isOnline = true;
            }
            playerFriends.push_back(blacklistInfo);
        }
        
        LOG_INFO("SOCIAL_MGR", "Player " + std::to_string(playerId) + " added " + std::to_string(targetId) + " to blacklist");
        return SocialResult::Success;
    }
    
    // 从黑名单移除
    SocialResult RemoveFromBlacklist(uint32_t playerId, uint32_t targetId)
    {
        std::unique_lock<std::shared_mutex> lock(m_friendsMutex);
        
        auto it = m_playerFriends.find(playerId);
        if (it == m_playerFriends.end())
            return SocialResult::NotFound;
        
        auto& friendList = it->second;
        auto blacklistIt = std::find_if(friendList.begin(), friendList.end(),
            [targetId](const FriendInfo& info) { 
                return info.playerId == targetId && info.relationship == FriendshipType::Blacklist; 
            });
        
        if (blacklistIt == friendList.end())
            return SocialResult::NotFound;
        
        friendList.erase(blacklistIt);
        
        LOG_INFO("SOCIAL_MGR", "Player " + std::to_string(playerId) + " removed " + std::to_string(targetId) + " from blacklist");
        return SocialResult::Success;
    }
    
    // 设置好友分组
    SocialResult SetFriendGroup(uint32_t playerId, uint32_t friendId, const std::string& groupName)
    {
        std::unique_lock<std::shared_mutex> lock(m_friendsMutex);
        
        auto it = m_playerFriends.find(playerId);
        if (it == m_playerFriends.end())
            return SocialResult::NotFound;
        
        auto& friendList = it->second;
        auto friendIt = std::find_if(friendList.begin(), friendList.end(),
            [friendId](const FriendInfo& info) { return info.playerId == friendId; });
        
        if (friendIt == friendList.end())
            return SocialResult::NotFound;
        
        friendIt->groupName = groupName;
        
        LOG_INFO("SOCIAL_MGR", "Player " + std::to_string(playerId) + " set friend " + std::to_string(friendId) + " to group: " + groupName);
        return SocialResult::Success;
    }
    
    // 获取好友列表
    std::vector<FriendInfo> GetFriendList(uint32_t playerId, FriendshipType type = FriendshipType::Friend) const
    {
        std::shared_lock<std::shared_mutex> lock(m_friendsMutex);
        
        auto it = m_playerFriends.find(playerId);
        if (it == m_playerFriends.end())
            return {};
        
        std::vector<FriendInfo> result;
        for (const auto& friendInfo : it->second)
        {
            if (type == FriendshipType::None || friendInfo.relationship == type)
            {
                result.push_back(friendInfo);
            }
        }
        
        return result;
    }
    
    // 获取在线好友
    std::vector<FriendInfo> GetOnlineFriends(uint32_t playerId) const
    {
        std::shared_lock<std::shared_mutex> lock(m_friendsMutex);
        
        auto it = m_playerFriends.find(playerId);
        if (it == m_playerFriends.end())
            return {};
        
        std::vector<FriendInfo> result;
        for (const auto& friendInfo : it->second)
        {
            if (friendInfo.isOnline && friendInfo.relationship == FriendshipType::Friend)
            {
                result.push_back(friendInfo);
            }
        }
        
        return result;
    }
    
    // 检查是否为好友
    bool IsFriend(uint32_t playerId, uint32_t friendId) const
    {
        std::shared_lock<std::shared_mutex> lock(m_friendsMutex);
        
        auto it = m_playerFriends.find(playerId);
        if (it == m_playerFriends.end())
            return false;
        
        auto friendIt = std::find_if(it->second.begin(), it->second.end(),
            [friendId](const FriendInfo& info) { 
                return info.playerId == friendId && info.relationship == FriendshipType::Friend; 
            });
        
        return friendIt != it->second.end();
    }
    
    // 检查是否在黑名单
    bool IsBlacklisted(uint32_t playerId, uint32_t targetId) const
    {
        std::shared_lock<std::shared_mutex> lock(m_friendsMutex);
        
        auto it = m_playerFriends.find(playerId);
        if (it == m_playerFriends.end())
            return false;
        
        auto blacklistIt = std::find_if(it->second.begin(), it->second.end(),
            [targetId](const FriendInfo& info) { 
                return info.playerId == targetId && info.relationship == FriendshipType::Blacklist; 
            });
        
        return blacklistIt != it->second.end();
    }
    
    // 更新好友在线状态
    void UpdateFriendOnlineStatus(uint32_t playerId, bool isOnline, FriendStatus status = FriendStatus::Online)
    {
        std::unique_lock<std::shared_mutex> lock(m_friendsMutex);
        
        // 更新所有将此玩家作为好友的玩家的好友列表
        for (auto& [ownerId, friendList] : m_playerFriends)
        {
            for (auto& friendInfo : friendList)
            {
                if (friendInfo.playerId == playerId)
                {
                    friendInfo.UpdateOnlineStatus(isOnline, status);
                }
            }
        }
    }
    
    // 更新好友信息
    void UpdateFriendInfo(uint32_t playerId, uint32_t level, PlayerSeries series, uint32_t mapId, const std::string& location)
    {
        std::unique_lock<std::shared_mutex> lock(m_friendsMutex);
        
        // 更新所有将此玩家作为好友的玩家的好友列表
        for (auto& [ownerId, friendList] : m_playerFriends)
        {
            for (auto& friendInfo : friendList)
            {
                if (friendInfo.playerId == playerId)
                {
                    friendInfo.level = level;
                    friendInfo.series = series;
                    friendInfo.mapId = mapId;
                    friendInfo.location = location;
                }
            }
        }
    }

private:
    mutable std::shared_mutex m_friendsMutex;
    std::unordered_map<uint32_t, std::vector<FriendInfo>> m_playerFriends;
};

// 聊天管理器
class ChatManager
{
public:
    ChatManager() : m_nextMessageId(1), m_nextChannelId(1000) {}
    ~ChatManager() = default;
    
    // 发送聊天消息
    SocialResult SendMessage(uint32_t senderId, const std::string& senderName, const std::string& content,
                           ChatChannelType channel, uint32_t receiverId = 0, const std::string& receiverName = "")
    {
        if (content.empty() || content.length() > 500)
            return SocialResult::MessageTooLong;
        
        // 检查垃圾信息
        if (IsSpamMessage(senderId, content))
            return SocialResult::SpamDetected;
        
        std::unique_lock<std::shared_mutex> lock(m_chatMutex);
        
        uint32_t messageId = m_nextMessageId++;
        ChatMessage message(senderId, senderName, content, channel);
        message.messageId = messageId;
        message.receiverId = receiverId;
        message.receiverName = receiverName;
        
        // 根据频道类型处理消息
        switch (channel)
        {
        case ChatChannelType::Private:
            if (receiverId == 0)
                return SocialResult::NotFound;
            
            // 检查是否被屏蔽
            if (IsPlayerBlocked(receiverId, senderId))
                return SocialResult::Blocked;
            
            // 存储私聊消息
            m_privateMessages[senderId].push_back(message);
            m_privateMessages[receiverId].push_back(message);
            break;
            
        case ChatChannelType::World:
        case ChatChannelType::Region:
        case ChatChannelType::Trade:
        case ChatChannelType::Newbie:
            // 存储公共频道消息
            m_channelMessages[channel].push_back(message);
            
            // 限制消息数量
            if (m_channelMessages[channel].size() > 100)
            {
                m_channelMessages[channel].erase(m_channelMessages[channel].begin());
            }
            break;
            
        case ChatChannelType::Team:
        case ChatChannelType::Guild:
            // 这些频道需要特殊处理，暂时存储到公共频道
            m_channelMessages[channel].push_back(message);
            break;
            
        default:
            break;
        }
        
        // 记录聊天历史
        m_chatHistory.push_back(message);
        if (m_chatHistory.size() > 1000)
        {
            m_chatHistory.erase(m_chatHistory.begin());
        }
        
        // 更新反垃圾信息记录
        UpdateSpamRecord(senderId, content);
        
        LOG_DEBUG("SOCIAL_MGR", "Chat message sent: " + senderName + " -> " + message.GetChannelDescription() + ": " + content);
        return SocialResult::Success;
    }
    
    // 获取私聊消息
    std::vector<ChatMessage> GetPrivateMessages(uint32_t playerId, uint32_t friendId, uint32_t count = 50) const
    {
        std::shared_lock<std::shared_mutex> lock(m_chatMutex);
        
        auto it = m_privateMessages.find(playerId);
        if (it == m_privateMessages.end())
            return {};
        
        std::vector<ChatMessage> result;
        for (const auto& message : it->second)
        {
            if ((message.senderId == playerId && message.receiverId == friendId) ||
                (message.senderId == friendId && message.receiverId == playerId))
            {
                result.push_back(message);
            }
        }
        
        // 只返回最近的消息
        if (result.size() > count)
        {
            result.erase(result.begin(), result.end() - count);
        }
        
        return result;
    }
    
    // 获取频道消息
    std::vector<ChatMessage> GetChannelMessages(ChatChannelType channel, uint32_t count = 50) const
    {
        std::shared_lock<std::shared_mutex> lock(m_chatMutex);
        
        auto it = m_channelMessages.find(channel);
        if (it == m_channelMessages.end())
            return {};
        
        std::vector<ChatMessage> result = it->second;
        
        // 只返回最近的消息
        if (result.size() > count)
        {
            result.erase(result.begin(), result.end() - count);
        }
        
        return result;
    }
    
    // 创建自定义频道
    uint32_t CreateCustomChannel(const std::string& channelName, uint32_t ownerId, const std::string& ownerName,
                                bool isPublic = true, const std::string& password = "")
    {
        std::unique_lock<std::shared_mutex> lock(m_channelMutex);
        
        uint32_t channelId = m_nextChannelId++;
        ChatChannel channel(channelName, ownerId, ownerName);
        channel.channelId = channelId;
        channel.type = ChatChannelType::Custom;
        channel.isPublic = isPublic;
        channel.requirePassword = !password.empty();
        channel.password = password;
        
        m_customChannels[channelId] = channel;
        
        LOG_INFO("SOCIAL_MGR", "Created custom channel: " + channelName + " (ID: " + std::to_string(channelId) + ")");
        return channelId;
    }
    
    // 加入频道
    SocialResult JoinChannel(uint32_t channelId, uint32_t playerId, const std::string& password = "")
    {
        std::unique_lock<std::shared_mutex> lock(m_channelMutex);
        
        auto it = m_customChannels.find(channelId);
        if (it == m_customChannels.end())
            return SocialResult::NotFound;
        
        auto& channel = it->second;
        
        // 检查是否被禁言
        if (channel.IsBanned(playerId))
            return SocialResult::Banned;
        
        // 检查密码
        if (channel.requirePassword && channel.password != password)
            return SocialResult::InvalidPassword;
        
        // 检查是否已满
        if (!channel.AddMember(playerId))
            return SocialResult::ChannelFull;
        
        LOG_INFO("SOCIAL_MGR", "Player " + std::to_string(playerId) + " joined channel " + std::to_string(channelId));
        return SocialResult::Success;
    }
    
    // 离开频道
    SocialResult LeaveChannel(uint32_t channelId, uint32_t playerId)
    {
        std::unique_lock<std::shared_mutex> lock(m_channelMutex);
        
        auto it = m_customChannels.find(channelId);
        if (it == m_customChannels.end())
            return SocialResult::NotFound;
        
        auto& channel = it->second;
        
        if (!channel.RemoveMember(playerId))
            return SocialResult::NotFound;
        
        // 如果是频道所有者离开，删除频道
        if (playerId == channel.ownerId && channel.members.empty())
        {
            m_customChannels.erase(it);
            LOG_INFO("SOCIAL_MGR", "Channel " + std::to_string(channelId) + " deleted (owner left)");
        }
        else
        {
            LOG_INFO("SOCIAL_MGR", "Player " + std::to_string(playerId) + " left channel " + std::to_string(channelId));
        }
        
        return SocialResult::Success;
    }
    
    // 获取频道列表
    std::vector<ChatChannel> GetChannelList(bool publicOnly = true) const
    {
        std::shared_lock<std::shared_mutex> lock(m_channelMutex);
        
        std::vector<ChatChannel> result;
        for (const auto& [channelId, channel] : m_customChannels)
        {
            if (!publicOnly || channel.isPublic)
            {
                result.push_back(channel);
            }
        }
        
        return result;
    }

private:
    mutable std::shared_mutex m_chatMutex;
    mutable std::shared_mutex m_channelMutex;
    
    std::atomic<uint32_t> m_nextMessageId;
    std::atomic<uint32_t> m_nextChannelId;
    
    std::unordered_map<uint32_t, std::vector<ChatMessage>> m_privateMessages;
    std::unordered_map<ChatChannelType, std::vector<ChatMessage>> m_channelMessages;
    std::vector<ChatMessage> m_chatHistory;
    
    std::unordered_map<uint32_t, ChatChannel> m_customChannels;
    
    // 反垃圾信息
    struct SpamRecord
    {
        std::vector<std::chrono::system_clock::time_point> messageTimes;
        std::vector<std::string> recentMessages;
    };
    std::unordered_map<uint32_t, SpamRecord> m_spamRecords;
    
    bool IsSpamMessage(uint32_t playerId, const std::string& content)
    {
        auto now = std::chrono::system_clock::now();
        auto& record = m_spamRecords[playerId];
        
        // 检查发送频率
        record.messageTimes.push_back(now);
        
        // 移除1分钟前的记录
        auto cutoff = now - std::chrono::minutes(1);
        record.messageTimes.erase(
            std::remove_if(record.messageTimes.begin(), record.messageTimes.end(),
                [cutoff](const auto& time) { return time < cutoff; }),
            record.messageTimes.end());
        
        // 如果1分钟内发送超过10条消息，认为是垃圾信息
        if (record.messageTimes.size() > 10)
            return true;
        
        // 检查重复消息
        record.recentMessages.push_back(content);
        if (record.recentMessages.size() > 5)
        {
            record.recentMessages.erase(record.recentMessages.begin());
        }
        
        // 如果最近5条消息中有3条相同，认为是垃圾信息
        int duplicateCount = 0;
        for (const auto& msg : record.recentMessages)
        {
            if (msg == content)
                duplicateCount++;
        }
        
        return duplicateCount >= 3;
    }
    
    void UpdateSpamRecord(uint32_t playerId, const std::string& content)
    {
        // 在IsSpamMessage中已经更新了记录
    }
    
    bool IsPlayerBlocked(uint32_t playerId, uint32_t senderId)
    {
        // 这里需要调用FriendManager来检查是否被屏蔽
        // 暂时返回false
        return false;
    }
};

// 社交管理器主类
class SocialManager : public Singleton<SocialManager>
{
public:
    SocialManager()
        : m_running(false), m_updateInterval(std::chrono::minutes(5)) {}

    ~SocialManager()
    {
        Stop();
    }

    // 启动社交管理器
    bool Start()
    {
        if (m_running) return true;

        m_running = true;
        m_updateThread = std::thread(&SocialManager::UpdateLoop, this);

        LOG_INFO("SOCIAL_MGR", "Social manager started");
        return true;
    }

    // 停止社交管理器
    void Stop()
    {
        if (!m_running) return;

        m_running = false;
        if (m_updateThread.joinable())
        {
            m_updateThread.join();
        }

        LOG_INFO("SOCIAL_MGR", "Social manager stopped");
    }

    // 获取好友管理器
    FriendManager& GetFriendManager() { return m_friendManager; }

    // 获取聊天管理器
    ChatManager& GetChatManager() { return m_chatManager; }

    // 获取邮件管理器
    MailManager& GetMailManager() { return m_mailManager; }

    // 玩家上线通知
    void OnPlayerLogin(uint32_t playerId, const std::string& playerName)
    {
        // 更新好友在线状态
        m_friendManager.UpdateFriendOnlineStatus(playerId, true, FriendStatus::Online);

        // 通知好友上线
        NotifyFriendsOnlineStatus(playerId, true);

        LOG_DEBUG("SOCIAL_MGR", "Player " + playerName + " logged in");
    }

    // 玩家下线通知
    void OnPlayerLogout(uint32_t playerId, const std::string& playerName)
    {
        // 更新好友在线状态
        m_friendManager.UpdateFriendOnlineStatus(playerId, false, FriendStatus::Offline);

        // 通知好友下线
        NotifyFriendsOnlineStatus(playerId, false);

        LOG_DEBUG("SOCIAL_MGR", "Player " + playerName + " logged out");
    }

    // 玩家信息更新通知
    void OnPlayerInfoUpdate(uint32_t playerId, uint32_t level, PlayerSeries series, uint32_t mapId, const std::string& location)
    {
        m_friendManager.UpdateFriendInfo(playerId, level, series, mapId, location);
    }

    // 玩家状态更新通知
    void OnPlayerStatusUpdate(uint32_t playerId, FriendStatus status)
    {
        m_friendManager.UpdateFriendOnlineStatus(playerId, true, status);
        NotifyFriendsStatusUpdate(playerId, status);
    }

private:
    std::atomic<bool> m_running;
    std::thread m_updateThread;
    std::chrono::minutes m_updateInterval;

    FriendManager m_friendManager;
    ChatManager m_chatManager;
    MailManager m_mailManager;

    // 更新循环
    void UpdateLoop()
    {
        while (m_running)
        {
            try
            {
                // 清理过期邮件
                m_mailManager.CleanupExpiredMails();

                // 清理聊天历史
                CleanupChatHistory();

                std::this_thread::sleep_for(m_updateInterval);
            }
            catch (const std::exception& e)
            {
                LOG_ERROR("SOCIAL_MGR", std::string("Error in update loop: ") + e.what());
            }
        }
    }

    void NotifyFriendsOnlineStatus(uint32_t playerId, bool isOnline)
    {
        // 获取所有将此玩家作为好友的玩家
        // 这里需要实现反向查找逻辑
        // 暂时省略具体实现
    }

    void NotifyFriendsStatusUpdate(uint32_t playerId, FriendStatus status)
    {
        // 通知好友状态更新
        // 暂时省略具体实现
    }

    void CleanupChatHistory()
    {
        // 清理聊天历史
        // 暂时省略具体实现
    }
};

} // namespace sword2

// 全局社交管理器访问
#define SOCIAL_MANAGER() sword2::SocialManager::getInstance()

// 便捷宏定义
#define START_SOCIAL_SYSTEM() SOCIAL_MANAGER().Start()
#define STOP_SOCIAL_SYSTEM() SOCIAL_MANAGER().Stop()

#define FRIEND_MANAGER() SOCIAL_MANAGER().GetFriendManager()
#define CHAT_MANAGER() SOCIAL_MANAGER().GetChatManager()
#define MAIL_MANAGER() SOCIAL_MANAGER().GetMailManager()

// 好友系统宏
#define ADD_FRIEND(playerId, friendId, groupName) FRIEND_MANAGER().AddFriend(playerId, friendId, groupName)
#define REMOVE_FRIEND(playerId, friendId) FRIEND_MANAGER().RemoveFriend(playerId, friendId)
#define ADD_TO_BLACKLIST(playerId, targetId) FRIEND_MANAGER().AddToBlacklist(playerId, targetId)
#define REMOVE_FROM_BLACKLIST(playerId, targetId) FRIEND_MANAGER().RemoveFromBlacklist(playerId, targetId)
#define SET_FRIEND_GROUP(playerId, friendId, groupName) FRIEND_MANAGER().SetFriendGroup(playerId, friendId, groupName)
#define GET_FRIEND_LIST(playerId, type) FRIEND_MANAGER().GetFriendList(playerId, type)
#define GET_ONLINE_FRIENDS(playerId) FRIEND_MANAGER().GetOnlineFriends(playerId)
#define IS_FRIEND(playerId, friendId) FRIEND_MANAGER().IsFriend(playerId, friendId)
#define IS_BLACKLISTED(playerId, targetId) FRIEND_MANAGER().IsBlacklisted(playerId, targetId)

// 聊天系统宏
#define SEND_MESSAGE(senderId, senderName, content, channel, receiverId, receiverName) \
    CHAT_MANAGER().SendMessage(senderId, senderName, content, channel, receiverId, receiverName)
#define GET_PRIVATE_MESSAGES(playerId, friendId, count) CHAT_MANAGER().GetPrivateMessages(playerId, friendId, count)
#define GET_CHANNEL_MESSAGES(channel, count) CHAT_MANAGER().GetChannelMessages(channel, count)
#define CREATE_CUSTOM_CHANNEL(channelName, ownerId, ownerName, isPublic, password) \
    CHAT_MANAGER().CreateCustomChannel(channelName, ownerId, ownerName, isPublic, password)
#define JOIN_CHANNEL(channelId, playerId, password) CHAT_MANAGER().JoinChannel(channelId, playerId, password)
#define LEAVE_CHANNEL(channelId, playerId) CHAT_MANAGER().LeaveChannel(channelId, playerId)
#define GET_CHANNEL_LIST(publicOnly) CHAT_MANAGER().GetChannelList(publicOnly)

// 邮件系统宏
#define SEND_MAIL(senderId, senderName, receiverId, receiverName, subject, content, type) \
    MAIL_MANAGER().SendMail(senderId, senderName, receiverId, receiverName, subject, content, type)
#define SEND_ITEM_MAIL(senderId, senderName, receiverId, receiverName, subject, content, itemId, quantity, money) \
    MAIL_MANAGER().SendItemMail(senderId, senderName, receiverId, receiverName, subject, content, itemId, quantity, money)
#define SEND_SYSTEM_MAIL(receiverId, receiverName, subject, content, itemId, quantity, money) \
    MAIL_MANAGER().SendSystemMail(receiverId, receiverName, subject, content, itemId, quantity, money)
#define SEND_BROADCAST_MAIL(receiverIds, subject, content, type) \
    MAIL_MANAGER().SendBroadcastMail(receiverIds, subject, content, type)
#define READ_MAIL(playerId, mailId) MAIL_MANAGER().ReadMail(playerId, mailId)
#define DELETE_MAIL(playerId, mailId) MAIL_MANAGER().DeleteMail(playerId, mailId)
#define COLLECT_ATTACHMENT(playerId, mailId, attachmentId) MAIL_MANAGER().CollectAttachment(playerId, mailId, attachmentId)
#define COLLECT_ALL_ATTACHMENTS(playerId, mailId) MAIL_MANAGER().CollectAllAttachments(playerId, mailId)
#define GET_MAIL_LIST(playerId, type, unreadOnly) MAIL_MANAGER().GetMailList(playerId, type, unreadOnly)
#define GET_MAIL(playerId, mailId) MAIL_MANAGER().GetMail(playerId, mailId)
#define GET_UNREAD_MAIL_COUNT(playerId) MAIL_MANAGER().GetUnreadMailCount(playerId)

// 社交事件通知宏
#define NOTIFY_PLAYER_LOGIN(playerId, playerName) SOCIAL_MANAGER().OnPlayerLogin(playerId, playerName)
#define NOTIFY_PLAYER_LOGOUT(playerId, playerName) SOCIAL_MANAGER().OnPlayerLogout(playerId, playerName)
#define NOTIFY_PLAYER_INFO_UPDATE(playerId, level, series, mapId, location) \
    SOCIAL_MANAGER().OnPlayerInfoUpdate(playerId, level, series, mapId, location)
#define NOTIFY_PLAYER_STATUS_UPDATE(playerId, status) SOCIAL_MANAGER().OnPlayerStatusUpdate(playerId, status)

#endif // SOCIAL_MANAGER_H
