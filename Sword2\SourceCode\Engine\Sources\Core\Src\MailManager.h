//---------------------------------------------------------------------------
// Sword2 Mail Manager (c) 2024
//
// File:	MailManager.h
// Date:	2024.07.02
// Code:	AI Assistant
// Desc:	Comprehensive mail management system
//---------------------------------------------------------------------------
#ifndef MAIL_MANAGER_H
#define MAIL_MANAGER_H

#include "SocialSystem.h"
#include "PlayerManager.h"
#include "ItemManager.h"
#include "InventoryManager.h"
#include "LuaScriptEngine.h"
#include "UnifiedLoggingSystem.h"
#include <unordered_map>
#include <memory>
#include <mutex>
#include <shared_mutex>
#include <thread>
#include <atomic>
#include <queue>
#include <functional>
#include <algorithm>

namespace sword2 {

// 邮件管理器
class MailManager
{
public:
    MailManager() : m_nextMailId(1), m_nextAttachmentId(1) {}
    ~MailManager() = default;
    
    // 发送邮件
    SocialResult SendMail(uint32_t senderId, const std::string& senderName, 
                         uint32_t receiverId, const std::string& receiverName,
                         const std::string& subject, const std::string& content,
                         MailType type = MailType::Personal)
    {
        if (subject.empty() || subject.length() > 100)
            return SocialResult::MessageTooLong;
        
        if (content.length() > 1000)
            return SocialResult::MessageTooLong;
        
        // 检查收件人是否存在
        if (receiverId != 0)
        {
            auto receiver = GET_ONLINE_PLAYER(receiverId);
            if (!receiver && !PlayerExists(receiverId))
                return SocialResult::NotFound;
        }
        
        // 检查邮箱是否已满
        if (GetMailCount(receiverId) >= GetMaxMailCount(receiverId))
            return SocialResult::MailboxFull;
        
        std::unique_lock<std::shared_mutex> lock(m_mailMutex);
        
        uint32_t mailId = m_nextMailId++;
        MailInfo mail(senderId, senderName, receiverId, receiverName, subject, content);
        mail.mailId = mailId;
        mail.type = type;
        
        // 系统邮件设置为重要
        if (type == MailType::System || type == MailType::Achievement || type == MailType::Event)
        {
            mail.isImportant = true;
            mail.expireTime = mail.sendTime + std::chrono::hours(720); // 30天过期
        }
        
        m_playerMails[receiverId].push_back(mail);
        
        // 记录发送历史
        m_sentMails[senderId].push_back(mail);
        if (m_sentMails[senderId].size() > 100)
        {
            m_sentMails[senderId].erase(m_sentMails[senderId].begin());
        }
        
        LOG_INFO("MAIL_MGR", "Mail sent from " + senderName + " to " + receiverName + ": " + subject);
        return SocialResult::Success;
    }
    
    // 发送带附件的邮件
    SocialResult SendMailWithAttachments(uint32_t senderId, const std::string& senderName,
                                        uint32_t receiverId, const std::string& receiverName,
                                        const std::string& subject, const std::string& content,
                                        const std::vector<MailAttachment>& attachments,
                                        MailType type = MailType::Personal)
    {
        SocialResult result = SendMail(senderId, senderName, receiverId, receiverName, subject, content, type);
        if (result != SocialResult::Success)
            return result;
        
        std::unique_lock<std::shared_mutex> lock(m_mailMutex);
        
        // 找到刚发送的邮件
        auto& mailList = m_playerMails[receiverId];
        if (!mailList.empty())
        {
            auto& mail = mailList.back();
            
            // 添加附件
            for (auto attachment : attachments)
            {
                attachment.attachmentId = m_nextAttachmentId++;
                mail.AddAttachment(attachment);
            }
            
            LOG_INFO("MAIL_MGR", "Added " + std::to_string(attachments.size()) + " attachments to mail " + std::to_string(mail.mailId));
        }
        
        return SocialResult::Success;
    }
    
    // 发送物品邮件
    SocialResult SendItemMail(uint32_t senderId, const std::string& senderName,
                             uint32_t receiverId, const std::string& receiverName,
                             const std::string& subject, const std::string& content,
                             uint32_t itemId, uint32_t quantity, uint32_t money = 0)
    {
        std::vector<MailAttachment> attachments;
        
        // 添加物品附件
        if (itemId != 0 && quantity > 0)
        {
            MailAttachment itemAttachment("item", "物品", "");
            itemAttachment.itemId = itemId;
            itemAttachment.quantity = quantity;
            attachments.push_back(itemAttachment);
        }
        
        // 添加金钱附件
        if (money > 0)
        {
            MailAttachment moneyAttachment("money", "金钱", "");
            moneyAttachment.money = money;
            attachments.push_back(moneyAttachment);
        }
        
        return SendMailWithAttachments(senderId, senderName, receiverId, receiverName, 
                                     subject, content, attachments, MailType::Personal);
    }
    
    // 读取邮件
    SocialResult ReadMail(uint32_t playerId, uint32_t mailId)
    {
        std::unique_lock<std::shared_mutex> lock(m_mailMutex);
        
        auto it = m_playerMails.find(playerId);
        if (it == m_playerMails.end())
            return SocialResult::NotFound;
        
        auto& mailList = it->second;
        auto mailIt = std::find_if(mailList.begin(), mailList.end(),
            [mailId](const MailInfo& mail) { return mail.mailId == mailId; });
        
        if (mailIt == mailList.end())
            return SocialResult::NotFound;
        
        mailIt->MarkAsRead();
        
        LOG_DEBUG("MAIL_MGR", "Player " + std::to_string(playerId) + " read mail " + std::to_string(mailId));
        return SocialResult::Success;
    }
    
    // 删除邮件
    SocialResult DeleteMail(uint32_t playerId, uint32_t mailId)
    {
        std::unique_lock<std::shared_mutex> lock(m_mailMutex);
        
        auto it = m_playerMails.find(playerId);
        if (it == m_playerMails.end())
            return SocialResult::NotFound;
        
        auto& mailList = it->second;
        auto mailIt = std::find_if(mailList.begin(), mailList.end(),
            [mailId](const MailInfo& mail) { return mail.mailId == mailId; });
        
        if (mailIt == mailList.end())
            return SocialResult::NotFound;
        
        // 检查是否有未收取的附件
        if (mailIt->hasAttachment)
        {
            for (const auto& attachment : mailIt->attachments)
            {
                if (!attachment.isCollected)
                    return SocialResult::AttachmentError;
            }
        }
        
        mailList.erase(mailIt);
        
        LOG_INFO("MAIL_MGR", "Player " + std::to_string(playerId) + " deleted mail " + std::to_string(mailId));
        return SocialResult::Success;
    }
    
    // 收取附件
    SocialResult CollectAttachment(uint32_t playerId, uint32_t mailId, uint32_t attachmentId)
    {
        std::unique_lock<std::shared_mutex> lock(m_mailMutex);
        
        auto it = m_playerMails.find(playerId);
        if (it == m_playerMails.end())
            return SocialResult::NotFound;
        
        auto& mailList = it->second;
        auto mailIt = std::find_if(mailList.begin(), mailList.end(),
            [mailId](const MailInfo& mail) { return mail.mailId == mailId; });
        
        if (mailIt == mailList.end())
            return SocialResult::NotFound;
        
        // 查找附件
        auto& attachments = mailIt->attachments;
        auto attachIt = std::find_if(attachments.begin(), attachments.end(),
            [attachmentId](const MailAttachment& attachment) { return attachment.attachmentId == attachmentId; });
        
        if (attachIt == attachments.end())
            return SocialResult::NotFound;
        
        if (attachIt->isCollected)
            return SocialResult::AlreadyExists;
        
        // 收取附件
        auto player = GET_ONLINE_PLAYER(playerId);
        if (!player)
            return SocialResult::Offline;
        
        // 处理不同类型的附件
        if (attachIt->type == "item" && attachIt->itemId != 0)
        {
            // 检查背包空间
            if (!HasEnoughSpace(*player, attachIt->itemId, attachIt->quantity))
                return SocialResult::Failed;
            
            // 给予物品
            if (!GiveItem(*player, attachIt->itemId, attachIt->quantity))
                return SocialResult::Failed;
        }
        else if (attachIt->type == "money" && attachIt->money > 0)
        {
            // 给予金钱
            player->money += attachIt->money;
        }
        
        // 标记为已收取
        attachIt->isCollected = true;
        
        LOG_INFO("MAIL_MGR", "Player " + std::to_string(playerId) + " collected attachment " + std::to_string(attachmentId));
        return SocialResult::Success;
    }
    
    // 收取所有附件
    SocialResult CollectAllAttachments(uint32_t playerId, uint32_t mailId)
    {
        std::unique_lock<std::shared_mutex> lock(m_mailMutex);
        
        auto it = m_playerMails.find(playerId);
        if (it == m_playerMails.end())
            return SocialResult::NotFound;
        
        auto& mailList = it->second;
        auto mailIt = std::find_if(mailList.begin(), mailList.end(),
            [mailId](const MailInfo& mail) { return mail.mailId == mailId; });
        
        if (mailIt == mailList.end())
            return SocialResult::NotFound;
        
        auto player = GET_ONLINE_PLAYER(playerId);
        if (!player)
            return SocialResult::Offline;
        
        // 检查所有物品附件的背包空间
        uint32_t totalItems = 0;
        for (const auto& attachment : mailIt->attachments)
        {
            if (!attachment.isCollected && attachment.type == "item" && attachment.itemId != 0)
            {
                totalItems += attachment.quantity;
            }
        }
        
        if (totalItems > 0 && !HasEnoughSpaceForItems(*player, totalItems))
            return SocialResult::Failed;
        
        // 收取所有附件
        uint32_t collectedCount = 0;
        for (auto& attachment : mailIt->attachments)
        {
            if (attachment.isCollected)
                continue;
            
            if (attachment.type == "item" && attachment.itemId != 0)
            {
                if (GiveItem(*player, attachment.itemId, attachment.quantity))
                {
                    attachment.isCollected = true;
                    collectedCount++;
                }
            }
            else if (attachment.type == "money" && attachment.money > 0)
            {
                player->money += attachment.money;
                attachment.isCollected = true;
                collectedCount++;
            }
        }
        
        LOG_INFO("MAIL_MGR", "Player " + std::to_string(playerId) + " collected " + std::to_string(collectedCount) + " attachments from mail " + std::to_string(mailId));
        return collectedCount > 0 ? SocialResult::Success : SocialResult::Failed;
    }
    
    // 获取邮件列表
    std::vector<MailInfo> GetMailList(uint32_t playerId, MailType type = MailType::Personal, bool unreadOnly = false) const
    {
        std::shared_lock<std::shared_mutex> lock(m_mailMutex);
        
        auto it = m_playerMails.find(playerId);
        if (it == m_playerMails.end())
            return {};
        
        std::vector<MailInfo> result;
        for (const auto& mail : it->second)
        {
            if (mail.isDeleted)
                continue;
            
            if (type != MailType::Personal && mail.type != type)
                continue;
            
            if (unreadOnly && mail.status != MailStatus::Unread)
                continue;
            
            result.push_back(mail);
        }
        
        // 按时间倒序排列
        std::sort(result.begin(), result.end(),
            [](const MailInfo& a, const MailInfo& b) {
                return a.sendTime > b.sendTime;
            });
        
        return result;
    }
    
    // 获取邮件详情
    MailInfo* GetMail(uint32_t playerId, uint32_t mailId)
    {
        std::shared_lock<std::shared_mutex> lock(m_mailMutex);
        
        auto it = m_playerMails.find(playerId);
        if (it == m_playerMails.end())
            return nullptr;
        
        auto& mailList = it->second;
        auto mailIt = std::find_if(mailList.begin(), mailList.end(),
            [mailId](const MailInfo& mail) { return mail.mailId == mailId; });
        
        return (mailIt != mailList.end()) ? &(*mailIt) : nullptr;
    }
    
    // 获取未读邮件数量
    uint32_t GetUnreadMailCount(uint32_t playerId) const
    {
        std::shared_lock<std::shared_mutex> lock(m_mailMutex);
        
        auto it = m_playerMails.find(playerId);
        if (it == m_playerMails.end())
            return 0;
        
        uint32_t count = 0;
        for (const auto& mail : it->second)
        {
            if (!mail.isDeleted && mail.status == MailStatus::Unread)
                count++;
        }
        
        return count;
    }
    
    // 获取邮件总数
    uint32_t GetMailCount(uint32_t playerId) const
    {
        std::shared_lock<std::shared_mutex> lock(m_mailMutex);
        
        auto it = m_playerMails.find(playerId);
        if (it == m_playerMails.end())
            return 0;
        
        uint32_t count = 0;
        for (const auto& mail : it->second)
        {
            if (!mail.isDeleted)
                count++;
        }
        
        return count;
    }
    
    // 获取最大邮件数量
    uint32_t GetMaxMailCount(uint32_t playerId) const
    {
        // 可以根据玩家VIP等级等因素调整
        return 100;
    }
    
    // 清理过期邮件
    void CleanupExpiredMails()
    {
        std::unique_lock<std::shared_mutex> lock(m_mailMutex);
        
        uint32_t cleanedCount = 0;
        auto now = std::chrono::system_clock::now();
        
        for (auto& [playerId, mailList] : m_playerMails)
        {
            auto it = mailList.begin();
            while (it != mailList.end())
            {
                if (it->IsExpired() && !it->hasAttachment)
                {
                    it = mailList.erase(it);
                    cleanedCount++;
                }
                else
                {
                    ++it;
                }
            }
        }
        
        if (cleanedCount > 0)
        {
            LOG_INFO("MAIL_MGR", "Cleaned up " + std::to_string(cleanedCount) + " expired mails");
        }
    }
    
    // 发送系统邮件
    SocialResult SendSystemMail(uint32_t receiverId, const std::string& receiverName,
                               const std::string& subject, const std::string& content,
                               uint32_t itemId = 0, uint32_t quantity = 0, uint32_t money = 0)
    {
        if (itemId != 0 && quantity > 0)
        {
            return SendItemMail(0, "系统", receiverId, receiverName, subject, content, itemId, quantity, money);
        }
        else if (money > 0)
        {
            return SendItemMail(0, "系统", receiverId, receiverName, subject, content, 0, 0, money);
        }
        else
        {
            return SendMail(0, "系统", receiverId, receiverName, subject, content, MailType::System);
        }
    }
    
    // 群发邮件
    SocialResult SendBroadcastMail(const std::vector<uint32_t>& receiverIds,
                                  const std::string& subject, const std::string& content,
                                  MailType type = MailType::System)
    {
        uint32_t successCount = 0;
        
        for (uint32_t receiverId : receiverIds)
        {
            auto receiver = GET_ONLINE_PLAYER(receiverId);
            std::string receiverName = receiver ? receiver->name : "Unknown";
            
            SocialResult result = SendMail(0, "系统", receiverId, receiverName, subject, content, type);
            if (result == SocialResult::Success)
                successCount++;
        }
        
        LOG_INFO("MAIL_MGR", "Broadcast mail sent to " + std::to_string(successCount) + "/" + std::to_string(receiverIds.size()) + " players");
        return successCount > 0 ? SocialResult::Success : SocialResult::Failed;
    }

private:
    mutable std::shared_mutex m_mailMutex;
    
    std::atomic<uint32_t> m_nextMailId;
    std::atomic<uint32_t> m_nextAttachmentId;
    
    std::unordered_map<uint32_t, std::vector<MailInfo>> m_playerMails;
    std::unordered_map<uint32_t, std::vector<MailInfo>> m_sentMails;
    
    // 辅助方法
    bool PlayerExists(uint32_t playerId) const
    {
        // 这里应该查询数据库检查玩家是否存在
        // 暂时返回true
        return true;
    }
    
    bool HasEnoughSpace(const Player& player, uint32_t itemId, uint32_t quantity) const
    {
        // 这里应该检查玩家背包空间
        // 暂时返回true
        return true;
    }
    
    bool HasEnoughSpaceForItems(const Player& player, uint32_t totalItems) const
    {
        // 这里应该检查玩家背包空间
        // 暂时返回true
        return true;
    }
    
    bool GiveItem(Player& player, uint32_t itemId, uint32_t quantity) const
    {
        // 这里应该给予玩家物品
        // 暂时返回true
        return true;
    }
};

} // namespace sword2

#endif // MAIL_MANAGER_H
