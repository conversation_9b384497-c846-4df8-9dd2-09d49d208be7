//---------------------------------------------------------------------------
// Sword2 NPC System (c) 2024
//
// File:	NPCSystem.h
// Date:	2024.07.02
// Code:	AI Assistant
// Desc:	Comprehensive NPC and monster management system for Sword2
//---------------------------------------------------------------------------
#ifndef NPC_SYSTEM_H
#define NPC_SYSTEM_H

#include "ModernCpp.h"
#include "UnifiedLoggingSystem.h"
#include "PlayerSystem.h"
#include "GameDataSystem.h"
#include <string>
#include <unordered_map>
#include <vector>
#include <memory>
#include <chrono>
#include <atomic>
#include <mutex>
#include <functional>

namespace sword2 {

// NPC类型枚举
enum class NPCType : uint8_t
{
    Normal = 0,         // 普通NPC
    Merchant,           // 商人
    Guard,              // 守卫
    Teacher,            // 师父/导师
    QuestGiver,         // 任务发布者
    Banker,             // 钱庄老板
    Teleporter,         // 传送员
    Monster,            // 怪物
    Boss,               // BOSS
    Pet                 // 宠物
};

// NPC状态枚举
enum class NPCStatus : uint8_t
{
    Idle = 0,           // 空闲
    Talking,            // 对话中
    Fighting,           // 战斗中
    Moving,             // 移动中
    Dead,               // 死亡
    Respawning,         // 重生中
    Disabled            // 禁用
};

// NPC行为模式
enum class NPCBehavior : uint8_t
{
    Static = 0,         // 静态（不移动）
    Patrol,             // 巡逻
    Random,             // 随机移动
    Follow,             // 跟随
    Aggressive,         // 主动攻击
    Defensive,          // 被动防御
    Flee                // 逃跑
};

// NPC AI状态
enum class NPCAIState : uint8_t
{
    Passive = 0,        // 被动
    Alert,              // 警戒
    Combat,             // 战斗
    Pursuit,            // 追击
    Return,             // 返回
    Patrol              // 巡逻
};

// NPC属性结构
struct NPCAttributes
{
    // 基础属性
    uint32_t level = 1;             // 等级
    uint32_t maxLife = 100;         // 最大生命值
    uint32_t currentLife = 100;     // 当前生命值
    uint32_t maxMana = 100;         // 最大内力值
    uint32_t currentMana = 100;     // 当前内力值
    
    // 战斗属性
    uint32_t minAttack = 1;         // 最小攻击力
    uint32_t maxAttack = 1;         // 最大攻击力
    uint32_t defense = 0;           // 防御力
    uint32_t magicDefense = 0;      // 法术防御
    uint32_t hit = 100;             // 命中率
    uint32_t dodge = 0;             // 闪避率
    uint32_t criticalRate = 0;      // 暴击率
    
    // 移动属性
    uint32_t moveSpeed = 100;       // 移动速度
    uint32_t attackSpeed = 100;     // 攻击速度
    uint32_t viewRange = 300;       // 视野范围
    uint32_t attackRange = 100;     // 攻击范围
    uint32_t pursuitRange = 500;    // 追击范围
    
    // 抗性属性
    uint32_t fireResist = 0;        // 火抗
    uint32_t iceResist = 0;         // 冰抗
    uint32_t lightningResist = 0;   // 雷抗
    uint32_t poisonResist = 0;      // 毒抗
    
    NPCAttributes() = default;
    
    // 检查是否死亡
    bool IsDead() const
    {
        return currentLife == 0;
    }
    
    // 恢复生命值
    void RestoreLife(uint32_t amount)
    {
        currentLife = std::min(maxLife, currentLife + amount);
    }
    
    // 造成伤害
    uint32_t TakeDamage(uint32_t damage)
    {
        uint32_t actualDamage = std::min(damage, currentLife);
        currentLife -= actualDamage;
        return actualDamage;
    }
    
    // 计算攻击力
    uint32_t GetRandomAttack() const
    {
        if (minAttack >= maxAttack) return minAttack;
        return minAttack + rand() % (maxAttack - minAttack + 1);
    }
};

// NPC掉落物品
struct NPCDropItem
{
    uint32_t itemId = 0;            // 物品ID
    uint32_t minCount = 1;          // 最小数量
    uint32_t maxCount = 1;          // 最大数量
    uint32_t dropRate = 100;        // 掉落概率(0-10000)
    bool isRare = false;            // 是否稀有
    
    NPCDropItem() = default;
    NPCDropItem(uint32_t id, uint32_t minCnt, uint32_t maxCnt, uint32_t rate, bool rare = false)
        : itemId(id), minCount(minCnt), maxCount(maxCnt), dropRate(rate), isRare(rare) {}
    
    // 检查是否掉落
    bool ShouldDrop() const
    {
        return (rand() % 10000) < dropRate;
    }
    
    // 获取掉落数量
    uint32_t GetDropCount() const
    {
        if (minCount >= maxCount) return minCount;
        return minCount + rand() % (maxCount - minCount + 1);
    }
};

// NPC巡逻点
struct PatrolPoint
{
    int32_t x = 0;                  // X坐标
    int32_t y = 0;                  // Y坐标
    uint32_t waitTime = 0;          // 等待时间(毫秒)
    
    PatrolPoint() = default;
    PatrolPoint(int32_t posX, int32_t posY, uint32_t wait = 0)
        : x(posX), y(posY), waitTime(wait) {}
    
    // 计算到另一点的距离
    double DistanceTo(const PatrolPoint& other) const
    {
        int32_t dx = x - other.x;
        int32_t dy = y - other.y;
        return std::sqrt(dx * dx + dy * dy);
    }
};

// NPC商店物品
struct ShopItem
{
    uint32_t itemId = 0;            // 物品ID
    uint32_t price = 0;             // 价格
    uint32_t stock = 0;             // 库存(-1表示无限)
    bool isLimited = false;         // 是否限量
    std::chrono::system_clock::time_point restockTime; // 补货时间
    
    ShopItem() = default;
    ShopItem(uint32_t id, uint32_t cost, uint32_t quantity = -1, bool limited = false)
        : itemId(id), price(cost), stock(quantity), isLimited(limited) {}
    
    // 检查是否有库存
    bool HasStock() const
    {
        return stock > 0 || !isLimited;
    }
    
    // 购买物品
    bool Purchase(uint32_t quantity = 1)
    {
        if (!HasStock() || (isLimited && stock < quantity))
            return false;
        
        if (isLimited)
            stock -= quantity;
        
        return true;
    }
};

// NPC对话选项
struct DialogOption
{
    std::string text;               // 选项文本
    std::string functionName;       // 对应的脚本函数名
    std::function<bool()> condition; // 显示条件
    
    DialogOption() = default;
    DialogOption(const std::string& txt, const std::string& func, std::function<bool()> cond = nullptr)
        : text(txt), functionName(func), condition(cond) {}
    
    // 检查是否应该显示
    bool ShouldShow() const
    {
        return !condition || condition();
    }
};

// NPC数据结构
class NPC
{
public:
    // 基础信息
    uint32_t npcId = 0;             // NPC ID
    uint32_t templateId = 0;        // 模板ID
    std::string name;               // NPC名称
    std::string title;              // 称号
    std::string description;        // 描述
    NPCType type = NPCType::Normal; // NPC类型
    NPCStatus status = NPCStatus::Idle; // 当前状态
    NPCBehavior behavior = NPCBehavior::Static; // 行为模式
    NPCAIState aiState = NPCAIState::Passive; // AI状态
    
    // 位置信息
    uint32_t mapId = 0;             // 地图ID
    int32_t x = 0;                  // X坐标
    int32_t y = 0;                  // Y坐标
    uint32_t direction = 0;         // 朝向
    int32_t spawnX = 0;             // 出生点X
    int32_t spawnY = 0;             // 出生点Y
    
    // 属性信息
    NPCAttributes attributes;       // NPC属性
    
    // 脚本信息
    std::string scriptPath;         // 脚本路径
    std::unordered_map<std::string, std::string> scriptFunctions; // 脚本函数映射
    
    // 商店信息（如果是商人）
    std::vector<ShopItem> shopItems; // 商店物品
    uint32_t shopType = 0;          // 商店类型
    
    // 掉落信息（如果是怪物）
    std::vector<NPCDropItem> dropItems; // 掉落物品
    uint32_t experience = 0;        // 给予经验
    uint32_t money = 0;             // 给予金钱
    
    // 巡逻信息
    std::vector<PatrolPoint> patrolPoints; // 巡逻路径
    size_t currentPatrolIndex = 0;  // 当前巡逻点索引
    std::chrono::system_clock::time_point patrolWaitUntil; // 巡逻等待结束时间
    
    // 战斗信息
    uint32_t targetPlayerId = 0;    // 目标玩家ID
    std::chrono::system_clock::time_point lastAttackTime; // 最后攻击时间
    std::chrono::system_clock::time_point combatStartTime; // 战斗开始时间
    
    // 重生信息
    uint32_t respawnTime = 30;      // 重生时间(秒)
    std::chrono::system_clock::time_point deathTime; // 死亡时间
    bool canRespawn = true;         // 是否可以重生
    
    // 时间信息
    std::chrono::system_clock::time_point createTime; // 创建时间
    std::chrono::system_clock::time_point lastUpdateTime; // 最后更新时间
    
    NPC() = default;
    NPC(uint32_t id, uint32_t templateId, const std::string& npcName)
        : npcId(id), templateId(templateId), name(npcName)
    {
        createTime = lastUpdateTime = std::chrono::system_clock::now();
    }
    
    // 设置位置
    void SetPosition(uint32_t map, int32_t posX, int32_t posY, uint32_t dir = 0)
    {
        mapId = map;
        x = posX;
        y = posY;
        direction = dir;
    }
    
    // 设置出生点
    void SetSpawnPoint(int32_t spawnPosX, int32_t spawnPosY)
    {
        spawnX = spawnPosX;
        spawnY = spawnPosY;
    }
    
    // 移动到指定位置
    bool MoveTo(int32_t targetX, int32_t targetY)
    {
        if (status == NPCStatus::Dead || status == NPCStatus::Disabled)
            return false;
        
        x = targetX;
        y = targetY;
        status = NPCStatus::Moving;
        lastUpdateTime = std::chrono::system_clock::now();
        
        return true;
    }
    
    // 返回出生点
    void ReturnToSpawn()
    {
        MoveTo(spawnX, spawnY);
        aiState = NPCAIState::Passive;
        targetPlayerId = 0;
    }
    
    // 计算与指定位置的距离
    double DistanceTo(int32_t targetX, int32_t targetY) const
    {
        int32_t dx = x - targetX;
        int32_t dy = y - targetY;
        return std::sqrt(dx * dx + dy * dy);
    }
    
    // 计算与玩家的距离
    double DistanceToPlayer(const Player& player) const
    {
        if (player.position.mapId != mapId) return -1.0;
        return DistanceTo(player.position.x, player.position.y);
    }
    
    // 检查是否在攻击范围内
    bool IsInAttackRange(int32_t targetX, int32_t targetY) const
    {
        return DistanceTo(targetX, targetY) <= attributes.attackRange;
    }
    
    // 检查是否在视野范围内
    bool IsInViewRange(int32_t targetX, int32_t targetY) const
    {
        return DistanceTo(targetX, targetY) <= attributes.viewRange;
    }
    
    // 检查是否在追击范围内
    bool IsInPursuitRange(int32_t targetX, int32_t targetY) const
    {
        return DistanceTo(targetX, targetY) <= attributes.pursuitRange;
    }
    
    // 攻击目标
    uint32_t AttackTarget(Player& target)
    {
        if (!IsInAttackRange(target.position.x, target.position.y))
            return 0;
        
        uint32_t damage = attributes.GetRandomAttack();
        
        // 计算命中
        if (rand() % 100 >= attributes.hit)
            return 0; // 未命中
        
        // 计算闪避
        if (rand() % 100 < target.attributes.dodge)
            return 0; // 被闪避
        
        // 计算防御减免
        uint32_t defense = target.attributes.defense;
        if (damage > defense)
            damage -= defense;
        else
            damage = 1; // 最少造成1点伤害
        
        // 造成伤害
        uint32_t actualDamage = std::min(damage, target.attributes.currentLife);
        target.attributes.currentLife -= actualDamage;
        
        lastAttackTime = std::chrono::system_clock::now();
        
        return actualDamage;
    }
    
    // 死亡处理
    void Die()
    {
        status = NPCStatus::Dead;
        aiState = NPCAIState::Passive;
        targetPlayerId = 0;
        deathTime = std::chrono::system_clock::now();
        
        LOG_INFO("NPC", "NPC " + name + " (ID: " + std::to_string(npcId) + ") died");
    }
    
    // 重生处理
    void Respawn()
    {
        status = NPCStatus::Idle;
        aiState = NPCAIState::Passive;
        attributes.currentLife = attributes.maxLife;
        attributes.currentMana = attributes.maxMana;
        targetPlayerId = 0;
        
        // 返回出生点
        x = spawnX;
        y = spawnY;
        
        LOG_INFO("NPC", "NPC " + name + " (ID: " + std::to_string(npcId) + ") respawned");
    }
    
    // 检查是否可以重生
    bool CanRespawn() const
    {
        if (!canRespawn || status != NPCStatus::Dead)
            return false;
        
        auto now = std::chrono::system_clock::now();
        auto elapsed = std::chrono::duration_cast<std::chrono::seconds>(now - deathTime);
        
        return elapsed.count() >= respawnTime;
    }
    
    // 添加巡逻点
    void AddPatrolPoint(int32_t patrolX, int32_t patrolY, uint32_t waitTime = 0)
    {
        patrolPoints.emplace_back(patrolX, patrolY, waitTime);
    }
    
    // 获取下一个巡逻点
    const PatrolPoint* GetNextPatrolPoint()
    {
        if (patrolPoints.empty()) return nullptr;
        
        currentPatrolIndex = (currentPatrolIndex + 1) % patrolPoints.size();
        return &patrolPoints[currentPatrolIndex];
    }
    
    // 添加商店物品
    void AddShopItem(uint32_t itemId, uint32_t price, uint32_t stock = -1, bool limited = false)
    {
        shopItems.emplace_back(itemId, price, stock, limited);
    }
    
    // 添加掉落物品
    void AddDropItem(uint32_t itemId, uint32_t minCount, uint32_t maxCount, uint32_t dropRate, bool isRare = false)
    {
        dropItems.emplace_back(itemId, minCount, maxCount, dropRate, isRare);
    }
    
    // 获取掉落物品列表
    std::vector<std::pair<uint32_t, uint32_t>> GetDropItems() const
    {
        std::vector<std::pair<uint32_t, uint32_t>> drops;
        
        for (const auto& dropItem : dropItems)
        {
            if (dropItem.ShouldDrop())
            {
                uint32_t count = dropItem.GetDropCount();
                drops.emplace_back(dropItem.itemId, count);
            }
        }
        
        return drops;
    }
    
    // 设置脚本函数
    void SetScriptFunction(const std::string& event, const std::string& functionName)
    {
        scriptFunctions[event] = functionName;
    }
    
    // 获取脚本函数
    std::string GetScriptFunction(const std::string& event) const
    {
        auto it = scriptFunctions.find(event);
        return (it != scriptFunctions.end()) ? it->second : "";
    }
    
    // 检查是否是怪物
    bool IsMonster() const
    {
        return type == NPCType::Monster || type == NPCType::Boss;
    }
    
    // 检查是否是商人
    bool IsMerchant() const
    {
        return type == NPCType::Merchant;
    }
    
    // 检查是否可以对话
    bool CanTalk() const
    {
        return status != NPCStatus::Dead && status != NPCStatus::Disabled && 
               status != NPCStatus::Fighting;
    }
    
    // 检查是否可以攻击
    bool CanAttack() const
    {
        return status != NPCStatus::Dead && status != NPCStatus::Disabled && 
               !attributes.IsDead();
    }
    
    // 更新NPC状态
    void Update()
    {
        lastUpdateTime = std::chrono::system_clock::now();
        
        // 检查重生
        if (status == NPCStatus::Dead && CanRespawn())
        {
            Respawn();
        }
    }
    
    // 获取类型描述
    std::string GetTypeDescription() const
    {
        switch (type)
        {
        case NPCType::Normal: return "普通NPC";
        case NPCType::Merchant: return "商人";
        case NPCType::Guard: return "守卫";
        case NPCType::Teacher: return "师父";
        case NPCType::QuestGiver: return "任务发布者";
        case NPCType::Banker: return "钱庄老板";
        case NPCType::Teleporter: return "传送员";
        case NPCType::Monster: return "怪物";
        case NPCType::Boss: return "BOSS";
        case NPCType::Pet: return "宠物";
        default: return "未知";
        }
    }
    
    // 获取状态描述
    std::string GetStatusDescription() const
    {
        switch (status)
        {
        case NPCStatus::Idle: return "空闲";
        case NPCStatus::Talking: return "对话中";
        case NPCStatus::Fighting: return "战斗中";
        case NPCStatus::Moving: return "移动中";
        case NPCStatus::Dead: return "死亡";
        case NPCStatus::Respawning: return "重生中";
        case NPCStatus::Disabled: return "禁用";
        default: return "未知";
        }
    }
};

} // namespace sword2

#endif // NPC_SYSTEM_H
