#ifndef KMagicAttribH
#define KMagicAttribH

#define	INVALID_ATTRIB	-1

enum MAGIC_ATTRIB
{
	magic_skill_begin,						//0 
	magic_skill_cost_v,						//1 
	magic_skill_costtype_v,					//2 
	magic_skill_mintimepercast_v,			//3 
	magic_skill_misslenum_v,				//4 
	magic_skill_misslesform_v,				//5 
	magic_skill_param1_v,					//6 
	magic_skill_param2_v,					//7 
	magic_skill_attackradius,				//8 
	magic_skill_eventskilllevel,			//9 
	magic_skill_showevent,					//10
	magic_skill_appendskill,				//11
	magic_skill_reserve3,					//12
	magic_skill_end,						//13
	magic_missle_begin,						//14
	magic_missle_movekind_v,				//15
	magic_missle_speed_v,					//16
	magic_missle_lifetime_v,				//17
	magic_missle_height_v,					//18
	magic_missle_damagerange_v,				//19
	magic_missle_radius_v,					//20
	magic_missle_missrate,					//21
	magic_missle_hitcount,					//22
	magic_missle_reserve3,					//23
	magic_missle_reserve4,					//24
	magic_missle_reserve5,					//25
	magic_missle_end,						//26
	magic_item_begin,						//27
	magic_weapondamagemin_v,				//28
	magic_weapondamagemax_v,				//29
	magic_armordefense_v,					//30
	magic_durability_v,						//31
	magic_requirestr,						//32
	magic_requiredex,						//33
	magic_requirevit,						//34
	magic_requireeng,						//35
	magic_requirelevel,						//36
	magic_requireseries,					//37
	magic_requiresex,						//38
	magic_requiremenpai,					//39
	magic_weapondamageenhance_p,			//40
	magic_armordefenseenhance_p,			//41
	magic_requirementreduce_p,				//42
	magic_indestructible_b,					//43
	magic_married,							//44
	magic_dec_percasttime,					//45
	magic_require_translife,				//46
	magic_require_fortune_value,			//47
	magic_item_reserve2,					//48
	magic_item_reserve3,					//49
	magic_item_purple,						//50
	magic_add_damage_p,						//51
	magic_not_add_pkvalue_p,				//52
	magic_add_boss_damage,					//53
	magic_item_end,							//54
	magic_damage_begin,						//55
	magic_attackrating_v,					//56
	magic_attackrating_p,					//57
	magic_ignoredefense_p,					//58
	magic_magicdamage_v,					//59
	magic_seriesdamage_p,					//60
	magic_physicsdamage_v,					//61
	magic_colddamage_v,						//62
	magic_firedamage_v,						//63
	magic_lightingdamage_v,					//64
	magic_poisondamage_v,					//65
	magic_physicsenhance_p,					//66
	magic_steallife_p,						//67
	magic_stealmana_p,						//68
	magic_stealstamina_p,					//69
	magic_knockback_p,						//70
	magic_deadlystrike_p,					//71
	magic_fatallystrike_p,					//72
	magic_stun_p,							//73
	magic_randmove=75,						//75 
	magic_damage_reserve2,					//76 
	magic_damage_reserve3,					//77 
	magic_damage_reserve4,					//78 
	magic_damage_reserve5,					//79 
	magic_damage_reserve6,					//80 
	magic_damage_reserve7,					//81 
	magic_damage_reserve8,					//82 
	magic_damage_end,						//83 
	magic_normal_begin,						//84 
	magic_lifemax_v,						//85 
	magic_lifemax_p,						//86 
	magic_life_v,							//87 
	magic_lifereplenish_v,					//88 
	magic_manamax_v,						//89 
	magic_manamax_p,						//90 
	magic_mana_v,							//91 
	magic_manareplenish_v,					//92 
	magic_staminamax_v,						//93 
	magic_staminamax_p,						//94 
	magic_stamina_v,						//95 
	magic_staminareplenish_v,				//96 
	magic_strength_v,						//97 
	magic_dexterity_v,						//98 
	magic_vitality_v,						//99 
	magic_energy_v,							//100
	magic_poisonres_p,						//101
	magic_fireres_p,						//102
	magic_lightingres_p,					//103
	magic_physicsres_p,						//104
	magic_coldres_p,						//105
	magic_freezetimereduce_p,				//106
	magic_burntimereduce_p,					//107
	magic_poisontimereduce_p,				//108
	magic_poisondamagereduce_v,				//109
	magic_stuntimereduce_p,					//110
	magic_fastwalkrun_p,					//111
	magic_visionradius_p,					//112
	magic_fasthitrecover_v,					//113
	magic_allres_p,							//114
	magic_attackspeed_v,					//115
	magic_castspeed_v,						//116
	magic_meleedamagereturn_v,				//117
	magic_meleedamagereturn_p,				//118
	magic_rangedamagereturn_v,				//119
	magic_rangedamagereturn_p,				//120
	magic_addphysicsdamage_v,				//121
	magic_addfiredamage_v,					//122
	magic_addcolddamage_v,					//123
	magic_addlightingdamage_v,				//124
	magic_addpoisondamage_v,				//125
	magic_addphysicsdamage_p,				//126
	magic_slowmissle_b,						//127
	magic_changecamp_b,						//128
	magic_physicsarmor_v,					//129
	magic_coldarmor_v,						//130
	magic_firearmor_v,						//131
	magic_poisonarmor_v,					//132
	magic_lightingarmor_v,					//133
	magic_damage2addmana_p,					//134
	magic_lucky_v,							//135
	magic_steallifeenhance_p,				//136
	magic_stealmanaenhance_p,				//137
	magic_stealstaminaenhance_p,			//138
	magic_allskill_v,						//139
	magic_addphysicsmagic_v,				//140
	magic_addcoldmagic_v,					//141
	magic_addfiremagic_v,					//142
	magic_addlightingmagic_v,				//143
	magic_addpoisonmagic_v,					//144
	magic_knockbackenhance_p,				//145
	magic_deadlystrikeenhance_p,			//146
	magic_stunenhance_p,					//147
	magic_badstatustimereduce_v,			//148
	magic_manashield_p,						//149
	magic_adddefense_v,						//150
	magic_adddefense_p,						//151
	magic_fatallystrikeenhance_p,			//152
	magic_lifepotion_v,						//153
	magic_manapotion_v,						//154
	magic_physicsresmax_p,					//155
	magic_coldresmax_p,						//156
	magic_fireresmax_p,						//157
	magic_lightingresmax_p,					//158
	magic_poisonresmax_p,					//159
	magic_allresmax_p,						//160
	magic_coldenhance_p,					//161
	magic_fireenhance_p,					//162
	magic_lightingenhance_p,				//163
	magic_poisonenhance_p,					//164
	magic_magicenhance_p,					//165
	magic_attackratingenhance_v,			//166
	magic_attackratingenhance_p,			//167
	magic_metalskill_v,						//168
	magic_woodskill_v,						//169
	magic_waterskill_v,						//170
	magic_fireskill_v,						//171
	magic_earthskill_v,						//172
	magic_fatallystrikeres_p,				//173
	magic_dynamicmagicshield_v,				//174
	magic_staticmagicshield_p,				//175
	magic_expenhance_p,						//176
	magic_ignoreskill_p,					//177
	magic_poisondamagereturn_v,				//178
	magic_poisondamagereturn_p,				//179
	magic_returnskill_p,					//180
	magic_skill_mintimepercastonhorse_v,	//181
	magic_poison2decmana_p,					//182
	magic_hide,								//183
	magic_autodeathskill,					//184
	magic_clearnegativestate,				//185
	magic_returnres_p,						//186
	magic_autoattackskill,					//187
	magic_dec_percasttimehorse,				//188
	normal_reserve4,						//189
	magic_lifereplenish_p,					//190
	magic_frozen_action,					//191
	magic_normal_reserve5,					//192
	magic_sorbdamage_p,						//193
	magic_autorescueskill,					//194
	magic_autoreplyskill,					//195
	magic_anti_hitrecover,					//196
	magic_magicdamage_p,					//197
	magic_anti_poisonres_p,					//198
	magic_anti_fireres_p,					//199
	magic_anti_lightingres_p,				//200
	magic_anti_physicsres_p,				//201
	magic_anti_coldres_p,					//202
	magic_fastwalkrun_yan_p,				//203
	magic_anti_poisontimereduce_p,			//204
	magic_do_hurt_p,						//205
	magic_me2wooddamage_p,					//206
	magic_skill_desc,						//207
	magic_skill_collideevent,				//208
	magic_skill_vanishedevent,				//209
	magic_skill_startevent,					//210
	magic_skill_flyevent,					//211
	magic_skill_dohurt,						//212
	magic_skill_bymissle,					//213
	magic_candetonate1,						//214
	magic_candetonate2,						//215
	magic_candetonate3,						//216
	magic_anti_fireres_yan_p,				//217
	magic_anti_maxres_p,					//218
	magic_anti_do_stun_p,					//219
	magic_anti_stuntimereduce_p,			//220
	magic_anti_poisonres_yan_p,				//221
	magic_anti_coldres_yan_p,				//222
	magic_anti_do_hurt_p,					//223
	magic_anti_allres_yan_p,				//224
	magic_pk_punish_weaken,					//225
	magic_block_rate,						//226
	magic_enhancehit_rate,					//227
	magic_poisonres_yan_p,					//228
	magic_lightingres_yan_p,				//229
	magic_fireres_yan_p,					//230
	magic_physicsres_yan_p,					//231
	magic_coldres_yan_p,					//232
	magic_lifemax_yan_v,					//233
	magic_lifemax_yan_p,					//234
	magic_manamax_yan_v,					//235
	magic_manamax_yan_p,					//236
	magic_sorbdamage_yan_p,					//237
	magic_pk_punish_enhance,				//238
	magic_attackspeed_yan_v,				//239
	magic_castspeed_yan_v,					//240
	magic_allres_yan_p,						//241
	magic_me2earthdamage_p,					//242
	magic_skill_enhance,					//243
	magic_anti_physicsres_yan_p,			//244
	magic_fasthitrecover_yan_v,				//245
	magic_five_elements_enhance_v,			//246
	magic_five_elements_resist_v,			//247
	magic_anti_lightingres_yan_p,			//248
	magic_metal2medamage_p,					//249
	magic_reduceskillcd3,					//250
	magic_clearallcd,						//251
	magic_anti_enhancehit_rate,				//252
	magic_me2metaldamage_p,					//253
	magic_normal_reserve6,					//254
	magic_normal_reserve7,					//255
	magic_wood2medamage_p,					//256
	magic_me2waterdamage_p,					//257
	magic_water2medamage_p,					//258
	magic_me2firedamage_p,					//259
	magic_fire2medamage_p,					//260
	magic_do_stun_p,						//261
	magic_earth2medamage_p,					//262
	magic_normal_reserve8,					//263
	magic_enhancehiteffect_rate,			//264
	magic_meleedamagereturnmana_p,			//265
	magic_rangedamagereturnmana_p,			//266
	magic_reduceskillcd1,					//267
	magic_reduceskillcd2,					//268
	magic_anti_sorbdamage_yan_p,			//269
	magic_anti_block_rate,					//270
	magic_addblockrate,						//271
	magic_walkrunshadow,					//272
	magic_returnskill2enemy,				//273
	magic_manatoskill_enhance,				//274
	magic_melee_returnres_p,				//275
	magic_range_returnres_p,				//276
	magic_addskilldamage1, 					//277
	magic_addskilldamage2, 					//278
	magic_addskilldamage3, 					//279
	magic_addskilldamage4, 					//280
	magic_addskilldamage5, 					//281
	magic_addskilldamage6,					//282
	magic_addskilldamage7, 					//283
	magic_addskilldamage8, 					//284
	magic_addskilldamage9,					//285
	magic_missle_exp_begin,					//286
	magic_missle_range,						//287
	magic_missle_dmginterval,				//288
	magic_missle_zspeed,					//289
	magic_missle_ablility,					//290
	magic_missle_param,						//291
	magic_missle_wait,						//292
	magic_missle_fly,						//293
	magic_missle_collide,					//294
	magic_missle_vanish,					//295
	magic_missle_exp_rev1,					//296
	magic_missle_exp_rev2,					//297
	magic_missle_exp_rev3,					//298
	magic_missle_exp_rev4,					//299
	magic_missle_exp_rev5,					//300
	magic_missle_exp_end,					//301
	magic_ignorenegativestate_p,			//302
	magic_normal_end,						//303
};

extern const char MAGIC_ATTRIB_STRING[magic_normal_end + 1][100] ; 


#endif
