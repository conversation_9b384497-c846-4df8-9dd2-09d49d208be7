#ifndef _CCRC32_H
#define _CCRC32_H

#define CRC32_POLYNOMIAL 0x04C11DB7
#define CRC32BUFSZ 1024

class CCRC32
{
	public:
		void Initialize(void){
			memset(&this->ulTable, 0, sizeof(this->ulTable));
			for(int iCodes = 0; iCodes <= 0xFF; iCodes++)
			{
				this->ulTable[iCodes] = this->Reflect(iCodes, 8) << 24;
				for(int iPos = 0; iPos < 8; iPos++)
				{
					this->ulTable[iCodes] = (this->ulTable[iCodes] << 1) ^ (this->ulTable[iCodes] & (1 << 31) ? CRC32_POLYNOMIAL : 0);
				}
				this->ulTable[iCodes] = this->Reflect(this->ulTable[iCodes], 32);
			}
		};
		unsigned long FileCRC(const char *sFileName)
		{
			unsigned long ulCRC = 0xffffffff;
			FILE *fSource = NULL;
			unsigned char sBuf[CRC32BUFSZ];
			int iBytesRead = 0;
			if( (fSource = fopen(sFileName, "rb")) == NULL)
			{
				return 0xffffffff;
			}
			do
			{
				iBytesRead = fread(sBuf, sizeof(char), CRC32BUFSZ, fSource);
				this->PartialCRC(&ulCRC, sBuf, iBytesRead);
			}
			while(iBytesRead == CRC32BUFSZ);
			fclose(fSource);
			return(ulCRC ^ 0xffffffff);
		};
		unsigned long FullCRC(unsigned char *sData, unsigned long ulLength)
		{
			unsigned long ulCRC = 0xffffffff;
			this->PartialCRC(&ulCRC, sData, ulLength);
			return ulCRC ^ 0xffffffff;
		};
		void PartialCRC(unsigned long *ulInCRC, unsigned char *sData, unsigned long ulLength)
		{
			while(ulLength--)
			{
				*ulInCRC = (*ulInCRC >> 8) ^ this->ulTable[(*ulInCRC & 0xFF) ^ *sData++];
			}
		};

	private:
		unsigned long Reflect(unsigned long ulReflect, char cChar)
		{
			unsigned long ulValue = 0;
			for(int iPos = 1; iPos < (cChar + 1); iPos++)
			{
			if(ulReflect & 1) ulValue |= 1 << (cChar - iPos);
			ulReflect >>= 1;
			}
			return ulValue;
		};
		unsigned long ulTable[256];
};
#endif