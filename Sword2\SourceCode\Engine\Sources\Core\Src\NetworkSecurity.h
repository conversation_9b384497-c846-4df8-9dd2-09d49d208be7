//---------------------------------------------------------------------------
// Sword2 Network Security System (c) 2024
//
// File:	NetworkSecurity.h
// Date:	2024.07.02
// Code:	AI Assistant
// Desc:	Network security and anti-cheat protection system
//---------------------------------------------------------------------------
#ifndef NETWORK_SECURITY_H
#define NETWORK_SECURITY_H

#include <windows.h>
#include <map>
#include <vector>

// 网络安全级别
enum NETWORK_SECURITY_LEVEL
{
    NSL_LOW = 0,                // 低安全级别
    NSL_MEDIUM,                 // 中等安全级别
    NSL_HIGH,                   // 高安全级别
    NSL_PARANOID                // 偏执安全级别
};

// 数据包验证结果
enum PACKET_VALIDATION_RESULT
{
    PVR_VALID = 0,              // 有效数据包
    PVR_INVALID_SIZE,           // 无效大小
    PVR_INVALID_HEADER,         // 无效头部
    PVR_INVALID_CRC,            // CRC校验失败
    PVR_INVALID_SEQUENCE,       // 序列号错误
    PVR_INVALID_TIMESTAMP,      // 时间戳错误
    PVR_RATE_LIMITED,           // 频率限制
    PVR_BLACKLISTED,            // 黑名单
    PVR_SUSPICIOUS_PATTERN      // 可疑模式
};

// 客户端安全状态
struct ClientSecurityState
{
    DWORD dwClientID;           // 客户端ID
    DWORD dwLastPacketTime;     // 最后数据包时间
    DWORD dwPacketCount;        // 数据包计数
    DWORD dwInvalidPackets;     // 无效数据包数
    DWORD dwSuspiciousActivity; // 可疑活动计数
    BOOL bBlacklisted;          // 是否被拉黑
    DWORD dwSecurityLevel;      // 安全级别
    DWORD dwLastSequenceNumber; // 最后序列号
};

// 数据包加密器
class CPacketEncryption
{
public:
    CPacketEncryption();
    ~CPacketEncryption();

    BOOL Initialize();
    void Cleanup();

    // 密钥管理
    BOOL GenerateSessionKey(DWORD dwClientID);
    BOOL ExchangeKeys(DWORD dwClientID, const BYTE* pPublicKey, DWORD dwKeySize);
    void InvalidateSessionKey(DWORD dwClientID);

    // 数据加密/解密
    BOOL EncryptPacket(DWORD dwClientID, const BYTE* pPlainData, DWORD dwDataSize,
                      BYTE* pEncryptedData, DWORD* pdwEncryptedSize);
    BOOL DecryptPacket(DWORD dwClientID, const BYTE* pEncryptedData, DWORD dwDataSize,
                      BYTE* pPlainData, DWORD* pdwPlainSize);

    // 完整性保护
    BOOL SignPacket(DWORD dwClientID, const BYTE* pData, DWORD dwDataSize,
                   BYTE* pSignature, DWORD* pdwSignatureSize);
    BOOL VerifyPacket(DWORD dwClientID, const BYTE* pData, DWORD dwDataSize,
                     const BYTE* pSignature, DWORD dwSignatureSize);

private:
    struct SessionKey
    {
        BYTE abyKey[32];        // 256位密钥
        BYTE abyIV[16];         // 128位初始向量
        DWORD dwKeyGenTime;     // 密钥生成时间
        BOOL bValid;            // 密钥是否有效
    };

    std::map<DWORD, SessionKey> m_SessionKeys;
    CRITICAL_SECTION m_CriticalSection;
    BOOL m_bInitialized;

    void GenerateRandomBytes(BYTE* pBuffer, DWORD dwSize);
    BOOL PerformAESEncryption(const BYTE* pKey, const BYTE* pIV,
                             const BYTE* pInput, DWORD dwInputSize,
                             BYTE* pOutput, DWORD* pdwOutputSize);
    BOOL PerformAESDecryption(const BYTE* pKey, const BYTE* pIV,
                             const BYTE* pInput, DWORD dwInputSize,
                             BYTE* pOutput, DWORD* pdwOutputSize);
};

// 网络入侵检测系统
class CNetworkIntrusionDetection
{
public:
    CNetworkIntrusionDetection();
    ~CNetworkIntrusionDetection();

    BOOL Initialize();
    void Cleanup();

    // 数据包分析
    PACKET_VALIDATION_RESULT AnalyzePacket(DWORD dwClientID, const BYTE* pPacket, DWORD dwPacketSize);
    void UpdateClientState(DWORD dwClientID, PACKET_VALIDATION_RESULT result);

    // 威胁检测
    BOOL DetectDDoSAttack(DWORD dwClientID);
    BOOL DetectPacketFlooding(DWORD dwClientID);
    BOOL DetectSuspiciousPattern(const BYTE* pPacket, DWORD dwPacketSize);
    BOOL DetectReplayAttack(DWORD dwClientID, DWORD dwSequenceNumber);

    // 防护措施
    void BlacklistClient(DWORD dwClientID, DWORD dwDuration = 3600); // 默认1小时
    void RateLimitClient(DWORD dwClientID, DWORD dwMaxPacketsPerSecond);
    void DisconnectClient(DWORD dwClientID, const char* pReason);

    // 统计和报告
    void GetSecurityStats(DWORD* pdwTotalPackets, DWORD* pdwInvalidPackets,
                         DWORD* pdwBlacklistedClients, DWORD* pdwActiveThreats);
    void GenerateSecurityReport();

private:
    std::map<DWORD, ClientSecurityState> m_ClientStates;
    std::vector<DWORD> m_BlacklistedIPs;
    NETWORK_SECURITY_LEVEL m_SecurityLevel;
    CRITICAL_SECTION m_CriticalSection;

    // 检测参数
    DWORD m_dwMaxPacketsPerSecond;
    DWORD m_dwMaxInvalidPackets;
    DWORD m_dwSuspiciousThreshold;
    DWORD m_dwBlacklistDuration;

    BOOL IsClientBlacklisted(DWORD dwClientID);
    BOOL IsRateLimited(DWORD dwClientID);
    void UpdatePacketStatistics(DWORD dwClientID);
    BOOL AnalyzePacketPattern(const BYTE* pPacket, DWORD dwPacketSize);
};

// 反外挂系统
class CAntiCheatSystem
{
public:
    CAntiCheatSystem();
    ~CAntiCheatSystem();

    BOOL Initialize();
    void Cleanup();

    // 客户端验证
    BOOL ValidateClientIntegrity(DWORD dwClientID);
    BOOL CheckClientVersion(DWORD dwClientID, const char* pVersion);
    BOOL VerifyClientFiles(DWORD dwClientID, const char* pFileHashes);

    // 行为分析
    BOOL AnalyzePlayerBehavior(DWORD dwClientID, const BYTE* pActionData, DWORD dwDataSize);
    BOOL DetectSpeedHack(DWORD dwClientID, float fMovementSpeed);
    BOOL DetectTeleportHack(DWORD dwClientID, float fX, float fY, float fZ);
    BOOL DetectItemDuplication(DWORD dwClientID, DWORD dwItemID, DWORD dwCount);

    // 内存保护
    BOOL EnableMemoryProtection();
    BOOL CheckMemoryIntegrity();
    BOOL DetectMemoryModification();

    // 进程保护
    BOOL EnableProcessProtection();
    BOOL DetectInjectedDLLs();
    BOOL DetectDebugger();
    BOOL DetectVirtualMachine();

    // 处罚系统
    void IssueWarning(DWORD dwClientID, const char* pReason);
    void TemporaryBan(DWORD dwClientID, DWORD dwDuration, const char* pReason);
    void PermanentBan(DWORD dwClientID, const char* pReason);

private:
    struct CheatDetectionState
    {
        DWORD dwClientID;
        DWORD dwWarningCount;
        DWORD dwLastViolationTime;
        float fSuspicionLevel;
        BOOL bUnderInvestigation;
    };

    std::map<DWORD, CheatDetectionState> m_DetectionStates;
    CRITICAL_SECTION m_CriticalSection;
    BOOL m_bMemoryProtectionEnabled;
    BOOL m_bProcessProtectionEnabled;

    // 检测阈值
    float m_fMaxMovementSpeed;
    float m_fMaxTeleportDistance;
    DWORD m_dwMaxItemsPerSecond;
    float m_fSuspicionThreshold;

    void UpdateSuspicionLevel(DWORD dwClientID, float fIncrease);
    BOOL IsPlayerSuspicious(DWORD dwClientID);
    void LogSecurityViolation(DWORD dwClientID, const char* pViolationType, const char* pDetails);
};

// 网络安全管理器
class CNetworkSecurityManager
{
public:
    CNetworkSecurityManager();
    ~CNetworkSecurityManager();

    BOOL Initialize();
    void Cleanup();

    // 安全级别管理
    void SetSecurityLevel(NETWORK_SECURITY_LEVEL level);
    NETWORK_SECURITY_LEVEL GetSecurityLevel() const { return m_SecurityLevel; }

    // 数据包处理
    PACKET_VALIDATION_RESULT ValidateIncomingPacket(DWORD dwClientID, const BYTE* pPacket, DWORD dwPacketSize);
    BOOL ProcessSecurePacket(DWORD dwClientID, const BYTE* pPacket, DWORD dwPacketSize);
    BOOL SendSecurePacket(DWORD dwClientID, const BYTE* pData, DWORD dwDataSize);

    // 客户端管理
    BOOL RegisterClient(DWORD dwClientID, const char* pClientInfo);
    void UnregisterClient(DWORD dwClientID);
    BOOL IsClientTrusted(DWORD dwClientID);

    // 安全事件处理
    void OnSecurityViolation(DWORD dwClientID, PACKET_VALIDATION_RESULT violation);
    void OnSuspiciousActivity(DWORD dwClientID, const char* pActivity);
    void OnCheatDetected(DWORD dwClientID, const char* pCheatType);

    // 统计和监控
    void GetSecurityStatistics(DWORD* pdwTotalClients, DWORD* pdwTrustedClients,
                              DWORD* pdwBlacklistedClients, DWORD* pdwSecurityViolations);
    void EnableSecurityLogging(BOOL bEnable) { m_bSecurityLogging = bEnable; }

private:
    CPacketEncryption m_PacketEncryption;
    CNetworkIntrusionDetection m_IntrusionDetection;
    CAntiCheatSystem m_AntiCheatSystem;

    NETWORK_SECURITY_LEVEL m_SecurityLevel;
    BOOL m_bSecurityLogging;
    BOOL m_bInitialized;

    void LogSecurityEvent(const char* pEvent, DWORD dwClientID = 0);
    void UpdateSecurityMetrics();
};

// 全局网络安全管理器
extern CNetworkSecurityManager g_NetworkSecurityManager;

// 安全宏定义
#define VALIDATE_PACKET(clientId, packet, size) \
    g_NetworkSecurityManager.ValidateIncomingPacket(clientId, packet, size)

#define SEND_SECURE_PACKET(clientId, data, size) \
    g_NetworkSecurityManager.SendSecurePacket(clientId, data, size)

#define LOG_SECURITY_VIOLATION(clientId, violation) \
    g_NetworkSecurityManager.OnSecurityViolation(clientId, violation)

#define CHECK_CLIENT_TRUST(clientId) \
    g_NetworkSecurityManager.IsClientTrusted(clientId)

// 安全常量
#define MAX_PACKET_SIZE             8192
#define MAX_PACKETS_PER_SECOND      100
#define MAX_INVALID_PACKETS         10
#define BLACKLIST_DURATION          3600
#define SESSION_KEY_LIFETIME        1800

#endif // NETWORK_SECURITY_H
