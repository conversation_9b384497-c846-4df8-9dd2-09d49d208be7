// stdafx.h : include file for standard system include files,
//  or project specific include files that are used frequently, but
//      are changed infrequently
//

#if !defined(AFX_STDAFX_H__6022AFC6_950E_43E7_9E33_2B9D057C085C__INCLUDED_)
#define AFX_STDAFX_H__6022AFC6_950E_43E7_9E33_2B9D057C085C__INCLUDED_

#if _MSC_VER > 1000
#pragma once
#endif // _MSC_VER > 1000

#define WIN32_LEAN_AND_MEAN

// TODO: reference additional headers your program requires here
#include <iostream>
#include <windows.h>
//{{AFX_INSERT_LOCATION}}
// Microsoft Visual C++ will insert additional declarations immediately before the previous line.

#endif // !defined(AFX_STDAFX_H__6022AFC6_950E_43E7_9E33_2B9D057C085C__INCLUDED_)
