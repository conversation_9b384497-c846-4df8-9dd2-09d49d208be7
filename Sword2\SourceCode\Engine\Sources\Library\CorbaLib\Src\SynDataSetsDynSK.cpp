// This file is generated by omniidl (C++ backend)- omniORB_3_0. Do not edit.

#include "SynDataSets.h"
#include <omniORB3/tcDescriptor.h>

static const char* _0RL_library_version = omniORB_3_0;

static CORBA::TypeCode_ptr _0RL_tc_DataSeq = CORBA::TypeCode::PR_alias_tc("IDL:DataSeq:1.0", "DataSeq", CORBA::TypeCode::PR_sequence_tc(0, CORBA::TypeCode::PR_any_tc()));

const CORBA::TypeCode_ptr _tc_DataSeq = _0RL_tc_DataSeq;

const CORBA::TypeCode_ptr _tc_SynDataSets = CORBA::TypeCode::PR_interface_tc("IDL:SynDataSets:1.0", "SynDataSets");

#ifndef __0RL_tcParser_buildDesc_s0_cany__
#define __0RL_tcParser_buildDesc_s0_cany__
static void
_0RL_tcParser_setElementCount_s0_cany(tcSequenceDesc* _desc, CORBA::ULong _len)
{
  ((_CORBA_Unbounded_Sequence< CORBA::Any> *)_desc->opq_seq)->length(_len);
}

static CORBA::ULong
_0RL_tcParser_getElementCount_s0_cany(tcSequenceDesc* _desc)
{
  return ((_CORBA_Unbounded_Sequence< CORBA::Any> *)_desc->opq_seq)->length();
}

static CORBA::Boolean
_0RL_tcParser_getElementDesc_s0_cany(tcSequenceDesc* _desc, CORBA::ULong _index, tcDescriptor& _newdesc, _CORBA_ULong& _contiguous)
{
  _0RL_buildDesc_cany(_newdesc, (*((_CORBA_Unbounded_Sequence< CORBA::Any> *)_desc->opq_seq))[_index]);
  
  return 1;
}

static void
_0RL_buildDesc_s0_cany(tcDescriptor &_desc, const _CORBA_Unbounded_Sequence< CORBA::Any> & _data)
{
  _desc.p_sequence.opq_seq = (void*) &_data;
  _desc.p_sequence.setElementCount =
    _0RL_tcParser_setElementCount_s0_cany;
  _desc.p_sequence.getElementCount =
    _0RL_tcParser_getElementCount_s0_cany;
  _desc.p_sequence.getElementDesc =
    _0RL_tcParser_getElementDesc_s0_cany;
  }
#endif

void operator <<= (CORBA::Any& _a, const DataSeq& _s)
{
  tcDescriptor tcdesc;
  _0RL_buildDesc_s0_cany(tcdesc, _s);
  _a.PR_packFrom(_tc_DataSeq, &tcdesc);
}

void _0RL_seq_delete_DataSeq(void* _data)
{
  delete (DataSeq*)_data;
}

CORBA::Boolean operator >>= (const CORBA::Any& _a, DataSeq*& _s_out)
{
  return _a >>= (const DataSeq*&) _s_out;
}

CORBA::Boolean operator >>= (const CORBA::Any& _a, const DataSeq*& _s_out)
{
  _s_out = 0;
  DataSeq* stmp = (DataSeq*) _a.PR_getCachedData();
  if( stmp == 0 ) {
    tcDescriptor tcdesc;
    stmp = new DataSeq;
    _0RL_buildDesc_s0_cany(tcdesc, *stmp);
    if( _a.PR_unpackTo(_tc_DataSeq, &tcdesc)) {
      ((CORBA::Any*)&_a)->PR_setCachedData((void*)stmp, _0RL_seq_delete_DataSeq);
      _s_out = stmp;
      return 1;
    } else {
      delete (DataSeq *)stmp;
      return 0;
    }
  } else {
    CORBA::TypeCode_var tctmp = _a.type();
    if( tctmp->equivalent(_tc_DataSeq) ) {
      _s_out = stmp;
      return 1;
    } else {
      return 0;
    }
  }
}

static void
_0RL_tcParser_setObjectPtr_SynDataSets(tcObjrefDesc *_desc, CORBA::Object_ptr _ptr)
{
  SynDataSets_ptr _p = SynDataSets::_narrow(_ptr);
  SynDataSets_ptr* pp = (SynDataSets_ptr*)_desc->opq_objref;
  if (_desc->opq_release && !CORBA::is_nil(*pp)) CORBA::release(*pp);
  *pp = _p;
  CORBA::release(_ptr);
}

static CORBA::Object_ptr
_0RL_tcParser_getObjectPtr_SynDataSets(tcObjrefDesc *_desc)
{
  return (CORBA::Object_ptr) *((SynDataSets_ptr*)_desc->opq_objref);
}

void _0RL_buildDesc_cSynDataSets(tcDescriptor& _desc, const _CORBA_ObjRef_tcDesc_arg< _objref_SynDataSets, SynDataSets_Helper> & _d)
{
  _desc.p_objref.opq_objref = (void*) &_d._data;
  _desc.p_objref.opq_release = _d._rel;
  _desc.p_objref.setObjectPtr = _0RL_tcParser_setObjectPtr_SynDataSets;
  _desc.p_objref.getObjectPtr = _0RL_tcParser_getObjectPtr_SynDataSets;
}

void _0RL_delete_SynDataSets(void* _data) {
  CORBA::release((SynDataSets_ptr) _data);
}

void operator<<=(CORBA::Any& _a, SynDataSets_ptr _s) {
  tcDescriptor tcd;
  _CORBA_ObjRef_tcDesc_arg< _objref_SynDataSets, SynDataSets_Helper>  tmp(_s,0);
  _0RL_buildDesc_cSynDataSets(tcd, tmp);
  _a.PR_packFrom(_tc_SynDataSets, &tcd);
}

void operator<<=(CORBA::Any& _a, SynDataSets_ptr* _sp) {
  _a <<= *_sp;
  CORBA::release(*_sp);
  *_sp = SynDataSets::_nil();
}

CORBA::Boolean operator>>=(const CORBA::Any& _a, SynDataSets_ptr& _s) {
  SynDataSets_ptr sp = (SynDataSets_ptr) _a.PR_getCachedData();
  if (sp == 0) {
    tcDescriptor tcd;
    SynDataSets_var tmp;
    _0RL_buildDesc_cSynDataSets(tcd, tmp);
    if( _a.PR_unpackTo(_tc_SynDataSets, &tcd) ) {
      if (!omniORB::omniORB_27_CompatibleAnyExtraction) {
        ((CORBA::Any*)&_a)->PR_setCachedData((void*)(SynDataSets_ptr)tmp,_0RL_delete_SynDataSets);
      }
      _s = tmp._retn();
      return 1;
    } else {
      _s = SynDataSets::_nil(); return 0;
    }
  }
  else {
    CORBA::TypeCode_var tc = _a.type();
    if (tc->equivalent(_tc_SynDataSets)) {
    _s = sp; return 1;
    }
    else {
    _s = SynDataSets::_nil(); return 0;
    }
  }
}

