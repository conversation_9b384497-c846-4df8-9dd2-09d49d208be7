//---------------------------------------------------------------------------
// Sword2 Business Monitoring System (c) 2024
//
// File:	BusinessMonitor.h
// Date:	2024.07.02
// Code:	AI Assistant
// Desc:	Game business metrics monitoring like online users, transactions, etc.
//---------------------------------------------------------------------------
#ifndef BUSINESS_MONITOR_H
#define BUSINESS_MONITOR_H

#include "ModernCpp.h"
#include "UnifiedLoggingSystem.h"
#include <chrono>
#include <atomic>
#include <unordered_map>
#include <vector>
#include <mutex>

namespace sword2 {

// 业务指标类型
enum class BusinessMetricType : uint32_t
{
    // 用户相关指标
    OnlineUsers = 0,            // 在线用户数
    NewRegistrations,           // 新注册用户数
    UserLogins,                 // 用户登录次数
    UserLogouts,                // 用户登出次数
    ActiveUsers,                // 活跃用户数
    
    // 游戏业务指标
    GameSessions,               // 游戏会话数
    AverageSessionDuration,     // 平均会话时长
    LevelUps,                   // 升级次数
    ItemTransactions,           // 物品交易次数
    SkillUsage,                 // 技能使用次数
    
    // 经济指标
    VirtualCurrencyEarned,      // 虚拟货币获得
    VirtualCurrencySpent,       // 虚拟货币消费
    ItemsSold,                  // 物品销售数量
    ItemsBought,                // 物品购买数量
    AuctionTransactions,        // 拍卖交易数
    
    // 社交指标
    ChatMessages,               // 聊天消息数
    FriendRequests,             // 好友请求数
    GuildActivities,            // 公会活动数
    TeamFormations,             // 组队次数
    
    // 技术指标
    DatabaseQueries,            // 数据库查询数
    CacheHitRate,               // 缓存命中率
    APIRequests,                // API请求数
    ErrorRate,                  // 错误率
    ResponseTime,               // 响应时间
    
    // 安全指标
    LoginAttempts,              // 登录尝试次数
    FailedLogins,               // 登录失败次数
    SuspiciousActivities,       // 可疑活动数
    BannedUsers,                // 被封用户数
    
    Custom                      // 自定义指标
};

// 业务事件
struct BusinessEvent
{
    BusinessMetricType type;
    double value;
    std::chrono::system_clock::time_point timestamp;
    std::string userId;
    std::string sessionId;
    std::unordered_map<std::string, std::string> properties;
    
    BusinessEvent(BusinessMetricType t, double v, const std::string& user = "", const std::string& session = "")
        : type(t), value(v), timestamp(std::chrono::system_clock::now()), userId(user), sessionId(session) {}
};

// 业务指标统计
struct BusinessMetricStats
{
    double totalValue = 0.0;
    double averageValue = 0.0;
    double minValue = std::numeric_limits<double>::max();
    double maxValue = std::numeric_limits<double>::lowest();
    size_t eventCount = 0;
    std::chrono::system_clock::time_point firstEvent;
    std::chrono::system_clock::time_point lastEvent;
    
    void Update(const BusinessEvent& event)
    {
        totalValue += event.value;
        eventCount++;
        averageValue = totalValue / eventCount;
        minValue = std::min(minValue, event.value);
        maxValue = std::max(maxValue, event.value);
        
        if (eventCount == 1)
        {
            firstEvent = event.timestamp;
        }
        lastEvent = event.timestamp;
    }
    
    void Reset()
    {
        totalValue = 0.0;
        averageValue = 0.0;
        minValue = std::numeric_limits<double>::max();
        maxValue = std::numeric_limits<double>::lowest();
        eventCount = 0;
    }
};

// 用户会话管理器
class UserSessionManager
{
public:
    struct UserSession
    {
        std::string userId;
        std::string sessionId;
        std::chrono::system_clock::time_point startTime;
        std::chrono::system_clock::time_point lastActivity;
        std::string ipAddress;
        std::unordered_map<std::string, std::string> properties;
        bool isActive;
        
        UserSession(const std::string& user, const std::string& session, const std::string& ip = "")
            : userId(user), sessionId(session), ipAddress(ip), isActive(true)
        {
            startTime = lastActivity = std::chrono::system_clock::now();
        }
        
        std::chrono::seconds GetDuration() const
        {
            auto endTime = isActive ? std::chrono::system_clock::now() : lastActivity;
            return std::chrono::duration_cast<std::chrono::seconds>(endTime - startTime);
        }
    };
    
    // 开始用户会话
    std::string StartSession(const std::string& userId, const std::string& ipAddress = "")
    {
        std::lock_guard<std::mutex> lock(m_mutex);
        
        std::string sessionId = GenerateSessionId();
        m_activeSessions[sessionId] = std::make_unique<UserSession>(userId, sessionId, ipAddress);
        m_userSessions[userId].insert(sessionId);
        
        return sessionId;
    }
    
    // 结束用户会话
    void EndSession(const std::string& sessionId)
    {
        std::lock_guard<std::mutex> lock(m_mutex);
        
        auto it = m_activeSessions.find(sessionId);
        if (it != m_activeSessions.end())
        {
            it->second->isActive = false;
            it->second->lastActivity = std::chrono::system_clock::now();
            
            // 移动到历史会话
            m_historicalSessions.push_back(std::move(it->second));
            m_activeSessions.erase(it);
            
            // 限制历史会话数量
            if (m_historicalSessions.size() > MAX_HISTORICAL_SESSIONS)
            {
                m_historicalSessions.erase(m_historicalSessions.begin());
            }
        }
    }
    
    // 更新会话活动时间
    void UpdateSessionActivity(const std::string& sessionId)
    {
        std::lock_guard<std::mutex> lock(m_mutex);
        
        auto it = m_activeSessions.find(sessionId);
        if (it != m_activeSessions.end())
        {
            it->second->lastActivity = std::chrono::system_clock::now();
        }
    }
    
    // 获取活跃会话数
    size_t GetActiveSessionCount() const
    {
        std::lock_guard<std::mutex> lock(m_mutex);
        return m_activeSessions.size();
    }
    
    // 获取在线用户数
    size_t GetOnlineUserCount() const
    {
        std::lock_guard<std::mutex> lock(m_mutex);
        std::unordered_set<std::string> uniqueUsers;
        for (const auto& session : m_activeSessions)
        {
            uniqueUsers.insert(session.second->userId);
        }
        return uniqueUsers.size();
    }
    
    // 获取用户的活跃会话
    std::vector<std::string> GetUserActiveSessions(const std::string& userId) const
    {
        std::lock_guard<std::mutex> lock(m_mutex);
        std::vector<std::string> sessions;
        
        auto it = m_userSessions.find(userId);
        if (it != m_userSessions.end())
        {
            for (const auto& sessionId : it->second)
            {
                if (m_activeSessions.find(sessionId) != m_activeSessions.end())
                {
                    sessions.push_back(sessionId);
                }
            }
        }
        
        return sessions;
    }
    
    // 清理超时会话
    void CleanupTimeoutSessions(std::chrono::minutes timeout = std::chrono::minutes(30))
    {
        std::lock_guard<std::mutex> lock(m_mutex);
        auto now = std::chrono::system_clock::now();
        
        auto it = m_activeSessions.begin();
        while (it != m_activeSessions.end())
        {
            auto timeSinceLastActivity = std::chrono::duration_cast<std::chrono::minutes>(
                now - it->second->lastActivity);
            
            if (timeSinceLastActivity > timeout)
            {
                it->second->isActive = false;
                it->second->lastActivity = now;
                m_historicalSessions.push_back(std::move(it->second));
                it = m_activeSessions.erase(it);
            }
            else
            {
                ++it;
            }
        }
    }

private:
    mutable std::mutex m_mutex;
    std::unordered_map<std::string, std::unique_ptr<UserSession>> m_activeSessions;
    std::unordered_map<std::string, std::unordered_set<std::string>> m_userSessions;
    std::vector<std::unique_ptr<UserSession>> m_historicalSessions;
    
    static constexpr size_t MAX_HISTORICAL_SESSIONS = 10000;
    
    std::string GenerateSessionId()
    {
        static std::atomic<uint64_t> counter{0};
        auto now = std::chrono::system_clock::now().time_since_epoch().count();
        return std::to_string(now) + "_" + std::to_string(counter++);
    }
};

// 业务监控管理器
class BusinessMonitor : public Singleton<BusinessMonitor>
{
public:
    BusinessMonitor() : m_running(false) {}
    
    ~BusinessMonitor()
    {
        Stop();
    }
    
    bool Start()
    {
        if (m_running) return true;
        
        m_running = true;
        m_cleanupThread = std::thread(&BusinessMonitor::CleanupLoop, this);
        
        LOG_INFO("BUSINESS", "Business monitor started");
        return true;
    }
    
    void Stop()
    {
        if (!m_running) return;
        
        m_running = false;
        if (m_cleanupThread.joinable())
        {
            m_cleanupThread.join();
        }
        
        LOG_INFO("BUSINESS", "Business monitor stopped");
    }
    
    // 记录业务事件
    void RecordEvent(BusinessMetricType type, double value = 1.0, 
                    const std::string& userId = "", const std::string& sessionId = "")
    {
        BusinessEvent event(type, value, userId, sessionId);
        
        {
            std::lock_guard<std::mutex> lock(m_mutex);
            m_recentEvents.push_back(event);
            m_statistics[type].Update(event);
            
            // 限制最近事件数量
            if (m_recentEvents.size() > MAX_RECENT_EVENTS)
            {
                m_recentEvents.erase(m_recentEvents.begin());
            }
        }
        
        // 记录日志
        LogBusinessEvent(event);
    }
    
    // 用户会话管理
    std::string StartUserSession(const std::string& userId, const std::string& ipAddress = "")
    {
        std::string sessionId = m_sessionManager.StartSession(userId, ipAddress);
        RecordEvent(BusinessMetricType::UserLogins, 1.0, userId, sessionId);
        return sessionId;
    }
    
    void EndUserSession(const std::string& sessionId)
    {
        m_sessionManager.EndSession(sessionId);
        RecordEvent(BusinessMetricType::UserLogouts, 1.0, "", sessionId);
    }
    
    void UpdateSessionActivity(const std::string& sessionId)
    {
        m_sessionManager.UpdateSessionActivity(sessionId);
    }
    
    // 获取实时指标
    size_t GetOnlineUserCount() const
    {
        return m_sessionManager.GetOnlineUserCount();
    }
    
    size_t GetActiveSessionCount() const
    {
        return m_sessionManager.GetActiveSessionCount();
    }
    
    // 获取业务统计
    BusinessMetricStats GetMetricStats(BusinessMetricType type) const
    {
        std::lock_guard<std::mutex> lock(m_mutex);
        auto it = m_statistics.find(type);
        return it != m_statistics.end() ? it->second : BusinessMetricStats{};
    }
    
    // 获取最近事件
    std::vector<BusinessEvent> GetRecentEvents(BusinessMetricType type, size_t count = 100) const
    {
        std::lock_guard<std::mutex> lock(m_mutex);
        std::vector<BusinessEvent> result;
        
        for (auto it = m_recentEvents.rbegin(); it != m_recentEvents.rend() && result.size() < count; ++it)
        {
            if (it->type == type)
            {
                result.push_back(*it);
            }
        }
        
        return result;
    }
    
    // 生成业务报告
    std::string GenerateBusinessReport() const;
    
    // 导出业务数据
    void ExportBusinessData(const std::string& filename, 
        std::chrono::system_clock::time_point since) const;

private:
    std::atomic<bool> m_running;
    std::thread m_cleanupThread;
    UserSessionManager m_sessionManager;
    
    mutable std::mutex m_mutex;
    std::vector<BusinessEvent> m_recentEvents;
    std::unordered_map<BusinessMetricType, BusinessMetricStats> m_statistics;
    
    static constexpr size_t MAX_RECENT_EVENTS = 10000;
    
    void CleanupLoop()
    {
        while (m_running)
        {
            try
            {
                // 清理超时会话
                m_sessionManager.CleanupTimeoutSessions();
                
                // 更新在线用户数指标
                RecordEvent(BusinessMetricType::OnlineUsers, 
                    static_cast<double>(GetOnlineUserCount()));
                
                // 更新活跃会话数指标
                RecordEvent(BusinessMetricType::GameSessions, 
                    static_cast<double>(GetActiveSessionCount()));
            }
            catch (const std::exception& e)
            {
                LOG_ERROR("BUSINESS", std::string("Error in cleanup loop: ") + e.what());
            }
            
            std::this_thread::sleep_for(std::chrono::minutes(1));
        }
    }
    
    void LogBusinessEvent(const BusinessEvent& event)
    {
        std::unordered_map<std::string, std::string> metadata;
        metadata["metric_type"] = std::to_string(static_cast<int>(event.type));
        metadata["value"] = std::to_string(event.value);
        metadata["user_id"] = event.userId;
        metadata["session_id"] = event.sessionId;
        
        for (const auto& prop : event.properties)
        {
            metadata[prop.first] = prop.second;
        }
        
        LOG_WITH_META(LogLevel::Info, "BUSINESS_EVENT", 
            "Business event recorded", metadata);
    }
};

} // namespace sword2

// 全局业务监控器访问
#define BUSINESS_MONITOR() sword2::BusinessMonitor::getInstance()

// 便捷宏定义
#define START_BUSINESS_MONITORING() BUSINESS_MONITOR().Start()
#define STOP_BUSINESS_MONITORING() BUSINESS_MONITOR().Stop()

#define RECORD_USER_LOGIN(userId, ip) BUSINESS_MONITOR().StartUserSession(userId, ip)
#define RECORD_USER_LOGOUT(sessionId) BUSINESS_MONITOR().EndUserSession(sessionId)
#define RECORD_BUSINESS_EVENT(type, value, userId, sessionId) \
    BUSINESS_MONITOR().RecordEvent(type, value, userId, sessionId)

// 常用业务事件记录宏
#define RECORD_LEVEL_UP(userId, sessionId) \
    RECORD_BUSINESS_EVENT(sword2::BusinessMetricType::LevelUps, 1.0, userId, sessionId)

#define RECORD_ITEM_TRANSACTION(userId, sessionId, amount) \
    RECORD_BUSINESS_EVENT(sword2::BusinessMetricType::ItemTransactions, amount, userId, sessionId)

#define RECORD_CHAT_MESSAGE(userId, sessionId) \
    RECORD_BUSINESS_EVENT(sword2::BusinessMetricType::ChatMessages, 1.0, userId, sessionId)

#define RECORD_SKILL_USAGE(userId, sessionId, skillId) \
    do { \
        sword2::BusinessEvent event(sword2::BusinessMetricType::SkillUsage, 1.0, userId, sessionId); \
        event.properties["skill_id"] = skillId; \
        BUSINESS_MONITOR().RecordEvent(event.type, event.value, event.userId, event.sessionId); \
    } while(0)

#endif // BUSINESS_MONITOR_H
