//---------------------------------------------------------------------------
// Sword2 Item Script Integration (c) 2024
//
// File:	ItemScriptIntegration.h
// Date:	2024.07.02
// Code:	AI Assistant
// Desc:	Integration layer between item system and Lua scripts
//---------------------------------------------------------------------------
#ifndef ITEM_SCRIPT_INTEGRATION_H
#define ITEM_SCRIPT_INTEGRATION_H

#include "ItemManager.h"
#include "InventoryManager.h"
#include "LuaScriptEngine.h"
#include "PlayerManager.h"
#include "UnifiedLoggingSystem.h"
#include <string>
#include <unordered_map>
#include <vector>
#include <functional>

namespace sword2 {

// 物品脚本API扩展
namespace ItemScriptAPI {
    
    // 物品基础API
    int GetItemName(lua_State* L);
    int GetItemDescription(lua_State* L);
    int GetItemLevel(lua_State* L);
    int GetItemType(lua_State* L);
    int GetItemGenre(lua_State* L);
    int GetItemPrice(lua_State* L);
    int GetItemWeight(lua_State* L);
    int GetItemStack(lua_State* L);
    
    // 物品实例API
    int CreateItemInstance(lua_State* L);
    int DestroyItemInstance(lua_State* L);
    int GetItemInstance(lua_State* L);
    int GetItemOwner(lua_State* L);
    int GetItemPosition(lua_State* L);
    int GetItemDurability(lua_State* L);
    int GetItemEnhanceLevel(lua_State* L);
    
    // 物品操作API
    int UseItem(lua_State* L);
    int MoveItem(lua_State* L);
    int EquipItem(lua_State* L);
    int UnequipItem(lua_State* L);
    int EnhanceItem(lua_State* L);
    int RepairItem(lua_State* L);
    int SocketGem(lua_State* L);
    int RemoveGem(lua_State* L);
    
    // 背包API
    int GetPlayerInventory(lua_State* L);
    int AddItemToInventory(lua_State* L);
    int RemoveItemFromInventory(lua_State* L);
    int GetInventorySpace(lua_State* L);
    int GetInventoryWeight(lua_State* L);
    int SortInventory(lua_State* L);
    int FindItemInInventory(lua_State* L);
    
    // 装备API
    int GetEquippedItem(lua_State* L);
    int GetEquipmentSlots(lua_State* L);
    int CalculateEquipmentAttributes(lua_State* L);
    int CheckEquipRequirement(lua_State* L);
    
    // 交易API
    int TradeItem(lua_State* L);
    int CanTradeItem(lua_State* L);
    int GetTradeValue(lua_State* L);
    int SellItem(lua_State* L);
    int BuyItem(lua_State* L);
    
    // 物品属性API
    int GetItemAttributes(lua_State* L);
    int SetItemAttribute(lua_State* L);
    int AddItemAttribute(lua_State* L);
    int RemoveItemAttribute(lua_State* L);
    int GetRandomAttributes(lua_State* L);
    
    // 物品效果API
    int ApplyItemEffect(lua_State* L);
    int RemoveItemEffect(lua_State* L);
    int GetItemEffects(lua_State* L);
    int TriggerItemScript(lua_State* L);
    
    // 物品查询API
    int FindItemsByType(lua_State* L);
    int FindItemsByLevel(lua_State* L);
    int FindItemsByName(lua_State* L);
    int GetItemStatistics(lua_State* L);
}

// 物品效果管理器
class ItemEffectManager
{
public:
    struct ItemEffect
    {
        uint32_t effectId = 0;      // 效果ID
        uint32_t itemId = 0;        // 物品ID
        uint32_t playerId = 0;      // 玩家ID
        std::string effectType;     // 效果类型
        int32_t value = 0;          // 效果值
        uint32_t duration = 0;      // 持续时间(毫秒)
        std::chrono::system_clock::time_point startTime; // 开始时间
        bool isActive = true;       // 是否激活
        
        ItemEffect() = default;
        ItemEffect(uint32_t id, uint32_t item, uint32_t player, const std::string& type, int32_t val, uint32_t dur)
            : effectId(id), itemId(item), playerId(player), effectType(type), value(val), duration(dur)
        {
            startTime = std::chrono::system_clock::now();
        }
        
        // 检查是否过期
        bool IsExpired() const
        {
            if (duration == 0) return false; // 永久效果
            
            auto now = std::chrono::system_clock::now();
            auto elapsed = std::chrono::duration_cast<std::chrono::milliseconds>(now - startTime);
            return elapsed.count() >= duration;
        }
        
        // 获取剩余时间
        uint32_t GetRemainingTime() const
        {
            if (duration == 0) return UINT32_MAX;
            
            auto now = std::chrono::system_clock::now();
            auto elapsed = std::chrono::duration_cast<std::chrono::milliseconds>(now - startTime);
            uint32_t elapsedMs = static_cast<uint32_t>(elapsed.count());
            
            return (elapsedMs >= duration) ? 0 : (duration - elapsedMs);
        }
    };
    
    // 应用物品效果
    uint32_t ApplyEffect(uint32_t itemId, uint32_t playerId, const std::string& effectType, 
                        int32_t value, uint32_t duration = 0)
    {
        std::lock_guard<std::mutex> lock(m_effectMutex);
        
        uint32_t effectId = m_nextEffectId++;
        ItemEffect effect(effectId, itemId, playerId, effectType, value, duration);
        
        m_activeEffects[effectId] = effect;
        m_playerEffects[playerId].push_back(effectId);
        
        LOG_DEBUG("ITEM_EFFECT", "Applied effect " + std::to_string(effectId) + 
                 " from item " + std::to_string(itemId) + " to player " + std::to_string(playerId));
        
        return effectId;
    }
    
    // 移除物品效果
    bool RemoveEffect(uint32_t effectId)
    {
        std::lock_guard<std::mutex> lock(m_effectMutex);
        
        auto it = m_activeEffects.find(effectId);
        if (it != m_activeEffects.end())
        {
            uint32_t playerId = it->second.playerId;
            
            // 从玩家效果列表中移除
            auto& playerEffects = m_playerEffects[playerId];
            playerEffects.erase(std::remove(playerEffects.begin(), playerEffects.end(), effectId), 
                               playerEffects.end());
            
            m_activeEffects.erase(it);
            
            LOG_DEBUG("ITEM_EFFECT", "Removed effect " + std::to_string(effectId));
            return true;
        }
        
        return false;
    }
    
    // 获取玩家的所有效果
    std::vector<ItemEffect> GetPlayerEffects(uint32_t playerId)
    {
        std::lock_guard<std::mutex> lock(m_effectMutex);
        std::vector<ItemEffect> effects;
        
        auto it = m_playerEffects.find(playerId);
        if (it != m_playerEffects.end())
        {
            for (uint32_t effectId : it->second)
            {
                auto effectIt = m_activeEffects.find(effectId);
                if (effectIt != m_activeEffects.end() && effectIt->second.isActive)
                {
                    effects.push_back(effectIt->second);
                }
            }
        }
        
        return effects;
    }
    
    // 更新效果
    void Update()
    {
        std::lock_guard<std::mutex> lock(m_effectMutex);
        
        std::vector<uint32_t> expiredEffects;
        
        for (auto& [effectId, effect] : m_activeEffects)
        {
            if (effect.IsExpired())
            {
                expiredEffects.push_back(effectId);
            }
        }
        
        // 移除过期效果
        for (uint32_t effectId : expiredEffects)
        {
            RemoveEffect(effectId);
        }
    }

private:
    std::mutex m_effectMutex;
    std::unordered_map<uint32_t, ItemEffect> m_activeEffects;
    std::unordered_map<uint32_t, std::vector<uint32_t>> m_playerEffects; // playerId -> effectIds
    std::atomic<uint32_t> m_nextEffectId{1};
};

// 物品交易管理器
class ItemTradeManager
{
public:
    struct TradeSession
    {
        uint32_t sessionId = 0;     // 交易会话ID
        uint32_t player1Id = 0;     // 玩家1 ID
        uint32_t player2Id = 0;     // 玩家2 ID
        std::vector<uint32_t> player1Items; // 玩家1的物品
        std::vector<uint32_t> player2Items; // 玩家2的物品
        uint32_t player1Money = 0;  // 玩家1的金钱
        uint32_t player2Money = 0;  // 玩家2的金钱
        bool player1Confirmed = false; // 玩家1确认
        bool player2Confirmed = false; // 玩家2确认
        std::chrono::system_clock::time_point startTime; // 开始时间
        
        TradeSession() = default;
        TradeSession(uint32_t id, uint32_t p1, uint32_t p2)
            : sessionId(id), player1Id(p1), player2Id(p2)
        {
            startTime = std::chrono::system_clock::now();
        }
        
        // 检查是否超时
        bool IsExpired() const
        {
            auto now = std::chrono::system_clock::now();
            auto elapsed = std::chrono::duration_cast<std::chrono::minutes>(now - startTime);
            return elapsed.count() >= 10; // 10分钟超时
        }
        
        // 检查是否可以完成交易
        bool CanComplete() const
        {
            return player1Confirmed && player2Confirmed;
        }
    };
    
    // 开始交易
    uint32_t StartTrade(uint32_t player1Id, uint32_t player2Id)
    {
        std::lock_guard<std::mutex> lock(m_tradeMutex);
        
        uint32_t sessionId = m_nextSessionId++;
        TradeSession session(sessionId, player1Id, player2Id);
        
        m_tradeSessions[sessionId] = session;
        
        LOG_INFO("ITEM_TRADE", "Started trade session " + std::to_string(sessionId) + 
                " between players " + std::to_string(player1Id) + " and " + std::to_string(player2Id));
        
        return sessionId;
    }
    
    // 添加交易物品
    bool AddTradeItem(uint32_t sessionId, uint32_t playerId, uint32_t itemId)
    {
        std::lock_guard<std::mutex> lock(m_tradeMutex);
        
        auto it = m_tradeSessions.find(sessionId);
        if (it == m_tradeSessions.end())
            return false;
        
        auto& session = it->second;
        
        if (playerId == session.player1Id)
        {
            session.player1Items.push_back(itemId);
            session.player1Confirmed = false; // 重置确认状态
        }
        else if (playerId == session.player2Id)
        {
            session.player2Items.push_back(itemId);
            session.player2Confirmed = false; // 重置确认状态
        }
        else
        {
            return false; // 不是交易参与者
        }
        
        LOG_DEBUG("ITEM_TRADE", "Added item " + std::to_string(itemId) + 
                 " to trade session " + std::to_string(sessionId) + " by player " + std::to_string(playerId));
        
        return true;
    }
    
    // 确认交易
    bool ConfirmTrade(uint32_t sessionId, uint32_t playerId)
    {
        std::lock_guard<std::mutex> lock(m_tradeMutex);
        
        auto it = m_tradeSessions.find(sessionId);
        if (it == m_tradeSessions.end())
            return false;
        
        auto& session = it->second;
        
        if (playerId == session.player1Id)
        {
            session.player1Confirmed = true;
        }
        else if (playerId == session.player2Id)
        {
            session.player2Confirmed = true;
        }
        else
        {
            return false; // 不是交易参与者
        }
        
        LOG_DEBUG("ITEM_TRADE", "Player " + std::to_string(playerId) + 
                 " confirmed trade session " + std::to_string(sessionId));
        
        // 检查是否可以完成交易
        if (session.CanComplete())
        {
            return CompleteTrade(sessionId);
        }
        
        return true;
    }
    
    // 完成交易
    bool CompleteTrade(uint32_t sessionId)
    {
        auto it = m_tradeSessions.find(sessionId);
        if (it == m_tradeSessions.end())
            return false;
        
        auto& session = it->second;
        
        // 交换物品
        for (uint32_t itemId : session.player1Items)
        {
            TRADE_ITEM(itemId, session.player1Id, session.player2Id);
        }
        
        for (uint32_t itemId : session.player2Items)
        {
            TRADE_ITEM(itemId, session.player2Id, session.player1Id);
        }
        
        // 交换金钱
        if (session.player1Money > 0)
        {
            // 这里应该实现金钱转移
            LOG_DEBUG("ITEM_TRADE", "Transfer " + std::to_string(session.player1Money) + 
                     " money from player " + std::to_string(session.player1Id) + 
                     " to player " + std::to_string(session.player2Id));
        }
        
        if (session.player2Money > 0)
        {
            // 这里应该实现金钱转移
            LOG_DEBUG("ITEM_TRADE", "Transfer " + std::to_string(session.player2Money) + 
                     " money from player " + std::to_string(session.player2Id) + 
                     " to player " + std::to_string(session.player1Id));
        }
        
        // 移除交易会话
        m_tradeSessions.erase(it);
        
        LOG_INFO("ITEM_TRADE", "Completed trade session " + std::to_string(sessionId));
        return true;
    }
    
    // 取消交易
    bool CancelTrade(uint32_t sessionId)
    {
        std::lock_guard<std::mutex> lock(m_tradeMutex);
        
        auto it = m_tradeSessions.find(sessionId);
        if (it != m_tradeSessions.end())
        {
            m_tradeSessions.erase(it);
            LOG_INFO("ITEM_TRADE", "Cancelled trade session " + std::to_string(sessionId));
            return true;
        }
        
        return false;
    }
    
    // 更新交易会话
    void Update()
    {
        std::lock_guard<std::mutex> lock(m_tradeMutex);
        
        std::vector<uint32_t> expiredSessions;
        
        for (const auto& [sessionId, session] : m_tradeSessions)
        {
            if (session.IsExpired())
            {
                expiredSessions.push_back(sessionId);
            }
        }
        
        // 移除过期会话
        for (uint32_t sessionId : expiredSessions)
        {
            m_tradeSessions.erase(sessionId);
            LOG_INFO("ITEM_TRADE", "Trade session " + std::to_string(sessionId) + " expired");
        }
    }

private:
    std::mutex m_tradeMutex;
    std::unordered_map<uint32_t, TradeSession> m_tradeSessions;
    std::atomic<uint32_t> m_nextSessionId{1};
};

// 物品脚本集成管理器
class ItemScriptIntegration : public Singleton<ItemScriptIntegration>
{
public:
    ItemScriptIntegration() = default;
    ~ItemScriptIntegration() = default;
    
    // 初始化物品脚本集成
    bool Initialize()
    {
        // 注册物品相关的Lua API函数
        RegisterItemScriptAPI();
        
        LOG_INFO("ITEM_SCRIPT", "Item script integration initialized");
        return true;
    }
    
    // 加载物品脚本
    bool LoadItemScript(const std::string& scriptPath)
    {
        ScriptResult result = LOAD_SCRIPT(scriptPath, ScriptType::Item);
        if (result == ScriptResult::Success)
        {
            LOG_INFO("ITEM_SCRIPT", "Loaded item script: " + scriptPath);
            return true;
        }
        else
        {
            LOG_ERROR("ITEM_SCRIPT", "Failed to load item script: " + scriptPath);
            return false;
        }
    }
    
    // 执行物品脚本函数
    bool ExecuteItemFunction(const std::string& functionName, uint32_t itemId, uint32_t playerId)
    {
        std::vector<LuaValue> args;
        args.push_back(LuaValue(static_cast<double>(itemId)));
        args.push_back(LuaValue(static_cast<double>(playerId)));
        
        ScriptResult result = EXECUTE_FUNCTION(functionName, args);
        return result == ScriptResult::Success;
    }
    
    // 应用物品效果
    uint32_t ApplyItemEffect(uint32_t itemId, uint32_t playerId, const std::string& effectType, 
                            int32_t value, uint32_t duration = 0)
    {
        return m_effectManager.ApplyEffect(itemId, playerId, effectType, value, duration);
    }
    
    // 移除物品效果
    bool RemoveItemEffect(uint32_t effectId)
    {
        return m_effectManager.RemoveEffect(effectId);
    }
    
    // 开始交易
    uint32_t StartTrade(uint32_t player1Id, uint32_t player2Id)
    {
        return m_tradeManager.StartTrade(player1Id, player2Id);
    }
    
    // 更新系统
    void Update()
    {
        m_effectManager.Update();
        m_tradeManager.Update();
    }
    
    // 获取效果管理器
    ItemEffectManager& GetEffectManager() { return m_effectManager; }
    
    // 获取交易管理器
    ItemTradeManager& GetTradeManager() { return m_tradeManager; }

private:
    ItemEffectManager m_effectManager;
    ItemTradeManager m_tradeManager;
    
    void RegisterItemScriptAPI()
    {
        // 注册物品基础API
        REGISTER_LUA_FUNCTION("GetItemName", ItemScriptAPI::GetItemName);
        REGISTER_LUA_FUNCTION("GetItemDescription", ItemScriptAPI::GetItemDescription);
        REGISTER_LUA_FUNCTION("GetItemLevel", ItemScriptAPI::GetItemLevel);
        REGISTER_LUA_FUNCTION("GetItemType", ItemScriptAPI::GetItemType);
        REGISTER_LUA_FUNCTION("GetItemGenre", ItemScriptAPI::GetItemGenre);
        
        // 注册物品实例API
        REGISTER_LUA_FUNCTION("CreateItemInstance", ItemScriptAPI::CreateItemInstance);
        REGISTER_LUA_FUNCTION("DestroyItemInstance", ItemScriptAPI::DestroyItemInstance);
        REGISTER_LUA_FUNCTION("GetItemInstance", ItemScriptAPI::GetItemInstance);
        REGISTER_LUA_FUNCTION("GetItemOwner", ItemScriptAPI::GetItemOwner);
        
        // 注册物品操作API
        REGISTER_LUA_FUNCTION("UseItem", ItemScriptAPI::UseItem);
        REGISTER_LUA_FUNCTION("MoveItem", ItemScriptAPI::MoveItem);
        REGISTER_LUA_FUNCTION("EquipItem", ItemScriptAPI::EquipItem);
        REGISTER_LUA_FUNCTION("UnequipItem", ItemScriptAPI::UnequipItem);
        REGISTER_LUA_FUNCTION("EnhanceItem", ItemScriptAPI::EnhanceItem);
        
        // 注册背包API
        REGISTER_LUA_FUNCTION("GetPlayerInventory", ItemScriptAPI::GetPlayerInventory);
        REGISTER_LUA_FUNCTION("AddItemToInventory", ItemScriptAPI::AddItemToInventory);
        REGISTER_LUA_FUNCTION("RemoveItemFromInventory", ItemScriptAPI::RemoveItemFromInventory);
        REGISTER_LUA_FUNCTION("GetInventorySpace", ItemScriptAPI::GetInventorySpace);
        
        // 注册装备API
        REGISTER_LUA_FUNCTION("GetEquippedItem", ItemScriptAPI::GetEquippedItem);
        REGISTER_LUA_FUNCTION("GetEquipmentSlots", ItemScriptAPI::GetEquipmentSlots);
        REGISTER_LUA_FUNCTION("CalculateEquipmentAttributes", ItemScriptAPI::CalculateEquipmentAttributes);
        
        // 注册交易API
        REGISTER_LUA_FUNCTION("TradeItem", ItemScriptAPI::TradeItem);
        REGISTER_LUA_FUNCTION("CanTradeItem", ItemScriptAPI::CanTradeItem);
        REGISTER_LUA_FUNCTION("SellItem", ItemScriptAPI::SellItem);
        REGISTER_LUA_FUNCTION("BuyItem", ItemScriptAPI::BuyItem);
        
        // 注册物品属性API
        REGISTER_LUA_FUNCTION("GetItemAttributes", ItemScriptAPI::GetItemAttributes);
        REGISTER_LUA_FUNCTION("SetItemAttribute", ItemScriptAPI::SetItemAttribute);
        REGISTER_LUA_FUNCTION("AddItemAttribute", ItemScriptAPI::AddItemAttribute);
        
        // 注册物品效果API
        REGISTER_LUA_FUNCTION("ApplyItemEffect", ItemScriptAPI::ApplyItemEffect);
        REGISTER_LUA_FUNCTION("RemoveItemEffect", ItemScriptAPI::RemoveItemEffect);
        REGISTER_LUA_FUNCTION("GetItemEffects", ItemScriptAPI::GetItemEffects);
        
        // 注册物品查询API
        REGISTER_LUA_FUNCTION("FindItemsByType", ItemScriptAPI::FindItemsByType);
        REGISTER_LUA_FUNCTION("FindItemsByLevel", ItemScriptAPI::FindItemsByLevel);
        REGISTER_LUA_FUNCTION("GetItemStatistics", ItemScriptAPI::GetItemStatistics);
        
        LOG_INFO("ITEM_SCRIPT", "Registered item script API functions");
    }
};

} // namespace sword2

// 全局物品脚本集成访问
#define ITEM_SCRIPT_INTEGRATION() sword2::ItemScriptIntegration::getInstance()

// 便捷宏定义
#define INIT_ITEM_SCRIPT_INTEGRATION() ITEM_SCRIPT_INTEGRATION().Initialize()
#define LOAD_ITEM_SCRIPT(scriptPath) ITEM_SCRIPT_INTEGRATION().LoadItemScript(scriptPath)
#define EXECUTE_ITEM_FUNCTION(functionName, itemId, playerId) ITEM_SCRIPT_INTEGRATION().ExecuteItemFunction(functionName, itemId, playerId)

#define APPLY_ITEM_EFFECT(itemId, playerId, effectType, value, duration) ITEM_SCRIPT_INTEGRATION().ApplyItemEffect(itemId, playerId, effectType, value, duration)
#define REMOVE_ITEM_EFFECT(effectId) ITEM_SCRIPT_INTEGRATION().RemoveItemEffect(effectId)

#define START_ITEM_TRADE(player1Id, player2Id) ITEM_SCRIPT_INTEGRATION().StartTrade(player1Id, player2Id)

#define ITEM_EFFECT_MANAGER() ITEM_SCRIPT_INTEGRATION().GetEffectManager()
#define ITEM_TRADE_MANAGER() ITEM_SCRIPT_INTEGRATION().GetTradeManager()

#endif // ITEM_SCRIPT_INTEGRATION_H
