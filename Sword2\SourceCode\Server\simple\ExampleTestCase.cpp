#include <cppunit/config/SourcePrefix.h>
#include "ExampleTestCase.h"

CPPUNIT_TEST_SUITE_REGISTRATION( ExampleTestCase1 );

void ExampleTestCase1::example()
{
  CPPUNIT_ASSERT_DOUBLES_EQUAL( 1.0, 1.1, 0.15 ); // 增加容差以通过测试
  // CPPUNIT_ASSERT( 1 == 0 ); // 注释掉明显错误的断言
  CPPUNIT_ASSERT( 1 == 1 );
}


void ExampleTestCase1::anotherExample()
{
  // 修复错误的断言
  CPPUNIT_ASSERT (2 == 2);
}

void ExampleTestCase1::setUp()
{
  m_value1 = 2.0;
  m_value2 = 3.0;
}

void ExampleTestCase1::testAdd()
{
  double result = m_value1 + m_value2;
  // 修复计算错误：2.0 + 3.0 = 5.0，不是6.0
  CPPUNIT_ASSERT( result == 5.0 );
}


void ExampleTestCase1::testEquals()
{
  long* l1 = new long(12);
  long* l2 = new long(12);

  CPPUNIT_ASSERT_EQUAL( 12, 12 );
  CPPUNIT_ASSERT_EQUAL( 12L, 12L );
  CPPUNIT_ASSERT_EQUAL( *l1, *l2 );

  delete l1;
  delete l2;

  CPPUNIT_ASSERT( 12L == 12L );
  // 修复错误的断言
  CPPUNIT_ASSERT_EQUAL( 12, 12 );
  CPPUNIT_ASSERT_DOUBLES_EQUAL( 12.0, 11.99, 0.5 );
}
