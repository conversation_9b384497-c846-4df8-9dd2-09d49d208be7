﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="4.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <ItemGroup>
    <Filter Include="Source Files">
      <UniqueIdentifier>{11374d1c-515e-46b1-be08-c1067b73ca7a}</UniqueIdentifier>
      <Extensions>cpp;c;cxx;rc;def;r;odl;idl;hpj;bat</Extensions>
    </Filter>
    <Filter Include="Header Files">
      <UniqueIdentifier>{b073dab4-ebd3-4b1c-a0e1-00a39e5d1879}</UniqueIdentifier>
      <Extensions>h;hpp;hxx;hm;inl</Extensions>
    </Filter>
    <Filter Include="Resource Files">
      <UniqueIdentifier>{b11e21ad-3f7a-412a-a1c7-97fa5e9c798c}</UniqueIdentifier>
      <Extensions>ico;cur;bmp;dlg;rc2;rct;bin;rgs;gif;jpg;jpeg;jpe</Extensions>
    </Filter>
    <Filter Include="lib">
      <UniqueIdentifier>{cf7c258a-dbd4-4169-9971-c791c7d29831}</UniqueIdentifier>
    </Filter>
    <Filter Include="lib\debug">
      <UniqueIdentifier>{50c9a60b-fff9-4952-a543-5a53cde270b9}</UniqueIdentifier>
    </Filter>
    <Filter Include="lib\release">
      <UniqueIdentifier>{0e0277c8-b9fd-41c3-a721-1d4d1bc02905}</UniqueIdentifier>
    </Filter>
  </ItemGroup>
  <ItemGroup>
    <ClCompile Include="ClientNode.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="DBBackup.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="DBDumpLoad.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="DBTable.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="FilterTextLib.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="Goddess.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="IDBRoleServer.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="RoleNameFilter.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="StdAfx.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
  </ItemGroup>
  <ItemGroup>
    <ClInclude Include="ClientNode.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="db.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="DBBackup.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="DBDumpLoad.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="DBTable.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="FilterTextLib.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="IDBRoleServer.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="RoleNameFilter.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="StdAfx.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="resource.h">
      <Filter>Resource Files</Filter>
    </ClInclude>
  </ItemGroup>
  <ItemGroup>
    <Library Include="..\..\..\Lib\debug\common.lib">
      <Filter>lib\debug</Filter>
    </Library>
    <Library Include="..\..\..\Lib\release\common.lib">
      <Filter>lib\release</Filter>
    </Library>
    <Library Include="..\..\..\Lib\libdb62d.lib">
      <Filter>lib\debug</Filter>
    </Library>
    <Library Include="..\..\..\Lib\debug\engine.lib">
      <Filter>lib\debug</Filter>
    </Library>
  </ItemGroup>
  <ItemGroup>
    <CustomBuild Include="res\goddess.ico">
      <Filter>Resource Files</Filter>
    </CustomBuild>
    <CustomBuild Include="ReadMe.txt" />
  </ItemGroup>
</Project>