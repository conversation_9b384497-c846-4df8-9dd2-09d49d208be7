﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="4.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <ItemGroup>
    <Filter Include="Source Files">
      <UniqueIdentifier>{bc513eb4-741e-4c05-b729-2e54025af423}</UniqueIdentifier>
      <Extensions>cpp;c;cxx;rc;def;r;odl;idl;hpj;bat</Extensions>
    </Filter>
    <Filter Include="Header Files">
      <UniqueIdentifier>{aebc12cc-ebfa-4146-a673-30c26e9057b7}</UniqueIdentifier>
      <Extensions>h;hpp;hxx;hm;inl</Extensions>
    </Filter>
    <Filter Include="Resource Files">
      <UniqueIdentifier>{d82eeef4-4450-4c5f-8066-bde4402c67f5}</UniqueIdentifier>
      <Extensions>ico;cur;bmp;dlg;rc2;rct;bin;rgs;gif;jpg;jpeg;jpe</Extensions>
    </Filter>
    <Filter Include="lib">
      <UniqueIdentifier>{aa531b9f-3acb-4a58-aa73-7e7a6d054570}</UniqueIdentifier>
    </Filter>
    <Filter Include="lib\release">
      <UniqueIdentifier>{1161d335-4675-445d-8385-7459e5d1f04b}</UniqueIdentifier>
    </Filter>
    <Filter Include="lib\debug">
      <UniqueIdentifier>{290a7a4e-a81a-4ff0-bace-3aa0577542ed}</UniqueIdentifier>
    </Filter>
    <Filter Include="injec">
      <UniqueIdentifier>{06ac03ad-c81f-47f7-8b24-ede630da6781}</UniqueIdentifier>
    </Filter>
  </ItemGroup>
  <ItemGroup>
    <ClCompile Include="Application.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="Bishop.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\Common\Event.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="GamePlayer.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="GameServer.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="Intercessor.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="Network.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="PlayerCreator.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="SmartClient.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="StdAfx.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
  </ItemGroup>
  <ItemGroup>
    <ClInclude Include="Application.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="GamePlayer.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="GameServer.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="ICommand.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="IGServer.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="Intercessor.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="IPlayer.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="msg_define.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="Network.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="PlayerCreator.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="SmartClient.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="StdAfx.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="resource.h">
      <Filter>Resource Files</Filter>
    </ClInclude>
    <ClInclude Include="dirent.h">
      <Filter>injec</Filter>
    </ClInclude>
  </ItemGroup>
  <ItemGroup>
    <Library Include="..\..\..\Lib\release\common.lib">
      <Filter>lib\release</Filter>
    </Library>
    <Library Include="..\..\..\Lib\release\engine.lib">
      <Filter>lib\release</Filter>
    </Library>
    <Library Include="..\..\..\Lib\debug\common.lib">
      <Filter>lib\debug</Filter>
    </Library>
    <Library Include="..\..\..\Lib\debug\engine.lib">
      <Filter>lib\debug</Filter>
    </Library>
  </ItemGroup>
  <ItemGroup>
    <CustomBuild Include="res\lock.ico">
      <Filter>Resource Files</Filter>
    </CustomBuild>
    <CustomBuild Include="ReadMe.txt" />
  </ItemGroup>
</Project>