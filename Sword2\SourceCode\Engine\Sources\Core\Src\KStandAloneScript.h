// KStandAloneScript.h: interface for the KStandAloneScript class.
//
//////////////////////////////////////////////////////////////////////

#if !defined(AFX_KSTANDALONESCRIPT_H__717D0FE5_F543_439C_BFC2_C338B6133019__INCLUDED_)
#define AFX_KSTANDALONESCRIPT_H__717D0FE5_F543_439C_BFC2_C338B6133019__INCLUDED_

#if _MSC_VER > 1000
#pragma once
#endif // _MSC_VER > 1000

class KStandAloneScript : public KStepLuaScript  
{
public:
	KStandAloneScript();
	virtual ~KStandAloneScript();

};

#endif // !defined(AFX_KSTANDALONESCRIPT_H__717D0FE5_F543_439C_BFC2_C338B6133019__INCLUDED_)
