//---------------------------------------------------------------------------
// Sword2 Shop Manager (c) 2024
//
// File:	ShopManager.h
// Date:	2024.07.02
// Code:	AI Assistant
// Desc:	Comprehensive shop management system
//---------------------------------------------------------------------------
#ifndef SHOP_MANAGER_H
#define SHOP_MANAGER_H

#include "ShopSystem.h"
#include "PlayerManager.h"
#include "NPCManager.h"
#include "ItemManager.h"
#include "InventoryManager.h"
#include "LuaScriptEngine.h"
#include "UnifiedLoggingSystem.h"
#include <unordered_map>
#include <memory>
#include <mutex>
#include <shared_mutex>
#include <thread>
#include <atomic>
#include <queue>
#include <functional>

namespace sword2 {

// 商店实例
class Shop
{
public:
    uint32_t shopId = 0;            // 商店ID
    ShopConfig config;              // 商店配置
    std::vector<ShopItem> items;    // 商店物品
    std::vector<PurchaseRecord> history; // 购买历史
    
    // 统计信息
    uint32_t totalCustomers = 0;    // 总顾客数
    uint32_t totalSales = 0;        // 总销售额
    uint32_t dailySales = 0;        // 日销售额
    std::chrono::system_clock::time_point lastResetTime; // 上次重置时间
    
    mutable std::shared_mutex itemsMutex; // 物品锁
    
    Shop() = default;
    Shop(uint32_t id, const ShopConfig& cfg) : shopId(id), config(cfg)
    {
        lastResetTime = std::chrono::system_clock::now();
    }
    
    // 添加物品
    bool AddItem(const ShopItem& item)
    {
        std::unique_lock<std::shared_mutex> lock(itemsMutex);
        
        if (items.size() >= config.maxItems)
            return false;
        
        items.push_back(item);
        LOG_DEBUG("SHOP", "Added item " + std::to_string(item.itemId) + " to shop " + std::to_string(shopId));
        return true;
    }
    
    // 移除物品
    bool RemoveItem(uint32_t itemId)
    {
        std::unique_lock<std::shared_mutex> lock(itemsMutex);
        
        auto it = std::find_if(items.begin(), items.end(),
            [itemId](const ShopItem& item) { return item.itemId == itemId; });
        
        if (it != items.end())
        {
            items.erase(it);
            LOG_DEBUG("SHOP", "Removed item " + std::to_string(itemId) + " from shop " + std::to_string(shopId));
            return true;
        }
        
        return false;
    }
    
    // 查找物品
    ShopItem* FindItem(uint32_t itemId)
    {
        std::shared_lock<std::shared_mutex> lock(itemsMutex);
        
        auto it = std::find_if(items.begin(), items.end(),
            [itemId](const ShopItem& item) { return item.itemId == itemId; });
        
        return (it != items.end()) ? &(*it) : nullptr;
    }
    
    // 获取所有物品
    std::vector<ShopItem> GetAllItems() const
    {
        std::shared_lock<std::shared_mutex> lock(itemsMutex);
        return items;
    }
    
    // 获取可购买物品
    std::vector<ShopItem> GetAvailableItems(const Player& player) const
    {
        std::shared_lock<std::shared_mutex> lock(itemsMutex);
        std::vector<ShopItem> availableItems;
        
        for (const auto& item : items)
        {
            if (!item.IsExpired() && item.CheckConditions(player) && item.HasStock())
            {
                availableItems.push_back(item);
            }
        }
        
        return availableItems;
    }
    
    // 更新物品
    void UpdateItems()
    {
        std::unique_lock<std::shared_mutex> lock(itemsMutex);
        
        auto now = std::chrono::system_clock::now();
        
        // 移除过期物品
        if (config.autoCleanup)
        {
            items.erase(std::remove_if(items.begin(), items.end(),
                [](const ShopItem& item) { return item.IsExpired(); }), items.end());
        }
        
        // 自动补货
        if (config.autoRestock)
        {
            for (auto& item : items)
            {
                if (item.NeedsRestock())
                {
                    item.Restock();
                }
            }
        }
        
        // 重置日销售统计
        auto timeSinceReset = std::chrono::duration_cast<std::chrono::hours>(now - lastResetTime);
        if (timeSinceReset.count() >= 24)
        {
            dailySales = 0;
            for (auto& item : items)
            {
                item.dailySold = 0;
            }
            lastResetTime = now;
        }
    }
    
    // 记录购买
    void RecordPurchase(const PurchaseRecord& record)
    {
        history.push_back(record);
        totalCustomers++;
        totalSales += record.totalPrice;
        dailySales += record.totalPrice;
        
        // 限制历史记录数量
        if (history.size() > 1000)
        {
            history.erase(history.begin(), history.begin() + 100);
        }
    }
    
    // 获取销售统计
    std::pair<uint32_t, uint32_t> GetSalesStats() const
    {
        return {totalSales, dailySales};
    }
    
    // 检查是否可以访问
    bool CanAccess(const Player& player) const
    {
        return config.IsOpen() && config.CheckAccess(player);
    }
};

// 商店管理器
class ShopManager : public Singleton<ShopManager>
{
public:
    ShopManager()
        : m_running(false), m_nextShopId(1), m_nextSessionId(1), 
          m_updateInterval(std::chrono::seconds(60)) {}
    
    ~ShopManager()
    {
        Stop();
    }
    
    // 启动商店管理器
    bool Start()
    {
        if (m_running) return true;
        
        m_running = true;
        m_updateThread = std::thread(&ShopManager::UpdateLoop, this);
        
        // 创建默认商店
        CreateDefaultShops();
        
        LOG_INFO("SHOP_MGR", "Shop manager started");
        return true;
    }
    
    // 停止商店管理器
    void Stop()
    {
        if (!m_running) return;
        
        m_running = false;
        if (m_updateThread.joinable())
        {
            m_updateThread.join();
        }
        
        // 清理数据
        {
            std::unique_lock<std::shared_mutex> lock(m_shopsMutex);
            m_shops.clear();
        }
        
        {
            std::unique_lock<std::shared_mutex> lock(m_sessionsMutex);
            m_sessions.clear();
        }
        
        LOG_INFO("SHOP_MGR", "Shop manager stopped");
    }
    
    // 创建商店
    uint32_t CreateShop(const ShopConfig& config)
    {
        std::unique_lock<std::shared_mutex> lock(m_shopsMutex);
        
        uint32_t shopId = m_nextShopId++;
        m_shops[shopId] = std::make_unique<Shop>(shopId, config);
        
        LOG_INFO("SHOP_MGR", "Created shop " + std::to_string(shopId) + ": " + config.name);
        return shopId;
    }
    
    // 删除商店
    bool DeleteShop(uint32_t shopId)
    {
        std::unique_lock<std::shared_mutex> lock(m_shopsMutex);
        
        auto it = m_shops.find(shopId);
        if (it != m_shops.end())
        {
            // 关闭所有相关会话
            CloseShopSessions(shopId);
            
            m_shops.erase(it);
            LOG_INFO("SHOP_MGR", "Deleted shop " + std::to_string(shopId));
            return true;
        }
        
        return false;
    }
    
    // 获取商店
    Shop* GetShop(uint32_t shopId)
    {
        std::shared_lock<std::shared_mutex> lock(m_shopsMutex);
        auto it = m_shops.find(shopId);
        return (it != m_shops.end()) ? it->second.get() : nullptr;
    }
    
    // 获取NPC商店
    uint32_t GetNPCShop(uint32_t npcId)
    {
        std::shared_lock<std::shared_mutex> lock(m_shopsMutex);
        
        for (const auto& [shopId, shop] : m_shops)
        {
            if (shop->config.type == ShopType::NPC && shop->config.ownerId == npcId)
            {
                return shopId;
            }
        }
        
        return 0;
    }
    
    // 获取玩家商店
    std::vector<uint32_t> GetPlayerShops(uint32_t playerId)
    {
        std::shared_lock<std::shared_mutex> lock(m_shopsMutex);
        std::vector<uint32_t> playerShops;
        
        for (const auto& [shopId, shop] : m_shops)
        {
            if (shop->config.type == ShopType::Player && shop->config.ownerId == playerId)
            {
                playerShops.push_back(shopId);
            }
        }
        
        return playerShops;
    }
    
    // 打开商店
    uint32_t OpenShop(uint32_t shopId, uint32_t playerId)
    {
        auto shop = GetShop(shopId);
        if (!shop)
        {
            LOG_WARNING("SHOP_MGR", "Shop not found: " + std::to_string(shopId));
            return 0;
        }
        
        auto player = GET_ONLINE_PLAYER(playerId);
        if (!player)
        {
            LOG_WARNING("SHOP_MGR", "Player not found: " + std::to_string(playerId));
            return 0;
        }
        
        if (!shop->CanAccess(*player))
        {
            LOG_WARNING("SHOP_MGR", "Player " + std::to_string(playerId) + " cannot access shop " + std::to_string(shopId));
            return 0;
        }
        
        std::unique_lock<std::shared_mutex> lock(m_sessionsMutex);
        
        uint32_t sessionId = m_nextSessionId++;
        ShopSession session(sessionId, shopId, playerId);
        m_sessions[sessionId] = session;
        
        LOG_INFO("SHOP_MGR", "Player " + std::to_string(playerId) + " opened shop " + std::to_string(shopId) + 
                " (Session: " + std::to_string(sessionId) + ")");
        
        return sessionId;
    }
    
    // 关闭商店
    bool CloseShop(uint32_t sessionId)
    {
        std::unique_lock<std::shared_mutex> lock(m_sessionsMutex);
        
        auto it = m_sessions.find(sessionId);
        if (it != m_sessions.end())
        {
            LOG_INFO("SHOP_MGR", "Closed shop session " + std::to_string(sessionId));
            m_sessions.erase(it);
            return true;
        }
        
        return false;
    }
    
    // 获取会话
    ShopSession* GetSession(uint32_t sessionId)
    {
        std::shared_lock<std::shared_mutex> lock(m_sessionsMutex);
        auto it = m_sessions.find(sessionId);
        return (it != m_sessions.end()) ? &it->second : nullptr;
    }
    
    // 购买物品
    ShopResult BuyItem(uint32_t sessionId, uint32_t itemId, uint32_t quantity = 1)
    {
        auto session = GetSession(sessionId);
        if (!session || !session->isActive)
        {
            return ShopResult::NotFound;
        }
        
        auto shop = GetShop(session->shopId);
        if (!shop)
        {
            return ShopResult::NotFound;
        }
        
        auto player = GET_ONLINE_PLAYER(session->playerId);
        if (!player)
        {
            return ShopResult::NotFound;
        }
        
        auto shopItem = shop->FindItem(itemId);
        if (!shopItem)
        {
            return ShopResult::NotFound;
        }
        
        // 检查库存
        if (!shopItem->HasStock(quantity))
        {
            return ShopResult::InsufficientStock;
        }
        
        // 检查购买条件
        if (!shopItem->CheckConditions(*player))
        {
            return ShopResult::PermissionDenied;
        }
        
        // 计算总价
        uint32_t totalPrice = shopItem->GetActualPrice() * quantity;
        
        // 检查玩家货币
        if (!HasEnoughCurrency(*player, shopItem->currency, totalPrice))
        {
            return ShopResult::InsufficientMoney;
        }
        
        // 检查背包空间
        if (!HasEnoughSpace(*player, itemId, quantity))
        {
            return ShopResult::InsufficientSpace;
        }
        
        // 扣除货币
        if (!DeductCurrency(*player, shopItem->currency, totalPrice))
        {
            return ShopResult::Failed;
        }
        
        // 给予物品
        if (!GiveItem(*player, itemId, quantity))
        {
            // 回退货币
            AddCurrency(*player, shopItem->currency, totalPrice);
            return ShopResult::Failed;
        }
        
        // 更新商店物品
        shopItem->Purchase(quantity);
        
        // 记录购买
        PurchaseRecord record(session->shopId, session->playerId, itemId, quantity, 
                            shopItem->GetActualPrice(), shopItem->currency);
        record.playerName = player->name;
        // record.itemName = GetItemName(itemId); // 需要从物品系统获取
        
        shop->RecordPurchase(record);
        
        // 更新会话
        session->UpdateActivity();
        
        LOG_INFO("SHOP_MGR", "Player " + std::to_string(session->playerId) + " bought " + 
                std::to_string(quantity) + "x item " + std::to_string(itemId) + 
                " for " + std::to_string(totalPrice));
        
        return ShopResult::Success;
    }
    
    // 出售物品给商店
    ShopResult SellItem(uint32_t sessionId, uint32_t itemInstanceId, uint32_t quantity = 1)
    {
        auto session = GetSession(sessionId);
        if (!session || !session->isActive)
        {
            return ShopResult::NotFound;
        }
        
        auto shop = GetShop(session->shopId);
        if (!shop)
        {
            return ShopResult::NotFound;
        }
        
        auto player = GET_ONLINE_PLAYER(session->playerId);
        if (!player)
        {
            return ShopResult::NotFound;
        }
        
        // 检查物品是否存在
        auto itemInstance = GET_ITEM_INSTANCE(itemInstanceId);
        if (!itemInstance || itemInstance->ownerId != session->playerId)
        {
            return ShopResult::NotFound;
        }
        
        // 检查是否可以交易
        if (!itemInstance->canTrade || itemInstance->isBound)
        {
            return ShopResult::CannotTrade;
        }
        
        // 检查数量
        if (itemInstance->stackCount < quantity)
        {
            return ShopResult::InvalidQuantity;
        }
        
        // 计算出售价格（通常是购买价格的一定比例）
        auto itemTemplate = GET_ITEM_TEMPLATE(itemInstance->templateId);
        if (!itemTemplate)
        {
            return ShopResult::Failed;
        }
        
        uint32_t sellPrice = CalculateSellPrice(itemTemplate->basePrice, quantity);
        
        // 移除物品
        if (!RemoveItemFromPlayer(*player, itemInstanceId, quantity))
        {
            return ShopResult::Failed;
        }
        
        // 给予金钱
        AddCurrency(*player, CurrencyType::Gold, sellPrice);
        
        // 更新会话
        session->UpdateActivity();
        
        LOG_INFO("SHOP_MGR", "Player " + std::to_string(session->playerId) + " sold " + 
                std::to_string(quantity) + "x item " + std::to_string(itemInstance->templateId) + 
                " for " + std::to_string(sellPrice));
        
        return ShopResult::Success;
    }
    
    // 添加到购物车
    bool AddToCart(uint32_t sessionId, uint32_t itemId, uint32_t quantity)
    {
        auto session = GetSession(sessionId);
        if (!session || !session->isActive)
        {
            return false;
        }
        
        session->AddToCart(itemId, quantity);
        session->UpdateActivity();
        
        return true;
    }
    
    // 从购物车移除
    bool RemoveFromCart(uint32_t sessionId, uint32_t itemId, uint32_t quantity = 0)
    {
        auto session = GetSession(sessionId);
        if (!session || !session->isActive)
        {
            return false;
        }
        
        bool result = session->RemoveFromCart(itemId, quantity);
        session->UpdateActivity();
        
        return result;
    }
    
    // 购买购物车中的所有物品
    ShopResult BuyCart(uint32_t sessionId)
    {
        auto session = GetSession(sessionId);
        if (!session || !session->isActive)
        {
            return ShopResult::NotFound;
        }
        
        if (session->cart.empty())
        {
            return ShopResult::Success;
        }
        
        // 逐个购买购物车中的物品
        for (const auto& [itemId, quantity] : session->cart)
        {
            ShopResult result = BuyItem(sessionId, itemId, quantity);
            if (result != ShopResult::Success)
            {
                return result;
            }
        }
        
        // 清空购物车
        session->ClearCart();
        
        return ShopResult::Success;
    }

private:
    std::atomic<bool> m_running;
    std::thread m_updateThread;
    std::chrono::seconds m_updateInterval;
    
    // 商店数据
    mutable std::shared_mutex m_shopsMutex;
    std::unordered_map<uint32_t, std::unique_ptr<Shop>> m_shops;
    std::atomic<uint32_t> m_nextShopId;
    
    // 会话数据
    mutable std::shared_mutex m_sessionsMutex;
    std::unordered_map<uint32_t, ShopSession> m_sessions;
    std::atomic<uint32_t> m_nextSessionId;
    
    // 更新循环
    void UpdateLoop()
    {
        while (m_running)
        {
            try
            {
                UpdateShops();
                CleanupSessions();
                std::this_thread::sleep_for(m_updateInterval);
            }
            catch (const std::exception& e)
            {
                LOG_ERROR("SHOP_MGR", std::string("Error in update loop: ") + e.what());
            }
        }
    }
    
    void UpdateShops();
    void CleanupSessions();
    void CloseShopSessions(uint32_t shopId);
    void CreateDefaultShops();
    
    // 辅助方法
    bool HasEnoughCurrency(const Player& player, CurrencyType currency, uint32_t amount);
    bool DeductCurrency(Player& player, CurrencyType currency, uint32_t amount);
    void AddCurrency(Player& player, CurrencyType currency, uint32_t amount);
    bool HasEnoughSpace(const Player& player, uint32_t itemId, uint32_t quantity);
    bool GiveItem(Player& player, uint32_t itemId, uint32_t quantity);
    bool RemoveItemFromPlayer(Player& player, uint32_t itemInstanceId, uint32_t quantity);
    uint32_t CalculateSellPrice(uint32_t basePrice, uint32_t quantity);
};

} // namespace sword2

// 全局商店管理器访问
#define SHOP_MANAGER() sword2::ShopManager::getInstance()

// 便捷宏定义
#define START_SHOP_SYSTEM() SHOP_MANAGER().Start()
#define STOP_SHOP_SYSTEM() SHOP_MANAGER().Stop()

#define CREATE_SHOP(config) SHOP_MANAGER().CreateShop(config)
#define DELETE_SHOP(shopId) SHOP_MANAGER().DeleteShop(shopId)
#define GET_SHOP(shopId) SHOP_MANAGER().GetShop(shopId)

#define OPEN_SHOP(shopId, playerId) SHOP_MANAGER().OpenShop(shopId, playerId)
#define CLOSE_SHOP(sessionId) SHOP_MANAGER().CloseShop(sessionId)

#define BUY_ITEM(sessionId, itemId, quantity) SHOP_MANAGER().BuyItem(sessionId, itemId, quantity)
#define SELL_ITEM(sessionId, itemInstanceId, quantity) SHOP_MANAGER().SellItem(sessionId, itemInstanceId, quantity)

#define ADD_TO_CART(sessionId, itemId, quantity) SHOP_MANAGER().AddToCart(sessionId, itemId, quantity)
#define REMOVE_FROM_CART(sessionId, itemId, quantity) SHOP_MANAGER().RemoveFromCart(sessionId, itemId, quantity)
#define BUY_CART(sessionId) SHOP_MANAGER().BuyCart(sessionId)

#endif // SHOP_MANAGER_H
