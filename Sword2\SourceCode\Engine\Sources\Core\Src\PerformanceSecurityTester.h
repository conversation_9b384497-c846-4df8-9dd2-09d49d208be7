//---------------------------------------------------------------------------
// Sword2 Performance Security Tester (c) 2024
//
// File:	PerformanceSecurityTester.h
// Date:	2024.07.02
// Code:	AI Assistant
// Desc:	Performance impact testing for security measures
//---------------------------------------------------------------------------
#ifndef PERFORMANCE_SECURITY_TESTER_H
#define PERFORMANCE_SECURITY_TESTER_H

#include <windows.h>
#include <vector>

// 性能测试类型
enum PERFORMANCE_TEST_TYPE
{
    PTT_BASELINE = 0,           // 基准测试
    PTT_SECURITY_OVERHEAD,      // 安全开销测试
    PTT_STRESS_SECURITY,        // 压力安全测试
    PTT_LOAD_SECURITY,          // 负载安全测试
    PTT_ENDURANCE_SECURITY      // 耐久安全测试
};

// 性能指标
struct PerformanceMetrics
{
    DWORD dwCPUUsage;           // CPU使用率
    DWORD dwMemoryUsage;        // 内存使用量
    DWORD dwNetworkThroughput;  // 网络吞吐量
    DWORD dwResponseTime;       // 响应时间
    DWORD dwTransactionsPerSec; // 每秒事务数
    float fFrameRate;           // 帧率
    DWORD dwSecurityOverhead;   // 安全开销
    DWORD dwErrorRate;          // 错误率
};

// 性能测试结果
struct PerformanceTestResult
{
    std::string strTestName;    // 测试名称
    PERFORMANCE_TEST_TYPE eType; // 测试类型
    PerformanceMetrics baseline; // 基准指标
    PerformanceMetrics withSecurity; // 启用安全后指标
    float fPerformanceImpact;   // 性能影响百分比
    BOOL bAcceptable;           // 是否可接受
    std::string strDetails;     // 详细信息
};

// 基准测试器
class CBaselineTester
{
public:
    CBaselineTester();
    ~CBaselineTester();

    // 基准性能测试
    PerformanceMetrics MeasureBaselinePerformance();
    PerformanceMetrics MeasureNetworkBaseline();
    PerformanceMetrics MeasureMemoryBaseline();
    PerformanceMetrics MeasureCPUBaseline();
    PerformanceMetrics MeasureGameLoopBaseline();

    // 建立基准
    void EstablishBaseline();
    void SaveBaseline(const char* pFilename);
    BOOL LoadBaseline(const char* pFilename);

private:
    PerformanceMetrics m_BaselineMetrics;
    BOOL m_bBaselineEstablished;

    void RunCPUIntensiveTask(DWORD dwDuration);
    void RunMemoryIntensiveTask(DWORD dwDuration);
    void RunNetworkIntensiveTask(DWORD dwDuration);
    void RunGameLoopTask(DWORD dwDuration);
};

// 安全开销测试器
class CSecurityOverheadTester
{
public:
    CSecurityOverheadTester();
    ~CSecurityOverheadTester();

    // 各模块开销测试
    PerformanceTestResult TestInputValidationOverhead();
    PerformanceTestResult TestEncryptionOverhead();
    PerformanceTestResult TestAccessControlOverhead();
    PerformanceTestResult TestAntiCheatOverhead();
    PerformanceTestResult TestNetworkSecurityOverhead();

    // 综合开销测试
    PerformanceTestResult TestOverallSecurityOverhead();
    PerformanceTestResult TestSecurityVsPerformanceTradeoff();

    void GenerateOverheadReport(std::vector<PerformanceTestResult>& results);

private:
    CBaselineTester m_BaselineTester;

    PerformanceMetrics MeasureWithSecurity(const char* pSecurityModule);
    PerformanceMetrics MeasureWithoutSecurity(const char* pSecurityModule);
    float CalculateOverheadPercentage(const PerformanceMetrics& baseline, 
                                     const PerformanceMetrics& withSecurity);
};

// 压力安全测试器
class CStressSecurityTester
{
public:
    CStressSecurityTester();
    ~CStressSecurityTester();

    // 高负载下的安全测试
    PerformanceTestResult TestSecurityUnderHighLoad();
    PerformanceTestResult TestSecurityUnderMemoryPressure();
    PerformanceTestResult TestSecurityUnderCPUStress();
    PerformanceTestResult TestSecurityUnderNetworkStress();

    // 极限条件测试
    PerformanceTestResult TestSecurityAtMaxCapacity();
    PerformanceTestResult TestSecurityDuringResourceExhaustion();
    PerformanceTestResult TestSecurityFailover();

    // 恢复能力测试
    PerformanceTestResult TestSecurityRecovery();
    PerformanceTestResult TestSecurityResilience();

    void GenerateStressReport(std::vector<PerformanceTestResult>& results);

private:
    void SimulateHighCPULoad();
    void SimulateHighMemoryUsage();
    void SimulateHighNetworkTraffic();
    void SimulateResourceExhaustion();
    BOOL MonitorSecurityEffectiveness();
};

// 负载安全测试器
class CLoadSecurityTester
{
public:
    CLoadSecurityTester();
    ~CLoadSecurityTester();

    // 并发用户测试
    PerformanceTestResult TestSecurityWithConcurrentUsers(DWORD dwUserCount);
    PerformanceTestResult TestSecurityWithIncreasingLoad();
    PerformanceTestResult TestSecurityLoadBalancing();

    // 事务负载测试
    PerformanceTestResult TestSecurityWithHighTransactionVolume();
    PerformanceTestResult TestSecurityWithMixedWorkload();
    PerformanceTestResult TestSecurityWithBurstTraffic();

    // 长期负载测试
    PerformanceTestResult TestSecurityUnderSustainedLoad(DWORD dwDurationMinutes);

    void GenerateLoadReport(std::vector<PerformanceTestResult>& results);

private:
    void SimulateConcurrentUsers(DWORD dwUserCount);
    void SimulateTransactionLoad(DWORD dwTransactionsPerSecond);
    void SimulateBurstTraffic();
    void MonitorSecurityDegradation();
};

// 耐久安全测试器
class CEnduranceSecurityTester
{
public:
    CEnduranceSecurityTester();
    ~CEnduranceSecurityTester();

    // 长期运行测试
    PerformanceTestResult TestSecurityEndurance(DWORD dwDurationHours);
    PerformanceTestResult TestSecurityMemoryLeaks();
    PerformanceTestResult TestSecurityPerformanceDegradation();

    // 稳定性测试
    PerformanceTestResult TestSecurityStability();
    PerformanceTestResult TestSecurityReliability();

    void GenerateEnduranceReport(std::vector<PerformanceTestResult>& results);

private:
    void RunLongTermTest(DWORD dwDurationHours);
    void MonitorMemoryUsage();
    void MonitorPerformanceTrends();
    BOOL DetectPerformanceDegradation();
};

// 安全性能基准管理器
class CSecurityBenchmarkManager
{
public:
    CSecurityBenchmarkManager();
    ~CSecurityBenchmarkManager();

    BOOL Initialize();
    void Cleanup();

    // 基准管理
    void EstablishSecurityBenchmarks();
    void UpdateBenchmarks();
    void CompareToBenchmarks(const PerformanceMetrics& current);

    // 性能阈值
    void SetPerformanceThresholds(float fCPUThreshold, float fMemoryThreshold, 
                                 float fNetworkThreshold, float fResponseTimeThreshold);
    BOOL IsPerformanceAcceptable(const PerformanceMetrics& metrics);

    // 报告生成
    void GenerateBenchmarkReport(const char* pFilename);
    void GeneratePerformanceComparison(const char* pFilename);

private:
    struct SecurityBenchmark
    {
        std::string strSecurityModule;
        PerformanceMetrics expectedMetrics;
        PerformanceMetrics actualMetrics;
        float fAcceptableVariance;
    };

    std::vector<SecurityBenchmark> m_Benchmarks;
    
    // 性能阈值
    float m_fCPUThreshold;
    float m_fMemoryThreshold;
    float m_fNetworkThreshold;
    float m_fResponseTimeThreshold;

    void LoadBenchmarkData();
    void SaveBenchmarkData();
};

// 性能安全测试管理器
class CPerformanceSecurityTestManager
{
public:
    CPerformanceSecurityTestManager();
    ~CPerformanceSecurityTestManager();

    BOOL Initialize();
    void Cleanup();

    // 测试执行
    void RunAllPerformanceSecurityTests();
    void RunBaselineTests();
    void RunOverheadTests();
    void RunStressTests();
    void RunLoadTests();
    void RunEnduranceTests();

    // 测试配置
    void SetTestDuration(DWORD dwDuration) { m_dwTestDuration = dwDuration; }
    void SetMaxConcurrentUsers(DWORD dwMaxUsers) { m_dwMaxConcurrentUsers = dwMaxUsers; }
    void SetAcceptableOverhead(float fMaxOverhead) { m_fAcceptableOverhead = fMaxOverhead; }

    // 结果管理
    void GetTestResults(std::vector<PerformanceTestResult>& results);
    void GeneratePerformanceReport(const char* pFilename);
    void GenerateExecutiveSummary(const char* pFilename);

    // 实时监控
    void StartPerformanceMonitoring();
    void StopPerformanceMonitoring();
    PerformanceMetrics GetCurrentMetrics();

    // 统计信息
    float GetAverageSecurityOverhead() const { return m_fAverageOverhead; }
    BOOL IsPerformanceWithinLimits() const { return m_bPerformanceAcceptable; }

private:
    CBaselineTester m_BaselineTester;
    CSecurityOverheadTester m_OverheadTester;
    CStressSecurityTester m_StressTester;
    CLoadSecurityTester m_LoadTester;
    CEnduranceSecurityTester m_EnduranceTester;
    CSecurityBenchmarkManager m_BenchmarkManager;

    std::vector<PerformanceTestResult> m_TestResults;
    
    DWORD m_dwTestDuration;
    DWORD m_dwMaxConcurrentUsers;
    float m_fAcceptableOverhead;
    BOOL m_bInitialized;
    BOOL m_bMonitoring;

    // 统计数据
    float m_fAverageOverhead;
    BOOL m_bPerformanceAcceptable;
    DWORD m_dwTotalTests;
    DWORD m_dwPassedTests;

    void UpdatePerformanceStatistics(const PerformanceTestResult& result);
    void LogPerformanceData(const PerformanceMetrics& metrics);
    BOOL ValidateTestEnvironment();
};

// 全局性能安全测试管理器
extern CPerformanceSecurityTestManager g_PerformanceSecurityTestManager;

// 性能测试宏定义
#define MEASURE_PERFORMANCE_START() \
    DWORD dwStartTime = GetTickCount()

#define MEASURE_PERFORMANCE_END(operation) \
    DWORD dwEndTime = GetTickCount(); \
    printf("[PERF] %s took %d ms\n", operation, dwEndTime - dwStartTime)

#define ASSERT_PERFORMANCE_ACCEPTABLE(overhead, threshold) \
    do { \
        if (overhead > threshold) { \
            printf("PERFORMANCE WARNING: Overhead %.2f%% exceeds threshold %.2f%%\n", \
                   overhead, threshold); \
        } \
    } while(0)

#define MONITOR_SECURITY_PERFORMANCE(module) \
    CPerformanceMonitor _perfMon(module)

// 性能监控辅助类
class CPerformanceMonitor
{
public:
    CPerformanceMonitor(const char* pModuleName) 
        : m_pModuleName(pModuleName)
    {
        m_dwStartTime = GetTickCount();
        QueryPerformanceCounter(&m_liStartTime);
    }
    
    ~CPerformanceMonitor()
    {
        DWORD dwEndTime = GetTickCount();
        LARGE_INTEGER liEndTime;
        QueryPerformanceCounter(&liEndTime);
        
        DWORD dwElapsed = dwEndTime - m_dwStartTime;
        printf("[SECURITY_PERF] %s: %d ms\n", m_pModuleName, dwElapsed);
    }

private:
    const char* m_pModuleName;
    DWORD m_dwStartTime;
    LARGE_INTEGER m_liStartTime;
};

#endif // PERFORMANCE_SECURITY_TESTER_H
