//---------------------------------------------------------------------------
// Sword2 Skill Manager (c) 2024
//
// File:	SkillManager.h
// Date:	2024.07.02
// Code:	AI Assistant
// Desc:	Comprehensive skill management system
//---------------------------------------------------------------------------
#ifndef SKILL_MANAGER_H
#define SKILL_MANAGER_H

#include "SkillSystem.h"
#include "GameDataSystem.h"
#include "LuaScriptEngine.h"
#include "UnifiedLoggingSystem.h"
#include "BusinessMonitor.h"
#include <unordered_map>
#include <memory>
#include <mutex>
#include <shared_mutex>
#include <thread>
#include <atomic>
#include <queue>
#include <functional>

namespace sword2 {

// 技能事件类型
enum class SkillEvent : uint8_t
{
    Learn = 0,          // 学习技能
    LevelUp,            // 技能升级
    Use,                // 使用技能
    Cooldown,           // 技能冷却
    Forget,             // 遗忘技能
    Enhance,            // 技能强化
    Reset               // 技能重置
};

// 技能使用结果
enum class SkillUseResult : uint8_t
{
    Success = 0,        // 成功
    NotLearned,         // 未学习
    InCooldown,         // 冷却中
    InsufficientMana,   // 内力不足
    InsufficientItem,   // 物品不足
    InvalidTarget,      // 目标无效
    OutOfRange,         // 超出范围
    Disabled,           // 技能禁用
    Failed              // 施放失败
};

// 技能管理器
class SkillManager : public Singleton<SkillManager>
{
public:
    SkillManager()
        : m_running(false), m_nextSkillId(1), m_updateInterval(std::chrono::seconds(1)) {}
    
    ~SkillManager()
    {
        Stop();
    }
    
    // 启动技能管理器
    bool Start()
    {
        if (m_running) return true;
        
        // 加载技能数据
        if (!LoadSkillData())
        {
            LOG_ERROR("SKILL_MGR", "Failed to load skill data");
            return false;
        }
        
        // 加载技能树数据
        if (!LoadSkillTrees())
        {
            LOG_ERROR("SKILL_MGR", "Failed to load skill trees");
            return false;
        }
        
        m_running = true;
        m_updateThread = std::thread(&SkillManager::UpdateLoop, this);
        
        LOG_INFO("SKILL_MGR", "Skill manager started with " + std::to_string(m_skillTemplates.size()) + " skill templates");
        return true;
    }
    
    // 停止技能管理器
    void Stop()
    {
        if (!m_running) return;
        
        m_running = false;
        if (m_updateThread.joinable())
        {
            m_updateThread.join();
        }
        
        // 清理数据
        {
            std::unique_lock<std::shared_mutex> lock(m_templatesMutex);
            m_skillTemplates.clear();
        }
        
        {
            std::unique_lock<std::shared_mutex> lock(m_treesMutex);
            m_skillTrees.clear();
        }
        
        LOG_INFO("SKILL_MGR", "Skill manager stopped");
    }
    
    // 注册技能模板
    bool RegisterSkillTemplate(const SkillTemplate& skillTemplate)
    {
        std::unique_lock<std::shared_mutex> lock(m_templatesMutex);
        
        if (m_skillTemplates.find(skillTemplate.skillId) != m_skillTemplates.end())
        {
            LOG_WARNING("SKILL_MGR", "Skill template already exists: " + std::to_string(skillTemplate.skillId));
            return false;
        }
        
        m_skillTemplates[skillTemplate.skillId] = std::make_shared<SkillTemplate>(skillTemplate);
        
        LOG_DEBUG("SKILL_MGR", "Registered skill template: " + skillTemplate.name + " (ID: " + std::to_string(skillTemplate.skillId) + ")");
        return true;
    }
    
    // 获取技能模板
    std::shared_ptr<SkillTemplate> GetSkillTemplate(uint32_t skillId)
    {
        std::shared_lock<std::shared_mutex> lock(m_templatesMutex);
        auto it = m_skillTemplates.find(skillId);
        return (it != m_skillTemplates.end()) ? it->second : nullptr;
    }
    
    // 获取门派技能树
    std::shared_ptr<FactionSkillTree> GetFactionSkillTree(PlayerSeries faction)
    {
        std::shared_lock<std::shared_mutex> lock(m_treesMutex);
        auto it = m_skillTrees.find(faction);
        return (it != m_skillTrees.end()) ? it->second : nullptr;
    }
    
    // 学习技能
    bool LearnSkill(uint32_t playerId, uint32_t skillId)
    {
        auto skillTemplate = GetSkillTemplate(skillId);
        if (!skillTemplate)
        {
            LOG_WARNING("SKILL_MGR", "Skill template not found: " + std::to_string(skillId));
            return false;
        }
        
        // 这里应该获取玩家对象进行检查
        // 暂时简化处理
        
        // 触发技能学习事件
        TriggerSkillEvent(SkillEvent::Learn, playerId, skillId, 1);
        
        LOG_INFO("SKILL_MGR", "Player " + std::to_string(playerId) + " learned skill " + skillTemplate->name);
        return true;
    }
    
    // 升级技能
    bool LevelUpSkill(uint32_t playerId, uint32_t skillId)
    {
        auto skillTemplate = GetSkillTemplate(skillId);
        if (!skillTemplate)
        {
            LOG_WARNING("SKILL_MGR", "Skill template not found: " + std::to_string(skillId));
            return false;
        }
        
        // 这里应该获取玩家技能进行升级检查
        // 暂时简化处理
        
        // 触发技能升级事件
        TriggerSkillEvent(SkillEvent::LevelUp, playerId, skillId, 0);
        
        LOG_INFO("SKILL_MGR", "Player " + std::to_string(playerId) + " leveled up skill " + skillTemplate->name);
        return true;
    }
    
    // 使用技能
    SkillUseResult UseSkill(uint32_t playerId, uint32_t skillId, uint32_t targetId = 0, int32_t x = 0, int32_t y = 0)
    {
        auto skillTemplate = GetSkillTemplate(skillId);
        if (!skillTemplate)
        {
            LOG_WARNING("SKILL_MGR", "Skill template not found: " + std::to_string(skillId));
            return SkillUseResult::NotLearned;
        }
        
        // 这里应该进行完整的技能使用检查
        // 1. 检查玩家是否学会该技能
        // 2. 检查技能是否在冷却中
        // 3. 检查消耗是否足够
        // 4. 检查目标是否有效
        // 5. 检查距离是否足够
        // 暂时简化处理
        
        // 触发技能使用事件
        TriggerSkillEvent(SkillEvent::Use, playerId, skillId, 0);
        
        LOG_INFO("SKILL_MGR", "Player " + std::to_string(playerId) + " used skill " + skillTemplate->name);
        return SkillUseResult::Success;
    }
    
    // 遗忘技能
    bool ForgetSkill(uint32_t playerId, uint32_t skillId)
    {
        auto skillTemplate = GetSkillTemplate(skillId);
        if (!skillTemplate)
        {
            LOG_WARNING("SKILL_MGR", "Skill template not found: " + std::to_string(skillId));
            return false;
        }
        
        // 触发技能遗忘事件
        TriggerSkillEvent(SkillEvent::Forget, playerId, skillId, 0);
        
        LOG_INFO("SKILL_MGR", "Player " + std::to_string(playerId) + " forgot skill " + skillTemplate->name);
        return true;
    }
    
    // 重置技能
    bool ResetSkills(uint32_t playerId, PlayerSeries faction = PlayerSeries::None)
    {
        // 这里应该重置玩家的所有技能或指定门派的技能
        // 暂时简化处理
        
        // 触发技能重置事件
        TriggerSkillEvent(SkillEvent::Reset, playerId, 0, 0);
        
        LOG_INFO("SKILL_MGR", "Reset skills for player " + std::to_string(playerId));
        return true;
    }
    
    // 获取技能列表（按门派）
    std::vector<uint32_t> GetFactionSkills(PlayerSeries faction)
    {
        std::vector<uint32_t> skills;
        
        std::shared_lock<std::shared_mutex> lock(m_templatesMutex);
        for (const auto& [skillId, skillTemplate] : m_skillTemplates)
        {
            if (skillTemplate && skillTemplate->requirement.requiredSeries == faction)
            {
                skills.push_back(skillId);
            }
        }
        
        return skills;
    }
    
    // 获取技能列表（按系列）
    std::vector<uint32_t> GetSeriesSkills(SkillSeries series)
    {
        std::vector<uint32_t> skills;
        
        std::shared_lock<std::shared_mutex> lock(m_templatesMutex);
        for (const auto& [skillId, skillTemplate] : m_skillTemplates)
        {
            if (skillTemplate && skillTemplate->series == series)
            {
                skills.push_back(skillId);
            }
        }
        
        return skills;
    }
    
    // 获取技能列表（按类型）
    std::vector<uint32_t> GetTypeSkills(SkillType type)
    {
        std::vector<uint32_t> skills;
        
        std::shared_lock<std::shared_mutex> lock(m_templatesMutex);
        for (const auto& [skillId, skillTemplate] : m_skillTemplates)
        {
            if (skillTemplate && skillTemplate->type == type)
            {
                skills.push_back(skillId);
            }
        }
        
        return skills;
    }
    
    // 计算技能伤害
    uint32_t CalculateSkillDamage(uint32_t skillId, uint32_t level, const Player& caster, const Player* target = nullptr)
    {
        auto skillTemplate = GetSkillTemplate(skillId);
        if (!skillTemplate) return 0;
        
        const auto* levelData = skillTemplate->GetLevelData(level);
        if (!levelData) return 0;
        
        // 基础伤害
        uint32_t baseDamage = levelData->damage;
        
        // 属性加成计算
        // 这里应该根据技能脚本中的公式进行计算
        // 暂时简化处理
        uint32_t attributeBonus = caster.attributes.strength / 10 + caster.attributes.energy / 10;
        
        // 等级加成
        uint32_t levelBonus = level * 5;
        
        // 系列加成
        uint32_t seriesBonus = 0;
        // 这里可以根据玩家的系列技能等级进行加成
        
        uint32_t totalDamage = baseDamage + attributeBonus + levelBonus + seriesBonus;
        
        LOG_DEBUG("SKILL_MGR", "Calculated skill damage: " + std::to_string(totalDamage) + 
                 " (base: " + std::to_string(baseDamage) + ", attr: " + std::to_string(attributeBonus) + 
                 ", level: " + std::to_string(levelBonus) + ", series: " + std::to_string(seriesBonus) + ")");
        
        return totalDamage;
    }
    
    // 检查技能学习条件
    bool CheckLearnRequirement(uint32_t skillId, const Player& player)
    {
        auto skillTemplate = GetSkillTemplate(skillId);
        if (!skillTemplate) return false;
        
        return skillTemplate->CanLearn(player);
    }
    
    // 获取技能升级所需经验
    uint32_t GetSkillUpgradeExp(uint32_t skillId, uint32_t currentLevel)
    {
        auto skillTemplate = GetSkillTemplate(skillId);
        if (!skillTemplate) return 0;
        
        const auto* levelData = skillTemplate->GetLevelData(currentLevel);
        if (!levelData) return 0;
        
        return levelData->nextLevelExp;
    }
    
    // 获取技能统计信息
    struct SkillStatistics
    {
        size_t totalSkills = 0;
        size_t activeSkills = 0;
        size_t passiveSkills = 0;
        std::unordered_map<SkillSeries, size_t> skillsBySeries;
        std::unordered_map<SkillType, size_t> skillsByType;
        std::unordered_map<PlayerSeries, size_t> skillsByFaction;
    };
    
    SkillStatistics GetStatistics()
    {
        std::shared_lock<std::shared_mutex> lock(m_templatesMutex);
        
        SkillStatistics stats;
        stats.totalSkills = m_skillTemplates.size();
        
        for (const auto& [skillId, skillTemplate] : m_skillTemplates)
        {
            if (!skillTemplate) continue;
            
            if (skillTemplate->type == SkillType::Active)
                stats.activeSkills++;
            else if (skillTemplate->type == SkillType::Passive)
                stats.passiveSkills++;
            
            stats.skillsBySeries[skillTemplate->series]++;
            stats.skillsByType[skillTemplate->type]++;
            stats.skillsByFaction[skillTemplate->requirement.requiredSeries]++;
        }
        
        return stats;
    }
    
    // 设置更新间隔
    void SetUpdateInterval(std::chrono::seconds interval)
    {
        m_updateInterval = interval;
        LOG_INFO("SKILL_MGR", "Skill update interval set to " + std::to_string(interval.count()) + " seconds");
    }

private:
    std::atomic<bool> m_running;
    std::thread m_updateThread;
    std::chrono::seconds m_updateInterval;
    
    // 技能数据
    mutable std::shared_mutex m_templatesMutex;
    std::unordered_map<uint32_t, std::shared_ptr<SkillTemplate>> m_skillTemplates;
    
    // 技能树数据
    mutable std::shared_mutex m_treesMutex;
    std::unordered_map<PlayerSeries, std::shared_ptr<FactionSkillTree>> m_skillTrees;
    
    std::atomic<uint32_t> m_nextSkillId;
    
    // 更新循环
    void UpdateLoop()
    {
        while (m_running)
        {
            try
            {
                UpdateSkillCooldowns();
                std::this_thread::sleep_for(m_updateInterval);
            }
            catch (const std::exception& e)
            {
                LOG_ERROR("SKILL_MGR", std::string("Error in update loop: ") + e.what());
            }
        }
    }
    
    void UpdateSkillCooldowns()
    {
        // 这里应该更新所有玩家的技能冷却状态
        // 暂时简化处理
    }
    
    bool LoadSkillData()
    {
        LOG_INFO("SKILL_MGR", "Loading skill data...");
        
        // 从GameDataSystem加载技能数据
        // 这里应该加载技能配置文件
        // 暂时创建一些示例技能
        
        CreateExampleSkills();
        
        LOG_INFO("SKILL_MGR", "Loaded " + std::to_string(m_skillTemplates.size()) + " skill templates");
        return true;
    }
    
    bool LoadSkillTrees()
    {
        LOG_INFO("SKILL_MGR", "Loading skill trees...");
        
        // 创建各门派的技能树
        CreateFactionSkillTrees();
        
        LOG_INFO("SKILL_MGR", "Loaded " + std::to_string(m_skillTrees.size()) + " faction skill trees");
        return true;
    }
    
    void CreateExampleSkills()
    {
        std::unique_lock<std::shared_mutex> lock(m_templatesMutex);
        
        // 少林技能示例
        {
            SkillTemplate skill(1001, "基础拳法", SkillType::Active);
            skill.description = "少林基础拳法，威力一般但消耗较少";
            skill.series = SkillSeries::Metal;
            skill.style = SkillStyle::Melee;
            skill.targetType = SkillTargetType::SingleEnemy;
            skill.requirement.requiredSeries = PlayerSeries::Shaolin;
            skill.requirement.requiredLevel = 1;
            skill.isBaseSkill = true;
            skill.maxLevel = 10;
            
            // 设置等级数据
            for (uint32_t i = 1; i <= skill.maxLevel; ++i)
            {
                SkillLevelData levelData;
                levelData.level = i;
                levelData.damage = 50 + i * 10;
                levelData.range = 100;
                levelData.cooldown = 1000;
                levelData.costs.emplace_back(SkillCostType::Mana, 10 + i * 2);
                levelData.effects.emplace_back(SkillEffectType::Damage, levelData.damage);
                skill.levelData.push_back(levelData);
            }
            
            m_skillTemplates[skill.skillId] = std::make_shared<SkillTemplate>(skill);
        }
        
        // 武当技能示例
        {
            SkillTemplate skill(2001, "太极拳", SkillType::Active);
            skill.description = "武当太极拳，攻守兼备";
            skill.series = SkillSeries::Water;
            skill.style = SkillStyle::Melee;
            skill.targetType = SkillTargetType::SingleEnemy;
            skill.requirement.requiredSeries = PlayerSeries::Wudang;
            skill.requirement.requiredLevel = 5;
            skill.maxLevel = 20;
            
            // 设置等级数据
            for (uint32_t i = 1; i <= skill.maxLevel; ++i)
            {
                SkillLevelData levelData;
                levelData.level = i;
                levelData.damage = 40 + i * 8;
                levelData.range = 120;
                levelData.cooldown = 1500;
                levelData.costs.emplace_back(SkillCostType::Mana, 15 + i * 3);
                levelData.effects.emplace_back(SkillEffectType::Damage, levelData.damage);
                levelData.effects.emplace_back(SkillEffectType::Buff, 10, 5000); // 5秒防御加成
                skill.levelData.push_back(levelData);
            }
            
            m_skillTemplates[skill.skillId] = std::make_shared<SkillTemplate>(skill);
        }
        
        // 峨眉技能示例
        {
            SkillTemplate skill(3001, "回春术", SkillType::Active);
            skill.description = "峨眉治疗术，恢复生命值";
            skill.series = SkillSeries::Wood;
            skill.style = SkillStyle::Magic;
            skill.targetType = SkillTargetType::SingleAlly;
            skill.requirement.requiredSeries = PlayerSeries::Emei;
            skill.requirement.requiredLevel = 3;
            skill.maxLevel = 15;
            
            // 设置等级数据
            for (uint32_t i = 1; i <= skill.maxLevel; ++i)
            {
                SkillLevelData levelData;
                levelData.level = i;
                levelData.damage = 0; // 治疗技能无伤害
                levelData.range = 200;
                levelData.cooldown = 2000;
                levelData.costs.emplace_back(SkillCostType::Mana, 20 + i * 4);
                levelData.effects.emplace_back(SkillEffectType::Heal, 80 + i * 15);
                skill.levelData.push_back(levelData);
            }
            
            m_skillTemplates[skill.skillId] = std::make_shared<SkillTemplate>(skill);
        }
    }
    
    void CreateFactionSkillTrees()
    {
        std::unique_lock<std::shared_mutex> lock(m_treesMutex);
        
        // 少林技能树
        {
            auto tree = std::make_shared<FactionSkillTree>(PlayerSeries::Shaolin, "少林");
            
            // 第一层：基础技能
            SkillTreeNode node1(1001, 1, 0);
            tree->AddSkillNode(node1);
            
            // 第二层：进阶技能
            SkillTreeNode node2(1002, 2, 0);
            node2.prerequisites.push_back(1001);
            tree->AddSkillNode(node2);
            
            m_skillTrees[PlayerSeries::Shaolin] = tree;
        }
        
        // 武当技能树
        {
            auto tree = std::make_shared<FactionSkillTree>(PlayerSeries::Wudang, "武当");
            
            SkillTreeNode node1(2001, 1, 0);
            tree->AddSkillNode(node1);
            
            m_skillTrees[PlayerSeries::Wudang] = tree;
        }
        
        // 峨眉技能树
        {
            auto tree = std::make_shared<FactionSkillTree>(PlayerSeries::Emei, "峨眉");
            
            SkillTreeNode node1(3001, 1, 0);
            tree->AddSkillNode(node1);
            
            m_skillTrees[PlayerSeries::Emei] = tree;
        }
    }
    
    void TriggerSkillEvent(SkillEvent event, uint32_t playerId, uint32_t skillId, uint32_t level)
    {
        // 这里可以触发脚本事件或其他处理
        std::string eventName = GetEventName(event);
        LOG_DEBUG("SKILL_MGR", "Skill event triggered: " + eventName + " for player " + std::to_string(playerId) + 
                 ", skill " + std::to_string(skillId));
        
        // 可以在这里执行脚本或其他逻辑
    }
    
    std::string GetEventName(SkillEvent event)
    {
        switch (event)
        {
        case SkillEvent::Learn: return "Learn";
        case SkillEvent::LevelUp: return "LevelUp";
        case SkillEvent::Use: return "Use";
        case SkillEvent::Cooldown: return "Cooldown";
        case SkillEvent::Forget: return "Forget";
        case SkillEvent::Enhance: return "Enhance";
        case SkillEvent::Reset: return "Reset";
        default: return "Unknown";
        }
    }
};

} // namespace sword2

// 全局技能管理器访问
#define SKILL_MANAGER() sword2::SkillManager::getInstance()

// 便捷宏定义
#define START_SKILL_SYSTEM() SKILL_MANAGER().Start()
#define STOP_SKILL_SYSTEM() SKILL_MANAGER().Stop()

#define REGISTER_SKILL_TEMPLATE(skillTemplate) SKILL_MANAGER().RegisterSkillTemplate(skillTemplate)
#define GET_SKILL_TEMPLATE(skillId) SKILL_MANAGER().GetSkillTemplate(skillId)

#define LEARN_SKILL(playerId, skillId) SKILL_MANAGER().LearnSkill(playerId, skillId)
#define LEVELUP_SKILL(playerId, skillId) SKILL_MANAGER().LevelUpSkill(playerId, skillId)
#define USE_SKILL(playerId, skillId, targetId, x, y) SKILL_MANAGER().UseSkill(playerId, skillId, targetId, x, y)
#define FORGET_SKILL(playerId, skillId) SKILL_MANAGER().ForgetSkill(playerId, skillId)

#define GET_FACTION_SKILLS(faction) SKILL_MANAGER().GetFactionSkills(faction)
#define GET_SERIES_SKILLS(series) SKILL_MANAGER().GetSeriesSkills(series)
#define GET_TYPE_SKILLS(type) SKILL_MANAGER().GetTypeSkills(type)

#define CALCULATE_SKILL_DAMAGE(skillId, level, caster, target) SKILL_MANAGER().CalculateSkillDamage(skillId, level, caster, target)

#endif // SKILL_MANAGER_H
