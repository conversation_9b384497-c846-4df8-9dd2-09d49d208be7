//---------------------------------------------------------------------------
// Sword2 Map System (c) 2024
//
// File:	MapSystem.h
// Date:	2024.07.02
// Code:	AI Assistant
// Desc:	Comprehensive map and scene management system for Sword2
//---------------------------------------------------------------------------
#ifndef MAP_SYSTEM_H
#define MAP_SYSTEM_H

#include "ModernCpp.h"
#include "UnifiedLoggingSystem.h"
#include "MapDataParser.h"
#include "PlayerSystem.h"
#include "NPCSystem.h"
#include <string>
#include <unordered_map>
#include <vector>
#include <memory>
#include <chrono>
#include <atomic>
#include <mutex>
#include <functional>

namespace sword2 {

// 场景对象类型
enum class SceneObjectType : uint8_t
{
    Static = 0,         // 静态对象
    Interactive,        // 可交互对象
    Trigger,            // 触发器
    Portal,             // 传送门
    Resource,           // 资源点
    Decoration,         // 装饰物
    Barrier,            // 障碍物
    Trap                // 陷阱
};

// 场景对象状态
enum class SceneObjectStatus : uint8_t
{
    Active = 0,         // 激活
    Inactive,           // 未激活
    Destroyed,          // 已摧毁
    Respawning          // 重生中
};

// 触发器类型
enum class TriggerType : uint8_t
{
    OnEnter = 0,        // 进入触发
    OnLeave,            // 离开触发
    OnUse,              // 使用触发
    OnTimer,            // 定时触发
    OnCondition,        // 条件触发
    OnKill              // 击杀触发
};

// 场景对象基类
class SceneObject
{
public:
    uint32_t objectId = 0;          // 对象ID
    uint32_t templateId = 0;        // 模板ID
    std::string name;               // 对象名称
    std::string description;        // 描述
    SceneObjectType type = SceneObjectType::Static; // 对象类型
    SceneObjectStatus status = SceneObjectStatus::Active; // 状态
    
    // 位置信息
    uint32_t mapId = 0;             // 地图ID
    int32_t x = 0;                  // X坐标
    int32_t y = 0;                  // Y坐标
    uint32_t direction = 0;         // 朝向
    
    // 碰撞信息
    int32_t width = 32;             // 宽度
    int32_t height = 32;            // 高度
    bool isCollidable = true;       // 是否可碰撞
    
    // 脚本信息
    std::string scriptPath;         // 脚本路径
    std::unordered_map<std::string, std::string> scriptFunctions; // 脚本函数
    
    // 时间信息
    std::chrono::system_clock::time_point createTime; // 创建时间
    std::chrono::system_clock::time_point lastUpdateTime; // 最后更新时间
    uint32_t respawnTime = 0;       // 重生时间(秒)
    
    SceneObject() = default;
    SceneObject(uint32_t id, const std::string& objName, SceneObjectType objType)
        : objectId(id), name(objName), type(objType)
    {
        createTime = lastUpdateTime = std::chrono::system_clock::now();
    }
    
    virtual ~SceneObject() = default;
    
    // 设置位置
    void SetPosition(uint32_t map, int32_t posX, int32_t posY, uint32_t dir = 0)
    {
        mapId = map;
        x = posX;
        y = posY;
        direction = dir;
    }
    
    // 检查碰撞
    bool CheckCollision(int32_t targetX, int32_t targetY, int32_t targetWidth = 32, int32_t targetHeight = 32) const
    {
        if (!isCollidable) return false;
        
        return !(targetX >= x + width || targetX + targetWidth <= x ||
                targetY >= y + height || targetY + targetHeight <= y);
    }
    
    // 计算距离
    double DistanceTo(int32_t targetX, int32_t targetY) const
    {
        int32_t dx = x - targetX;
        int32_t dy = y - targetY;
        return std::sqrt(dx * dx + dy * dy);
    }
    
    // 检查是否在范围内
    bool IsInRange(int32_t targetX, int32_t targetY, double range) const
    {
        return DistanceTo(targetX, targetY) <= range;
    }
    
    // 更新对象
    virtual void Update()
    {
        lastUpdateTime = std::chrono::system_clock::now();
    }
    
    // 激活对象
    virtual void Activate()
    {
        status = SceneObjectStatus::Active;
    }
    
    // 停用对象
    virtual void Deactivate()
    {
        status = SceneObjectStatus::Inactive;
    }
    
    // 摧毁对象
    virtual void Destroy()
    {
        status = SceneObjectStatus::Destroyed;
    }
    
    // 设置脚本函数
    void SetScriptFunction(const std::string& event, const std::string& functionName)
    {
        scriptFunctions[event] = functionName;
    }
    
    // 获取脚本函数
    std::string GetScriptFunction(const std::string& event) const
    {
        auto it = scriptFunctions.find(event);
        return (it != scriptFunctions.end()) ? it->second : "";
    }
    
    // 获取类型描述
    std::string GetTypeDescription() const
    {
        switch (type)
        {
        case SceneObjectType::Static: return "静态对象";
        case SceneObjectType::Interactive: return "可交互对象";
        case SceneObjectType::Trigger: return "触发器";
        case SceneObjectType::Portal: return "传送门";
        case SceneObjectType::Resource: return "资源点";
        case SceneObjectType::Decoration: return "装饰物";
        case SceneObjectType::Barrier: return "障碍物";
        case SceneObjectType::Trap: return "陷阱";
        default: return "未知";
        }
    }
    
    // 获取状态描述
    std::string GetStatusDescription() const
    {
        switch (status)
        {
        case SceneObjectStatus::Active: return "激活";
        case SceneObjectStatus::Inactive: return "未激活";
        case SceneObjectStatus::Destroyed: return "已摧毁";
        case SceneObjectStatus::Respawning: return "重生中";
        default: return "未知";
        }
    }
};

// 触发器对象
class TriggerObject : public SceneObject
{
public:
    TriggerType triggerType = TriggerType::OnEnter; // 触发类型
    uint32_t triggerRange = 50;     // 触发范围
    uint32_t cooldownTime = 0;      // 冷却时间(毫秒)
    std::chrono::system_clock::time_point lastTriggerTime; // 最后触发时间
    
    // 触发条件
    std::string condition;          // 条件表达式
    uint32_t requiredLevel = 0;     // 需求等级
    uint32_t requiredItem = 0;      // 需求物品
    bool requiresParty = false;     // 是否需要组队
    
    // 触发效果
    std::string effectScript;       // 效果脚本
    std::vector<uint32_t> rewardItems; // 奖励物品
    uint32_t rewardExp = 0;         // 奖励经验
    uint32_t rewardMoney = 0;       // 奖励金钱
    
    TriggerObject() : SceneObject()
    {
        type = SceneObjectType::Trigger;
        isCollidable = false;
    }
    
    TriggerObject(uint32_t id, const std::string& name, TriggerType trigType)
        : SceneObject(id, name, SceneObjectType::Trigger), triggerType(trigType)
    {
        isCollidable = false;
    }
    
    // 检查是否可以触发
    bool CanTrigger(const Player& player) const
    {
        if (status != SceneObjectStatus::Active)
            return false;
        
        // 检查冷却时间
        if (cooldownTime > 0)
        {
            auto now = std::chrono::system_clock::now();
            auto elapsed = std::chrono::duration_cast<std::chrono::milliseconds>(now - lastTriggerTime);
            if (elapsed.count() < cooldownTime)
                return false;
        }
        
        // 检查等级要求
        if (requiredLevel > 0 && player.level < requiredLevel)
            return false;
        
        // 检查物品要求
        if (requiredItem > 0)
        {
            // 这里应该检查玩家是否拥有指定物品
            // 暂时返回true
        }
        
        // 检查组队要求
        if (requiresParty)
        {
            // 这里应该检查玩家是否在队伍中
            // 暂时返回true
        }
        
        return true;
    }
    
    // 触发效果
    bool Trigger(uint32_t playerId)
    {
        if (status != SceneObjectStatus::Active)
            return false;
        
        lastTriggerTime = std::chrono::system_clock::now();
        
        // 执行脚本效果
        if (!effectScript.empty())
        {
            // 这里应该执行脚本
            LOG_DEBUG("TRIGGER", "Executing trigger script: " + effectScript);
        }
        
        // 给予奖励
        if (rewardExp > 0 || rewardMoney > 0 || !rewardItems.empty())
        {
            LOG_INFO("TRIGGER", "Player " + std::to_string(playerId) + " triggered " + name + 
                    " and received rewards");
        }
        
        return true;
    }
};

// 传送门对象
class PortalObject : public SceneObject
{
public:
    uint32_t targetMapId = 0;       // 目标地图ID
    int32_t targetX = 0;            // 目标X坐标
    int32_t targetY = 0;            // 目标Y坐标
    uint32_t requiredLevel = 1;     // 需求等级
    uint32_t cost = 0;              // 传送费用
    bool isActive = true;           // 是否激活
    
    PortalObject() : SceneObject()
    {
        type = SceneObjectType::Portal;
    }
    
    PortalObject(uint32_t id, const std::string& name, uint32_t targetMap, int32_t targetPosX, int32_t targetPosY)
        : SceneObject(id, name, SceneObjectType::Portal), targetMapId(targetMap), targetX(targetPosX), targetY(targetPosY)
    {
    }
    
    // 检查玩家是否可以使用传送门
    bool CanUse(const Player& player) const
    {
        if (!isActive || status != SceneObjectStatus::Active)
            return false;
        
        if (player.level < requiredLevel)
            return false;
        
        if (cost > 0 && player.money < cost)
            return false;
        
        return true;
    }
    
    // 使用传送门
    bool Use(Player& player)
    {
        if (!CanUse(player))
            return false;
        
        // 扣除费用
        if (cost > 0)
        {
            player.money -= cost;
        }
        
        // 传送玩家
        player.TeleportTo(targetMapId, targetX, targetY);
        
        LOG_INFO("PORTAL", "Player " + player.playerName + " used portal " + name + 
                " to map " + std::to_string(targetMapId));
        
        return true;
    }
};

// 资源点对象
class ResourceObject : public SceneObject
{
public:
    std::string resourceType;       // 资源类型
    uint32_t maxQuantity = 100;     // 最大数量
    uint32_t currentQuantity = 100; // 当前数量
    uint32_t respawnTime = 300;     // 重生时间(秒)
    std::chrono::system_clock::time_point lastHarvestTime; // 最后采集时间
    
    // 采集要求
    uint32_t requiredSkill = 0;     // 需求技能
    uint32_t requiredLevel = 1;     // 需求等级
    uint32_t requiredTool = 0;      // 需求工具
    
    // 掉落物品
    std::vector<std::pair<uint32_t, uint32_t>> dropItems; // 物品ID和数量
    
    ResourceObject() : SceneObject()
    {
        type = SceneObjectType::Resource;
    }
    
    ResourceObject(uint32_t id, const std::string& name, const std::string& resType)
        : SceneObject(id, name, SceneObjectType::Resource), resourceType(resType)
    {
    }
    
    // 检查是否可以采集
    bool CanHarvest(const Player& player) const
    {
        if (status != SceneObjectStatus::Active || currentQuantity == 0)
            return false;
        
        if (player.level < requiredLevel)
            return false;
        
        // 检查技能要求
        if (requiredSkill > 0)
        {
            // 这里应该检查玩家技能等级
            // 暂时返回true
        }
        
        // 检查工具要求
        if (requiredTool > 0)
        {
            // 这里应该检查玩家是否拥有工具
            // 暂时返回true
        }
        
        return true;
    }
    
    // 采集资源
    std::vector<std::pair<uint32_t, uint32_t>> Harvest(const Player& player)
    {
        std::vector<std::pair<uint32_t, uint32_t>> harvested;
        
        if (!CanHarvest(player))
            return harvested;
        
        // 减少资源数量
        uint32_t harvestAmount = std::min(currentQuantity, uint32_t(10)); // 每次最多采集10个
        currentQuantity -= harvestAmount;
        
        // 生成掉落物品
        for (const auto& [itemId, baseCount] : dropItems)
        {
            uint32_t count = baseCount * harvestAmount / 10; // 按比例计算
            if (count > 0)
            {
                harvested.emplace_back(itemId, count);
            }
        }
        
        lastHarvestTime = std::chrono::system_clock::now();
        
        // 如果资源耗尽，开始重生
        if (currentQuantity == 0)
        {
            status = SceneObjectStatus::Respawning;
        }
        
        LOG_INFO("RESOURCE", "Player " + player.playerName + " harvested " + 
                std::to_string(harvestAmount) + " " + resourceType);
        
        return harvested;
    }
    
    // 更新资源状态
    void Update() override
    {
        SceneObject::Update();
        
        // 检查重生
        if (status == SceneObjectStatus::Respawning)
        {
            auto now = std::chrono::system_clock::now();
            auto elapsed = std::chrono::duration_cast<std::chrono::seconds>(now - lastHarvestTime);
            
            if (elapsed.count() >= respawnTime)
            {
                currentQuantity = maxQuantity;
                status = SceneObjectStatus::Active;
                LOG_DEBUG("RESOURCE", "Resource " + name + " respawned");
            }
        }
    }
};

// 地图实例
class MapInstance
{
public:
    uint32_t instanceId = 0;        // 实例ID
    uint32_t mapId = 0;             // 地图ID
    std::string mapName;            // 地图名称
    EnhancedMapData mapData;        // 地图数据
    
    // 实例状态
    bool isActive = true;           // 是否激活
    std::chrono::system_clock::time_point createTime; // 创建时间
    std::chrono::system_clock::time_point lastUpdateTime; // 最后更新时间
    uint32_t timeLimit = 0;         // 时间限制(秒)
    
    // 玩家管理
    std::unordered_set<uint32_t> players; // 玩家列表
    uint32_t maxPlayers = 100;      // 最大玩家数
    
    // 场景对象
    std::unordered_map<uint32_t, std::shared_ptr<SceneObject>> sceneObjects;
    std::unordered_map<uint32_t, std::shared_ptr<TriggerObject>> triggers;
    std::unordered_map<uint32_t, std::shared_ptr<PortalObject>> portals;
    std::unordered_map<uint32_t, std::shared_ptr<ResourceObject>> resources;
    
    // NPC管理
    std::unordered_set<uint32_t> npcs; // NPC列表
    
    MapInstance() = default;
    MapInstance(uint32_t instId, uint32_t mapDataId, const EnhancedMapData& data)
        : instanceId(instId), mapId(mapDataId), mapData(data)
    {
        mapName = data.name;
        maxPlayers = data.maxPlayers;
        timeLimit = data.timeLimit;
        createTime = lastUpdateTime = std::chrono::system_clock::now();
    }
    
    // 添加玩家
    bool AddPlayer(uint32_t playerId)
    {
        if (players.size() >= maxPlayers)
            return false;
        
        players.insert(playerId);
        LOG_DEBUG("MAP", "Player " + std::to_string(playerId) + " entered map instance " + std::to_string(instanceId));
        return true;
    }
    
    // 移除玩家
    bool RemovePlayer(uint32_t playerId)
    {
        auto it = players.find(playerId);
        if (it != players.end())
        {
            players.erase(it);
            LOG_DEBUG("MAP", "Player " + std::to_string(playerId) + " left map instance " + std::to_string(instanceId));
            return true;
        }
        return false;
    }
    
    // 检查玩家是否在地图中
    bool HasPlayer(uint32_t playerId) const
    {
        return players.find(playerId) != players.end();
    }
    
    // 获取玩家数量
    size_t GetPlayerCount() const
    {
        return players.size();
    }
    
    // 添加场景对象
    bool AddSceneObject(std::shared_ptr<SceneObject> object)
    {
        if (!object) return false;
        
        object->mapId = mapId;
        sceneObjects[object->objectId] = object;
        
        // 根据类型添加到特定容器
        switch (object->type)
        {
        case SceneObjectType::Trigger:
            if (auto trigger = std::dynamic_pointer_cast<TriggerObject>(object))
            {
                triggers[object->objectId] = trigger;
            }
            break;
        case SceneObjectType::Portal:
            if (auto portal = std::dynamic_pointer_cast<PortalObject>(object))
            {
                portals[object->objectId] = portal;
            }
            break;
        case SceneObjectType::Resource:
            if (auto resource = std::dynamic_pointer_cast<ResourceObject>(object))
            {
                resources[object->objectId] = resource;
            }
            break;
        default:
            break;
        }
        
        LOG_DEBUG("MAP", "Added scene object " + object->name + " to map instance " + std::to_string(instanceId));
        return true;
    }
    
    // 移除场景对象
    bool RemoveSceneObject(uint32_t objectId)
    {
        auto it = sceneObjects.find(objectId);
        if (it != sceneObjects.end())
        {
            // 从特定容器中移除
            triggers.erase(objectId);
            portals.erase(objectId);
            resources.erase(objectId);
            
            sceneObjects.erase(it);
            LOG_DEBUG("MAP", "Removed scene object " + std::to_string(objectId) + " from map instance " + std::to_string(instanceId));
            return true;
        }
        return false;
    }
    
    // 获取场景对象
    std::shared_ptr<SceneObject> GetSceneObject(uint32_t objectId)
    {
        auto it = sceneObjects.find(objectId);
        return (it != sceneObjects.end()) ? it->second : nullptr;
    }
    
    // 获取范围内的场景对象
    std::vector<std::shared_ptr<SceneObject>> GetSceneObjectsInRange(int32_t x, int32_t y, double range)
    {
        std::vector<std::shared_ptr<SceneObject>> result;
        
        for (const auto& [objectId, object] : sceneObjects)
        {
            if (object && object->IsInRange(x, y, range))
            {
                result.push_back(object);
            }
        }
        
        return result;
    }
    
    // 检查碰撞
    bool CheckCollision(int32_t x, int32_t y, int32_t width = 32, int32_t height = 32)
    {
        for (const auto& [objectId, object] : sceneObjects)
        {
            if (object && object->CheckCollision(x, y, width, height))
            {
                return true;
            }
        }
        return false;
    }
    
    // 更新地图实例
    void Update()
    {
        lastUpdateTime = std::chrono::system_clock::now();
        
        // 检查时间限制
        if (timeLimit > 0)
        {
            auto elapsed = std::chrono::duration_cast<std::chrono::seconds>(lastUpdateTime - createTime);
            if (elapsed.count() >= timeLimit)
            {
                // 地图时间到期，应该清理玩家
                LOG_INFO("MAP", "Map instance " + std::to_string(instanceId) + " time limit reached");
            }
        }
        
        // 更新所有场景对象
        for (const auto& [objectId, object] : sceneObjects)
        {
            if (object)
            {
                object->Update();
            }
        }
    }
    
    // 检查是否应该销毁实例
    bool ShouldDestroy() const
    {
        // 如果没有玩家且超过一定时间，可以销毁实例
        if (players.empty())
        {
            auto now = std::chrono::system_clock::now();
            auto elapsed = std::chrono::duration_cast<std::chrono::minutes>(now - lastUpdateTime);
            return elapsed.count() >= 10; // 10分钟无人则销毁
        }
        
        return false;
    }
};

} // namespace sword2

#endif // MAP_SYSTEM_H
