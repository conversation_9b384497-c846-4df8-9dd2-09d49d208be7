//---------------------------------------------------------------------------
// Sword2 Adapter Pattern Implementation (c) 2024
//
// File:	AdapterPattern.h
// Date:	2024.07.02
// Code:	AI Assistant
// Desc:	Modern C++ adapter pattern for interface adaptation and compatibility
//---------------------------------------------------------------------------
#ifndef ADAPTER_PATTERN_H
#define ADAPTER_PATTERN_H

#include "ModernCpp.h"
#include <memory>
#include <functional>
#include <type_traits>

namespace sword2 {

// 通用适配器基类
template<typename TargetInterface, typename Adaptee>
class IAdapter : public TargetInterface
{
public:
    explicit IAdapter(std::unique_ptr<Adaptee> adaptee)
        : m_adaptee(std::move(adaptee)) {}
    
    explicit IAdapter(Adaptee* adaptee)
        : m_adapteePtr(adaptee) {}

protected:
    std::unique_ptr<Adaptee> m_adaptee;
    Adaptee* m_adapteePtr = nullptr;
    
    Adaptee* GetAdaptee() const
    {
        return m_adaptee ? m_adaptee.get() : m_adapteePtr;
    }
};

// 函数式适配器
template<typename TargetInterface, typename Adaptee, typename AdapterFunc>
class FunctionAdapter : public IAdapter<TargetInterface, Adaptee>
{
public:
    FunctionAdapter(std::unique_ptr<Adaptee> adaptee, AdapterFunc adapterFunc)
        : IAdapter<TargetInterface, Adaptee>(std::move(adaptee)), m_adapterFunc(std::move(adapterFunc)) {}
    
    // 这里需要根据具体接口实现相应的方法
    // 使用m_adapterFunc来适配方法调用

private:
    AdapterFunc m_adapterFunc;
};

// 现代音频接口
class IModernAudioPlayer
{
public:
    virtual ~IModernAudioPlayer() = default;
    virtual bool Play(const std::string& filename) = 0;
    virtual void Stop() = 0;
    virtual void Pause() = 0;
    virtual void Resume() = 0;
    virtual void SetVolume(float volume) = 0;
    virtual float GetVolume() const = 0;
    virtual bool IsPlaying() const = 0;
};

// 旧版音频系统
class LegacyAudioSystem
{
public:
    void StartSound(const char* soundFile)
    {
        printf("[LEGACY] Starting sound: %s\n", soundFile);
        m_isPlaying = true;
    }
    
    void StopSound()
    {
        printf("[LEGACY] Stopping sound\n");
        m_isPlaying = false;
    }
    
    void PauseSound()
    {
        printf("[LEGACY] Pausing sound\n");
        m_isPaused = true;
    }
    
    void ResumeSound()
    {
        printf("[LEGACY] Resuming sound\n");
        m_isPaused = false;
    }
    
    void SetSoundVolume(int vol) // 0-100
    {
        printf("[LEGACY] Setting volume to: %d\n", vol);
        m_volume = vol;
    }
    
    int GetSoundVolume() const { return m_volume; }
    bool IsSoundPlaying() const { return m_isPlaying && !m_isPaused; }

private:
    bool m_isPlaying = false;
    bool m_isPaused = false;
    int m_volume = 50;
};

// 音频适配器
class AudioAdapter : public IAdapter<IModernAudioPlayer, LegacyAudioSystem>
{
public:
    explicit AudioAdapter(std::unique_ptr<LegacyAudioSystem> legacySystem)
        : IAdapter<IModernAudioPlayer, LegacyAudioSystem>(std::move(legacySystem)) {}
    
    bool Play(const std::string& filename) override
    {
        GetAdaptee()->StartSound(filename.c_str());
        return true;
    }
    
    void Stop() override
    {
        GetAdaptee()->StopSound();
    }
    
    void Pause() override
    {
        GetAdaptee()->PauseSound();
    }
    
    void Resume() override
    {
        GetAdaptee()->ResumeSound();
    }
    
    void SetVolume(float volume) override
    {
        // 转换 0.0-1.0 到 0-100
        int legacyVolume = static_cast<int>(volume * 100.0f);
        GetAdaptee()->SetSoundVolume(legacyVolume);
    }
    
    float GetVolume() const override
    {
        // 转换 0-100 到 0.0-1.0
        return GetAdaptee()->GetSoundVolume() / 100.0f;
    }
    
    bool IsPlaying() const override
    {
        return GetAdaptee()->IsSoundPlaying();
    }
};

// 现代渲染接口
class IModernRenderer
{
public:
    virtual ~IModernRenderer() = default;
    virtual void BeginFrame() = 0;
    virtual void EndFrame() = 0;
    virtual void DrawMesh(const std::string& meshName, const float* transform) = 0;
    virtual void SetShader(const std::string& shaderName) = 0;
    virtual void SetTexture(const std::string& textureName) = 0;
};

// 旧版渲染系统
class LegacyRenderSystem
{
public:
    void StartRender()
    {
        printf("[LEGACY] Starting render\n");
    }
    
    void FinishRender()
    {
        printf("[LEGACY] Finishing render\n");
    }
    
    void RenderObject(const char* objName, float x, float y, float z)
    {
        printf("[LEGACY] Rendering object: %s at (%.2f, %.2f, %.2f)\n", objName, x, y, z);
    }
    
    void UseShader(const char* shader)
    {
        printf("[LEGACY] Using shader: %s\n", shader);
    }
    
    void BindTexture(const char* texture)
    {
        printf("[LEGACY] Binding texture: %s\n", texture);
    }
};

// 渲染适配器
class RenderAdapter : public IAdapter<IModernRenderer, LegacyRenderSystem>
{
public:
    explicit RenderAdapter(std::unique_ptr<LegacyRenderSystem> legacyRenderer)
        : IAdapter<IModernRenderer, LegacyRenderSystem>(std::move(legacyRenderer)) {}
    
    void BeginFrame() override
    {
        GetAdaptee()->StartRender();
    }
    
    void EndFrame() override
    {
        GetAdaptee()->FinishRender();
    }
    
    void DrawMesh(const std::string& meshName, const float* transform) override
    {
        // 从变换矩阵提取位置（简化示例）
        float x = transform ? transform[12] : 0.0f;
        float y = transform ? transform[13] : 0.0f;
        float z = transform ? transform[14] : 0.0f;
        
        GetAdaptee()->RenderObject(meshName.c_str(), x, y, z);
    }
    
    void SetShader(const std::string& shaderName) override
    {
        GetAdaptee()->UseShader(shaderName.c_str());
    }
    
    void SetTexture(const std::string& textureName) override
    {
        GetAdaptee()->BindTexture(textureName.c_str());
    }
};

// 现代网络接口
class IModernNetworkClient
{
public:
    virtual ~IModernNetworkClient() = default;
    virtual bool Connect(const std::string& address, uint16_t port) = 0;
    virtual void Disconnect() = 0;
    virtual bool SendData(const std::vector<uint8_t>& data) = 0;
    virtual std::vector<uint8_t> ReceiveData() = 0;
    virtual bool IsConnected() const = 0;
};

// 旧版网络系统
class LegacyNetworkAPI
{
public:
    int ConnectToServer(const char* ip, int port)
    {
        printf("[LEGACY] Connecting to %s:%d\n", ip, port);
        m_connected = true;
        return 1; // 成功
    }
    
    void CloseConnection()
    {
        printf("[LEGACY] Closing connection\n");
        m_connected = false;
    }
    
    int SendBuffer(const char* buffer, int size)
    {
        printf("[LEGACY] Sending %d bytes\n", size);
        return size; // 返回发送的字节数
    }
    
    int ReceiveBuffer(char* buffer, int maxSize)
    {
        printf("[LEGACY] Receiving data\n");
        const char* testData = "test_data";
        int dataSize = strlen(testData);
        if (dataSize <= maxSize)
        {
            memcpy(buffer, testData, dataSize);
            return dataSize;
        }
        return 0;
    }
    
    int IsConnectionActive() const { return m_connected ? 1 : 0; }

private:
    bool m_connected = false;
};

// 网络适配器
class NetworkAdapter : public IAdapter<IModernNetworkClient, LegacyNetworkAPI>
{
public:
    explicit NetworkAdapter(std::unique_ptr<LegacyNetworkAPI> legacyNetwork)
        : IAdapter<IModernNetworkClient, LegacyNetworkAPI>(std::move(legacyNetwork)) {}
    
    bool Connect(const std::string& address, uint16_t port) override
    {
        return GetAdaptee()->ConnectToServer(address.c_str(), port) == 1;
    }
    
    void Disconnect() override
    {
        GetAdaptee()->CloseConnection();
    }
    
    bool SendData(const std::vector<uint8_t>& data) override
    {
        int sent = GetAdaptee()->SendBuffer(reinterpret_cast<const char*>(data.data()), 
                                           static_cast<int>(data.size()));
        return sent == static_cast<int>(data.size());
    }
    
    std::vector<uint8_t> ReceiveData() override
    {
        char buffer[1024];
        int received = GetAdaptee()->ReceiveBuffer(buffer, sizeof(buffer));
        
        std::vector<uint8_t> data;
        if (received > 0)
        {
            data.resize(received);
            memcpy(data.data(), buffer, received);
        }
        
        return data;
    }
    
    bool IsConnected() const override
    {
        return GetAdaptee()->IsConnectionActive() == 1;
    }
};

// 类型适配器 - 用于不同类型之间的转换
template<typename From, typename To>
class TypeAdapter
{
public:
    using ConvertFunc = std::function<To(const From&)>;
    
    explicit TypeAdapter(ConvertFunc converter) : m_converter(std::move(converter)) {}
    
    To Convert(const From& value) const
    {
        return m_converter(value);
    }
    
    template<typename Container>
    auto ConvertContainer(const Container& container) const
    {
        std::vector<To> result;
        result.reserve(container.size());
        
        for (const auto& item : container)
        {
            result.push_back(Convert(item));
        }
        
        return result;
    }

private:
    ConvertFunc m_converter;
};

// 接口适配器工厂
template<typename TargetInterface>
class AdapterFactory
{
public:
    template<typename Adaptee, typename AdapterType = IAdapter<TargetInterface, Adaptee>>
    static std::unique_ptr<TargetInterface> CreateAdapter(std::unique_ptr<Adaptee> adaptee)
    {
        return std::make_unique<AdapterType>(std::move(adaptee));
    }
    
    template<typename Adaptee, typename AdapterFunc>
    static std::unique_ptr<TargetInterface> CreateFunctionAdapter(
        std::unique_ptr<Adaptee> adaptee, 
        AdapterFunc adapterFunc)
    {
        return std::make_unique<FunctionAdapter<TargetInterface, Adaptee, AdapterFunc>>(
            std::move(adaptee), std::move(adapterFunc));
    }
};

// 双向适配器
template<typename InterfaceA, typename InterfaceB>
class BidirectionalAdapter : public InterfaceA, public InterfaceB
{
public:
    BidirectionalAdapter(std::unique_ptr<InterfaceA> a, std::unique_ptr<InterfaceB> b)
        : m_componentA(std::move(a)), m_componentB(std::move(b)) {}

protected:
    std::unique_ptr<InterfaceA> m_componentA;
    std::unique_ptr<InterfaceB> m_componentB;
};

// 适配器链
template<typename TargetInterface>
class AdapterChain
{
public:
    template<typename Adaptee>
    explicit AdapterChain(std::unique_ptr<Adaptee> adaptee)
        : m_component(std::move(adaptee)) {}
    
    template<typename AdapterType, typename... Args>
    AdapterChain& AddAdapter(Args&&... args)
    {
        auto adapter = std::make_unique<AdapterType>(std::move(m_component), std::forward<Args>(args)...);
        m_component = std::move(adapter);
        return *this;
    }
    
    std::unique_ptr<TargetInterface> Build()
    {
        return std::move(m_component);
    }

private:
    std::unique_ptr<TargetInterface> m_component;
};

} // namespace sword2

// 便捷宏定义
#define MAKE_ADAPTER(targetInterface, adapteeType, adapterType, adaptee) \
    std::make_unique<adapterType>(std::move(adaptee))

#define MAKE_AUDIO_ADAPTER(legacySystem) \
    std::make_unique<sword2::AudioAdapter>(std::move(legacySystem))

#define MAKE_RENDER_ADAPTER(legacyRenderer) \
    std::make_unique<sword2::RenderAdapter>(std::move(legacyRenderer))

#define MAKE_NETWORK_ADAPTER(legacyNetwork) \
    std::make_unique<sword2::NetworkAdapter>(std::move(legacyNetwork))

#define MAKE_TYPE_ADAPTER(fromType, toType, converter) \
    sword2::TypeAdapter<fromType, toType>(converter)

#define CREATE_ADAPTER_FACTORY(targetInterface) \
    sword2::AdapterFactory<targetInterface>

#endif // ADAPTER_PATTERN_H
