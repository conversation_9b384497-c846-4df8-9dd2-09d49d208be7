//---------------------------------------------------------------------------
// Sword2 Memory Defragmenter (c) 2024
//
// File:	MemoryDefragmenter.h
// Date:	2024.07.02
// Code:	AI Assistant
// Desc:	Advanced memory defragmentation and optimization system
//---------------------------------------------------------------------------
#ifndef MEMORY_DEFRAGMENTER_H
#define MEMORY_DEFRAGMENTER_H

#include <windows.h>
#include <vector>

// 内存块信息
struct MemoryBlockInfo
{
    void* pAddress;             // 内存地址
    DWORD dwSize;               // 块大小
    BOOL bFree;                 // 是否空闲
    DWORD dwAge;                // 分配时间
    DWORD dwAccessCount;        // 访问次数
    DWORD dwLastAccess;         // 最后访问时间
};

// 内存区域信息
struct MemoryRegionInfo
{
    void* pStartAddress;        // 起始地址
    void* pEndAddress;          // 结束地址
    DWORD dwTotalSize;          // 总大小
    DWORD dwUsedSize;           // 已使用大小
    DWORD dwFreeSize;           // 空闲大小
    DWORD dwFragmentation;      // 碎片化程度 (0-100)
    std::vector<MemoryBlockInfo> blocks; // 内存块列表
};

// 碎片整理策略
enum DEFRAG_STRATEGY
{
    DS_COMPACT = 0,             // 紧凑整理
    DS_COALESCE,                // 合并空闲块
    DS_RELOCATE,                // 重新定位
    DS_AGGRESSIVE,              // 激进整理
    DS_CONSERVATIVE             // 保守整理
};

// 碎片整理统计
struct DefragStats
{
    DWORD dwTotalRuns;          // 总运行次数
    DWORD dwTotalTime;          // 总耗时(ms)
    DWORD dwBytesDefragmented;  // 整理的字节数
    DWORD dwBlocksMoved;        // 移动的块数
    DWORD dwBlocksCoalesced;    // 合并的块数
    float fFragmentationBefore; // 整理前碎片率
    float fFragmentationAfter;  // 整理后碎片率
    DWORD dwMemorySaved;        // 节省的内存
};

// 内存碎片整理器
class CMemoryDefragmenter
{
public:
    CMemoryDefragmenter();
    ~CMemoryDefragmenter();

    BOOL Initialize();
    void Cleanup();

    // 碎片分析
    float AnalyzeFragmentation();
    void ScanMemoryRegions();
    BOOL NeedsDefragmentation();

    // 碎片整理
    BOOL DefragmentMemory(DEFRAG_STRATEGY eStrategy = DS_COMPACT);
    BOOL DefragmentRegion(MemoryRegionInfo* pRegion);
    
    // 自动整理
    void SetAutoDefragEnabled(BOOL bEnabled) { m_bAutoDefrag = bEnabled; }
    void SetFragmentationThreshold(float fThreshold) { m_fFragThreshold = fThreshold; }
    void CheckAndDefragment();

    // 统计信息
    void GetStats(DefragStats* pStats);
    void ResetStats();
    void LogStats();

    // 配置
    void SetStrategy(DEFRAG_STRATEGY eStrategy) { m_eStrategy = eStrategy; }
    void SetMaxDefragTime(DWORD dwMaxTime) { m_dwMaxDefragTime = dwMaxTime; }

private:
    std::vector<MemoryRegionInfo> m_Regions;    // 内存区域列表
    DefragStats m_Stats;                        // 统计信息
    DEFRAG_STRATEGY m_eStrategy;                // 整理策略
    
    BOOL m_bInitialized;
    BOOL m_bAutoDefrag;
    float m_fFragThreshold;                     // 碎片阈值
    DWORD m_dwMaxDefragTime;                    // 最大整理时间
    DWORD m_dwLastDefragTime;                   // 上次整理时间
    
    CRITICAL_SECTION m_CriticalSection;

    // 内部方法
    float CalculateFragmentation(const MemoryRegionInfo& region);
    BOOL CompactRegion(MemoryRegionInfo* pRegion);
    BOOL CoalesceBlocks(MemoryRegionInfo* pRegion);
    BOOL RelocateBlocks(MemoryRegionInfo* pRegion);
    void UpdateBlockInfo(MemoryBlockInfo* pBlock);
    BOOL CanMoveBlock(const MemoryBlockInfo& block);
};

// 内存热度分析器
class CMemoryHeatAnalyzer
{
public:
    CMemoryHeatAnalyzer();
    ~CMemoryHeatAnalyzer();

    // 热度跟踪
    void RecordAccess(void* pAddress, DWORD dwSize);
    void RecordAllocation(void* pAddress, DWORD dwSize);
    void RecordDeallocation(void* pAddress);

    // 热度分析
    float GetHotness(void* pAddress);
    void GetHotRegions(std::vector<MemoryRegionInfo>& hotRegions);
    void GetColdRegions(std::vector<MemoryRegionInfo>& coldRegions);

    // 优化建议
    void GenerateOptimizationSuggestions();

private:
    struct HeatInfo
    {
        void* pAddress;
        DWORD dwSize;
        DWORD dwAccessCount;
        DWORD dwLastAccess;
        float fHotness;
    };

    std::vector<HeatInfo> m_HeatMap;
    CRITICAL_SECTION m_CriticalSection;

    void UpdateHeatMap();
    float CalculateHotness(const HeatInfo& info);
};

// 内存压缩器
class CMemoryCompressor
{
public:
    CMemoryCompressor();
    ~CMemoryCompressor();

    // 压缩操作
    BOOL CompressUnusedMemory();
    BOOL CompressRegion(void* pAddress, DWORD dwSize);
    
    // 解压操作
    BOOL DecompressMemory(void* pAddress);
    
    // 压缩统计
    DWORD GetCompressionRatio();
    DWORD GetCompressedSize();

private:
    struct CompressedBlock
    {
        void* pOriginalAddress;
        void* pCompressedData;
        DWORD dwOriginalSize;
        DWORD dwCompressedSize;
        DWORD dwCompressionTime;
    };

    std::vector<CompressedBlock> m_CompressedBlocks;
    CRITICAL_SECTION m_CriticalSection;

    BOOL CompressData(const void* pInput, DWORD dwInputSize, 
                     void** ppOutput, DWORD* pdwOutputSize);
    BOOL DecompressData(const void* pInput, DWORD dwInputSize,
                       void* pOutput, DWORD dwOutputSize);
};

// 全局实例
extern CMemoryDefragmenter g_MemoryDefragmenter;
extern CMemoryHeatAnalyzer g_MemoryHeatAnalyzer;
extern CMemoryCompressor g_MemoryCompressor;

// 便捷宏定义
#define MEMORY_RECORD_ACCESS(addr, size)    g_MemoryHeatAnalyzer.RecordAccess(addr, size)
#define MEMORY_RECORD_ALLOC(addr, size)     g_MemoryHeatAnalyzer.RecordAllocation(addr, size)
#define MEMORY_RECORD_FREE(addr)            g_MemoryHeatAnalyzer.RecordDeallocation(addr)
#define MEMORY_CHECK_DEFRAG()               g_MemoryDefragmenter.CheckAndDefragment()

// 自动内存管理类
class CAutoMemoryManager
{
public:
    CAutoMemoryManager();
    ~CAutoMemoryManager();

    BOOL Initialize();
    void Cleanup();

    // 自动管理
    void StartAutoManagement();
    void StopAutoManagement();
    void RunMaintenanceCycle();

    // 配置
    void SetMaintenanceInterval(DWORD dwInterval) { m_dwMaintenanceInterval = dwInterval; }
    void SetAggressiveMode(BOOL bAggressive) { m_bAggressiveMode = bAggressive; }

private:
    HANDLE m_hMaintenanceThread;
    HANDLE m_hStopEvent;
    DWORD m_dwMaintenanceInterval;
    BOOL m_bAggressiveMode;
    BOOL m_bRunning;

    static DWORD WINAPI MaintenanceThreadProc(LPVOID pParam);
    void PerformMaintenance();
};

// 全局自动内存管理器
extern CAutoMemoryManager g_AutoMemoryManager;

#endif // MEMORY_DEFRAGMENTER_H
