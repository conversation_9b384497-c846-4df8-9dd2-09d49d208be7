//---------------------------------------------------------------------------
// Sword2 NPC Script Integration (c) 2024
//
// File:	NPCScriptIntegration.h
// Date:	2024.07.02
// Code:	AI Assistant
// Desc:	Integration layer between NPC system and Lua scripts
//---------------------------------------------------------------------------
#ifndef NPC_SCRIPT_INTEGRATION_H
#define NPC_SCRIPT_INTEGRATION_H

#include "NPCManager.h"
#include "LuaScriptEngine.h"
#include "PlayerManager.h"
#include "UnifiedLoggingSystem.h"
#include <string>
#include <unordered_map>
#include <vector>
#include <functional>

namespace sword2 {

// NPC脚本API扩展
namespace NPCScriptAPI {
    
    // NPC相关API函数
    int GetNPCName(lua_State* L);
    int GetNPCLevel(lua_State* L);
    int GetNPCPosition(lua_State* L);
    int SetNPCPosition(lua_State* L);
    int GetNPCHP(lua_State* L);
    int SetNPCHP(lua_State* L);
    int GetNPCStatus(lua_State* L);
    int SetNPCStatus(lua_State* L);
    
    // NPC行为API
    int NPCMoveTo(lua_State* L);
    int NPCPatrol(lua_State* L);
    int NPCAttackPlayer(lua_State* L);
    int NPCFollowPlayer(lua_State* L);
    int NPCReturnToSpawn(lua_State* L);
    
    // NPC对话API
    int NPCChat(lua_State* L);
    int NPCSay(lua_State* L);
    int NPCAsk(lua_State* L);
    int ShowNPCDialog(lua_State* L);
    int CloseNPCDialog(lua_State* L);
    
    // NPC商店API
    int OpenShop(lua_State* L);
    int CloseShop(lua_State* L);
    int AddShopItem(lua_State* L);
    int RemoveShopItem(lua_State* L);
    int GetShopItemPrice(lua_State* L);
    int SetShopItemPrice(lua_State* L);
    
    // NPC任务API
    int GiveQuest(lua_State* L);
    int CompleteQuest(lua_State* L);
    int CheckQuestStatus(lua_State* L);
    int UpdateQuestProgress(lua_State* L);
    
    // NPC创建和管理API
    int CreateNPCInstance(lua_State* L);
    int RemoveNPCInstance(lua_State* L);
    int GetNearbyNPCs(lua_State* L);
    int GetNPCsInMap(lua_State* L);
    
    // NPC特效和动画API
    int PlayNPCAnimation(lua_State* L);
    int ShowNPCEffect(lua_State* L);
    int SetNPCDirection(lua_State* L);
    int GetNPCDirection(lua_State* L);
    
    // NPC AI控制API
    int SetNPCBehavior(lua_State* L);
    int GetNPCBehavior(lua_State* L);
    int SetNPCAIState(lua_State* L);
    int GetNPCAIState(lua_State* L);
    
    // NPC事件API
    int TriggerNPCEvent(lua_State* L);
    int RegisterNPCEventHandler(lua_State* L);
    int UnregisterNPCEventHandler(lua_State* L);
}

// NPC对话管理器
class NPCDialogManager
{
public:
    struct DialogOption
    {
        std::string text;           // 选项文本
        std::string functionName;   // 对应函数名
        std::string condition;      // 显示条件
        bool isDefault = false;     // 是否默认选项
        
        DialogOption() = default;
        DialogOption(const std::string& txt, const std::string& func, const std::string& cond = "", bool def = false)
            : text(txt), functionName(func), condition(cond), isDefault(def) {}
    };
    
    struct DialogData
    {
        uint32_t npcId = 0;
        uint32_t playerId = 0;
        std::string currentText;
        std::vector<DialogOption> options;
        bool isActive = false;
        std::chrono::system_clock::time_point startTime;
        
        DialogData() = default;
        DialogData(uint32_t npc, uint32_t player)
            : npcId(npc), playerId(player), isActive(true)
        {
            startTime = std::chrono::system_clock::now();
        }
    };
    
    // 开始对话
    bool StartDialog(uint32_t npcId, uint32_t playerId, const std::string& initialText = "")
    {
        std::string dialogKey = GetDialogKey(npcId, playerId);
        
        std::lock_guard<std::mutex> lock(m_dialogMutex);
        m_activeDialogs[dialogKey] = DialogData(npcId, playerId);
        
        if (!initialText.empty())
        {
            m_activeDialogs[dialogKey].currentText = initialText;
        }
        
        LOG_DEBUG("NPC_DIALOG", "Started dialog between NPC " + std::to_string(npcId) + " and player " + std::to_string(playerId));
        return true;
    }
    
    // 结束对话
    bool EndDialog(uint32_t npcId, uint32_t playerId)
    {
        std::string dialogKey = GetDialogKey(npcId, playerId);
        
        std::lock_guard<std::mutex> lock(m_dialogMutex);
        auto it = m_activeDialogs.find(dialogKey);
        if (it != m_activeDialogs.end())
        {
            m_activeDialogs.erase(it);
            LOG_DEBUG("NPC_DIALOG", "Ended dialog between NPC " + std::to_string(npcId) + " and player " + std::to_string(playerId));
            return true;
        }
        
        return false;
    }
    
    // 设置对话文本
    bool SetDialogText(uint32_t npcId, uint32_t playerId, const std::string& text)
    {
        std::string dialogKey = GetDialogKey(npcId, playerId);
        
        std::lock_guard<std::mutex> lock(m_dialogMutex);
        auto it = m_activeDialogs.find(dialogKey);
        if (it != m_activeDialogs.end())
        {
            it->second.currentText = text;
            return true;
        }
        
        return false;
    }
    
    // 添加对话选项
    bool AddDialogOption(uint32_t npcId, uint32_t playerId, const DialogOption& option)
    {
        std::string dialogKey = GetDialogKey(npcId, playerId);
        
        std::lock_guard<std::mutex> lock(m_dialogMutex);
        auto it = m_activeDialogs.find(dialogKey);
        if (it != m_activeDialogs.end())
        {
            it->second.options.push_back(option);
            return true;
        }
        
        return false;
    }
    
    // 清除对话选项
    bool ClearDialogOptions(uint32_t npcId, uint32_t playerId)
    {
        std::string dialogKey = GetDialogKey(npcId, playerId);
        
        std::lock_guard<std::mutex> lock(m_dialogMutex);
        auto it = m_activeDialogs.find(dialogKey);
        if (it != m_activeDialogs.end())
        {
            it->second.options.clear();
            return true;
        }
        
        return false;
    }
    
    // 获取对话数据
    DialogData GetDialogData(uint32_t npcId, uint32_t playerId)
    {
        std::string dialogKey = GetDialogKey(npcId, playerId);
        
        std::lock_guard<std::mutex> lock(m_dialogMutex);
        auto it = m_activeDialogs.find(dialogKey);
        if (it != m_activeDialogs.end())
        {
            return it->second;
        }
        
        return DialogData();
    }
    
    // 检查对话是否活跃
    bool IsDialogActive(uint32_t npcId, uint32_t playerId)
    {
        std::string dialogKey = GetDialogKey(npcId, playerId);
        
        std::lock_guard<std::mutex> lock(m_dialogMutex);
        auto it = m_activeDialogs.find(dialogKey);
        return it != m_activeDialogs.end() && it->second.isActive;
    }
    
    // 处理对话选项选择
    bool SelectDialogOption(uint32_t npcId, uint32_t playerId, size_t optionIndex)
    {
        std::string dialogKey = GetDialogKey(npcId, playerId);
        
        std::lock_guard<std::mutex> lock(m_dialogMutex);
        auto it = m_activeDialogs.find(dialogKey);
        if (it != m_activeDialogs.end() && optionIndex < it->second.options.size())
        {
            const auto& option = it->second.options[optionIndex];
            
            // 执行对应的脚本函数
            if (!option.functionName.empty())
            {
                std::vector<LuaValue> args = {
                    LuaValue(static_cast<double>(playerId)),
                    LuaValue(static_cast<double>(npcId))
                };
                
                ScriptResult result = EXECUTE_FUNCTION(option.functionName, args);
                if (result != ScriptResult::Success)
                {
                    LOG_WARNING("NPC_DIALOG", "Failed to execute dialog function: " + option.functionName);
                }
            }
            
            LOG_DEBUG("NPC_DIALOG", "Player " + std::to_string(playerId) + " selected option: " + option.text);
            return true;
        }
        
        return false;
    }

private:
    std::mutex m_dialogMutex;
    std::unordered_map<std::string, DialogData> m_activeDialogs;
    
    std::string GetDialogKey(uint32_t npcId, uint32_t playerId)
    {
        return std::to_string(npcId) + "_" + std::to_string(playerId);
    }
};

// NPC商店管理器
class NPCShopManager
{
public:
    struct ShopSession
    {
        uint32_t npcId = 0;
        uint32_t playerId = 0;
        uint32_t shopType = 0;
        bool isActive = false;
        std::chrono::system_clock::time_point startTime;
        
        ShopSession() = default;
        ShopSession(uint32_t npc, uint32_t player, uint32_t type)
            : npcId(npc), playerId(player), shopType(type), isActive(true)
        {
            startTime = std::chrono::system_clock::now();
        }
    };
    
    // 打开商店
    bool OpenShop(uint32_t npcId, uint32_t playerId, uint32_t shopType = 0)
    {
        auto npc = GET_NPC(npcId);
        if (!npc || !npc->IsMerchant())
        {
            LOG_WARNING("NPC_SHOP", "Cannot open shop for non-merchant NPC: " + std::to_string(npcId));
            return false;
        }
        
        std::string sessionKey = GetSessionKey(npcId, playerId);
        
        std::lock_guard<std::mutex> lock(m_shopMutex);
        m_activeSessions[sessionKey] = ShopSession(npcId, playerId, shopType);
        
        LOG_INFO("NPC_SHOP", "Opened shop for player " + std::to_string(playerId) + " at NPC " + std::to_string(npcId));
        return true;
    }
    
    // 关闭商店
    bool CloseShop(uint32_t npcId, uint32_t playerId)
    {
        std::string sessionKey = GetSessionKey(npcId, playerId);
        
        std::lock_guard<std::mutex> lock(m_shopMutex);
        auto it = m_activeSessions.find(sessionKey);
        if (it != m_activeSessions.end())
        {
            m_activeSessions.erase(it);
            LOG_INFO("NPC_SHOP", "Closed shop for player " + std::to_string(playerId) + " at NPC " + std::to_string(npcId));
            return true;
        }
        
        return false;
    }
    
    // 购买物品
    bool BuyItem(uint32_t npcId, uint32_t playerId, uint32_t itemId, uint32_t quantity = 1)
    {
        auto npc = GET_NPC(npcId);
        if (!npc) return false;
        
        // 查找商店物品
        for (auto& shopItem : npc->shopItems)
        {
            if (shopItem.itemId == itemId)
            {
                if (!shopItem.HasStock() || (shopItem.isLimited && shopItem.stock < quantity))
                {
                    LOG_WARNING("NPC_SHOP", "Insufficient stock for item " + std::to_string(itemId));
                    return false;
                }
                
                uint32_t totalPrice = shopItem.price * quantity;
                
                // 检查玩家金钱（这里应该调用玩家系统）
                auto player = GET_ONLINE_PLAYER(playerId);
                if (!player || player->money < totalPrice)
                {
                    LOG_WARNING("NPC_SHOP", "Player " + std::to_string(playerId) + " has insufficient money");
                    return false;
                }
                
                // 扣除金钱
                player->money -= totalPrice;
                
                // 减少库存
                if (shopItem.isLimited)
                {
                    shopItem.stock -= quantity;
                }
                
                // 给予物品（这里应该调用物品系统）
                LOG_INFO("NPC_SHOP", "Player " + std::to_string(playerId) + " bought item " + 
                        std::to_string(itemId) + " x" + std::to_string(quantity) + " for " + std::to_string(totalPrice));
                
                return true;
            }
        }
        
        LOG_WARNING("NPC_SHOP", "Item not found in shop: " + std::to_string(itemId));
        return false;
    }
    
    // 出售物品
    bool SellItem(uint32_t npcId, uint32_t playerId, uint32_t itemId, uint32_t quantity = 1)
    {
        // 这里应该实现物品出售逻辑
        LOG_INFO("NPC_SHOP", "Player " + std::to_string(playerId) + " sold item " + 
                std::to_string(itemId) + " x" + std::to_string(quantity));
        return true;
    }
    
    // 检查商店会话是否活跃
    bool IsShopActive(uint32_t npcId, uint32_t playerId)
    {
        std::string sessionKey = GetSessionKey(npcId, playerId);
        
        std::lock_guard<std::mutex> lock(m_shopMutex);
        auto it = m_activeSessions.find(sessionKey);
        return it != m_activeSessions.end() && it->second.isActive;
    }

private:
    std::mutex m_shopMutex;
    std::unordered_map<std::string, ShopSession> m_activeSessions;
    
    std::string GetSessionKey(uint32_t npcId, uint32_t playerId)
    {
        return std::to_string(npcId) + "_" + std::to_string(playerId);
    }
};

// NPC脚本集成管理器
class NPCScriptIntegration : public Singleton<NPCScriptIntegration>
{
public:
    NPCScriptIntegration() = default;
    ~NPCScriptIntegration() = default;
    
    // 初始化NPC脚本集成
    bool Initialize()
    {
        // 注册NPC相关的Lua API函数
        RegisterNPCScriptAPI();
        
        LOG_INFO("NPC_SCRIPT", "NPC script integration initialized");
        return true;
    }
    
    // 加载NPC脚本
    bool LoadNPCScript(const std::string& scriptPath)
    {
        ScriptResult result = LOAD_SCRIPT(scriptPath, ScriptType::NPC);
        if (result == ScriptResult::Success)
        {
            LOG_INFO("NPC_SCRIPT", "Loaded NPC script: " + scriptPath);
            return true;
        }
        else
        {
            LOG_ERROR("NPC_SCRIPT", "Failed to load NPC script: " + scriptPath);
            return false;
        }
    }
    
    // 执行NPC脚本函数
    bool ExecuteNPCFunction(const std::string& functionName, uint32_t npcId, uint32_t playerId = 0)
    {
        std::vector<LuaValue> args;
        if (playerId != 0)
        {
            args.push_back(LuaValue(static_cast<double>(playerId)));
        }
        args.push_back(LuaValue(static_cast<double>(npcId)));
        
        ScriptResult result = EXECUTE_FUNCTION(functionName, args);
        return result == ScriptResult::Success;
    }
    
    // 获取对话管理器
    NPCDialogManager& GetDialogManager() { return m_dialogManager; }
    
    // 获取商店管理器
    NPCShopManager& GetShopManager() { return m_shopManager; }

private:
    NPCDialogManager m_dialogManager;
    NPCShopManager m_shopManager;
    
    void RegisterNPCScriptAPI()
    {
        // 注册NPC相关API
        REGISTER_LUA_FUNCTION("GetNPCName", NPCScriptAPI::GetNPCName);
        REGISTER_LUA_FUNCTION("GetNPCLevel", NPCScriptAPI::GetNPCLevel);
        REGISTER_LUA_FUNCTION("GetNPCPosition", NPCScriptAPI::GetNPCPosition);
        REGISTER_LUA_FUNCTION("SetNPCPosition", NPCScriptAPI::SetNPCPosition);
        REGISTER_LUA_FUNCTION("GetNPCHP", NPCScriptAPI::GetNPCHP);
        REGISTER_LUA_FUNCTION("SetNPCHP", NPCScriptAPI::SetNPCHP);
        
        // 注册NPC行为API
        REGISTER_LUA_FUNCTION("NPCMoveTo", NPCScriptAPI::NPCMoveTo);
        REGISTER_LUA_FUNCTION("NPCPatrol", NPCScriptAPI::NPCPatrol);
        REGISTER_LUA_FUNCTION("NPCAttackPlayer", NPCScriptAPI::NPCAttackPlayer);
        REGISTER_LUA_FUNCTION("NPCReturnToSpawn", NPCScriptAPI::NPCReturnToSpawn);
        
        // 注册NPC对话API
        REGISTER_LUA_FUNCTION("NPCChat", NPCScriptAPI::NPCChat);
        REGISTER_LUA_FUNCTION("NPCSay", NPCScriptAPI::NPCSay);
        REGISTER_LUA_FUNCTION("ShowNPCDialog", NPCScriptAPI::ShowNPCDialog);
        REGISTER_LUA_FUNCTION("CloseNPCDialog", NPCScriptAPI::CloseNPCDialog);
        
        // 注册NPC商店API
        REGISTER_LUA_FUNCTION("OpenShop", NPCScriptAPI::OpenShop);
        REGISTER_LUA_FUNCTION("CloseShop", NPCScriptAPI::CloseShop);
        REGISTER_LUA_FUNCTION("AddShopItem", NPCScriptAPI::AddShopItem);
        
        // 注册NPC创建API
        REGISTER_LUA_FUNCTION("CreateNPCInstance", NPCScriptAPI::CreateNPCInstance);
        REGISTER_LUA_FUNCTION("RemoveNPCInstance", NPCScriptAPI::RemoveNPCInstance);
        
        LOG_INFO("NPC_SCRIPT", "Registered NPC script API functions");
    }
};

} // namespace sword2

// 全局NPC脚本集成访问
#define NPC_SCRIPT_INTEGRATION() sword2::NPCScriptIntegration::getInstance()

// 便捷宏定义
#define INIT_NPC_SCRIPT_INTEGRATION() NPC_SCRIPT_INTEGRATION().Initialize()
#define LOAD_NPC_SCRIPT(scriptPath) NPC_SCRIPT_INTEGRATION().LoadNPCScript(scriptPath)
#define EXECUTE_NPC_FUNCTION(functionName, npcId, playerId) NPC_SCRIPT_INTEGRATION().ExecuteNPCFunction(functionName, npcId, playerId)

#define NPC_DIALOG_MANAGER() NPC_SCRIPT_INTEGRATION().GetDialogManager()
#define NPC_SHOP_MANAGER() NPC_SCRIPT_INTEGRATION().GetShopManager()

#define START_NPC_DIALOG(npcId, playerId, text) NPC_DIALOG_MANAGER().StartDialog(npcId, playerId, text)
#define END_NPC_DIALOG(npcId, playerId) NPC_DIALOG_MANAGER().EndDialog(npcId, playerId)
#define SET_DIALOG_TEXT(npcId, playerId, text) NPC_DIALOG_MANAGER().SetDialogText(npcId, playerId, text)

#define OPEN_NPC_SHOP(npcId, playerId, shopType) NPC_SHOP_MANAGER().OpenShop(npcId, playerId, shopType)
#define CLOSE_NPC_SHOP(npcId, playerId) NPC_SHOP_MANAGER().CloseShop(npcId, playerId)
#define BUY_FROM_NPC(npcId, playerId, itemId, quantity) NPC_SHOP_MANAGER().BuyItem(npcId, playerId, itemId, quantity)

#endif // NPC_SCRIPT_INTEGRATION_H
