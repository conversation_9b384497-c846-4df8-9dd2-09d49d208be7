//---------------------------------------------------------------------------
// Sword2 NPC Manager (c) 2024
//
// File:	NPCManager.h
// Date:	2024.07.02
// Code:	AI Assistant
// Desc:	Comprehensive NPC and monster management system
//---------------------------------------------------------------------------
#ifndef NPC_MANAGER_H
#define NPC_MANAGER_H

#include "NPCSystem.h"
#include "LuaScriptEngine.h"
#include "UnifiedLoggingSystem.h"
#include "BusinessMonitor.h"
#include <unordered_map>
#include <unordered_set>
#include <memory>
#include <mutex>
#include <shared_mutex>
#include <thread>
#include <atomic>
#include <queue>
#include <functional>

namespace sword2 {

// NPC模板数据
struct NPCTemplate
{
    uint32_t templateId = 0;        // 模板ID
    std::string name;               // 名称
    std::string title;              // 称号
    std::string description;        // 描述
    NPCType type = NPCType::Normal; // 类型
    NPCBehavior behavior = NPCBehavior::Static; // 行为模式
    
    // 属性模板
    NPCAttributes baseAttributes;   // 基础属性
    
    // 脚本信息
    std::string scriptPath;         // 脚本路径
    std::unordered_map<std::string, std::string> scriptFunctions; // 脚本函数
    
    // 商店信息
    std::vector<ShopItem> shopItems; // 商店物品
    uint32_t shopType = 0;          // 商店类型
    
    // 掉落信息
    std::vector<NPCDropItem> dropItems; // 掉落物品
    uint32_t experience = 0;        // 给予经验
    uint32_t money = 0;             // 给予金钱
    
    // 重生信息
    uint32_t respawnTime = 30;      // 重生时间
    bool canRespawn = true;         // 是否可重生
    
    NPCTemplate() = default;
    NPCTemplate(uint32_t id, const std::string& npcName, NPCType npcType)
        : templateId(id), name(npcName), type(npcType) {}
    
    // 创建NPC实例
    std::unique_ptr<NPC> CreateInstance(uint32_t npcId) const
    {
        auto npc = std::make_unique<NPC>(npcId, templateId, name);
        npc->title = title;
        npc->description = description;
        npc->type = type;
        npc->behavior = behavior;
        npc->attributes = baseAttributes;
        npc->scriptPath = scriptPath;
        npc->scriptFunctions = scriptFunctions;
        npc->shopItems = shopItems;
        npc->shopType = shopType;
        npc->dropItems = dropItems;
        npc->experience = experience;
        npc->money = money;
        npc->respawnTime = respawnTime;
        npc->canRespawn = canRespawn;
        
        return npc;
    }
};

// NPC事件类型
enum class NPCEvent : uint8_t
{
    OnCreate = 0,       // 创建时
    OnSpawn,            // 出生时
    OnTalk,             // 对话时
    OnAttack,           // 攻击时
    OnDamage,           // 受伤时
    OnDeath,            // 死亡时
    OnRespawn,          // 重生时
    OnTimer,            // 定时器
    OnPlayerEnter,      // 玩家进入视野
    OnPlayerLeave       // 玩家离开视野
};

// NPC AI管理器
class NPCAIManager
{
public:
    NPCAIManager() = default;
    
    // 更新NPC AI
    void UpdateNPC(NPC& npc, const std::vector<std::shared_ptr<Player>>& nearbyPlayers)
    {
        if (npc.status == NPCStatus::Dead || npc.status == NPCStatus::Disabled)
            return;
        
        switch (npc.behavior)
        {
        case NPCBehavior::Static:
            UpdateStaticNPC(npc);
            break;
        case NPCBehavior::Patrol:
            UpdatePatrolNPC(npc);
            break;
        case NPCBehavior::Random:
            UpdateRandomNPC(npc);
            break;
        case NPCBehavior::Aggressive:
            UpdateAggressiveNPC(npc, nearbyPlayers);
            break;
        case NPCBehavior::Defensive:
            UpdateDefensiveNPC(npc, nearbyPlayers);
            break;
        default:
            break;
        }
    }

private:
    void UpdateStaticNPC(NPC& npc)
    {
        // 静态NPC只需要检查状态
        if (npc.status == NPCStatus::Moving)
        {
            npc.status = NPCStatus::Idle;
        }
    }
    
    void UpdatePatrolNPC(NPC& npc)
    {
        if (npc.patrolPoints.empty())
        {
            UpdateStaticNPC(npc);
            return;
        }
        
        auto now = std::chrono::system_clock::now();
        
        // 检查是否在等待
        if (now < npc.patrolWaitUntil)
            return;
        
        // 获取当前巡逻点
        if (npc.currentPatrolIndex >= npc.patrolPoints.size())
            npc.currentPatrolIndex = 0;
        
        const auto& currentPoint = npc.patrolPoints[npc.currentPatrolIndex];
        
        // 检查是否到达巡逻点
        if (npc.DistanceTo(currentPoint.x, currentPoint.y) <= 10.0)
        {
            // 到达巡逻点，设置等待时间
            if (currentPoint.waitTime > 0)
            {
                npc.patrolWaitUntil = now + std::chrono::milliseconds(currentPoint.waitTime);
            }
            
            // 移动到下一个巡逻点
            const auto* nextPoint = npc.GetNextPatrolPoint();
            if (nextPoint)
            {
                npc.MoveTo(nextPoint->x, nextPoint->y);
            }
        }
        else
        {
            // 继续向巡逻点移动
            npc.MoveTo(currentPoint.x, currentPoint.y);
        }
    }
    
    void UpdateRandomNPC(NPC& npc)
    {
        // 随机移动逻辑
        if (rand() % 100 < 5) // 5%概率移动
        {
            int32_t newX = npc.spawnX + (rand() % 200 - 100); // 在出生点附近100像素范围内移动
            int32_t newY = npc.spawnY + (rand() % 200 - 100);
            npc.MoveTo(newX, newY);
        }
    }
    
    void UpdateAggressiveNPC(NPC& npc, const std::vector<std::shared_ptr<Player>>& nearbyPlayers)
    {
        if (!npc.IsMonster())
            return;
        
        // 寻找攻击目标
        std::shared_ptr<Player> target = nullptr;
        double minDistance = npc.attributes.viewRange;
        
        for (const auto& player : nearbyPlayers)
        {
            if (!player || player->attributes.IsDead())
                continue;
            
            double distance = npc.DistanceToPlayer(*player);
            if (distance >= 0 && distance < minDistance)
            {
                target = player;
                minDistance = distance;
            }
        }
        
        if (target)
        {
            npc.targetPlayerId = target->playerId;
            npc.aiState = NPCAIState::Combat;
            
            // 如果在攻击范围内，进行攻击
            if (npc.IsInAttackRange(target->position.x, target->position.y))
            {
                auto now = std::chrono::system_clock::now();
                auto timeSinceLastAttack = std::chrono::duration_cast<std::chrono::milliseconds>(now - npc.lastAttackTime);
                
                // 检查攻击间隔
                if (timeSinceLastAttack.count() >= (1000 * 100 / npc.attributes.attackSpeed))
                {
                    uint32_t damage = npc.AttackTarget(*target);
                    if (damage > 0)
                    {
                        LOG_DEBUG("NPC_AI", "NPC " + npc.name + " attacked player " + target->playerName + " for " + std::to_string(damage) + " damage");
                    }
                }
            }
            else
            {
                // 移动向目标
                npc.MoveTo(target->position.x, target->position.y);
            }
        }
        else
        {
            // 没有目标，返回出生点
            if (npc.DistanceTo(npc.spawnX, npc.spawnY) > 10.0)
            {
                npc.ReturnToSpawn();
            }
            else
            {
                npc.aiState = NPCAIState::Passive;
                npc.status = NPCStatus::Idle;
            }
        }
    }
    
    void UpdateDefensiveNPC(NPC& npc, const std::vector<std::shared_ptr<Player>>& nearbyPlayers)
    {
        // 防御型NPC只有被攻击时才反击
        if (npc.targetPlayerId != 0)
        {
            // 寻找目标玩家
            std::shared_ptr<Player> target = nullptr;
            for (const auto& player : nearbyPlayers)
            {
                if (player && player->playerId == npc.targetPlayerId)
                {
                    target = player;
                    break;
                }
            }
            
            if (target && !target->attributes.IsDead())
            {
                // 反击逻辑与攻击型类似
                UpdateAggressiveNPC(npc, {target});
            }
            else
            {
                // 目标消失，停止战斗
                npc.targetPlayerId = 0;
                npc.aiState = NPCAIState::Passive;
                npc.ReturnToSpawn();
            }
        }
    }
};

// NPC管理器
class NPCManager : public Singleton<NPCManager>
{
public:
    NPCManager()
        : m_running(false), m_nextNPCId(1), m_aiUpdateInterval(std::chrono::milliseconds(500)) {}
    
    ~NPCManager()
    {
        Stop();
    }
    
    // 启动NPC管理器
    bool Start()
    {
        if (m_running) return true;
        
        m_running = true;
        m_aiThread = std::thread(&NPCManager::AIUpdateLoop, this);
        
        LOG_INFO("NPC_MGR", "NPC manager started");
        return true;
    }
    
    // 停止NPC管理器
    void Stop()
    {
        if (!m_running) return;
        
        m_running = false;
        if (m_aiThread.joinable())
        {
            m_aiThread.join();
        }
        
        // 清理所有NPC
        {
            std::unique_lock<std::shared_mutex> lock(m_npcsMutex);
            m_npcs.clear();
        }
        
        {
            std::lock_guard<std::mutex> lock(m_templatesMutex);
            m_templates.clear();
        }
        
        LOG_INFO("NPC_MGR", "NPC manager stopped");
    }
    
    // 注册NPC模板
    bool RegisterTemplate(const NPCTemplate& npcTemplate)
    {
        std::lock_guard<std::mutex> lock(m_templatesMutex);
        
        if (m_templates.find(npcTemplate.templateId) != m_templates.end())
        {
            LOG_WARNING("NPC_MGR", "Template already exists: " + std::to_string(npcTemplate.templateId));
            return false;
        }
        
        m_templates[npcTemplate.templateId] = npcTemplate;
        LOG_INFO("NPC_MGR", "Registered NPC template: " + npcTemplate.name + " (ID: " + std::to_string(npcTemplate.templateId) + ")");
        
        return true;
    }
    
    // 创建NPC
    uint32_t CreateNPC(uint32_t templateId, uint32_t mapId, int32_t x, int32_t y, uint32_t direction = 0)
    {
        // 获取模板
        std::shared_ptr<NPCTemplate> npcTemplate;
        {
            std::lock_guard<std::mutex> lock(m_templatesMutex);
            auto it = m_templates.find(templateId);
            if (it == m_templates.end())
            {
                LOG_ERROR("NPC_MGR", "Template not found: " + std::to_string(templateId));
                return 0;
            }
            npcTemplate = std::make_shared<NPCTemplate>(it->second);
        }
        
        // 生成NPC ID
        uint32_t npcId = m_nextNPCId++;
        
        // 创建NPC实例
        auto npc = npcTemplate->CreateInstance(npcId);
        npc->SetPosition(mapId, x, y, direction);
        npc->SetSpawnPoint(x, y);
        
        // 添加到管理器
        {
            std::unique_lock<std::shared_mutex> lock(m_npcsMutex);
            m_npcs[npcId] = std::move(npc);
            m_npcsByMap[mapId].insert(npcId);
        }
        
        // 触发创建事件
        TriggerNPCEvent(npcId, NPCEvent::OnCreate, 0);
        TriggerNPCEvent(npcId, NPCEvent::OnSpawn, 0);
        
        LOG_INFO("NPC_MGR", "Created NPC: " + npcTemplate->name + " (ID: " + std::to_string(npcId) + ") at map " + std::to_string(mapId));
        
        return npcId;
    }
    
    // 删除NPC
    bool RemoveNPC(uint32_t npcId)
    {
        std::unique_lock<std::shared_mutex> lock(m_npcsMutex);
        
        auto it = m_npcs.find(npcId);
        if (it == m_npcs.end())
        {
            LOG_WARNING("NPC_MGR", "NPC not found for removal: " + std::to_string(npcId));
            return false;
        }
        
        uint32_t mapId = it->second->mapId;
        std::string npcName = it->second->name;
        
        // 从地图索引中移除
        auto mapIt = m_npcsByMap.find(mapId);
        if (mapIt != m_npcsByMap.end())
        {
            mapIt->second.erase(npcId);
            if (mapIt->second.empty())
            {
                m_npcsByMap.erase(mapIt);
            }
        }
        
        // 移除NPC
        m_npcs.erase(it);
        
        LOG_INFO("NPC_MGR", "Removed NPC: " + npcName + " (ID: " + std::to_string(npcId) + ")");
        return true;
    }
    
    // 获取NPC
    std::shared_ptr<NPC> GetNPC(uint32_t npcId)
    {
        std::shared_lock<std::shared_mutex> lock(m_npcsMutex);
        auto it = m_npcs.find(npcId);
        return (it != m_npcs.end()) ? it->second : nullptr;
    }
    
    // 根据地图获取NPC列表
    std::vector<std::shared_ptr<NPC>> GetNPCsInMap(uint32_t mapId)
    {
        std::shared_lock<std::shared_mutex> lock(m_npcsMutex);
        std::vector<std::shared_ptr<NPC>> npcs;
        
        auto mapIt = m_npcsByMap.find(mapId);
        if (mapIt != m_npcsByMap.end())
        {
            npcs.reserve(mapIt->second.size());
            for (uint32_t npcId : mapIt->second)
            {
                auto it = m_npcs.find(npcId);
                if (it != m_npcs.end())
                {
                    npcs.push_back(it->second);
                }
            }
        }
        
        return npcs;
    }
    
    // 根据范围获取NPC列表
    std::vector<std::shared_ptr<NPC>> GetNPCsInRange(uint32_t mapId, int32_t centerX, int32_t centerY, double range)
    {
        std::vector<std::shared_ptr<NPC>> result;
        auto npcs = GetNPCsInMap(mapId);
        
        for (const auto& npc : npcs)
        {
            if (npc && npc->DistanceTo(centerX, centerY) <= range)
            {
                result.push_back(npc);
            }
        }
        
        return result;
    }
    
    // 触发NPC事件
    bool TriggerNPCEvent(uint32_t npcId, NPCEvent event, uint32_t playerId = 0)
    {
        auto npc = GetNPC(npcId);
        if (!npc) return false;
        
        std::string eventName = GetEventName(event);
        std::string functionName = npc->GetScriptFunction(eventName);
        
        if (functionName.empty())
        {
            // 使用默认函数名
            functionName = eventName;
        }
        
        // 执行脚本函数
        std::vector<LuaValue> args;
        if (playerId != 0)
        {
            args.push_back(LuaValue(static_cast<double>(playerId)));
        }
        args.push_back(LuaValue(static_cast<double>(npcId)));
        
        ScriptResult result = EXECUTE_FUNCTION(functionName, args);
        
        if (result != ScriptResult::Success)
        {
            LOG_DEBUG("NPC_MGR", "Failed to execute NPC event " + eventName + " for NPC " + std::to_string(npcId));
            return false;
        }
        
        return true;
    }
    
    // NPC对话
    bool NPCTalk(uint32_t npcId, uint32_t playerId)
    {
        auto npc = GetNPC(npcId);
        if (!npc || !npc->CanTalk())
        {
            LOG_WARNING("NPC_MGR", "Cannot talk to NPC " + std::to_string(npcId));
            return false;
        }
        
        npc->status = NPCStatus::Talking;
        
        // 触发对话事件
        bool result = TriggerNPCEvent(npcId, NPCEvent::OnTalk, playerId);
        
        npc->status = NPCStatus::Idle;
        
        return result;
    }
    
    // NPC攻击
    bool NPCAttack(uint32_t npcId, uint32_t targetPlayerId)
    {
        auto npc = GetNPC(npcId);
        if (!npc || !npc->CanAttack())
        {
            return false;
        }
        
        npc->targetPlayerId = targetPlayerId;
        npc->status = NPCStatus::Fighting;
        npc->aiState = NPCAIState::Combat;
        
        // 触发攻击事件
        TriggerNPCEvent(npcId, NPCEvent::OnAttack, targetPlayerId);
        
        return true;
    }
    
    // NPC受伤
    bool NPCTakeDamage(uint32_t npcId, uint32_t damage, uint32_t attackerId = 0)
    {
        auto npc = GetNPC(npcId);
        if (!npc || npc->attributes.IsDead())
        {
            return false;
        }
        
        uint32_t actualDamage = npc->attributes.TakeDamage(damage);
        
        // 触发受伤事件
        TriggerNPCEvent(npcId, NPCEvent::OnDamage, attackerId);
        
        // 检查是否死亡
        if (npc->attributes.IsDead())
        {
            npc->Die();
            TriggerNPCEvent(npcId, NPCEvent::OnDeath, attackerId);
            
            // 处理掉落
            if (attackerId != 0)
            {
                HandleNPCDrop(npcId, attackerId);
            }
        }
        else if (attackerId != 0 && npc->behavior == NPCBehavior::Defensive)
        {
            // 防御型NPC被攻击后反击
            npc->targetPlayerId = attackerId;
            npc->aiState = NPCAIState::Combat;
        }
        
        LOG_DEBUG("NPC_MGR", "NPC " + npc->name + " took " + std::to_string(actualDamage) + " damage");
        
        return true;
    }
    
    // 获取NPC统计信息
    struct NPCStatistics
    {
        size_t totalNPCs = 0;
        size_t aliveNPCs = 0;
        size_t deadNPCs = 0;
        std::unordered_map<NPCType, size_t> npcsByType;
        std::unordered_map<uint32_t, size_t> npcsPerMap;
        std::unordered_map<NPCStatus, size_t> npcsByStatus;
    };
    
    NPCStatistics GetStatistics()
    {
        std::shared_lock<std::shared_mutex> lock(m_npcsMutex);
        
        NPCStatistics stats;
        stats.totalNPCs = m_npcs.size();
        
        for (const auto& [npcId, npc] : m_npcs)
        {
            stats.npcsByType[npc->type]++;
            stats.npcsPerMap[npc->mapId]++;
            stats.npcsByStatus[npc->status]++;
            
            if (npc->status == NPCStatus::Dead)
                stats.deadNPCs++;
            else
                stats.aliveNPCs++;
        }
        
        return stats;
    }
    
    // 设置AI更新间隔
    void SetAIUpdateInterval(std::chrono::milliseconds interval)
    {
        m_aiUpdateInterval = interval;
        LOG_INFO("NPC_MGR", "AI update interval set to " + std::to_string(interval.count()) + "ms");
    }

private:
    std::atomic<bool> m_running;
    std::thread m_aiThread;
    std::chrono::milliseconds m_aiUpdateInterval;
    
    // NPC数据
    mutable std::shared_mutex m_npcsMutex;
    std::unordered_map<uint32_t, std::shared_ptr<NPC>> m_npcs;
    std::unordered_map<uint32_t, std::unordered_set<uint32_t>> m_npcsByMap; // mapId -> npcIds
    
    // 模板数据
    mutable std::mutex m_templatesMutex;
    std::unordered_map<uint32_t, NPCTemplate> m_templates;
    
    std::atomic<uint32_t> m_nextNPCId;
    NPCAIManager m_aiManager;
    
    // AI更新循环
    void AIUpdateLoop()
    {
        while (m_running)
        {
            try
            {
                UpdateAllNPCs();
                std::this_thread::sleep_for(m_aiUpdateInterval);
            }
            catch (const std::exception& e)
            {
                LOG_ERROR("NPC_MGR", std::string("Error in AI update loop: ") + e.what());
            }
        }
    }
    
    void UpdateAllNPCs()
    {
        std::shared_lock<std::shared_mutex> lock(m_npcsMutex);
        
        for (const auto& [npcId, npc] : m_npcs)
        {
            if (!npc) continue;
            
            // 更新NPC基础状态
            npc->Update();
            
            // 获取附近的玩家
            auto nearbyPlayers = GetNearbyPlayers(npc->mapId, npc->x, npc->y, npc->attributes.viewRange);
            
            // 更新AI
            m_aiManager.UpdateNPC(*npc, nearbyPlayers);
        }
    }
    
    std::vector<std::shared_ptr<Player>> GetNearbyPlayers(uint32_t mapId, int32_t x, int32_t y, double range)
    {
        // 这里应该从PlayerManager获取附近的玩家
        // 暂时返回空列表
        return {};
    }
    
    void HandleNPCDrop(uint32_t npcId, uint32_t playerId)
    {
        auto npc = GetNPC(npcId);
        if (!npc) return;
        
        // 获取掉落物品
        auto dropItems = npc->GetDropItems();
        
        if (!dropItems.empty())
        {
            LOG_INFO("NPC_MGR", "NPC " + npc->name + " dropped " + std::to_string(dropItems.size()) + " items");
            
            // 这里应该将物品给予玩家或放置在地面
            // 暂时只记录日志
            for (const auto& [itemId, count] : dropItems)
            {
                LOG_DEBUG("NPC_MGR", "Dropped item " + std::to_string(itemId) + " x" + std::to_string(count));
            }
        }
        
        // 给予经验和金钱
        if (npc->experience > 0 || npc->money > 0)
        {
            LOG_INFO("NPC_MGR", "Player " + std::to_string(playerId) + " gained " + 
                    std::to_string(npc->experience) + " exp and " + std::to_string(npc->money) + " money");
        }
    }
    
    std::string GetEventName(NPCEvent event)
    {
        switch (event)
        {
        case NPCEvent::OnCreate: return "OnCreate";
        case NPCEvent::OnSpawn: return "OnSpawn";
        case NPCEvent::OnTalk: return "OnTalk";
        case NPCEvent::OnAttack: return "OnAttack";
        case NPCEvent::OnDamage: return "OnDamage";
        case NPCEvent::OnDeath: return "OnDeath";
        case NPCEvent::OnRespawn: return "OnRespawn";
        case NPCEvent::OnTimer: return "OnTimer";
        case NPCEvent::OnPlayerEnter: return "OnPlayerEnter";
        case NPCEvent::OnPlayerLeave: return "OnPlayerLeave";
        default: return "Unknown";
        }
    }
};

} // namespace sword2

// 全局NPC管理器访问
#define NPC_MANAGER() sword2::NPCManager::getInstance()

// 便捷宏定义
#define START_NPC_SYSTEM() NPC_MANAGER().Start()
#define STOP_NPC_SYSTEM() NPC_MANAGER().Stop()

#define REGISTER_NPC_TEMPLATE(npcTemplate) NPC_MANAGER().RegisterTemplate(npcTemplate)
#define CREATE_NPC(templateId, mapId, x, y, direction) NPC_MANAGER().CreateNPC(templateId, mapId, x, y, direction)
#define REMOVE_NPC(npcId) NPC_MANAGER().RemoveNPC(npcId)

#define GET_NPC(npcId) NPC_MANAGER().GetNPC(npcId)
#define GET_NPCS_IN_MAP(mapId) NPC_MANAGER().GetNPCsInMap(mapId)
#define GET_NPCS_IN_RANGE(mapId, x, y, range) NPC_MANAGER().GetNPCsInRange(mapId, x, y, range)

#define NPC_TALK(npcId, playerId) NPC_MANAGER().NPCTalk(npcId, playerId)
#define NPC_ATTACK(npcId, targetId) NPC_MANAGER().NPCAttack(npcId, targetId)
#define NPC_TAKE_DAMAGE(npcId, damage, attackerId) NPC_MANAGER().NPCTakeDamage(npcId, damage, attackerId)

#define TRIGGER_NPC_EVENT(npcId, event, playerId) NPC_MANAGER().TriggerNPCEvent(npcId, event, playerId)

#endif // NPC_MANAGER_H
