//---------------------------------------------------------------------------
// Sword2 NPC System Demo (c) 2024
//
// File:	NPCSystemDemo.cpp
// Date:	2024.07.02
// Code:	AI Assistant
// Desc:	Comprehensive demonstration of the NPC and monster system
//---------------------------------------------------------------------------

#include "KCore.h"
#include "NPCSystem.h"
#include "NPCManager.h"
#include "NPCScriptIntegration.h"
#include "PlayerSystem.h"
#include "PlayerManager.h"
#include "LuaScriptEngine.h"
#include "UnifiedLoggingSystem.h"

namespace sword2 {

// NPC系统演示类
class NPCSystemDemo
{
public:
    NPCSystemDemo()
    {
        m_initialized = false;
    }
    
    bool Initialize()
    {
        if (m_initialized) return true;
        
        printf("[NPC_DEMO] Initializing NPC system demo...\n");
        
        // 初始化日志系统
        LOGGER().Initialize();
        LOGGER().AddConsoleAppender(LogLevel::Debug);
        
        // 启动玩家管理器
        if (!START_PLAYER_SYSTEM())
        {
            printf("[NPC_DEMO] Failed to start player system\n");
            return false;
        }
        
        // 初始化脚本引擎
        if (!INIT_SCRIPT_ENGINE("../../../Server/script"))
        {
            printf("[NPC_DEMO] Failed to initialize script engine\n");
            return false;
        }
        
        // 启动NPC管理器
        if (!START_NPC_SYSTEM())
        {
            printf("[NPC_DEMO] Failed to start NPC system\n");
            return false;
        }
        
        // 初始化NPC脚本集成
        if (!INIT_NPC_SCRIPT_INTEGRATION())
        {
            printf("[NPC_DEMO] Failed to initialize NPC script integration\n");
            return false;
        }
        
        m_initialized = true;
        LOG_INFO("NPC_DEMO", "NPC system demo initialized successfully");
        
        return true;
    }
    
    void RunDemo()
    {
        if (!m_initialized)
        {
            printf("[NPC_DEMO] System not initialized\n");
            return;
        }
        
        LOG_INFO("NPC_DEMO", "Starting NPC system demonstration...");
        
        // 演示各个功能
        DemoNPCTemplateCreation();
        DemoNPCCreation();
        DemoNPCBehavior();
        DemoNPCDialog();
        DemoNPCShop();
        DemoNPCCombat();
        DemoNPCScripting();
        DemoNPCStatistics();
        
        LOG_INFO("NPC_DEMO", "NPC system demonstration completed");
    }
    
    void Cleanup()
    {
        if (!m_initialized) return;
        
        LOG_INFO("NPC_DEMO", "Cleaning up NPC system demo...");
        
        // 清理创建的NPC
        for (uint32_t npcId : m_createdNPCs)
        {
            REMOVE_NPC(npcId);
        }
        m_createdNPCs.clear();
        
        // 停止系统
        STOP_NPC_SYSTEM();
        SHUTDOWN_SCRIPT_ENGINE();
        STOP_PLAYER_SYSTEM();
        
        m_initialized = false;
        LOG_INFO("NPC_DEMO", "NPC system demo cleanup completed");
    }

private:
    bool m_initialized;
    std::vector<uint32_t> m_createdNPCs;
    
    void DemoNPCTemplateCreation()
    {
        LOG_INFO("NPC_DEMO", "=== NPC Template Creation Demo ===");
        
        // 创建商人模板
        NPCTemplate merchantTemplate(1001, "武器店老板", NPCType::Merchant);
        merchantTemplate.title = "武器商人";
        merchantTemplate.description = "专门出售各种武器的商人";
        merchantTemplate.behavior = NPCBehavior::Static;
        merchantTemplate.baseAttributes.level = 50;
        merchantTemplate.baseAttributes.maxLife = 1000;
        merchantTemplate.baseAttributes.currentLife = 1000;
        merchantTemplate.scriptPath = "npc/weapon_merchant.lua";
        
        // 添加商店物品
        merchantTemplate.shopItems.emplace_back(1001, 100, 10, true);  // 青钢剑
        merchantTemplate.shopItems.emplace_back(1002, 200, 5, true);   // 精钢剑
        merchantTemplate.shopItems.emplace_back(2001, 50, -1, false);  // 生命药水
        
        REGISTER_NPC_TEMPLATE(merchantTemplate);
        LOG_INFO("NPC_DEMO", "Created merchant template: " + merchantTemplate.name);
        
        // 创建守卫模板
        NPCTemplate guardTemplate(1002, "城门守卫", NPCType::Guard);
        guardTemplate.title = "守卫";
        guardTemplate.description = "保护城市安全的守卫";
        guardTemplate.behavior = NPCBehavior::Defensive;
        guardTemplate.baseAttributes.level = 30;
        guardTemplate.baseAttributes.maxLife = 800;
        guardTemplate.baseAttributes.currentLife = 800;
        guardTemplate.baseAttributes.minAttack = 50;
        guardTemplate.baseAttributes.maxAttack = 80;
        guardTemplate.baseAttributes.defense = 30;
        guardTemplate.scriptPath = "npc/city_guard.lua";
        
        REGISTER_NPC_TEMPLATE(guardTemplate);
        LOG_INFO("NPC_DEMO", "Created guard template: " + guardTemplate.name);
        
        // 创建怪物模板
        NPCTemplate monsterTemplate(1003, "野狼", NPCType::Monster);
        monsterTemplate.description = "凶猛的野狼";
        monsterTemplate.behavior = NPCBehavior::Aggressive;
        monsterTemplate.baseAttributes.level = 10;
        monsterTemplate.baseAttributes.maxLife = 200;
        monsterTemplate.baseAttributes.currentLife = 200;
        monsterTemplate.baseAttributes.minAttack = 20;
        monsterTemplate.baseAttributes.maxAttack = 35;
        monsterTemplate.baseAttributes.viewRange = 300;
        monsterTemplate.baseAttributes.attackRange = 50;
        monsterTemplate.experience = 50;
        monsterTemplate.money = 10;
        monsterTemplate.respawnTime = 60;
        
        // 添加掉落物品
        monsterTemplate.dropItems.emplace_back(3001, 1, 1, 3000, false); // 狼皮 30%
        monsterTemplate.dropItems.emplace_back(3002, 1, 2, 1000, true);  // 狼牙 10%
        
        REGISTER_NPC_TEMPLATE(monsterTemplate);
        LOG_INFO("NPC_DEMO", "Created monster template: " + monsterTemplate.name);
        
        // 创建BOSS模板
        NPCTemplate bossTemplate(1004, "狼王", NPCType::Boss);
        bossTemplate.title = "森林之王";
        bossTemplate.description = "统领狼群的强大狼王";
        bossTemplate.behavior = NPCBehavior::Aggressive;
        bossTemplate.baseAttributes.level = 25;
        bossTemplate.baseAttributes.maxLife = 1500;
        bossTemplate.baseAttributes.currentLife = 1500;
        bossTemplate.baseAttributes.minAttack = 80;
        bossTemplate.baseAttributes.maxAttack = 120;
        bossTemplate.baseAttributes.defense = 50;
        bossTemplate.baseAttributes.viewRange = 500;
        bossTemplate.baseAttributes.attackRange = 100;
        bossTemplate.experience = 500;
        bossTemplate.money = 100;
        bossTemplate.respawnTime = 300; // 5分钟重生
        
        // 添加稀有掉落
        bossTemplate.dropItems.emplace_back(4001, 1, 1, 5000, true);  // 狼王之牙 50%
        bossTemplate.dropItems.emplace_back(4002, 1, 1, 1000, true);  // 狼王皮甲 10%
        
        REGISTER_NPC_TEMPLATE(bossTemplate);
        LOG_INFO("NPC_DEMO", "Created boss template: " + bossTemplate.name);
    }
    
    void DemoNPCCreation()
    {
        LOG_INFO("NPC_DEMO", "=== NPC Creation Demo ===");
        
        // 创建商人NPC
        uint32_t merchantId = CREATE_NPC(1001, 100, 1000, 1000, 0);
        if (merchantId != 0)
        {
            m_createdNPCs.push_back(merchantId);
            LOG_INFO("NPC_DEMO", "Created merchant NPC with ID: " + std::to_string(merchantId));
        }
        
        // 创建守卫NPC
        uint32_t guardId = CREATE_NPC(1002, 100, 1100, 1000, 0);
        if (guardId != 0)
        {
            m_createdNPCs.push_back(guardId);
            LOG_INFO("NPC_DEMO", "Created guard NPC with ID: " + std::to_string(guardId));
        }
        
        // 创建多个怪物NPC
        for (int i = 0; i < 3; ++i)
        {
            uint32_t monsterId = CREATE_NPC(1003, 101, 2000 + i * 100, 2000 + i * 50, 0);
            if (monsterId != 0)
            {
                m_createdNPCs.push_back(monsterId);
                LOG_INFO("NPC_DEMO", "Created monster NPC with ID: " + std::to_string(monsterId));
                
                // 设置巡逻路径
                auto monster = GET_NPC(monsterId);
                if (monster)
                {
                    monster->AddPatrolPoint(2000 + i * 100, 2000 + i * 50, 2000);
                    monster->AddPatrolPoint(2050 + i * 100, 2050 + i * 50, 2000);
                    monster->AddPatrolPoint(2000 + i * 100, 2100 + i * 50, 2000);
                }
            }
        }
        
        // 创建BOSS NPC
        uint32_t bossId = CREATE_NPC(1004, 101, 2500, 2500, 0);
        if (bossId != 0)
        {
            m_createdNPCs.push_back(bossId);
            LOG_INFO("NPC_DEMO", "Created boss NPC with ID: " + std::to_string(bossId));
        }
        
        LOG_INFO("NPC_DEMO", "Created " + std::to_string(m_createdNPCs.size()) + " NPCs total");
    }
    
    void DemoNPCBehavior()
    {
        LOG_INFO("NPC_DEMO", "=== NPC Behavior Demo ===");
        
        if (m_createdNPCs.empty()) return;
        
        // 演示NPC移动
        for (size_t i = 0; i < std::min(m_createdNPCs.size(), size_t(3)); ++i)
        {
            auto npc = GET_NPC(m_createdNPCs[i]);
            if (npc)
            {
                LOG_INFO("NPC_DEMO", "NPC " + npc->name + " behavior: " + 
                        std::to_string(static_cast<int>(npc->behavior)));
                
                // 演示移动
                int32_t newX = npc->x + (rand() % 100 - 50);
                int32_t newY = npc->y + (rand() % 100 - 50);
                npc->MoveTo(newX, newY);
                
                LOG_INFO("NPC_DEMO", "Moved NPC " + npc->name + " to (" + 
                        std::to_string(newX) + ", " + std::to_string(newY) + ")");
            }
        }
        
        // 演示AI状态变化
        if (m_createdNPCs.size() > 3)
        {
            auto monster = GET_NPC(m_createdNPCs[3]);
            if (monster && monster->IsMonster())
            {
                LOG_INFO("NPC_DEMO", "Monster " + monster->name + " AI state: " + 
                        std::to_string(static_cast<int>(monster->aiState)));
                
                // 模拟进入战斗状态
                monster->aiState = NPCAIState::Combat;
                monster->status = NPCStatus::Fighting;
                
                LOG_INFO("NPC_DEMO", "Monster " + monster->name + " entered combat state");
            }
        }
    }
    
    void DemoNPCDialog()
    {
        LOG_INFO("NPC_DEMO", "=== NPC Dialog Demo ===");
        
        if (m_createdNPCs.empty()) return;
        
        // 创建测试玩家
        PlayerCreationParams params("对话测试玩家", "dialog_test");
        params.series = PlayerSeries::Wudang;
        uint32_t playerId = CREATE_PLAYER(params);
        
        if (playerId != 0)
        {
            uint32_t sessionId;
            PLAYER_LOGIN(playerId, "127.0.0.1", 1003, sessionId);
            
            // 与商人对话
            uint32_t merchantId = m_createdNPCs[0];
            bool dialogResult = NPC_TALK(merchantId, playerId);
            
            if (dialogResult)
            {
                LOG_INFO("NPC_DEMO", "Successfully started dialog with merchant");
                
                // 演示对话管理
                START_NPC_DIALOG(merchantId, playerId, "欢迎来到我的武器店！");
                
                // 添加对话选项
                auto& dialogManager = NPC_DIALOG_MANAGER();
                NPCDialogManager::DialogOption option1("查看武器", "show_weapons");
                NPCDialogManager::DialogOption option2("购买装备", "buy_equipment");
                NPCDialogManager::DialogOption option3("离开", "leave_shop");
                
                dialogManager.AddDialogOption(merchantId, playerId, option1);
                dialogManager.AddDialogOption(merchantId, playerId, option2);
                dialogManager.AddDialogOption(merchantId, playerId, option3);
                
                // 模拟选择选项
                dialogManager.SelectDialogOption(merchantId, playerId, 0);
                
                // 结束对话
                END_NPC_DIALOG(merchantId, playerId);
            }
            
            PLAYER_LOGOUT(playerId);
        }
    }
    
    void DemoNPCShop()
    {
        LOG_INFO("NPC_DEMO", "=== NPC Shop Demo ===");
        
        if (m_createdNPCs.empty()) return;
        
        // 创建测试玩家
        PlayerCreationParams params("商店测试玩家", "shop_test");
        params.series = PlayerSeries::Shaolin;
        uint32_t playerId = CREATE_PLAYER(params);
        
        if (playerId != 0)
        {
            uint32_t sessionId;
            PLAYER_LOGIN(playerId, "127.0.0.1", 1004, sessionId);
            
            auto player = GET_ONLINE_PLAYER(playerId);
            if (player)
            {
                player->money = 1000; // 给玩家一些金钱
                
                // 打开商店
                uint32_t merchantId = m_createdNPCs[0];
                bool shopResult = OPEN_NPC_SHOP(merchantId, playerId, 0);
                
                if (shopResult)
                {
                    LOG_INFO("NPC_DEMO", "Successfully opened shop");
                    
                    // 尝试购买物品
                    auto& shopManager = NPC_SHOP_MANAGER();
                    bool buyResult = shopManager.BuyItem(merchantId, playerId, 1001, 1); // 购买青钢剑
                    
                    if (buyResult)
                    {
                        LOG_INFO("NPC_DEMO", "Successfully bought item from shop");
                    }
                    else
                    {
                        LOG_WARNING("NPC_DEMO", "Failed to buy item from shop");
                    }
                    
                    // 关闭商店
                    CLOSE_NPC_SHOP(merchantId, playerId);
                }
            }
            
            PLAYER_LOGOUT(playerId);
        }
    }
    
    void DemoNPCCombat()
    {
        LOG_INFO("NPC_DEMO", "=== NPC Combat Demo ===");
        
        if (m_createdNPCs.size() < 4) return;
        
        // 创建测试玩家
        PlayerCreationParams params("战斗测试玩家", "combat_test");
        params.series = PlayerSeries::Beggar;
        uint32_t playerId = CREATE_PLAYER(params);
        
        if (playerId != 0)
        {
            uint32_t sessionId;
            PLAYER_LOGIN(playerId, "127.0.0.1", 1005, sessionId);
            
            auto player = GET_ONLINE_PLAYER(playerId);
            if (player)
            {
                // 传送到怪物附近
                player->TeleportTo(101, 2000, 2000);
                
                // 获取怪物
                uint32_t monsterId = m_createdNPCs[3]; // 第一个怪物
                auto monster = GET_NPC(monsterId);
                
                if (monster && monster->IsMonster())
                {
                    LOG_INFO("NPC_DEMO", "Starting combat between player and " + monster->name);
                    
                    // 模拟战斗
                    LOG_INFO("NPC_DEMO", "Monster HP: " + std::to_string(monster->attributes.currentLife) + 
                            "/" + std::to_string(monster->attributes.maxLife));
                    LOG_INFO("NPC_DEMO", "Player HP: " + std::to_string(player->attributes.currentLife) + 
                            "/" + std::to_string(player->attributes.maxLife));
                    
                    // 怪物攻击玩家
                    uint32_t damage = monster->AttackTarget(*player);
                    if (damage > 0)
                    {
                        LOG_INFO("NPC_DEMO", "Monster dealt " + std::to_string(damage) + " damage to player");
                    }
                    
                    // 玩家攻击怪物
                    bool damageResult = NPC_TAKE_DAMAGE(monsterId, 50, playerId);
                    if (damageResult)
                    {
                        LOG_INFO("NPC_DEMO", "Player dealt 50 damage to monster");
                        LOG_INFO("NPC_DEMO", "Monster HP: " + std::to_string(monster->attributes.currentLife) + 
                                "/" + std::to_string(monster->attributes.maxLife));
                    }
                    
                    // 如果怪物死亡，演示掉落
                    if (monster->attributes.IsDead())
                    {
                        auto dropItems = monster->GetDropItems();
                        LOG_INFO("NPC_DEMO", "Monster died and dropped " + std::to_string(dropItems.size()) + " items");
                        
                        for (const auto& [itemId, count] : dropItems)
                        {
                            LOG_INFO("NPC_DEMO", "Dropped: Item " + std::to_string(itemId) + " x" + std::to_string(count));
                        }
                    }
                }
            }
            
            PLAYER_LOGOUT(playerId);
        }
    }
    
    void DemoNPCScripting()
    {
        LOG_INFO("NPC_DEMO", "=== NPC Scripting Demo ===");
        
        // 创建测试脚本
        std::string testScript = R"(
            function npc_test_function(playerId, npcId)
                WriteLog("NPC script function called!")
                WriteLog("Player ID: " .. playerId)
                WriteLog("NPC ID: " .. npcId)
                
                local npcName = GetNPCName(npcId)
                local npcLevel = GetNPCLevel(npcId)
                WriteLog("NPC Name: " .. npcName)
                WriteLog("NPC Level: " .. npcLevel)
                
                -- 移动NPC
                NPCMoveTo(npcId, 1200, 1200)
                WriteLog("Moved NPC to new position")
                
                -- 显示NPC对话
                if playerId > 0 then
                    ShowNPCDialog(npcId, playerId, "这是来自脚本的对话！")
                    WriteLog("Showed dialog to player")
                end
                
                return true
            end
            
            function merchant_talk(playerId, npcId)
                WriteLog("Merchant talk function called")
                
                local playerName = GetPlayerName(playerId)
                ShowNPCDialog(npcId, playerId, "欢迎光临，" .. playerName .. "！")
                
                -- 打开商店
                OpenShop(npcId, playerId, 1)
                
                return true
            end
        )";
        
        // 执行测试脚本
        auto context = std::make_unique<LuaScriptContext>();
        context->Initialize();
        
        ScriptResult result = context->ExecuteString(testScript);
        if (result == ScriptResult::Success)
        {
            LOG_INFO("NPC_DEMO", "Successfully loaded NPC test script");
            
            if (!m_createdNPCs.empty())
            {
                // 测试脚本函数调用
                std::vector<LuaValue> args = {
                    LuaValue(static_cast<double>(1001)), // playerId
                    LuaValue(static_cast<double>(m_createdNPCs[0])) // npcId
                };
                
                result = context->CallFunction("npc_test_function", args);
                if (result == ScriptResult::Success)
                {
                    LOG_INFO("NPC_DEMO", "Successfully executed NPC script function");
                }
                else
                {
                    LOG_ERROR("NPC_DEMO", "Failed to execute NPC script function");
                }
            }
        }
        else
        {
            LOG_ERROR("NPC_DEMO", "Failed to load NPC test script");
        }
    }
    
    void DemoNPCStatistics()
    {
        LOG_INFO("NPC_DEMO", "=== NPC Statistics Demo ===");
        
        auto stats = NPC_MANAGER().GetStatistics();
        
        LOG_INFO("NPC_DEMO", "NPC system statistics:");
        LOG_INFO("NPC_DEMO", "  Total NPCs: " + std::to_string(stats.totalNPCs));
        LOG_INFO("NPC_DEMO", "  Alive NPCs: " + std::to_string(stats.aliveNPCs));
        LOG_INFO("NPC_DEMO", "  Dead NPCs: " + std::to_string(stats.deadNPCs));
        
        LOG_INFO("NPC_DEMO", "NPCs by type:");
        for (const auto& [type, count] : stats.npcsByType)
        {
            std::string typeName;
            switch (type)
            {
            case NPCType::Normal: typeName = "Normal"; break;
            case NPCType::Merchant: typeName = "Merchant"; break;
            case NPCType::Guard: typeName = "Guard"; break;
            case NPCType::Monster: typeName = "Monster"; break;
            case NPCType::Boss: typeName = "Boss"; break;
            default: typeName = "Other"; break;
            }
            
            LOG_INFO("NPC_DEMO", "  " + typeName + ": " + std::to_string(count));
        }
        
        LOG_INFO("NPC_DEMO", "NPCs by map:");
        for (const auto& [mapId, count] : stats.npcsPerMap)
        {
            LOG_INFO("NPC_DEMO", "  Map " + std::to_string(mapId) + ": " + std::to_string(count));
        }
        
        LOG_INFO("NPC_DEMO", "NPCs by status:");
        for (const auto& [status, count] : stats.npcsByStatus)
        {
            std::string statusName;
            switch (status)
            {
            case NPCStatus::Idle: statusName = "Idle"; break;
            case NPCStatus::Moving: statusName = "Moving"; break;
            case NPCStatus::Fighting: statusName = "Fighting"; break;
            case NPCStatus::Dead: statusName = "Dead"; break;
            default: statusName = "Other"; break;
            }
            
            LOG_INFO("NPC_DEMO", "  " + statusName + ": " + std::to_string(count));
        }
    }
};

} // namespace sword2

// 全局NPC系统演示实例
sword2::NPCSystemDemo g_NPCDemo;

// 初始化NPC系统
bool InitializeNPCSystem()
{
    return g_NPCDemo.Initialize();
}

// 运行NPC系统演示
void RunNPCSystemDemo()
{
    g_NPCDemo.RunDemo();
}

// 清理NPC系统
void CleanupNPCSystem()
{
    g_NPCDemo.Cleanup();
}
