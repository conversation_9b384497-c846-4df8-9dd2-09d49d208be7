//---------------------------------------------------------------------------
// Sword2 Render Batch System (c) 2024
//
// File:	RenderBatch.h
// Date:	2024.07.02
// Code:	AI Assistant
// Desc:	High-performance batch rendering system
//---------------------------------------------------------------------------
#ifndef RENDER_BATCH_H
#define RENDER_BATCH_H

#include <windows.h>
#include <vector>

// 渲染对象类型
enum RENDER_OBJECT_TYPE
{
    ROT_SPRITE = 0,         // 精灵
    ROT_PARTICLE,           // 粒子
    ROT_UI_ELEMENT,         // UI元素
    ROT_EFFECT,             // 特效
    ROT_TERRAIN,            // 地形
    ROT_COUNT
};

// 渲染层级
enum RENDER_LAYER
{
    RL_BACKGROUND = 0,      // 背景层
    RL_TERRAIN,             // 地形层
    RL_OBJECTS,             // 对象层
    RL_CHARACTERS,          // 角色层
    RL_EFFECTS,             // 特效层
    RL_UI,                  // UI层
    RL_DEBUG,               // 调试层
    RL_COUNT
};

// 渲染状态
struct RenderState
{
    DWORD dwTexture;        // 纹理ID
    DWORD dwBlendMode;      // 混合模式
    DWORD dwShader;         // 着色器ID
    BOOL bDepthTest;        // 深度测试
    BOOL bAlphaTest;        // Alpha测试
    float fAlphaRef;        // Alpha参考值
};

// 渲染项
struct RenderItem
{
    RENDER_OBJECT_TYPE eType;   // 对象类型
    RENDER_LAYER eLayer;        // 渲染层级
    RenderState state;          // 渲染状态
    void* pVertexData;          // 顶点数据
    DWORD dwVertexCount;        // 顶点数量
    void* pIndexData;           // 索引数据
    DWORD dwIndexCount;         // 索引数量
    float fDepth;               // 深度值
    DWORD dwSortKey;            // 排序键
};

// 渲染批次
struct RenderBatch
{
    RenderState state;              // 批次状态
    std::vector<RenderItem*> items; // 渲染项列表
    DWORD dwVertexCount;            // 总顶点数
    DWORD dwIndexCount;             // 总索引数
    void* pBatchVertexBuffer;       // 批次顶点缓冲
    void* pBatchIndexBuffer;        // 批次索引缓冲
};

// 渲染统计信息
struct RenderStats
{
    DWORD dwFrameCount;         // 帧数
    DWORD dwDrawCalls;          // 绘制调用数
    DWORD dwBatchCount;         // 批次数
    DWORD dwVertexCount;        // 顶点数
    DWORD dwTriangleCount;      // 三角形数
    DWORD dwStateChanges;       // 状态切换数
    DWORD dwTextureBinds;       // 纹理绑定数
    float fBatchEfficiency;     // 批处理效率
};

// 渲染批处理器
class CRenderBatcher
{
public:
    CRenderBatcher();
    ~CRenderBatcher();

    BOOL Initialize();
    void Cleanup();

    // 渲染项管理
    void AddRenderItem(const RenderItem& item);
    void ClearRenderItems();

    // 批处理
    void SortRenderItems();
    void BuildBatches();
    void RenderBatches();

    // 立即渲染模式
    void SetImmediateMode(BOOL bImmediate) { m_bImmediateMode = bImmediate; }
    void FlushImmediate();

    // 统计信息
    void GetStats(RenderStats* pStats);
    void ResetStats();

    // 配置
    void SetMaxBatchSize(DWORD dwMaxVertices, DWORD dwMaxIndices);
    void SetSortingEnabled(BOOL bEnabled) { m_bSortingEnabled = bEnabled; }

private:
    std::vector<RenderItem> m_RenderItems[RL_COUNT];    // 按层级分组的渲染项
    std::vector<RenderBatch> m_RenderBatches;           // 渲染批次
    RenderStats m_Stats;                                // 统计信息
    
    BOOL m_bInitialized;
    BOOL m_bImmediateMode;
    BOOL m_bSortingEnabled;
    DWORD m_dwMaxBatchVertices;
    DWORD m_dwMaxBatchIndices;
    
    // 批处理缓冲区
    void* m_pVertexBuffer;
    void* m_pIndexBuffer;
    DWORD m_dwVertexBufferSize;
    DWORD m_dwIndexBufferSize;

    // 内部方法
    void SortLayer(RENDER_LAYER eLayer);
    BOOL CanBatch(const RenderItem& item1, const RenderItem& item2);
    void CreateBatch(RENDER_LAYER eLayer, DWORD dwStartIndex, DWORD dwEndIndex);
    void RenderBatch(const RenderBatch& batch);
    DWORD GenerateSortKey(const RenderItem& item);
    void UpdateStats(const RenderBatch& batch);
};

// 渲染优化器
class CRenderOptimizer
{
public:
    CRenderOptimizer();
    ~CRenderOptimizer();

    // 纹理图集优化
    BOOL OptimizeTextureAtlas();
    
    // 几何体优化
    BOOL OptimizeGeometry();
    
    // 状态排序优化
    void OptimizeStateChanges();
    
    // 视锥剔除
    void FrustumCull();
    
    // 遮挡剔除
    void OcclusionCull();
    
    // LOD管理
    void UpdateLOD();

private:
    // 优化参数
    float m_fLODDistance[4];        // LOD距离
    DWORD m_dwMaxDrawCalls;         // 最大绘制调用数
    BOOL m_bFrustumCulling;         // 视锥剔除开关
    BOOL m_bOcclusionCulling;       // 遮挡剔除开关
};

// 全局渲染批处理器
extern CRenderBatcher g_RenderBatcher;
extern CRenderOptimizer g_RenderOptimizer;

// 便捷宏定义
#define RENDER_ADD_ITEM(item)       g_RenderBatcher.AddRenderItem(item)
#define RENDER_FLUSH()              g_RenderBatcher.RenderBatches()
#define RENDER_CLEAR()              g_RenderBatcher.ClearRenderItems()

// 渲染项构建辅助函数
inline RenderItem CreateSpriteRenderItem(DWORD dwTexture, void* pVertices, DWORD dwVertexCount, 
                                         RENDER_LAYER eLayer = RL_OBJECTS, float fDepth = 0.0f)
{
    RenderItem item;
    item.eType = ROT_SPRITE;
    item.eLayer = eLayer;
    item.state.dwTexture = dwTexture;
    item.state.dwBlendMode = 0; // 默认混合模式
    item.state.dwShader = 0;    // 默认着色器
    item.state.bDepthTest = TRUE;
    item.state.bAlphaTest = TRUE;
    item.state.fAlphaRef = 0.5f;
    item.pVertexData = pVertices;
    item.dwVertexCount = dwVertexCount;
    item.pIndexData = NULL;
    item.dwIndexCount = 0;
    item.fDepth = fDepth;
    item.dwSortKey = 0;
    return item;
}

inline RenderItem CreateUIRenderItem(DWORD dwTexture, void* pVertices, DWORD dwVertexCount, float fDepth = 0.0f)
{
    RenderItem item = CreateSpriteRenderItem(dwTexture, pVertices, dwVertexCount, RL_UI, fDepth);
    item.eType = ROT_UI_ELEMENT;
    item.state.bDepthTest = FALSE; // UI通常不需要深度测试
    return item;
}

// 性能分析宏
#ifdef _DEBUG
#define RENDER_PROFILE_BEGIN(name) \
    DWORD dwStart##name = GetTickCount()

#define RENDER_PROFILE_END(name) \
    DWORD dwEnd##name = GetTickCount(); \
    OutputDebugStringA("[RENDER] " #name ": "); \
    char szTime[64]; \
    sprintf_s(szTime, sizeof(szTime), "%d ms\n", dwEnd##name - dwStart##name); \
    OutputDebugStringA(szTime)
#else
#define RENDER_PROFILE_BEGIN(name)
#define RENDER_PROFILE_END(name)
#endif

#endif // RENDER_BATCH_H
