//---------------------------------------------------------------------------
// Sword2 Game Loop Optimizer (c) 2024
//
// File:	GameLoopOptimizer.h
// Date:	2024.07.02
// Code:	AI Assistant
// Desc:	Game loop performance optimization system
//---------------------------------------------------------------------------
#ifndef GAME_LOOP_OPTIMIZER_H
#define GAME_LOOP_OPTIMIZER_H

#include <windows.h>

// 游戏循环阶段
enum GAME_LOOP_PHASE
{
    GLP_INPUT = 0,          // 输入处理
    GLP_NETWORK,            // 网络处理
    GLP_LOGIC,              // 游戏逻辑
    GLP_PHYSICS,            // 物理模拟
    GLP_ANIMATION,          // 动画更新
    GLP_RENDER,             // 渲染
    GLP_AUDIO,              // 音频处理
    GLP_COUNT
};

// 性能预算配置
struct PerformanceBudget
{
    float fTargetFPS;                       // 目标帧率
    DWORD dwTargetFrameTime;                // 目标帧时间(ms)
    DWORD dwPhaseBudget[GLP_COUNT];         // 各阶段时间预算(ms)
    DWORD dwMaxFrameTime;                   // 最大帧时间(ms)
    BOOL bAdaptiveQuality;                  // 自适应质量
    BOOL bFrameSkipping;                    // 跳帧
};

// 性能统计
struct PerformanceStats
{
    DWORD dwFrameCount;                     // 帧数统计
    DWORD dwCurrentFrameTime;               // 当前帧时间
    DWORD dwAverageFrameTime;               // 平均帧时间
    DWORD dwMaxFrameTime;                   // 最大帧时间
    DWORD dwMinFrameTime;                   // 最小帧时间
    DWORD dwPhaseTime[GLP_COUNT];           // 各阶段时间
    DWORD dwPhaseOverruns[GLP_COUNT];       // 各阶段超时次数
    float fCurrentFPS;                      // 当前FPS
    float fAverageFPS;                      // 平均FPS
    DWORD dwSkippedFrames;                  // 跳过的帧数
    DWORD dwQualityAdjustments;             // 质量调整次数
};

// 自适应质量设置
struct AdaptiveQuality
{
    int nRenderQuality;         // 渲染质量 (0-100)
    int nEffectQuality;         // 特效质量 (0-100)
    int nShadowQuality;         // 阴影质量 (0-100)
    int nTextureQuality;        // 纹理质量 (0-100)
    int nParticleQuality;       // 粒子质量 (0-100)
    BOOL bVSync;                // 垂直同步
    BOOL bAntiAliasing;         // 抗锯齿
    BOOL bAnisotropicFilter;    // 各向异性过滤
};

// 游戏循环优化器
class CGameLoopOptimizer
{
public:
    CGameLoopOptimizer();
    ~CGameLoopOptimizer();

    BOOL Initialize();
    void Cleanup();

    // 帧管理
    void BeginFrame();
    void EndFrame();
    void BeginPhase(GAME_LOOP_PHASE ePhase);
    void EndPhase(GAME_LOOP_PHASE ePhase);

    // 性能预算管理
    void SetPerformanceBudget(const PerformanceBudget& budget);
    const PerformanceBudget& GetPerformanceBudget() const { return m_Budget; }

    // 自适应质量
    void UpdateAdaptiveQuality();
    void SetQualityLevel(int nLevel); // 0-100
    const AdaptiveQuality& GetCurrentQuality() const { return m_CurrentQuality; }

    // 帧率控制
    void LimitFrameRate();
    BOOL ShouldSkipFrame();
    void ForceFrameSkip() { m_bForceSkipNextFrame = TRUE; }

    // 统计信息
    void GetStats(PerformanceStats* pStats);
    void ResetStats();
    void LogStats();

    // 配置
    void SetTargetFPS(float fFPS);
    void SetAdaptiveQualityEnabled(BOOL bEnabled) { m_bAdaptiveQuality = bEnabled; }
    void SetFrameSkippingEnabled(BOOL bEnabled) { m_bFrameSkipping = bEnabled; }

private:
    PerformanceBudget m_Budget;             // 性能预算
    PerformanceStats m_Stats;               // 性能统计
    AdaptiveQuality m_CurrentQuality;       // 当前质量设置
    AdaptiveQuality m_DefaultQuality;       // 默认质量设置

    // 时间管理
    LARGE_INTEGER m_liFrequency;            // 高精度计时器频率
    LARGE_INTEGER m_liFrameStart;           // 帧开始时间
    LARGE_INTEGER m_liPhaseStart[GLP_COUNT]; // 各阶段开始时间
    DWORD m_dwLastFrameTime;                // 上一帧时间
    DWORD m_dwFrameTimeHistory[60];         // 帧时间历史(1秒)
    int m_nFrameHistoryIndex;               // 历史索引

    // 控制标志
    BOOL m_bInitialized;
    BOOL m_bAdaptiveQuality;
    BOOL m_bFrameSkipping;
    BOOL m_bForceSkipNextFrame;
    GAME_LOOP_PHASE m_eCurrentPhase;

    // 质量调整
    DWORD m_dwLastQualityAdjust;            // 上次质量调整时间
    int m_nQualityAdjustDirection;          // 质量调整方向 (-1, 0, 1)
    DWORD m_dwConsecutiveOverruns;          // 连续超时次数

    // 内部方法
    void UpdateFrameTimeHistory();
    void CheckPerformanceBudget();
    void AdjustQuality(int nDirection);
    DWORD GetAverageFrameTime();
    BOOL IsPerformanceStable();
    void ApplyQualitySettings();
};

// 性能分析器
class CPerformanceProfiler
{
public:
    CPerformanceProfiler();
    ~CPerformanceProfiler();

    // 性能标记
    void BeginSample(const char* pName);
    void EndSample(const char* pName);

    // 自动性能标记类
    class CSampleScope
    {
    public:
        CSampleScope(CPerformanceProfiler* pProfiler, const char* pName)
            : m_pProfiler(pProfiler), m_pName(pName)
        {
            if (m_pProfiler) m_pProfiler->BeginSample(m_pName);
        }
        
        ~CSampleScope()
        {
            if (m_pProfiler) m_pProfiler->EndSample(m_pName);
        }

    private:
        CPerformanceProfiler* m_pProfiler;
        const char* m_pName;
    };

    // 报告生成
    void GenerateReport();
    void SaveReport(const char* pFilename);

private:
    struct SampleData
    {
        const char* pName;
        LARGE_INTEGER liStartTime;
        LARGE_INTEGER liTotalTime;
        DWORD dwCallCount;
        DWORD dwMaxTime;
        DWORD dwMinTime;
    };

    std::vector<SampleData> m_Samples;
    LARGE_INTEGER m_liFrequency;
    CRITICAL_SECTION m_CriticalSection;
};

// 全局实例
extern CGameLoopOptimizer g_GameLoopOptimizer;
extern CPerformanceProfiler g_PerformanceProfiler;

// 便捷宏定义
#define GAME_LOOP_BEGIN_FRAME()         g_GameLoopOptimizer.BeginFrame()
#define GAME_LOOP_END_FRAME()           g_GameLoopOptimizer.EndFrame()
#define GAME_LOOP_BEGIN_PHASE(phase)    g_GameLoopOptimizer.BeginPhase(phase)
#define GAME_LOOP_END_PHASE(phase)      g_GameLoopOptimizer.EndPhase(phase)
#define GAME_LOOP_LIMIT_FPS()           g_GameLoopOptimizer.LimitFrameRate()
#define GAME_LOOP_SHOULD_SKIP()         g_GameLoopOptimizer.ShouldSkipFrame()

#define PERF_SAMPLE(name) \
    CPerformanceProfiler::CSampleScope _sample(&g_PerformanceProfiler, name)

// 默认性能预算配置
inline PerformanceBudget GetDefaultPerformanceBudget()
{
    PerformanceBudget budget;
    budget.fTargetFPS = 60.0f;
    budget.dwTargetFrameTime = 16; // ~60 FPS
    budget.dwPhaseBudget[GLP_INPUT] = 1;
    budget.dwPhaseBudget[GLP_NETWORK] = 2;
    budget.dwPhaseBudget[GLP_LOGIC] = 4;
    budget.dwPhaseBudget[GLP_PHYSICS] = 2;
    budget.dwPhaseBudget[GLP_ANIMATION] = 2;
    budget.dwPhaseBudget[GLP_RENDER] = 10;
    budget.dwPhaseBudget[GLP_AUDIO] = 1;
    budget.dwMaxFrameTime = 33; // 最大30 FPS
    budget.bAdaptiveQuality = TRUE;
    budget.bFrameSkipping = TRUE;
    return budget;
}

// 默认质量设置
inline AdaptiveQuality GetDefaultQuality()
{
    AdaptiveQuality quality;
    quality.nRenderQuality = 80;
    quality.nEffectQuality = 70;
    quality.nShadowQuality = 60;
    quality.nTextureQuality = 80;
    quality.nParticleQuality = 70;
    quality.bVSync = FALSE;
    quality.bAntiAliasing = FALSE;
    quality.bAnisotropicFilter = FALSE;
    return quality;
}

#endif // GAME_LOOP_OPTIMIZER_H
