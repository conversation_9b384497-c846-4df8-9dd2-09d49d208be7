//---------------------------------------------------------------------------
// Sword2 Combat Manager (c) 2024
//
// File:	CombatManager.h
// Date:	2024.07.02
// Code:	AI Assistant
// Desc:	Comprehensive combat management system
//---------------------------------------------------------------------------
#ifndef COMBAT_MANAGER_H
#define COMBAT_MANAGER_H

#include "CombatSystem.h"
#include "PlayerManager.h"
#include "NPCManager.h"
#include "SkillManager.h"
#include "ItemManager.h"
#include "LuaScriptEngine.h"
#include "UnifiedLoggingSystem.h"
#include <unordered_map>
#include <memory>
#include <mutex>
#include <shared_mutex>
#include <thread>
#include <atomic>
#include <queue>
#include <functional>

namespace sword2 {

// 战斗操作结果
enum class CombatResult : uint8_t
{
    Success = 0,        // 成功
    Failed,             // 失败
    NotInRange,         // 不在范围内
    NotInCombat,        // 不在战斗中
    AlreadyInCombat,    // 已在战斗中
    InvalidTarget,      // 无效目标
    InsufficientMana,   // 内力不足
    OnCooldown,         // 技能冷却中
    Stunned,            // 被眩晕
    Silenced,           // 被沉默
    Dead,               // 已死亡
    Immune              // 免疫
};

// 战斗管理器
class CombatManager : public Singleton<CombatManager>
{
public:
    CombatManager()
        : m_running(false), m_nextEffectId(1), m_updateInterval(std::chrono::milliseconds(100)) {}
    
    ~CombatManager()
    {
        Stop();
    }
    
    // 启动战斗管理器
    bool Start()
    {
        if (m_running) return true;
        
        m_running = true;
        m_updateThread = std::thread(&CombatManager::UpdateLoop, this);
        
        LOG_INFO("COMBAT_MGR", "Combat manager started");
        return true;
    }
    
    // 停止战斗管理器
    void Stop()
    {
        if (!m_running) return;
        
        m_running = false;
        if (m_updateThread.joinable())
        {
            m_updateThread.join();
        }
        
        // 清理数据
        {
            std::unique_lock<std::shared_mutex> lock(m_participantsMutex);
            m_participants.clear();
        }
        
        {
            std::unique_lock<std::shared_mutex> lock(m_effectsMutex);
            m_activeEffects.clear();
            m_targetEffects.clear();
        }
        
        LOG_INFO("COMBAT_MGR", "Combat manager stopped");
    }
    
    // 注册战斗参与者
    void RegisterParticipant(uint32_t entityId, bool isPlayer)
    {
        std::unique_lock<std::shared_mutex> lock(m_participantsMutex);
        
        if (m_participants.find(entityId) == m_participants.end())
        {
            m_participants[entityId] = std::make_unique<CombatParticipant>(entityId, isPlayer);
            LOG_DEBUG("COMBAT_MGR", "Registered combat participant: " + std::to_string(entityId));
        }
    }
    
    // 注销战斗参与者
    void UnregisterParticipant(uint32_t entityId)
    {
        std::unique_lock<std::shared_mutex> lock(m_participantsMutex);
        
        auto it = m_participants.find(entityId);
        if (it != m_participants.end())
        {
            // 清理该参与者的所有状态效果
            ClearAllEffects(entityId);
            
            m_participants.erase(it);
            LOG_DEBUG("COMBAT_MGR", "Unregistered combat participant: " + std::to_string(entityId));
        }
    }
    
    // 获取战斗参与者
    CombatParticipant* GetParticipant(uint32_t entityId)
    {
        std::shared_lock<std::shared_mutex> lock(m_participantsMutex);
        auto it = m_participants.find(entityId);
        return (it != m_participants.end()) ? it->second.get() : nullptr;
    }
    
    // 开始攻击
    CombatResult StartAttack(uint32_t attackerId, uint32_t targetId)
    {
        auto attacker = GetParticipant(attackerId);
        auto target = GetParticipant(targetId);
        
        if (!attacker || !target)
        {
            return CombatResult::InvalidTarget;
        }
        
        // 检查攻击者状态
        if (HasEffectType(attackerId, CombatStateType::Stun))
        {
            return CombatResult::Stunned;
        }
        
        if (HasEffectType(attackerId, CombatStateType::Silence))
        {
            return CombatResult::Silenced;
        }
        
        // 进入战斗状态
        attacker->EnterCombat(targetId);
        target->EnterCombat(attackerId);
        
        // 触发战斗开始事件
        TriggerCombatEvent(CombatEvent::CombatStart, attackerId, targetId);
        
        LOG_DEBUG("COMBAT_MGR", "Combat started between " + std::to_string(attackerId) + " and " + std::to_string(targetId));
        return CombatResult::Success;
    }
    
    // 执行普通攻击
    CombatResult PerformAttack(uint32_t attackerId, uint32_t targetId)
    {
        auto attackerPlayer = GET_ONLINE_PLAYER(attackerId);
        auto targetPlayer = GET_ONLINE_PLAYER(targetId);
        
        if (!attackerPlayer || !targetPlayer)
        {
            return CombatResult::InvalidTarget;
        }
        
        // 检查距离
        if (!IsInAttackRange(attackerId, targetId))
        {
            return CombatResult::NotInRange;
        }
        
        // 创建伤害信息
        DamageInfo damageInfo(attackerId, targetId, 0, DamageType::Physical);
        
        // 计算命中
        if (!CombatCalculator::CalculateHit(*attackerPlayer, *targetPlayer))
        {
            damageInfo.result = AttackResult::Miss;
            TriggerCombatEvent(CombatEvent::AttackMiss, attackerId, targetId);
            LOG_DEBUG("COMBAT_MGR", "Attack missed: " + std::to_string(attackerId) + " -> " + std::to_string(targetId));
            return CombatResult::Success;
        }
        
        // 计算闪避
        if ((rand() % 100) < targetPlayer->attributes.dodge)
        {
            damageInfo.result = AttackResult::Dodge;
            damageInfo.isDodged = true;
            TriggerCombatEvent(CombatEvent::AttackMiss, attackerId, targetId);
            LOG_DEBUG("COMBAT_MGR", "Attack dodged: " + std::to_string(attackerId) + " -> " + std::to_string(targetId));
            return CombatResult::Success;
        }
        
        // 计算暴击
        bool isCritical = CombatCalculator::CalculateCritical(*attackerPlayer, *targetPlayer);
        if (isCritical)
        {
            damageInfo.result = AttackResult::Critical;
            damageInfo.isCritical = true;
        }
        
        // 计算伤害
        uint32_t damage = CombatCalculator::CalculatePhysicalDamage(*attackerPlayer, *targetPlayer, isCritical);
        damageInfo.baseDamage = damage;
        damageInfo.finalDamage = damage;
        
        // 应用伤害
        ApplyDamage(damageInfo);
        
        return CombatResult::Success;
    }
    
    // 执行技能攻击
    CombatResult PerformSkillAttack(uint32_t casterId, uint32_t skillId, uint32_t targetId = 0, int32_t x = 0, int32_t y = 0)
    {
        auto casterPlayer = GET_ONLINE_PLAYER(casterId);
        if (!casterPlayer)
        {
            return CombatResult::InvalidTarget;
        }
        
        // 检查技能是否存在
        auto skillTemplate = GET_SKILL_TEMPLATE(skillId);
        if (!skillTemplate)
        {
            return CombatResult::Failed;
        }
        
        // 检查施法者状态
        if (HasEffectType(casterId, CombatStateType::Silence))
        {
            return CombatResult::Silenced;
        }
        
        if (HasEffectType(casterId, CombatStateType::Stun))
        {
            return CombatResult::Stunned;
        }
        
        // 检查内力消耗
        uint32_t manaCost = skillTemplate->GetManaCost(1); // 暂时使用1级
        if (casterPlayer->attributes.currentMana < manaCost)
        {
            return CombatResult::InsufficientMana;
        }
        
        // 扣除内力
        casterPlayer->attributes.currentMana -= manaCost;
        
        // 处理不同类型的技能
        if (skillTemplate->targetType == SkillTargetType::Enemy && targetId != 0)
        {
            // 单体攻击技能
            auto targetPlayer = GET_ONLINE_PLAYER(targetId);
            if (targetPlayer)
            {
                uint32_t damage = CombatCalculator::CalculateSkillDamage(skillId, 1, *casterPlayer, *targetPlayer);
                
                DamageInfo damageInfo(casterId, targetId, damage, skillTemplate->damageType);
                damageInfo.skillId = skillId;
                
                ApplyDamage(damageInfo);
            }
        }
        else if (skillTemplate->targetType == SkillTargetType::Self)
        {
            // 自身增益技能
            ApplySkillEffects(skillId, casterId, casterId);
        }
        else if (skillTemplate->targetType == SkillTargetType::Area)
        {
            // 范围技能
            ApplyAreaSkill(skillId, casterId, x, y);
        }
        
        LOG_DEBUG("COMBAT_MGR", "Player " + std::to_string(casterId) + " used skill " + std::to_string(skillId));
        return CombatResult::Success;
    }
    
    // 应用伤害
    void ApplyDamage(const DamageInfo& damageInfo)
    {
        auto targetPlayer = GET_ONLINE_PLAYER(damageInfo.targetId);
        if (!targetPlayer)
            return;
        
        // 检查免疫
        if (HasEffectType(damageInfo.targetId, CombatStateType::Invincible) ||
            HasEffectType(damageInfo.targetId, CombatStateType::Immune))
        {
            LOG_DEBUG("COMBAT_MGR", "Damage immune: " + std::to_string(damageInfo.targetId));
            return;
        }
        
        uint32_t finalDamage = damageInfo.finalDamage;
        
        // 检查护盾
        if (HasEffectType(damageInfo.targetId, CombatStateType::Shield))
        {
            finalDamage = ApplyShieldDamage(damageInfo.targetId, finalDamage);
        }
        
        // 应用伤害
        uint32_t actualDamage = std::min(finalDamage, targetPlayer->attributes.currentLife);
        targetPlayer->attributes.currentLife -= actualDamage;
        
        // 更新战斗统计
        auto attacker = GetParticipant(damageInfo.attackerId);
        auto target = GetParticipant(damageInfo.targetId);
        
        if (attacker)
        {
            attacker->totalDamageDealt += actualDamage;
        }
        
        if (target)
        {
            target->totalDamageTaken += actualDamage;
        }
        
        // 触发受伤事件
        TriggerCombatEvent(CombatEvent::TakeDamage, damageInfo.targetId, damageInfo.attackerId);
        
        // 检查死亡
        if (targetPlayer->attributes.currentLife == 0)
        {
            HandleDeath(damageInfo.targetId, damageInfo.attackerId);
        }
        
        LOG_DEBUG("COMBAT_MGR", "Applied " + std::to_string(actualDamage) + " damage to " + std::to_string(damageInfo.targetId));
    }
    
    // 应用治疗
    void ApplyHeal(const HealInfo& healInfo)
    {
        auto targetPlayer = GET_ONLINE_PLAYER(healInfo.targetId);
        if (!targetPlayer)
            return;
        
        uint32_t finalHeal = healInfo.finalHeal;
        
        // 计算治疗加成
        if (healInfo.isCritical)
        {
            finalHeal = static_cast<uint32_t>(finalHeal * healInfo.criticalMultiplier);
        }
        
        // 应用治疗
        uint32_t maxLife = targetPlayer->attributes.maxLife;
        uint32_t currentLife = targetPlayer->attributes.currentLife;
        uint32_t actualHeal = std::min(finalHeal, maxLife - currentLife);
        
        targetPlayer->attributes.currentLife += actualHeal;
        
        // 更新战斗统计
        auto healer = GetParticipant(healInfo.healerId);
        if (healer)
        {
            healer->totalHealingDone += actualHeal;
        }
        
        // 触发治疗事件
        TriggerCombatEvent(CombatEvent::Heal, healInfo.targetId, healInfo.healerId);
        
        LOG_DEBUG("COMBAT_MGR", "Applied " + std::to_string(actualHeal) + " heal to " + std::to_string(healInfo.targetId));
    }
    
    // 应用状态效果
    uint32_t ApplyStateEffect(uint32_t casterId, uint32_t targetId, CombatStateType type, 
                             int32_t value, uint32_t duration, uint32_t skillId = 0)
    {
        std::unique_lock<std::shared_mutex> lock(m_effectsMutex);
        
        uint32_t effectId = m_nextEffectId++;
        CombatStateEffect effect(effectId, type, casterId, targetId, value, duration, skillId);
        
        // 检查是否可以叠加
        bool canStack = CanStackEffect(targetId, type);
        if (!canStack)
        {
            // 移除同类型的旧效果
            RemoveEffectsByType(targetId, type);
        }
        
        m_activeEffects[effectId] = effect;
        m_targetEffects[targetId].push_back(effectId);
        
        // 更新参与者状态
        auto participant = GetParticipant(targetId);
        if (participant)
        {
            participant->AddEffect(effectId);
        }
        
        // 触发状态施加事件
        TriggerCombatEvent(CombatEvent::StateApply, targetId, casterId);
        
        LOG_DEBUG("COMBAT_MGR", "Applied state effect " + std::to_string(static_cast<int>(type)) + 
                 " to " + std::to_string(targetId) + " (ID: " + std::to_string(effectId) + ")");
        
        return effectId;
    }
    
    // 移除状态效果
    bool RemoveStateEffect(uint32_t effectId)
    {
        std::unique_lock<std::shared_mutex> lock(m_effectsMutex);
        
        auto it = m_activeEffects.find(effectId);
        if (it != m_activeEffects.end())
        {
            uint32_t targetId = it->second.targetId;
            
            // 从目标效果列表中移除
            auto& targetEffects = m_targetEffects[targetId];
            targetEffects.erase(std::remove(targetEffects.begin(), targetEffects.end(), effectId), 
                               targetEffects.end());
            
            // 从参与者中移除
            auto participant = GetParticipant(targetId);
            if (participant)
            {
                participant->RemoveEffect(effectId);
            }
            
            m_activeEffects.erase(it);
            
            // 触发状态移除事件
            TriggerCombatEvent(CombatEvent::StateRemove, targetId, 0);
            
            LOG_DEBUG("COMBAT_MGR", "Removed state effect " + std::to_string(effectId));
            return true;
        }
        
        return false;
    }
    
    // 检查是否有特定类型的状态效果
    bool HasEffectType(uint32_t targetId, CombatStateType type)
    {
        std::shared_lock<std::shared_mutex> lock(m_effectsMutex);
        
        auto it = m_targetEffects.find(targetId);
        if (it != m_targetEffects.end())
        {
            for (uint32_t effectId : it->second)
            {
                auto effectIt = m_activeEffects.find(effectId);
                if (effectIt != m_activeEffects.end() && 
                    effectIt->second.type == type && 
                    effectIt->second.isActive)
                {
                    return true;
                }
            }
        }
        
        return false;
    }
    
    // 获取目标的所有状态效果
    std::vector<CombatStateEffect> GetTargetEffects(uint32_t targetId)
    {
        std::shared_lock<std::shared_mutex> lock(m_effectsMutex);
        std::vector<CombatStateEffect> effects;
        
        auto it = m_targetEffects.find(targetId);
        if (it != m_targetEffects.end())
        {
            for (uint32_t effectId : it->second)
            {
                auto effectIt = m_activeEffects.find(effectId);
                if (effectIt != m_activeEffects.end() && effectIt->second.isActive)
                {
                    effects.push_back(effectIt->second);
                }
            }
        }
        
        return effects;
    }
    
    // 清理目标的所有状态效果
    void ClearAllEffects(uint32_t targetId)
    {
        std::unique_lock<std::shared_mutex> lock(m_effectsMutex);
        
        auto it = m_targetEffects.find(targetId);
        if (it != m_targetEffects.end())
        {
            for (uint32_t effectId : it->second)
            {
                m_activeEffects.erase(effectId);
            }
            
            it->second.clear();
        }
        
        // 从参与者中清理
        auto participant = GetParticipant(targetId);
        if (participant)
        {
            participant->activeEffects.clear();
        }
        
        LOG_DEBUG("COMBAT_MGR", "Cleared all effects for target " + std::to_string(targetId));
    }
    
    // 驱散状态效果
    uint32_t DispelEffects(uint32_t targetId, bool dispelDebuffs = true, uint32_t maxCount = 1)
    {
        std::unique_lock<std::shared_mutex> lock(m_effectsMutex);
        
        auto it = m_targetEffects.find(targetId);
        if (it == m_targetEffects.end())
            return 0;
        
        std::vector<uint32_t> toRemove;
        uint32_t dispelCount = 0;
        
        for (uint32_t effectId : it->second)
        {
            auto effectIt = m_activeEffects.find(effectId);
            if (effectIt != m_activeEffects.end())
            {
                const auto& effect = effectIt->second;
                if (effect.canDispel && effect.isDebuff == dispelDebuffs)
                {
                    toRemove.push_back(effectId);
                    dispelCount++;
                    
                    if (dispelCount >= maxCount)
                        break;
                }
            }
        }
        
        // 移除选中的效果
        for (uint32_t effectId : toRemove)
        {
            RemoveStateEffect(effectId);
        }
        
        LOG_DEBUG("COMBAT_MGR", "Dispelled " + std::to_string(dispelCount) + " effects from " + std::to_string(targetId));
        return dispelCount;
    }

private:
    std::atomic<bool> m_running;
    std::thread m_updateThread;
    std::chrono::milliseconds m_updateInterval;
    
    // 战斗参与者
    mutable std::shared_mutex m_participantsMutex;
    std::unordered_map<uint32_t, std::unique_ptr<CombatParticipant>> m_participants;
    
    // 状态效果
    mutable std::shared_mutex m_effectsMutex;
    std::unordered_map<uint32_t, CombatStateEffect> m_activeEffects;
    std::unordered_map<uint32_t, std::vector<uint32_t>> m_targetEffects; // targetId -> effectIds
    
    std::atomic<uint32_t> m_nextEffectId;
    
    // 更新循环
    void UpdateLoop()
    {
        while (m_running)
        {
            try
            {
                UpdateEffects();
                UpdateCombatStates();
                std::this_thread::sleep_for(m_updateInterval);
            }
            catch (const std::exception& e)
            {
                LOG_ERROR("COMBAT_MGR", std::string("Error in update loop: ") + e.what());
            }
        }
    }
    
    void UpdateEffects()
    {
        std::vector<uint32_t> expiredEffects;

        {
            std::shared_lock<std::shared_mutex> lock(m_effectsMutex);
            for (const auto& [effectId, effect] : m_activeEffects)
            {
                if (effect.IsExpired())
                {
                    expiredEffects.push_back(effectId);
                }
                else if (effect.ShouldTrigger())
                {
                    // 处理周期性效果
                    ProcessPeriodicEffect(effect);
                }
            }
        }

        // 移除过期效果
        for (uint32_t effectId : expiredEffects)
        {
            RemoveStateEffect(effectId);
        }
    }

    void UpdateCombatStates()
    {
        std::shared_lock<std::shared_mutex> lock(m_participantsMutex);

        for (const auto& [entityId, participant] : m_participants)
        {
            if (!participant || !participant->IsInCombat())
                continue;

            // 检查是否应该退出战斗
            if (ShouldExitCombat(entityId))
            {
                participant->ExitCombat();
                TriggerCombatEvent(CombatEvent::CombatEnd, entityId);
            }
        }
    }

    void HandleDeath(uint32_t targetId, uint32_t killerId)
    {
        // 更新击杀统计
        auto killer = GetParticipant(killerId);
        auto target = GetParticipant(targetId);

        if (killer)
        {
            killer->killCount++;
        }

        if (target)
        {
            target->deathCount++;
            target->ExitCombat();
        }

        // 清理死亡目标的所有状态效果
        ClearAllEffects(targetId);

        // 触发死亡事件
        TriggerCombatEvent(CombatEvent::Death, targetId, killerId);

        LOG_INFO("COMBAT_MGR", "Player " + std::to_string(targetId) + " killed by " + std::to_string(killerId));
    }

    void ApplySkillEffects(uint32_t skillId, uint32_t casterId, uint32_t targetId)
    {
        auto skillTemplate = GET_SKILL_TEMPLATE(skillId);
        if (!skillTemplate) return;

        // 应用技能的状态效果
        for (const auto& effect : skillTemplate->effects)
        {
            CombatStateType stateType = static_cast<CombatStateType>(effect.type);
            ApplyStateEffect(casterId, targetId, stateType, effect.value, effect.duration, skillId);
        }

        // 如果是治疗技能
        if (skillTemplate->healAmount > 0)
        {
            auto caster = GET_ONLINE_PLAYER(casterId);
            if (caster)
            {
                uint32_t healAmount = CombatCalculator::CalculateHealAmount(skillId, 1, *caster);
                HealInfo healInfo(casterId, targetId, healAmount);
                healInfo.skillId = skillId;
                ApplyHeal(healInfo);
            }
        }
    }

    void ApplyAreaSkill(uint32_t skillId, uint32_t casterId, int32_t x, int32_t y)
    {
        auto skillTemplate = GET_SKILL_TEMPLATE(skillId);
        if (!skillTemplate) return;

        auto caster = GET_ONLINE_PLAYER(casterId);
        if (!caster) return;

        // 获取范围内的所有目标
        std::vector<uint32_t> targets = GetTargetsInArea(x, y, skillTemplate->areaRange);

        for (uint32_t targetId : targets)
        {
            auto target = GET_ONLINE_PLAYER(targetId);
            if (!target) continue;

            // 检查是否为有效目标
            if (!IsValidTarget(casterId, targetId, skillTemplate->targetType))
                continue;

            // 计算伤害
            if (skillTemplate->damage > 0)
            {
                uint32_t damage = CombatCalculator::CalculateSkillDamage(skillId, 1, *caster, *target);
                DamageInfo damageInfo(casterId, targetId, damage, skillTemplate->damageType);
                damageInfo.skillId = skillId;
                ApplyDamage(damageInfo);
            }

            // 应用状态效果
            ApplySkillEffects(skillId, casterId, targetId);
        }
    }

    uint32_t ApplyShieldDamage(uint32_t targetId, uint32_t damage)
    {
        // 查找护盾效果
        auto effects = GetTargetEffects(targetId);
        uint32_t remainingDamage = damage;

        for (auto& effect : effects)
        {
            if (effect.type == CombatStateType::Shield && effect.isActive)
            {
                if (effect.value >= remainingDamage)
                {
                    // 护盾完全吸收伤害
                    effect.value -= remainingDamage;
                    remainingDamage = 0;
                    break;
                }
                else
                {
                    // 护盾部分吸收伤害
                    remainingDamage -= effect.value;
                    effect.value = 0;
                    effect.isActive = false;
                }
            }
        }

        return remainingDamage;
    }

    bool IsInAttackRange(uint32_t attackerId, uint32_t targetId)
    {
        auto attacker = GET_ONLINE_PLAYER(attackerId);
        auto target = GET_ONLINE_PLAYER(targetId);

        if (!attacker || !target)
            return false;

        // 计算距离
        float distance = CombatAIHelper::CalculateDistance(
            attacker->position.x, attacker->position.y,
            target->position.x, target->position.y);

        // 获取攻击范围（这里应该根据武器类型计算）
        float attackRange = 2.0f; // 默认近战范围

        return distance <= attackRange;
    }

    bool CanStackEffect(uint32_t targetId, CombatStateType type)
    {
        // 大部分状态效果不能叠加，只有少数可以
        switch (type)
        {
        case CombatStateType::Poison:
        case CombatStateType::Burn:
        case CombatStateType::Shield:
            return true;
        default:
            return false;
        }
    }

    void RemoveEffectsByType(uint32_t targetId, CombatStateType type)
    {
        std::unique_lock<std::shared_mutex> lock(m_effectsMutex);

        auto it = m_targetEffects.find(targetId);
        if (it != m_targetEffects.end())
        {
            std::vector<uint32_t> toRemove;

            for (uint32_t effectId : it->second)
            {
                auto effectIt = m_activeEffects.find(effectId);
                if (effectIt != m_activeEffects.end() && effectIt->second.type == type)
                {
                    toRemove.push_back(effectId);
                }
            }

            for (uint32_t effectId : toRemove)
            {
                RemoveStateEffect(effectId);
            }
        }
    }

    void TriggerCombatEvent(CombatEvent event, uint32_t entityId, uint32_t otherId = 0)
    {
        TRIGGER_COMBAT_EVENT(event, entityId, otherId);
    }

    // 辅助方法
    void ProcessPeriodicEffect(const CombatStateEffect& effect)
    {
        // 处理周期性效果（如毒、燃烧、恢复等）
        switch (effect.type)
        {
        case CombatStateType::Poison:
        case CombatStateType::Burn:
            {
                DamageInfo damageInfo(effect.casterId, effect.targetId, effect.value, DamageType::Poison);
                damageInfo.skillId = effect.skillId;
                ApplyDamage(damageInfo);
            }
            break;

        case CombatStateType::LifeRegen:
            {
                HealInfo healInfo(effect.casterId, effect.targetId, effect.value);
                healInfo.skillId = effect.skillId;
                ApplyHeal(healInfo);
            }
            break;

        case CombatStateType::ManaRegen:
            {
                auto target = GET_ONLINE_PLAYER(effect.targetId);
                if (target)
                {
                    uint32_t maxMana = target->attributes.maxMana;
                    uint32_t currentMana = target->attributes.currentMana;
                    uint32_t healAmount = std::min(effect.value, maxMana - currentMana);
                    target->attributes.currentMana += healAmount;
                }
            }
            break;

        default:
            break;
        }

        // 更新触发时间
        const_cast<CombatStateEffect&>(effect).UpdateTriggerTime();
    }

    bool ShouldExitCombat(uint32_t entityId)
    {
        auto participant = GetParticipant(entityId);
        if (!participant || !participant->IsInCombat())
            return false;

        // 检查是否长时间没有战斗活动
        auto now = std::chrono::system_clock::now();
        auto timeSinceLastAttack = std::chrono::duration_cast<std::chrono::seconds>(now - participant->lastAttackTime);

        return timeSinceLastAttack.count() > 10; // 10秒没有攻击则退出战斗
    }

    std::vector<uint32_t> GetTargetsInArea(int32_t x, int32_t y, uint32_t range)
    {
        std::vector<uint32_t> targets;

        // 这里应该查询地图系统获取范围内的所有实体
        // 暂时返回空列表
        return targets;
    }

    bool IsValidTarget(uint32_t casterId, uint32_t targetId, SkillTargetType targetType)
    {
        if (casterId == targetId)
        {
            return targetType == SkillTargetType::Self;
        }

        // 这里应该检查阵营、PK模式等
        return targetType == SkillTargetType::Enemy || targetType == SkillTargetType::Any;
    }
};

} // namespace sword2

// 全局战斗管理器访问
#define COMBAT_MANAGER() sword2::CombatManager::getInstance()

// 便捷宏定义
#define START_COMBAT_SYSTEM() COMBAT_MANAGER().Start()
#define STOP_COMBAT_SYSTEM() COMBAT_MANAGER().Stop()

#define REGISTER_COMBAT_PARTICIPANT(entityId, isPlayer) COMBAT_MANAGER().RegisterParticipant(entityId, isPlayer)
#define UNREGISTER_COMBAT_PARTICIPANT(entityId) COMBAT_MANAGER().UnregisterParticipant(entityId)

#define START_ATTACK(attackerId, targetId) COMBAT_MANAGER().StartAttack(attackerId, targetId)
#define PERFORM_ATTACK(attackerId, targetId) COMBAT_MANAGER().PerformAttack(attackerId, targetId)
#define PERFORM_SKILL_ATTACK(casterId, skillId, targetId, x, y) COMBAT_MANAGER().PerformSkillAttack(casterId, skillId, targetId, x, y)

#define APPLY_STATE_EFFECT(casterId, targetId, type, value, duration, skillId) COMBAT_MANAGER().ApplyStateEffect(casterId, targetId, type, value, duration, skillId)
#define REMOVE_STATE_EFFECT(effectId) COMBAT_MANAGER().RemoveStateEffect(effectId)
#define HAS_EFFECT_TYPE(targetId, type) COMBAT_MANAGER().HasEffectType(targetId, type)
#define GET_TARGET_EFFECTS(targetId) COMBAT_MANAGER().GetTargetEffects(targetId)
#define CLEAR_ALL_EFFECTS(targetId) COMBAT_MANAGER().ClearAllEffects(targetId)
#define DISPEL_EFFECTS(targetId, dispelDebuffs, maxCount) COMBAT_MANAGER().DispelEffects(targetId, dispelDebuffs, maxCount)

#endif // COMBAT_MANAGER_H
