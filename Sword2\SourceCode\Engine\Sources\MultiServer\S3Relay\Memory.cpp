// Memory.cpp: implementation of the CMemory class.
//
//////////////////////////////////////////////////////////////////////

#include "stdafx.h"
#include "Memory.h"
#include "../../Core/Src/MemoryPool.h"

//////////////////////////////////////////////////////////////////////
// Construction/Destruction
//////////////////////////////////////////////////////////////////////

CMemory::CMemory()
{

}

CMemory::~CMemory()
{

}


BOOL CMemory::Initialize()
{
	return TRUE;
}

BOOL CMemory::Uninitialize()
{
	return TRUE;
}



void* CMemory::Alloc(unsigned len)
{
	assert(len > 0);

	if (len > 0)
	{
		// 使用高性能内存池进行分配
		void* pMemory = g_MemoryPool.Allocate(len + sizeof(DWORD));
		if (pMemory)
		{
			// 存储引用计数
			*(DWORD*)pMemory = 1;
			return (BYTE*)pMemory + sizeof(DWORD);
		}

		// 内存池分配失败，回退到系统分配
		BYTE* pRef = (BYTE*)malloc(len + sizeof(DWORD));
		if (pRef)
		{
			*(DWORD*)pRef = 1;
			return pRef + sizeof(DWORD);
		}
	}

	return NULL;
}

void CMemory::Reuse(void* p)
{
	assert(p);

	if (p)
	{
		DWORD* pRef = (DWORD*)((BYTE*)p - sizeof(DWORD));
		(*pRef)++;
	}
}


void CMemory::Free(void* p)
{
	assert(p);

	if (p)
	{
		DWORD* pRef = (DWORD*)((BYTE*)p - sizeof(DWORD));
		if (--(*pRef) == 0)
		{
			// 尝试使用内存池释放
			if (!g_MemoryPool.Free(pRef))
			{
				// 内存池释放失败，使用系统释放
				free(pRef);
			}
		}
	}
}

unsigned CMemory::Size(void* p)
{
	assert(p);

	if (p)
	{
		BYTE* pRef = (BYTE*)p - 1;
		return _msize(p) - 1;
	}

	return 0;
}
