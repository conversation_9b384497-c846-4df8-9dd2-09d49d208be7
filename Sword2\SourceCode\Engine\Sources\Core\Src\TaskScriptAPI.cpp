//---------------------------------------------------------------------------
// Sword2 Task Script API Implementation (c) 2024
//
// File:	TaskScriptAPI.cpp
// Date:	2024.07.02
// Code:	AI Assistant
// Desc:	Implementation of task-related Lua script API functions
//---------------------------------------------------------------------------

#include "TaskScriptIntegration.h"
#include "TaskManager.h"
#include "PlayerManager.h"
#include "LuaScriptEngine.h"

namespace sword2 {
namespace TaskScriptAPI {

// 辅助函数：获取任务ID参数
uint32_t GetTaskIdFromLua(lua_State* L, int index = 1)
{
    if (lua_isnumber(L, index))
    {
        return static_cast<uint32_t>(lua_tonumber(L, index));
    }
    return 0;
}

// 辅助函数：获取玩家ID参数
uint32_t GetPlayerIdFromLua(lua_State* L, int index = 2)
{
    if (lua_isnumber(L, index))
    {
        return static_cast<uint32_t>(lua_tonumber(L, index));
    }
    return 0;
}

// 任务基础API实现
int GetTaskName(lua_State* L)
{
    uint32_t taskId = GetTaskIdFromLua(L);
    auto taskTemplate = GET_TASK_TEMPLATE(taskId);
    
    if (taskTemplate)
    {
        lua_pushstring(L, taskTemplate->taskName.c_str());
    }
    else
    {
        lua_pushstring(L, "");
    }
    return 1;
}

int GetTaskDescription(lua_State* L)
{
    uint32_t taskId = GetTaskIdFromLua(L);
    auto taskTemplate = GET_TASK_TEMPLATE(taskId);
    
    if (taskTemplate)
    {
        lua_pushstring(L, taskTemplate->description.c_str());
    }
    else
    {
        lua_pushstring(L, "");
    }
    return 1;
}

int GetTaskType(lua_State* L)
{
    uint32_t taskId = GetTaskIdFromLua(L);
    auto taskTemplate = GET_TASK_TEMPLATE(taskId);
    
    if (taskTemplate)
    {
        lua_pushnumber(L, static_cast<int>(taskTemplate->type));
        lua_pushstring(L, taskTemplate->GetTypeDescription().c_str());
        return 2;
    }
    else
    {
        lua_pushnumber(L, 0);
        lua_pushstring(L, "Unknown");
        return 2;
    }
}

int GetTaskLevel(lua_State* L)
{
    uint32_t taskId = GetTaskIdFromLua(L);
    auto taskTemplate = GET_TASK_TEMPLATE(taskId);
    
    if (taskTemplate)
    {
        lua_pushnumber(L, taskTemplate->level);
    }
    else
    {
        lua_pushnumber(L, 0);
    }
    return 1;
}

int GetTaskState(lua_State* L)
{
    uint32_t taskId = GetTaskIdFromLua(L);
    uint32_t playerId = GetPlayerIdFromLua(L);
    
    auto instance = GET_PLAYER_TASK(playerId, taskId);
    if (instance)
    {
        lua_pushnumber(L, static_cast<int>(instance->state));
        lua_pushstring(L, instance->GetStateDescription().c_str());
        return 2;
    }
    else
    {
        lua_pushnumber(L, static_cast<int>(TaskState::NotStarted));
        lua_pushstring(L, "Not Started");
        return 2;
    }
}

int GetTaskProgress(lua_State* L)
{
    uint32_t taskId = GetTaskIdFromLua(L);
    uint32_t playerId = GetPlayerIdFromLua(L);
    
    auto instance = GET_PLAYER_TASK(playerId, taskId);
    if (instance)
    {
        float progress = instance->GetProgressPercentage();
        lua_pushnumber(L, progress * 100.0f); // 返回百分比
        
        // 创建详细进度表
        lua_newtable(L);
        for (size_t i = 0; i < instance->entities.size(); ++i)
        {
            const auto& entity = instance->entities[i];
            
            lua_pushnumber(L, i + 1);
            lua_newtable(L);
            
            lua_pushstring(L, "description");
            lua_pushstring(L, entity.description.c_str());
            lua_settable(L, -3);
            
            lua_pushstring(L, "current");
            lua_pushnumber(L, entity.currentProgress);
            lua_settable(L, -3);
            
            lua_pushstring(L, "target");
            lua_pushnumber(L, entity.targetProgress);
            lua_settable(L, -3);
            
            lua_pushstring(L, "completed");
            lua_pushboolean(L, entity.IsCompleted() ? 1 : 0);
            lua_settable(L, -3);
            
            lua_settable(L, -3);
        }
        
        return 2;
    }
    else
    {
        lua_pushnumber(L, 0);
        lua_newtable(L);
        return 2;
    }
}

int GetTaskTimeLimit(lua_State* L)
{
    uint32_t taskId = GetTaskIdFromLua(L);
    auto taskTemplate = GET_TASK_TEMPLATE(taskId);
    
    if (taskTemplate)
    {
        lua_pushnumber(L, taskTemplate->timeLimit);
    }
    else
    {
        lua_pushnumber(L, 0);
    }
    return 1;
}

int GetTaskRemainingTime(lua_State* L)
{
    uint32_t taskId = GetTaskIdFromLua(L);
    uint32_t playerId = GetPlayerIdFromLua(L);
    
    auto instance = GET_PLAYER_TASK(playerId, taskId);
    if (instance)
    {
        lua_pushnumber(L, instance->GetRemainingTime());
    }
    else
    {
        lua_pushnumber(L, 0);
    }
    return 1;
}

// 任务操作API实现
int AcceptTask(lua_State* L)
{
    uint32_t playerId = GetPlayerIdFromLua(L, 1);
    uint32_t taskId = GetTaskIdFromLua(L, 2);
    
    TaskOperationResult result = ACCEPT_TASK(playerId, taskId);
    lua_pushnumber(L, static_cast<int>(result));
    
    if (result == TaskOperationResult::Success)
    {
        LOG_INFO("TASK_API", "Player " + std::to_string(playerId) + " accepted task " + std::to_string(taskId));
    }
    
    return 1;
}

int CompleteTask(lua_State* L)
{
    uint32_t playerId = GetPlayerIdFromLua(L, 1);
    uint32_t taskId = GetTaskIdFromLua(L, 2);
    
    TaskOperationResult result = COMPLETE_TASK(playerId, taskId);
    lua_pushnumber(L, static_cast<int>(result));
    
    if (result == TaskOperationResult::Success)
    {
        LOG_INFO("TASK_API", "Player " + std::to_string(playerId) + " completed task " + std::to_string(taskId));
    }
    
    return 1;
}

int SubmitTask(lua_State* L)
{
    uint32_t playerId = GetPlayerIdFromLua(L, 1);
    uint32_t taskId = GetTaskIdFromLua(L, 2);
    
    TaskOperationResult result = SUBMIT_TASK(playerId, taskId);
    lua_pushnumber(L, static_cast<int>(result));
    
    if (result == TaskOperationResult::Success)
    {
        LOG_INFO("TASK_API", "Player " + std::to_string(playerId) + " submitted task " + std::to_string(taskId));
    }
    
    return 1;
}

int CancelTask(lua_State* L)
{
    uint32_t playerId = GetPlayerIdFromLua(L, 1);
    uint32_t taskId = GetTaskIdFromLua(L, 2);
    
    TaskOperationResult result = CANCEL_TASK(playerId, taskId);
    lua_pushnumber(L, static_cast<int>(result));
    
    if (result == TaskOperationResult::Success)
    {
        LOG_INFO("TASK_API", "Player " + std::to_string(playerId) + " cancelled task " + std::to_string(taskId));
    }
    
    return 1;
}

int SkipTask(lua_State* L)
{
    uint32_t playerId = GetPlayerIdFromLua(L, 1);
    uint32_t taskId = GetTaskIdFromLua(L, 2);
    
    TaskOperationResult result = SKIP_TASK(playerId, taskId);
    lua_pushnumber(L, static_cast<int>(result));
    
    if (result == TaskOperationResult::Success)
    {
        LOG_INFO("TASK_API", "Player " + std::to_string(playerId) + " skipped task " + std::to_string(taskId));
    }
    
    return 1;
}

int UpdateTaskProgress(lua_State* L)
{
    uint32_t playerId = GetPlayerIdFromLua(L, 1);
    uint32_t taskId = GetTaskIdFromLua(L, 2);
    size_t entityIndex = lua_isnumber(L, 3) ? static_cast<size_t>(lua_tonumber(L, 3)) : 0;
    uint32_t amount = lua_isnumber(L, 4) ? static_cast<uint32_t>(lua_tonumber(L, 4)) : 1;
    
    bool success = UPDATE_TASK_PROGRESS(playerId, taskId, entityIndex, amount);
    lua_pushboolean(L, success ? 1 : 0);
    
    if (success)
    {
        LOG_DEBUG("TASK_API", "Updated task progress for player " + std::to_string(playerId) + 
                 " task " + std::to_string(taskId) + " entity " + std::to_string(entityIndex) + 
                 " amount " + std::to_string(amount));
    }
    
    return 1;
}

int SetTaskVariable(lua_State* L)
{
    uint32_t playerId = GetPlayerIdFromLua(L, 1);
    uint32_t taskId = GetTaskIdFromLua(L, 2);
    
    if (lua_isstring(L, 3) && lua_isnumber(L, 4))
    {
        std::string varName = lua_tostring(L, 3);
        int32_t value = static_cast<int32_t>(lua_tonumber(L, 4));
        
        auto instance = GET_PLAYER_TASK(playerId, taskId);
        if (instance)
        {
            instance->SetVariable(varName, value);
            lua_pushboolean(L, 1);
            
            LOG_DEBUG("TASK_API", "Set task variable " + varName + " = " + std::to_string(value) + 
                     " for player " + std::to_string(playerId) + " task " + std::to_string(taskId));
        }
        else
        {
            lua_pushboolean(L, 0);
        }
    }
    else
    {
        lua_pushboolean(L, 0);
    }
    
    return 1;
}

int GetTaskVariable(lua_State* L)
{
    uint32_t playerId = GetPlayerIdFromLua(L, 1);
    uint32_t taskId = GetTaskIdFromLua(L, 2);
    
    if (lua_isstring(L, 3))
    {
        std::string varName = lua_tostring(L, 3);
        
        auto instance = GET_PLAYER_TASK(playerId, taskId);
        if (instance)
        {
            int32_t value = instance->GetVariable(varName);
            lua_pushnumber(L, value);
        }
        else
        {
            lua_pushnumber(L, 0);
        }
    }
    else
    {
        lua_pushnumber(L, 0);
    }
    
    return 1;
}

// 任务查询API实现
int HasTask(lua_State* L)
{
    uint32_t playerId = GetPlayerIdFromLua(L, 1);
    uint32_t taskId = GetTaskIdFromLua(L, 2);
    
    bool hasTask = HAS_TASK(playerId, taskId);
    lua_pushboolean(L, hasTask ? 1 : 0);
    
    return 1;
}

int GetPlayerTasks(lua_State* L)
{
    uint32_t playerId = GetPlayerIdFromLua(L, 1);
    int state = lua_isnumber(L, 2) ? static_cast<int>(lua_tonumber(L, 2)) : static_cast<int>(TaskState::InProgress);
    
    auto tasks = GET_PLAYER_TASKS(playerId, static_cast<TaskState>(state));
    
    lua_newtable(L);
    int index = 1;
    
    for (const auto& task : tasks)
    {
        if (!task) continue;
        
        lua_pushnumber(L, index++);
        lua_newtable(L);
        
        lua_pushstring(L, "taskId");
        lua_pushnumber(L, task->templateId);
        lua_settable(L, -3);
        
        lua_pushstring(L, "instanceId");
        lua_pushnumber(L, task->instanceId);
        lua_settable(L, -3);
        
        lua_pushstring(L, "state");
        lua_pushnumber(L, static_cast<int>(task->state));
        lua_settable(L, -3);
        
        lua_pushstring(L, "progress");
        lua_pushnumber(L, task->GetProgressPercentage() * 100.0f);
        lua_settable(L, -3);
        
        lua_pushstring(L, "remainingTime");
        lua_pushnumber(L, task->GetRemainingTime());
        lua_settable(L, -3);
        
        lua_settable(L, -3);
    }
    
    return 1;
}

int GetAvailableTasks(lua_State* L)
{
    uint32_t playerId = GetPlayerIdFromLua(L, 1);
    
    auto availableTasks = GET_AVAILABLE_TASKS(playerId);
    
    lua_newtable(L);
    int index = 1;
    
    for (uint32_t taskId : availableTasks)
    {
        lua_pushnumber(L, index++);
        lua_pushnumber(L, taskId);
        lua_settable(L, -3);
    }
    
    return 1;
}

int GetCompletedTasks(lua_State* L)
{
    uint32_t playerId = GetPlayerIdFromLua(L, 1);
    
    auto completedTasks = GET_PLAYER_TASKS(playerId, TaskState::Submitted);
    
    lua_newtable(L);
    int index = 1;
    
    for (const auto& task : completedTasks)
    {
        if (!task) continue;
        
        lua_pushnumber(L, index++);
        lua_pushnumber(L, task->templateId);
        lua_settable(L, -3);
    }
    
    return 1;
}

int GetTaskByName(lua_State* L)
{
    if (lua_isstring(L, 1))
    {
        std::string taskName = lua_tostring(L, 1);
        auto taskTemplate = TASK_MANAGER().GetTaskTemplate(taskName);
        
        if (taskTemplate)
        {
            lua_pushnumber(L, taskTemplate->taskId);
        }
        else
        {
            lua_pushnumber(L, 0);
        }
    }
    else
    {
        lua_pushnumber(L, 0);
    }
    
    return 1;
}

int GetTaskChain(lua_State* L)
{
    uint32_t taskId = GetTaskIdFromLua(L);
    auto taskTemplate = GET_TASK_TEMPLATE(taskId);
    
    if (taskTemplate)
    {
        lua_newtable(L);
        
        lua_pushstring(L, "prevTask");
        lua_pushnumber(L, taskTemplate->prevTaskId);
        lua_settable(L, -3);
        
        lua_pushstring(L, "nextTask");
        lua_pushnumber(L, taskTemplate->nextTaskId);
        lua_settable(L, -3);
        
        lua_pushstring(L, "branchTasks");
        lua_newtable(L);
        for (size_t i = 0; i < taskTemplate->branchTasks.size(); ++i)
        {
            lua_pushnumber(L, i + 1);
            lua_pushnumber(L, taskTemplate->branchTasks[i]);
            lua_settable(L, -3);
        }
        lua_settable(L, -3);
    }
    else
    {
        lua_newtable(L);
    }
    
    return 1;
}

} // namespace TaskScriptAPI
} // namespace sword2
