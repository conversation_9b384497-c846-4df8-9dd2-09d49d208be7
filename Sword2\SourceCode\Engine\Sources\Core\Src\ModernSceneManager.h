//---------------------------------------------------------------------------
// Sword2 Modern Scene Manager (c) 2024
//
// File:	ModernSceneManager.h
// Date:	2024.07.02
// Code:	AI Assistant
// Desc:	Modern C++ scene management system with component architecture
//---------------------------------------------------------------------------
#ifndef MODERN_SCENE_MANAGER_H
#define MODERN_SCENE_MANAGER_H

#include "ModernCpp.h"
#include "ModernGameObject.h"
#include <chrono>
#include <array>
#include <memory>
#include <vector>
#include <unordered_set>

namespace sword2 {

// 环境光照时间段
enum class TimeOfDay : uint8_t
{
    Midnight = 0,   // 深夜 23:00-1:00
    Dawn,           // 黎明 1:00-3:00  
    Morning,        // 清晨 3:00-5:00
    Forenoon,       // 上午 5:00-7:00
    Noon,           // 正午 7:00-9:00
    Afternoon,      // 下午 9:00-11:00
    Dusk,           // 黄昏 11:00-13:00
    Evening,        // 傍晚 13:00-15:00
    Night,          // 夜晚 15:00-17:00
    LateNight,      // 深夜 17:00-19:00
    Count
};

// 颜色结构
struct Color
{
    float r = 1.0f, g = 1.0f, b = 1.0f, a = 1.0f;
    
    Color() = default;
    Color(float r_, float g_, float b_, float a_ = 1.0f) : r(r_), g(g_), b(b_), a(a_) {}
    
    Color operator*(float factor) const
    {
        return Color(r * factor, g * factor, b * factor, a);
    }
    
    Color Lerp(const Color& other, float t) const
    {
        return Color(
            r + (other.r - r) * t,
            g + (other.g - g) * t,
            b + (other.b - b) * t,
            a + (other.a - a) * t
        );
    }
};

// 环境光照管理器
class EnvironmentLighting
{
public:
    EnvironmentLighting();
    
    // 设置时间段光照颜色
    void SetTimeOfDayColor(TimeOfDay timeOfDay, const Color& color);
    Color GetTimeOfDayColor(TimeOfDay timeOfDay) const;
    
    // 根据游戏时间计算当前光照
    Color CalculateCurrentLighting(std::chrono::hours gameTime) const;
    
    // 平滑过渡
    void SetTransitionDuration(std::chrono::minutes duration) { m_transitionDuration = duration; }
    
    // 预设配置
    void LoadDefaultLighting();
    void LoadFromConfig(const std::string& configPath);

private:
    std::array<Color, static_cast<size_t>(TimeOfDay::Count)> m_lightColors;
    std::chrono::minutes m_transitionDuration{30};
    
    TimeOfDay GetTimeOfDayFromHour(int hour) const;
    float CalculateTransitionFactor(std::chrono::hours gameTime, TimeOfDay currentPeriod) const;
};

// 背景图层类型
enum class BackgroundLayer : uint8_t
{
    Background = 0,
    OverCloud,
    BackgroundCloud,
    Count
};

// 背景管理器
class BackgroundManager
{
public:
    BackgroundManager();
    ~BackgroundManager() = default;
    
    // 背景图片管理
    bool LoadBackgroundImage(BackgroundLayer layer, const std::string& imagePath);
    void SetBackgroundVisible(BackgroundLayer layer, bool visible);
    void SetBackgroundAlpha(BackgroundLayer layer, float alpha);
    
    // 云层动画
    void SetCloudScrollSpeed(float speed) { m_cloudScrollSpeed = speed; }
    void UpdateCloudAnimation(float deltaTime);
    
    // 渲染
    void RenderBackground();
    void RenderClouds();

private:
    struct BackgroundInfo
    {
        std::string imagePath;
        bool visible = true;
        float alpha = 1.0f;
        TransformComponent::Vector3 offset{0.0f, 0.0f, 0.0f};
    };
    
    std::array<BackgroundInfo, static_cast<size_t>(BackgroundLayer::Count)> m_backgrounds;
    float m_cloudScrollSpeed = 1.0f;
    float m_cloudOffset = 0.0f;
};

// 场景区域
class SceneRegion
{
public:
    explicit SceneRegion(const std::string& name);
    ~SceneRegion() = default;
    
    // 禁止拷贝，允许移动
    SceneRegion(const SceneRegion&) = delete;
    SceneRegion& operator=(const SceneRegion&) = delete;
    SceneRegion(SceneRegion&&) = default;
    SceneRegion& operator=(SceneRegion&&) = default;
    
    // 基本属性
    const std::string& GetName() const { return m_name; }
    void SetBounds(const TransformComponent::Vector3& min, const TransformComponent::Vector3& max);
    
    // 对象管理
    void AddGameObject(std::shared_ptr<GameObject> obj);
    void RemoveGameObject(uint32_t objectId);
    void RemoveGameObject(GameObject* obj);
    
    // 查询
    std::vector<GameObject*> GetObjectsInRadius(const TransformComponent::Vector3& center, float radius);
    std::vector<GameObject*> GetVisibleObjects(const TransformComponent::Vector3& viewPos, float viewDistance);
    
    // 更新和渲染
    void Update(float deltaTime);
    void Render();
    
    // 碰撞检测
    bool IsPointInRegion(const TransformComponent::Vector3& point) const;
    std::vector<GameObject*> GetObjectsAtPoint(const TransformComponent::Vector3& point);

private:
    std::string m_name;
    TransformComponent::Vector3 m_boundsMin;
    TransformComponent::Vector3 m_boundsMax;
    std::unordered_set<std::shared_ptr<GameObject>> m_gameObjects;
    
    // 空间分割优化
    struct SpatialGrid
    {
        static constexpr int GRID_SIZE = 64;
        std::array<std::vector<GameObject*>, GRID_SIZE * GRID_SIZE> cells;
    } m_spatialGrid;
    
    void UpdateSpatialGrid();
    int GetGridIndex(const TransformComponent::Vector3& pos) const;
};

// 天气系统
class WeatherSystem : public ComponentSystem
{
public:
    enum class WeatherType
    {
        Clear,
        Cloudy,
        Rain,
        Snow,
        Storm,
        Fog
    };
    
    WeatherSystem();
    
    void Update(float deltaTime) override;
    void Render() override;
    
    // 天气控制
    void SetWeather(WeatherType weather, float intensity = 1.0f);
    WeatherType GetCurrentWeather() const { return m_currentWeather; }
    float GetWeatherIntensity() const { return m_weatherIntensity; }
    
    // 天气过渡
    void TransitionToWeather(WeatherType weather, std::chrono::seconds duration);
    
    // 粒子效果
    void SetRainParticleCount(int count) { m_rainParticleCount = count; }
    void SetSnowParticleCount(int count) { m_snowParticleCount = count; }

private:
    WeatherType m_currentWeather = WeatherType::Clear;
    WeatherType m_targetWeather = WeatherType::Clear;
    float m_weatherIntensity = 0.0f;
    float m_targetIntensity = 0.0f;
    
    // 过渡控制
    bool m_isTransitioning = false;
    std::chrono::steady_clock::time_point m_transitionStart;
    std::chrono::seconds m_transitionDuration{0};
    
    // 粒子系统参数
    int m_rainParticleCount = 1000;
    int m_snowParticleCount = 500;
    
    void UpdateWeatherTransition(float deltaTime);
    void RenderWeatherEffects();
};

// 现代化的场景管理器
class ModernSceneManager : public Singleton<ModernSceneManager>
{
public:
    ModernSceneManager();
    ~ModernSceneManager() = default;
    
    // 场景生命周期
    Result<bool, std::string> LoadScene(const std::string& scenePath);
    void UnloadScene();
    bool IsSceneLoaded() const { return m_sceneLoaded; }
    
    // 区域管理
    std::shared_ptr<SceneRegion> CreateRegion(const std::string& name);
    std::shared_ptr<SceneRegion> GetRegion(const std::string& name);
    void RemoveRegion(const std::string& name);
    
    // 环境管理
    EnvironmentLighting& GetLighting() { return m_lighting; }
    BackgroundManager& GetBackground() { return m_background; }
    WeatherSystem& GetWeather() { return m_weather; }
    
    // 时间管理
    void SetGameTime(std::chrono::hours time) { m_gameTime = time; }
    std::chrono::hours GetGameTime() const { return m_gameTime; }
    void SetTimeScale(float scale) { m_timeScale = scale; }
    
    // 场景更新
    void Update(float deltaTime);
    void Render();
    
    // 相机管理
    void SetCameraPosition(const TransformComponent::Vector3& position);
    void SetCameraTarget(const TransformComponent::Vector3& target);
    TransformComponent::Vector3 GetCameraPosition() const { return m_cameraPosition; }
    
    // 可见性剔除
    void SetViewDistance(float distance) { m_viewDistance = distance; }
    void EnableFrustumCulling(bool enable) { m_frustumCulling = enable; }
    
    // 性能统计
    struct SceneStats
    {
        size_t totalObjects = 0;
        size_t visibleObjects = 0;
        size_t totalRegions = 0;
        size_t activeRegions = 0;
        float frameTime = 0.0f;
        float renderTime = 0.0f;
    };
    
    const SceneStats& GetStats() const { return m_stats; }

private:
    // 场景状态
    bool m_sceneLoaded = false;
    std::string m_currentScenePath;
    
    // 区域管理
    std::unordered_map<std::string, std::shared_ptr<SceneRegion>> m_regions;
    
    // 环境系统
    EnvironmentLighting m_lighting;
    BackgroundManager m_background;
    WeatherSystem m_weather;
    
    // 时间系统
    std::chrono::hours m_gameTime{12}; // 默认正午
    float m_timeScale = 1.0f;
    
    // 相机
    TransformComponent::Vector3 m_cameraPosition{0.0f, 0.0f, 0.0f};
    TransformComponent::Vector3 m_cameraTarget{0.0f, 0.0f, 1.0f};
    
    // 渲染设置
    float m_viewDistance = 1000.0f;
    bool m_frustumCulling = true;
    
    // 性能统计
    mutable SceneStats m_stats;
    
    // 内部方法
    void UpdateGameTime(float deltaTime);
    void UpdateVisibility();
    void UpdateStats();
    std::vector<SceneRegion*> GetActiveRegions();
};

} // namespace sword2

// 全局场景管理器访问
#define SCENE() sword2::ModernSceneManager::getInstance()

// 便捷宏
#define SCENE_LOAD(path) SCENE().LoadScene(path)
#define SCENE_CREATE_REGION(name) SCENE().CreateRegion(name)
#define SCENE_GET_REGION(name) SCENE().GetRegion(name)
#define SCENE_SET_CAMERA(pos) SCENE().SetCameraPosition(pos)
#define SCENE_SET_TIME(time) SCENE().SetGameTime(time)

#endif // MODERN_SCENE_MANAGER_H
