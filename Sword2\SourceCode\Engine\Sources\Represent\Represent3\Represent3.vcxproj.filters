﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="4.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <ItemGroup>
    <Filter Include="Source Files">
      <UniqueIdentifier>{1f9920a8-9bc1-45cd-97f3-0a8eb374d972}</UniqueIdentifier>
      <Extensions>cpp;c;cxx;rc;def;r;odl;idl;hpj;bat</Extensions>
    </Filter>
    <Filter Include="Header Files">
      <UniqueIdentifier>{40cc7488-407d-4ec5-8fe5-97bb15066b2e}</UniqueIdentifier>
      <Extensions>h;hpp;hxx;hm;inl</Extensions>
    </Filter>
    <Filter Include="Lib">
      <UniqueIdentifier>{0e1c1ceb-97fc-449a-b100-bf465443ad0f}</UniqueIdentifier>
    </Filter>
    <Filter Include="Lib\debug">
      <UniqueIdentifier>{2ba1b70a-74e2-442d-ab84-9d0a754a62a3}</UniqueIdentifier>
    </Filter>
    <Filter Include="Lib\release">
      <UniqueIdentifier>{7da7cedb-8090-4ed9-a2eb-94f4407941ae}</UniqueIdentifier>
    </Filter>
    <Filter Include="Resource Files">
      <UniqueIdentifier>{253e3908-3936-4cb4-92db-eddc81b4df58}</UniqueIdentifier>
      <Extensions>ico;cur;bmp;dlg;rc2;rct;bin;rgs;gif;jpg;jpeg;jpe</Extensions>
    </Filter>
  </ItemGroup>
  <ItemGroup>
    <ClCompile Include="D3D_Device.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D3D_Shell.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D3D_Utils.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\iRepresent\Font\KCharSet.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\iRepresent\Font\KFont3.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\iRepresent\Font\KFontData.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\iRepresent\Font\KFontRes.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\iRepresent\Font\KMRU.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="KRepresentShell3.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="precompile.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\iRepresent\RepresentUtility.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\iRepresent\Text\TextProcess.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="TextureRes.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="TextureResMgr.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
  </ItemGroup>
  <ItemGroup>
    <ClInclude Include="BaseInclude.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D3D_Device.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D3D_Shell.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D3D_Utils.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\iRepresent\Font\KFont3.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="KRepresentShell3.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="precompile.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\iRepresent\Text\TextProcess.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\iRepresent\Text\TextProcessDef.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="TextureRes.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="TextureResMgr.h">
      <Filter>Header Files</Filter>
    </ClInclude>
  </ItemGroup>
  <ItemGroup>
    <Library Include="..\..\..\Lib\debug\engine.lib">
      <Filter>Lib\debug</Filter>
    </Library>
    <Library Include="..\..\..\Lib\release\engine.lib">
      <Filter>Lib\release</Filter>
    </Library>
  </ItemGroup>
</Project>