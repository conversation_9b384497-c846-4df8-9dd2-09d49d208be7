// stdafx.h : include file for standard system include files,
//  or project specific include files that are used frequently, but
//      are changed infrequently
//

#if !defined(AFX_STDAFX_H__D37121BB_0F27_47FA_833A_9584641D625F__INCLUDED_)
#define AFX_STDAFX_H__D37121BB_0F27_47FA_833A_9584641D625F__INCLUDED_

#if _MSC_VER > 1000
#pragma once
#endif // _MSC_VER > 1000


// TODO: reference additional headers your program requires here
#pragma warning(disable : 4786)
//#ifndef _STANDALONE
//#include "windows.h"
//#endif
#include <winsock2.h>
#include "windows.h"
#include "KEngine.h"

//{{AFX_INSERT_LOCATION}}
// Microsoft Visual C++ will insert additional declarations immediately before the previous line.

#endif // !defined(AFX_STDAFX_H__D37121BB_0F27_47FA_833A_9584641D625F__INCLUDED_)
