//---------------------------------------------------------------------------
// Sword3 Engine (c) 1999-2000 by Kingsoft
//
// File:	KDrawSprite.h
// Date:	2000.08.08
// Code:	Wang<PERSON>ei(Daphnis)
// Desc:	Header File
//---------------------------------------------------------------------------
#ifndef KDrawSprite_H
#define KDrawSprite_H
//---------------------------------------------------------------------------
void g_DrawSprite(void* node, void* canvas);
void g_DrawSpriteWithColor(void* node, void* canvas);
void g_DrawSpriteMixColor(void* node, void* canvas);
void g_DrawSpriteBorder(void* node, void* canvas);
//---------------------------------------------------------------------------
#endif
