//---------------------------------------------------------------------------
// Sword2 Decorator Pattern Implementation (c) 2024
//
// File:	DecoratorPattern.h
// Date:	2024.07.02
// Code:	AI Assistant
// Desc:	Modern C++ decorator pattern for feature extension and enhancement
//---------------------------------------------------------------------------
#ifndef DECORATOR_PATTERN_H
#define DECORATOR_PATTERN_H

#include "ModernCpp.h"
#include <memory>
#include <vector>
#include <functional>
#include <chrono>

namespace sword2 {

// 装饰器基类接口
template<typename Interface>
class IDecorator : public Interface
{
public:
    explicit IDecorator(std::unique_ptr<Interface> component)
        : m_component(std::move(component)) {}
    
    virtual ~IDecorator() = default;

protected:
    std::unique_ptr<Interface> m_component;
    
    // 便捷的组件访问
    Interface* GetComponent() const { return m_component.get(); }
};

// 函数式装饰器
template<typename Interface, typename Func>
class FunctionDecorator : public IDecorator<Interface>
{
public:
    FunctionDecorator(std::unique_ptr<Interface> component, Func decorator)
        : IDecorator<Interface>(std::move(component)), m_decorator(std::move(decorator)) {}
    
    // 这里需要根据具体接口实现相应的方法
    // 示例：如果Interface有Execute方法
    auto Execute(auto&&... args) -> decltype(this->m_component->Execute(args...))
    {
        return m_decorator([this](auto&&... innerArgs) {
            return this->m_component->Execute(std::forward<decltype(innerArgs)>(innerArgs)...);
        }, std::forward<decltype(args)>(args)...);
    }

private:
    Func m_decorator;
};

// 渲染组件接口
class IRenderComponent
{
public:
    virtual ~IRenderComponent() = default;
    virtual void Render() = 0;
    virtual void SetVisible(bool visible) = 0;
    virtual bool IsVisible() const = 0;
};

// 基础渲染组件
class BasicRenderComponent : public IRenderComponent
{
public:
    explicit BasicRenderComponent(const std::string& meshName)
        : m_meshName(meshName), m_visible(true) {}
    
    void Render() override
    {
        if (m_visible)
        {
            // 基础渲染逻辑
            printf("[RENDER] Rendering mesh: %s\n", m_meshName.c_str());
        }
    }
    
    void SetVisible(bool visible) override { m_visible = visible; }
    bool IsVisible() const override { return m_visible; }

private:
    std::string m_meshName;
    bool m_visible;
};

// 阴影装饰器
class ShadowDecorator : public IDecorator<IRenderComponent>
{
public:
    explicit ShadowDecorator(std::unique_ptr<IRenderComponent> component)
        : IDecorator<IRenderComponent>(std::move(component)), m_shadowEnabled(true) {}
    
    void Render() override
    {
        // 先渲染原组件
        m_component->Render();
        
        // 然后渲染阴影
        if (m_shadowEnabled && m_component->IsVisible())
        {
            RenderShadow();
        }
    }
    
    void SetVisible(bool visible) override
    {
        m_component->SetVisible(visible);
    }
    
    bool IsVisible() const override
    {
        return m_component->IsVisible();
    }
    
    void SetShadowEnabled(bool enabled) { m_shadowEnabled = enabled; }
    bool IsShadowEnabled() const { return m_shadowEnabled; }

private:
    bool m_shadowEnabled;
    
    void RenderShadow()
    {
        printf("[RENDER] Rendering shadow\n");
    }
};

// 发光效果装饰器
class GlowDecorator : public IDecorator<IRenderComponent>
{
public:
    explicit GlowDecorator(std::unique_ptr<IRenderComponent> component, float intensity = 1.0f)
        : IDecorator<IRenderComponent>(std::move(component)), m_glowIntensity(intensity) {}
    
    void Render() override
    {
        // 先渲染原组件
        m_component->Render();
        
        // 然后渲染发光效果
        if (m_glowIntensity > 0.0f && m_component->IsVisible())
        {
            RenderGlow();
        }
    }
    
    void SetVisible(bool visible) override
    {
        m_component->SetVisible(visible);
    }
    
    bool IsVisible() const override
    {
        return m_component->IsVisible();
    }
    
    void SetGlowIntensity(float intensity) { m_glowIntensity = std::max(0.0f, intensity); }
    float GetGlowIntensity() const { return m_glowIntensity; }

private:
    float m_glowIntensity;
    
    void RenderGlow()
    {
        printf("[RENDER] Rendering glow effect (intensity: %.2f)\n", m_glowIntensity);
    }
};

// 动画装饰器
class AnimationDecorator : public IDecorator<IRenderComponent>
{
public:
    explicit AnimationDecorator(std::unique_ptr<IRenderComponent> component)
        : IDecorator<IRenderComponent>(std::move(component)), m_animationTime(0.0f) {}
    
    void Render() override
    {
        // 更新动画
        UpdateAnimation();
        
        // 渲染组件
        m_component->Render();
    }
    
    void SetVisible(bool visible) override
    {
        m_component->SetVisible(visible);
    }
    
    bool IsVisible() const override
    {
        return m_component->IsVisible();
    }
    
    void SetAnimationSpeed(float speed) { m_animationSpeed = speed; }
    float GetAnimationTime() const { return m_animationTime; }

private:
    float m_animationTime;
    float m_animationSpeed = 1.0f;
    
    void UpdateAnimation()
    {
        m_animationTime += m_animationSpeed * 0.016f; // 假设60FPS
        printf("[RENDER] Animation time: %.2f\n", m_animationTime);
    }
};

// 网络组件接口
class INetworkComponent
{
public:
    virtual ~INetworkComponent() = default;
    virtual bool SendMessage(const std::string& message) = 0;
    virtual std::string ReceiveMessage() = 0;
    virtual bool IsConnected() const = 0;
};

// 基础网络组件
class BasicNetworkComponent : public INetworkComponent
{
public:
    BasicNetworkComponent() : m_connected(true) {}
    
    bool SendMessage(const std::string& message) override
    {
        if (m_connected)
        {
            printf("[NETWORK] Sending: %s\n", message.c_str());
            return true;
        }
        return false;
    }
    
    std::string ReceiveMessage() override
    {
        if (m_connected)
        {
            return "received_message";
        }
        return "";
    }
    
    bool IsConnected() const override { return m_connected; }

private:
    bool m_connected;
};

// 加密装饰器
class EncryptionDecorator : public IDecorator<INetworkComponent>
{
public:
    explicit EncryptionDecorator(std::unique_ptr<INetworkComponent> component)
        : IDecorator<INetworkComponent>(std::move(component)) {}
    
    bool SendMessage(const std::string& message) override
    {
        std::string encryptedMessage = Encrypt(message);
        return m_component->SendMessage(encryptedMessage);
    }
    
    std::string ReceiveMessage() override
    {
        std::string encryptedMessage = m_component->ReceiveMessage();
        return Decrypt(encryptedMessage);
    }
    
    bool IsConnected() const override
    {
        return m_component->IsConnected();
    }

private:
    std::string Encrypt(const std::string& message)
    {
        // 简单的加密示例
        return "[ENCRYPTED]" + message + "[/ENCRYPTED]";
    }
    
    std::string Decrypt(const std::string& encryptedMessage)
    {
        // 简单的解密示例
        if (encryptedMessage.find("[ENCRYPTED]") == 0)
        {
            size_t start = 11; // "[ENCRYPTED]"的长度
            size_t end = encryptedMessage.find("[/ENCRYPTED]");
            if (end != std::string::npos)
            {
                return encryptedMessage.substr(start, end - start);
            }
        }
        return encryptedMessage;
    }
};

// 压缩装饰器
class CompressionDecorator : public IDecorator<INetworkComponent>
{
public:
    explicit CompressionDecorator(std::unique_ptr<INetworkComponent> component)
        : IDecorator<INetworkComponent>(std::move(component)) {}
    
    bool SendMessage(const std::string& message) override
    {
        std::string compressedMessage = Compress(message);
        return m_component->SendMessage(compressedMessage);
    }
    
    std::string ReceiveMessage() override
    {
        std::string compressedMessage = m_component->ReceiveMessage();
        return Decompress(compressedMessage);
    }
    
    bool IsConnected() const override
    {
        return m_component->IsConnected();
    }

private:
    std::string Compress(const std::string& message)
    {
        // 简单的压缩示例
        return "[COMPRESSED]" + message + "[/COMPRESSED]";
    }
    
    std::string Decompress(const std::string& compressedMessage)
    {
        // 简单的解压示例
        if (compressedMessage.find("[COMPRESSED]") == 0)
        {
            size_t start = 12; // "[COMPRESSED]"的长度
            size_t end = compressedMessage.find("[/COMPRESSED]");
            if (end != std::string::npos)
            {
                return compressedMessage.substr(start, end - start);
            }
        }
        return compressedMessage;
    }
};

// 日志装饰器
class LoggingDecorator : public IDecorator<INetworkComponent>
{
public:
    explicit LoggingDecorator(std::unique_ptr<INetworkComponent> component)
        : IDecorator<INetworkComponent>(std::move(component)) {}
    
    bool SendMessage(const std::string& message) override
    {
        auto start = std::chrono::steady_clock::now();
        bool result = m_component->SendMessage(message);
        auto end = std::chrono::steady_clock::now();
        
        auto duration = std::chrono::duration_cast<std::chrono::microseconds>(end - start);
        printf("[LOG] SendMessage: %s, Result: %s, Time: %lld μs\n", 
               message.c_str(), result ? "Success" : "Failed", duration.count());
        
        return result;
    }
    
    std::string ReceiveMessage() override
    {
        auto start = std::chrono::steady_clock::now();
        std::string result = m_component->ReceiveMessage();
        auto end = std::chrono::steady_clock::now();
        
        auto duration = std::chrono::duration_cast<std::chrono::microseconds>(end - start);
        printf("[LOG] ReceiveMessage: %s, Time: %lld μs\n", 
               result.c_str(), duration.count());
        
        return result;
    }
    
    bool IsConnected() const override
    {
        bool result = m_component->IsConnected();
        printf("[LOG] IsConnected: %s\n", result ? "true" : "false");
        return result;
    }
};

// 装饰器构建器
template<typename Interface>
class DecoratorBuilder
{
public:
    explicit DecoratorBuilder(std::unique_ptr<Interface> component)
        : m_component(std::move(component)) {}
    
    template<typename Decorator, typename... Args>
    DecoratorBuilder& AddDecorator(Args&&... args)
    {
        m_component = std::make_unique<Decorator>(std::move(m_component), std::forward<Args>(args)...);
        return *this;
    }
    
    std::unique_ptr<Interface> Build()
    {
        return std::move(m_component);
    }

private:
    std::unique_ptr<Interface> m_component;
};

// 装饰器链
template<typename Interface>
class DecoratorChain
{
public:
    explicit DecoratorChain(std::unique_ptr<Interface> baseComponent)
        : m_component(std::move(baseComponent)) {}
    
    template<typename Decorator, typename... Args>
    DecoratorChain& Add(Args&&... args)
    {
        m_component = std::make_unique<Decorator>(std::move(m_component), std::forward<Args>(args)...);
        return *this;
    }
    
    Interface* Get() const { return m_component.get(); }
    std::unique_ptr<Interface> Release() { return std::move(m_component); }

private:
    std::unique_ptr<Interface> m_component;
};

} // namespace sword2

// 便捷宏定义
#define MAKE_DECORATOR_BUILDER(interface, component) \
    sword2::DecoratorBuilder<interface>(std::move(component))

#define MAKE_DECORATOR_CHAIN(interface, component) \
    sword2::DecoratorChain<interface>(std::move(component))

// 渲染组件装饰器宏
#define ADD_SHADOW_DECORATOR(component) \
    std::make_unique<sword2::ShadowDecorator>(std::move(component))

#define ADD_GLOW_DECORATOR(component, intensity) \
    std::make_unique<sword2::GlowDecorator>(std::move(component), intensity)

#define ADD_ANIMATION_DECORATOR(component) \
    std::make_unique<sword2::AnimationDecorator>(std::move(component))

// 网络组件装饰器宏
#define ADD_ENCRYPTION_DECORATOR(component) \
    std::make_unique<sword2::EncryptionDecorator>(std::move(component))

#define ADD_COMPRESSION_DECORATOR(component) \
    std::make_unique<sword2::CompressionDecorator>(std::move(component))

#define ADD_LOGGING_DECORATOR(component) \
    std::make_unique<sword2::LoggingDecorator>(std::move(component))

#endif // DECORATOR_PATTERN_H
