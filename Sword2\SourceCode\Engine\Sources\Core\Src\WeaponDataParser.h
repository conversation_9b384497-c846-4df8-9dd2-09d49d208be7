//---------------------------------------------------------------------------
// Sword2 Weapon Data Parser (c) 2024
//
// File:	WeaponDataParser.h
// Date:	2024.07.02
// Code:	AI Assistant
// Desc:	Specialized parser for weapon.txt and equipment data files
//---------------------------------------------------------------------------
#ifndef WEAPON_DATA_PARSER_H
#define WEAPON_DATA_PARSER_H

#include "GameDataSystem.h"
#include <codecvt>
#include <locale>

namespace sword2 {

// 武器类型枚举
enum class WeaponType : uint8_t
{
    Fist = 0,           // 拳套
    Sword = 1,          // 剑
    Blade = 2,          // 刀
    Spear = 3,          // 枪
    Staff = 4,          // 杖
    Bow = 5,            // 弓
    DualWeapon = 6,     // 双武器
    Special = 7         // 特殊武器
};

// 武器子类型
enum class WeaponSubType : uint8_t
{
    Normal = 0,         // 普通
    Magic = 1,          // 法术
    Rare = 2,           // 稀有
    Epic = 3,           // 史诗
    Legendary = 4       // 传说
};

// 门派系列
enum class SeriesType : uint8_t
{
    None = 0,           // 无门派限制
    Shaolin = 1,        // 少林
    Wudang = 2,         // 武当
    Emei = 3,           // 峨眉
    Beggar = 4,         // 丐帮
    Tangmen = 5,        // 唐门
    Wudu = 6,           // 五毒
    Kunlun = 7,         // 昆仑
    Mingjiao = 8,       // 明教
    Cuiyan = 9,         // 翠烟
    Gaibang = 10,       // 丐帮
    Tianwang = 11,      // 天王
    Tianren = 12,       // 天忍
    Wuxian = 13,        // 武仙
    Cangjian = 14,      // 藏剑
    Gaoli = 15          // 高丽
};

// 增强的武器数据结构
struct EnhancedWeaponData : public WeaponData
{
    WeaponType weaponTypeEnum = WeaponType::Fist;
    WeaponSubType subTypeEnum = WeaponSubType::Normal;
    SeriesType seriesTypeEnum = SeriesType::None;
    
    // 扩展属性
    uint32_t attackRange = 1;       // 攻击范围
    uint32_t criticalRate = 0;      // 暴击率
    uint32_t hitRate = 100;         // 命中率
    uint32_t dodgeRate = 0;         // 闪避率
    
    // 特殊效果
    std::vector<std::string> specialEffects;
    std::unordered_map<std::string, int> magicAttributes;
    
    // 升级信息
    uint32_t maxLevel = 1;          // 最大等级
    uint32_t upgradeExp = 0;        // 升级经验
    
    // 套装信息
    std::string suitName;           // 套装名称
    uint32_t suitId = 0;            // 套装ID
    
    EnhancedWeaponData() = default;
    EnhancedWeaponData(uint32_t id, const std::string& name) : WeaponData(id, name) {}
    
    // 计算武器总攻击力
    uint32_t GetTotalAttack() const
    {
        return (minDamage + maxDamage) / 2 + (minMagicDamage + maxMagicDamage) / 2;
    }
    
    // 获取武器品质描述
    std::string GetQualityDescription() const
    {
        switch (subTypeEnum)
        {
        case WeaponSubType::Normal: return "普通";
        case WeaponSubType::Magic: return "魔法";
        case WeaponSubType::Rare: return "稀有";
        case WeaponSubType::Epic: return "史诗";
        case WeaponSubType::Legendary: return "传说";
        default: return "未知";
        }
    }
    
    // 获取门派名称
    std::string GetSeriesName() const
    {
        switch (seriesTypeEnum)
        {
        case SeriesType::Shaolin: return "少林";
        case SeriesType::Wudang: return "武当";
        case SeriesType::Emei: return "峨眉";
        case SeriesType::Beggar: return "丐帮";
        case SeriesType::Tangmen: return "唐门";
        case SeriesType::Wudu: return "五毒";
        case SeriesType::Kunlun: return "昆仑";
        case SeriesType::Mingjiao: return "明教";
        case SeriesType::Cuiyan: return "翠烟";
        case SeriesType::Cangjian: return "藏剑";
        default: return "通用";
        }
    }
};

// 专用武器数据解析器
class WeaponDataParser : public IDataFileParser
{
public:
    WeaponDataParser()
    {
        InitializeWeaponTypeMap();
        InitializeSeriesMap();
    }
    
    bool ParseFile(const std::string& filePath, std::vector<GameDataItem>& items) override
    {
        // 首先尝试以UTF-8编码读取
        std::vector<EnhancedWeaponData> weapons;
        if (!ParseWeaponFile(filePath, weapons))
        {
            LOG_ERROR("WEAPON", "Failed to parse weapon file: " + filePath);
            return false;
        }
        
        // 转换为基础数据项
        items.clear();
        items.reserve(weapons.size());
        
        for (const auto& weapon : weapons)
        {
            GameDataItem item;
            item.id = weapon.id;
            item.name = weapon.name;
            item.description = weapon.description;
            
            // 复制所有属性
            item.properties = weapon.properties;
            
            // 添加武器特有属性
            item.SetProperty("weapon_type", static_cast<int>(weapon.weaponTypeEnum));
            item.SetProperty("sub_type", static_cast<int>(weapon.subTypeEnum));
            item.SetProperty("series_type", static_cast<int>(weapon.seriesTypeEnum));
            item.SetProperty("min_damage", weapon.minDamage);
            item.SetProperty("max_damage", weapon.maxDamage);
            item.SetProperty("min_magic_damage", weapon.minMagicDamage);
            item.SetProperty("max_magic_damage", weapon.maxMagicDamage);
            item.SetProperty("attack_speed", weapon.attackSpeed);
            item.SetProperty("required_level", weapon.requiredLevel);
            item.SetProperty("required_series", weapon.requiredSeries);
            item.SetProperty("price", weapon.price);
            item.SetProperty("durability", weapon.durability);
            item.SetProperty("image_path", weapon.imagePath);
            item.SetProperty("script_file", weapon.scriptFile);
            
            items.push_back(std::move(item));
        }
        
        LOG_INFO("WEAPON", "Successfully parsed " + std::to_string(items.size()) + " weapons from " + filePath);
        return true;
    }
    
    DataFileFormat GetSupportedFormat() const override
    {
        return DataFileFormat::TXT;
    }
    
    // 专门解析武器数据
    bool ParseWeaponFile(const std::string& filePath, std::vector<EnhancedWeaponData>& weapons)
    {
        std::ifstream file(filePath, std::ios::binary);
        if (!file.is_open())
        {
            LOG_ERROR("WEAPON", "Cannot open weapon file: " + filePath);
            return false;
        }
        
        std::string line;
        std::vector<std::string> headers;
        bool isHeaderParsed = false;
        uint32_t lineNumber = 0;
        
        while (std::getline(file, line))
        {
            lineNumber++;
            
            // 处理编码问题 - 移除BOM和特殊字符
            if (lineNumber == 1 && line.size() >= 3)
            {
                if (static_cast<unsigned char>(line[0]) == 0xEF &&
                    static_cast<unsigned char>(line[1]) == 0xBB &&
                    static_cast<unsigned char>(line[2]) == 0xBF)
                {
                    line = line.substr(3); // 移除UTF-8 BOM
                }
            }
            
            // 跳过空行
            if (line.empty() || IsLineEmpty(line))
                continue;
            
            // 解析制表符分隔的数据
            std::vector<std::string> columns = SplitByTab(line);
            
            if (!isHeaderParsed)
            {
                // 解析表头
                headers = columns;
                isHeaderParsed = true;
                LOG_DEBUG("WEAPON", "Parsed " + std::to_string(headers.size()) + " headers");
                continue;
            }
            
            // 跳过说明行和空数据行
            if (columns.size() < 10 || columns[0].empty())
                continue;
            
            // 解析武器数据
            EnhancedWeaponData weapon;
            if (ParseWeaponLine(columns, headers, weapon))
            {
                weapons.push_back(std::move(weapon));
            }
        }
        
        LOG_INFO("WEAPON", "Parsed " + std::to_string(weapons.size()) + " weapons from file");
        return true;
    }

private:
    std::unordered_map<std::string, WeaponType> m_weaponTypeMap;
    std::unordered_map<std::string, SeriesType> m_seriesMap;
    
    void InitializeWeaponTypeMap()
    {
        m_weaponTypeMap["拳套"] = WeaponType::Fist;
        m_weaponTypeMap["剑"] = WeaponType::Sword;
        m_weaponTypeMap["刀"] = WeaponType::Blade;
        m_weaponTypeMap["枪"] = WeaponType::Spear;
        m_weaponTypeMap["杖"] = WeaponType::Staff;
        m_weaponTypeMap["弓"] = WeaponType::Bow;
        m_weaponTypeMap["双武器"] = WeaponType::DualWeapon;
    }
    
    void InitializeSeriesMap()
    {
        m_seriesMap["少林"] = SeriesType::Shaolin;
        m_seriesMap["武当"] = SeriesType::Wudang;
        m_seriesMap["峨眉"] = SeriesType::Emei;
        m_seriesMap["丐帮"] = SeriesType::Beggar;
        m_seriesMap["唐门"] = SeriesType::Tangmen;
        m_seriesMap["五毒"] = SeriesType::Wudu;
        m_seriesMap["昆仑"] = SeriesType::Kunlun;
        m_seriesMap["明教"] = SeriesType::Mingjiao;
        m_seriesMap["翠烟"] = SeriesType::Cuiyan;
        m_seriesMap["藏剑"] = SeriesType::Cangjian;
    }
    
    bool IsLineEmpty(const std::string& line)
    {
        for (char c : line)
        {
            if (c != ' ' && c != '\t' && c != '\r' && c != '\n')
                return false;
        }
        return true;
    }
    
    std::vector<std::string> SplitByTab(const std::string& line)
    {
        std::vector<std::string> result;
        std::stringstream ss(line);
        std::string item;
        
        while (std::getline(ss, item, '\t'))
        {
            // 清理字符串
            item = TrimString(item);
            result.push_back(item);
        }
        
        return result;
    }
    
    std::string TrimString(const std::string& str)
    {
        size_t start = str.find_first_not_of(" \t\r\n");
        if (start == std::string::npos)
            return "";
        
        size_t end = str.find_last_not_of(" \t\r\n");
        return str.substr(start, end - start + 1);
    }
    
    bool ParseWeaponLine(const std::vector<std::string>& columns, 
                        const std::vector<std::string>& headers,
                        EnhancedWeaponData& weapon)
    {
        try
        {
            // 基础信息
            weapon.name = columns[0];
            weapon.weaponType = SafeParseUInt(columns, 1);
            weapon.subType = SafeParseUInt(columns, 2);
            weapon.id = SafeParseUInt(columns, 3);
            weapon.level = SafeParseUInt(columns, 4);
            weapon.price = SafeParseUInt(columns, 5);
            weapon.durability = SafeParseUInt(columns, 7);
            
            // 伤害值解析
            ParseDamageRange(SafeGetColumn(columns, 9), weapon.minDamage, weapon.maxDamage);
            ParseDamageRange(SafeGetColumn(columns, 10), weapon.minMagicDamage, weapon.maxMagicDamage);
            
            weapon.attackSpeed = SafeParseUInt(columns, 12);
            weapon.requiredLevel = SafeParseUInt(columns, 13);
            weapon.requiredSeries = SafeParseUInt(columns, 14);
            
            // 图像和脚本路径
            if (columns.size() > 17)
                weapon.imagePath = columns[17];
            if (columns.size() > 19)
                weapon.scriptFile = columns[19];
            
            // 设置枚举类型
            weapon.weaponTypeEnum = static_cast<WeaponType>(weapon.weaponType);
            weapon.subTypeEnum = static_cast<WeaponSubType>(weapon.subType);
            weapon.seriesTypeEnum = static_cast<SeriesType>(weapon.requiredSeries);
            
            // 存储所有原始属性
            for (size_t i = 0; i < columns.size() && i < headers.size(); ++i)
            {
                if (!columns[i].empty())
                {
                    weapon.properties[headers[i]] = columns[i];
                }
            }
            
            return true;
        }
        catch (const std::exception& e)
        {
            LOG_ERROR("WEAPON", "Error parsing weapon line: " + std::string(e.what()));
            return false;
        }
    }
    
    uint32_t SafeParseUInt(const std::vector<std::string>& columns, size_t index)
    {
        if (index >= columns.size() || columns[index].empty())
            return 0;
        
        try
        {
            return static_cast<uint32_t>(std::stoul(columns[index]));
        }
        catch (...)
        {
            return 0;
        }
    }
    
    std::string SafeGetColumn(const std::vector<std::string>& columns, size_t index)
    {
        return (index < columns.size()) ? columns[index] : "";
    }
    
    void ParseDamageRange(const std::string& damageStr, uint32_t& minDamage, uint32_t& maxDamage)
    {
        if (damageStr.empty())
        {
            minDamage = maxDamage = 0;
            return;
        }
        
        size_t semicolonPos = damageStr.find(';');
        if (semicolonPos != std::string::npos)
        {
            try
            {
                minDamage = std::stoul(damageStr.substr(0, semicolonPos));
                maxDamage = std::stoul(damageStr.substr(semicolonPos + 1));
            }
            catch (...)
            {
                minDamage = maxDamage = 0;
            }
        }
        else
        {
            try
            {
                minDamage = maxDamage = std::stoul(damageStr);
            }
            catch (...)
            {
                minDamage = maxDamage = 0;
            }
        }
    }
};

} // namespace sword2

#endif // WEAPON_DATA_PARSER_H
