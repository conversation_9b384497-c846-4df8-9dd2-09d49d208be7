//---------------------------------------------------------------------------
// Sword2 Lua Script Engine Demo (c) 2024
//
// File:	LuaScriptEngineDemo.cpp
// Date:	2024.07.02
// Code:	AI Assistant
// Desc:	Comprehensive demonstration of the Lua script engine
//---------------------------------------------------------------------------

#include "KCore.h"
#include "LuaScriptEngine.h"
#include "PlayerSystem.h"
#include "PlayerManager.h"
#include "GameDataSystem.h"
#include "UnifiedLoggingSystem.h"

namespace sword2 {

// Lua脚本引擎演示类
class LuaScriptEngineDemo
{
public:
    LuaScriptEngineDemo()
    {
        m_initialized = false;
    }
    
    bool Initialize()
    {
        if (m_initialized) return true;
        
        printf("[LUA_DEMO] Initializing Lua script engine demo...\n");
        
        // 初始化日志系统
        LOGGER().Initialize();
        LOGGER().AddConsoleAppender(LogLevel::Debug);
        
        // 启动玩家管理器
        if (!START_PLAYER_SYSTEM())
        {
            printf("[LUA_DEMO] Failed to start player system\n");
            return false;
        }
        
        // 初始化脚本引擎
        if (!INIT_SCRIPT_ENGINE("../../../Server/script"))
        {
            printf("[LUA_DEMO] Failed to initialize script engine\n");
            return false;
        }
        
        m_initialized = true;
        LOG_INFO("LUA_DEMO", "Lua script engine demo initialized successfully");
        
        return true;
    }
    
    void RunDemo()
    {
        if (!m_initialized)
        {
            printf("[LUA_DEMO] System not initialized\n");
            return;
        }
        
        LOG_INFO("LUA_DEMO", "Starting Lua script engine demonstration...");
        
        // 演示各个功能
        DemoBasicScriptExecution();
        DemoGameAPIFunctions();
        DemoPlayerScriptInteraction();
        DemoScriptVariables();
        DemoErrorHandling();
        DemoScriptStatistics();
        
        LOG_INFO("LUA_DEMO", "Lua script engine demonstration completed");
    }
    
    void Cleanup()
    {
        if (!m_initialized) return;
        
        LOG_INFO("LUA_DEMO", "Cleaning up Lua script engine demo...");
        
        // 关闭脚本引擎
        SHUTDOWN_SCRIPT_ENGINE();
        
        // 停止玩家系统
        STOP_PLAYER_SYSTEM();
        
        m_initialized = false;
        LOG_INFO("LUA_DEMO", "Lua script engine demo cleanup completed");
    }

private:
    bool m_initialized;
    
    void DemoBasicScriptExecution()
    {
        LOG_INFO("LUA_DEMO", "=== Basic Script Execution Demo ===");
        
        // 执行简单的Lua代码
        std::string simpleCode = R"(
            function test_function()
                WriteLog("Hello from Lua script!")
                return 42, "success"
            end
            
            -- 定义一些全局变量
            demo_value = 100
            demo_string = "Sword2 Script Engine"
        )";
        
        auto& scriptManager = SCRIPT_MANAGER();
        auto context = std::make_unique<LuaScriptContext>();
        context->Initialize();
        
        ScriptResult result = context->ExecuteString(simpleCode);
        if (result == ScriptResult::Success)
        {
            LOG_INFO("LUA_DEMO", "Successfully executed simple Lua code");
            
            // 调用Lua函数
            std::vector<LuaValue> results;
            result = context->CallFunction("test_function", {}, &results);
            
            if (result == ScriptResult::Success && results.size() >= 2)
            {
                LOG_INFO("LUA_DEMO", "Function returned: " + 
                        std::to_string(results[0].AsNumber()) + ", " + results[1].AsString());
            }
            
            // 获取全局变量
            LuaValue value = context->GetGlobal("demo_value");
            LuaValue str = context->GetGlobal("demo_string");
            
            LOG_INFO("LUA_DEMO", "Global variables: demo_value = " + 
                    std::to_string(value.AsNumber()) + ", demo_string = " + str.AsString());
        }
        else
        {
            LOG_ERROR("LUA_DEMO", "Failed to execute simple Lua code: " + context->GetLastError());
        }
    }
    
    void DemoGameAPIFunctions()
    {
        LOG_INFO("LUA_DEMO", "=== Game API Functions Demo ===");
        
        // 创建测试玩家
        PlayerCreationParams params("测试玩家", "test_account");
        params.series = PlayerSeries::Wudang;
        params.startMapId = 100;
        params.startX = 1000;
        params.startY = 1000;
        
        uint32_t playerId = CREATE_PLAYER(params);
        if (playerId == 0)
        {
            LOG_ERROR("LUA_DEMO", "Failed to create test player");
            return;
        }
        
        // 登录玩家
        uint32_t sessionId;
        LoginResult loginResult = PLAYER_LOGIN(playerId, "127.0.0.1", 1001, sessionId);
        if (loginResult != LoginResult::Success)
        {
            LOG_ERROR("LUA_DEMO", "Failed to login test player");
            return;
        }
        
        // 测试游戏API的Lua脚本
        std::string apiTestCode = R"(
            function test_player_api(playerId)
                WriteLog("Testing player API functions...")
                
                -- 获取玩家信息
                local name = GetPlayerName(playerId)
                local level = GetPlayerLevel(playerId)
                local money = GetPlayerMoney(playerId)
                
                WriteLog("Player: " .. name .. ", Level: " .. level .. ", Money: " .. money)
                
                -- 设置玩家金钱
                SetPlayerMoney(playerId, 10000)
                local newMoney = GetPlayerMoney(playerId)
                WriteLog("New money: " .. newMoney)
                
                -- 增加经验
                local leveledUp = AddPlayerExp(playerId, 500)
                if leveledUp then
                    WriteLog("Player leveled up!")
                else
                    WriteLog("Player gained experience")
                end
                
                -- 获取位置
                local mapId, x, y = GetPlayerPosition(playerId)
                WriteLog("Position: Map " .. mapId .. " (" .. x .. ", " .. y .. ")")
                
                -- 传送玩家
                TeleportPlayer(playerId, 101, 2000, 2000)
                mapId, x, y = GetPlayerPosition(playerId)
                WriteLog("New position: Map " .. mapId .. " (" .. x .. ", " .. y .. ")")
                
                return true
            end
            
            function test_system_api()
                WriteLog("Testing system API functions...")
                
                -- 获取当前时间
                local currentTime = GetCurrentTime()
                WriteLog("Current time: " .. currentTime)
                
                -- 生成随机数
                local rand1 = Random()
                local rand2 = Random(10)
                local rand3 = Random(1, 100)
                WriteLog("Random numbers: " .. rand1 .. ", " .. rand2 .. ", " .. rand3)
                
                -- 广播消息
                Broadcast("This is a test broadcast from Lua!")
                
                return true
            end
        )";
        
        auto context = std::make_unique<LuaScriptContext>();
        context->Initialize();
        
        ScriptResult result = context->ExecuteString(apiTestCode);
        if (result == ScriptResult::Success)
        {
            // 测试玩家API
            std::vector<LuaValue> args = { LuaValue(static_cast<double>(playerId)) };
            result = context->CallFunction("test_player_api", args);
            
            if (result == ScriptResult::Success)
            {
                LOG_INFO("LUA_DEMO", "Player API test completed successfully");
            }
            else
            {
                LOG_ERROR("LUA_DEMO", "Player API test failed: " + context->GetLastError());
            }
            
            // 测试系统API
            result = context->CallFunction("test_system_api");
            
            if (result == ScriptResult::Success)
            {
                LOG_INFO("LUA_DEMO", "System API test completed successfully");
            }
            else
            {
                LOG_ERROR("LUA_DEMO", "System API test failed: " + context->GetLastError());
            }
        }
        else
        {
            LOG_ERROR("LUA_DEMO", "Failed to load API test script: " + context->GetLastError());
        }
        
        // 清理测试玩家
        PLAYER_LOGOUT(playerId);
    }
    
    void DemoPlayerScriptInteraction()
    {
        LOG_INFO("LUA_DEMO", "=== Player Script Interaction Demo ===");
        
        // 模拟NPC对话脚本
        std::string npcScript = R"(
            function npc_talk(playerId, npcId)
                WriteLog("NPC " .. npcId .. " talking to player " .. playerId)
                
                local playerName = GetPlayerName(playerId)
                local playerLevel = GetPlayerLevel(playerId)
                
                if playerLevel < 10 then
                    Talk("你好，" .. playerName .. "！你还是个新手，需要多多努力啊！")
                    
                    -- 给新手一些金钱
                    SetPlayerMoney(playerId, 1000)
                    Talk("我给你一些启动资金，好好利用吧！")
                else
                    Talk("欢迎回来，" .. playerName .. "！你已经是个经验丰富的武者了。")
                    
                    -- 检查是否有特定物品
                    if HasItem(playerId, 1001) then
                        Talk("我看到你有青钢剑，真是不错的武器！")
                    else
                        Talk("你需要一把好武器，我这里有青钢剑出售。")
                        GiveItem(playerId, 1001, 1)
                    end
                end
                
                return true
            end
            
            function item_use(playerId, itemId)
                WriteLog("Player " .. playerId .. " used item " .. itemId)
                
                if itemId == 2001 then  -- 生命药水
                    local currentHP, maxHP = GetPlayerHP(playerId)
                    if currentHP < maxHP then
                        HealPlayer(playerId, 100)
                        Talk("你使用了生命药水，恢复了100点生命值。")
                    else
                        Talk("你的生命值已经满了，不需要使用药水。")
                        return false
                    end
                elseif itemId == 2002 then  -- 内力药水
                    local currentMP, maxMP = GetPlayerMP(playerId)
                    if currentMP < maxMP then
                        SetPlayerMP(playerId, maxMP)
                        Talk("你使用了内力药水，内力完全恢复了。")
                    else
                        Talk("你的内力已经满了，不需要使用药水。")
                        return false
                    end
                end
                
                return true
            end
        )";
        
        auto context = std::make_unique<LuaScriptContext>();
        context->Initialize();
        
        ScriptResult result = context->ExecuteString(npcScript);
        if (result == ScriptResult::Success)
        {
            LOG_INFO("LUA_DEMO", "Successfully loaded player interaction scripts");
            
            // 创建测试玩家
            PlayerCreationParams params("脚本测试玩家", "script_test");
            params.series = PlayerSeries::Shaolin;
            uint32_t playerId = CREATE_PLAYER(params);
            
            if (playerId != 0)
            {
                uint32_t sessionId;
                PLAYER_LOGIN(playerId, "127.0.0.1", 1002, sessionId);
                
                // 模拟NPC对话
                std::vector<LuaValue> args = { 
                    LuaValue(static_cast<double>(playerId)), 
                    LuaValue(static_cast<double>(1001)) 
                };
                
                result = context->CallFunction("npc_talk", args);
                if (result == ScriptResult::Success)
                {
                    LOG_INFO("LUA_DEMO", "NPC interaction completed successfully");
                }
                
                // 模拟物品使用
                args = { 
                    LuaValue(static_cast<double>(playerId)), 
                    LuaValue(static_cast<double>(2001)) 
                };
                
                result = context->CallFunction("item_use", args);
                if (result == ScriptResult::Success)
                {
                    LOG_INFO("LUA_DEMO", "Item usage completed successfully");
                }
                
                PLAYER_LOGOUT(playerId);
            }
        }
        else
        {
            LOG_ERROR("LUA_DEMO", "Failed to load interaction script: " + context->GetLastError());
        }
    }
    
    void DemoScriptVariables()
    {
        LOG_INFO("LUA_DEMO", "=== Script Variables Demo ===");
        
        auto& scriptManager = SCRIPT_MANAGER();
        
        // 设置脚本变量
        scriptManager.SetScriptVariable("server_name", LuaValue("Sword2 Demo Server"));
        scriptManager.SetScriptVariable("max_players", LuaValue(1000.0));
        scriptManager.SetScriptVariable("debug_mode", LuaValue(true));
        
        // 获取脚本变量
        LuaValue serverName = scriptManager.GetScriptVariable("server_name");
        LuaValue maxPlayers = scriptManager.GetScriptVariable("max_players");
        LuaValue debugMode = scriptManager.GetScriptVariable("debug_mode");
        
        LOG_INFO("LUA_DEMO", "Script variables:");
        LOG_INFO("LUA_DEMO", "  server_name: " + serverName.AsString());
        LOG_INFO("LUA_DEMO", "  max_players: " + std::to_string(static_cast<int>(maxPlayers.AsNumber())));
        LOG_INFO("LUA_DEMO", "  debug_mode: " + std::string(debugMode.AsBool() ? "true" : "false"));
        
        // 在脚本中使用这些变量
        std::string variableTestCode = R"(
            function test_variables()
                WriteLog("Server: " .. server_name)
                WriteLog("Max players: " .. max_players)
                WriteLog("Debug mode: " .. tostring(debug_mode))
                
                -- 修改变量
                max_players = max_players + 500
                debug_mode = not debug_mode
                
                WriteLog("Modified max_players: " .. max_players)
                WriteLog("Modified debug_mode: " .. tostring(debug_mode))
            end
        )";
        
        ScriptResult result = EXECUTE_FUNCTION("test_variables");
        if (result == ScriptResult::Success)
        {
            LOG_INFO("LUA_DEMO", "Script variables test completed successfully");
        }
    }
    
    void DemoErrorHandling()
    {
        LOG_INFO("LUA_DEMO", "=== Error Handling Demo ===");
        
        auto context = std::make_unique<LuaScriptContext>();
        context->Initialize();
        
        // 测试语法错误
        std::string syntaxErrorCode = R"(
            function bad_syntax(
                -- 缺少右括号，这会导致语法错误
                WriteLog("This will cause a syntax error")
            end
        )";
        
        ScriptResult result = context->ExecuteString(syntaxErrorCode);
        if (result != ScriptResult::Success)
        {
            LOG_INFO("LUA_DEMO", "Syntax error correctly detected: " + context->GetLastError());
        }
        
        // 测试运行时错误
        std::string runtimeErrorCode = R"(
            function runtime_error()
                local nil_value = nil
                return nil_value.some_field  -- 这会导致运行时错误
            end
        )";
        
        result = context->ExecuteString(runtimeErrorCode);
        if (result == ScriptResult::Success)
        {
            result = context->CallFunction("runtime_error");
            if (result != ScriptResult::Success)
            {
                LOG_INFO("LUA_DEMO", "Runtime error correctly detected: " + context->GetLastError());
            }
        }
        
        // 测试调用不存在的函数
        result = context->CallFunction("non_existent_function");
        if (result != ScriptResult::Success)
        {
            LOG_INFO("LUA_DEMO", "Function not found error correctly detected: " + context->GetLastError());
        }
    }
    
    void DemoScriptStatistics()
    {
        LOG_INFO("LUA_DEMO", "=== Script Statistics Demo ===");
        
        auto stats = SCRIPT_MANAGER().GetStatistics();
        
        LOG_INFO("LUA_DEMO", "Script engine statistics:");
        LOG_INFO("LUA_DEMO", "  Root path: " + stats.rootPath);
        LOG_INFO("LUA_DEMO", "  Total scripts: " + std::to_string(stats.totalScripts));
        LOG_INFO("LUA_DEMO", "  Loaded scripts: " + std::to_string(stats.loadedScripts));
        LOG_INFO("LUA_DEMO", "  Failed scripts: " + std::to_string(stats.failedScripts));
        
        LOG_INFO("LUA_DEMO", "Scripts by type:");
        for (const auto& [type, count] : stats.scriptsByType)
        {
            std::string typeName;
            switch (type)
            {
            case ScriptType::Global: typeName = "Global"; break;
            case ScriptType::NPC: typeName = "NPC"; break;
            case ScriptType::Item: typeName = "Item"; break;
            case ScriptType::Mission: typeName = "Mission"; break;
            case ScriptType::Skill: typeName = "Skill"; break;
            case ScriptType::Map: typeName = "Map"; break;
            case ScriptType::Event: typeName = "Event"; break;
            default: typeName = "Unknown"; break;
            }
            
            LOG_INFO("LUA_DEMO", "  " + typeName + ": " + std::to_string(count));
        }
    }
};

} // namespace sword2

// 全局脚本引擎演示实例
sword2::LuaScriptEngineDemo g_LuaDemo;

// 初始化脚本引擎
bool InitializeLuaScriptEngine()
{
    return g_LuaDemo.Initialize();
}

// 运行脚本引擎演示
void RunLuaScriptEngineDemo()
{
    g_LuaDemo.RunDemo();
}

// 清理脚本引擎
void CleanupLuaScriptEngine()
{
    g_LuaDemo.Cleanup();
}
