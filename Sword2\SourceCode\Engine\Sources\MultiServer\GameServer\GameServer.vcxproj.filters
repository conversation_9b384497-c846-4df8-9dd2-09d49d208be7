﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="4.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <ItemGroup>
    <Filter Include="Source Files">
      <UniqueIdentifier>{30ca7a80-b8c6-4610-8357-7b71200ceffb}</UniqueIdentifier>
      <Extensions>cpp;c;cxx;rc;def;r;odl;idl;hpj;bat</Extensions>
    </Filter>
    <Filter Include="Header Files">
      <UniqueIdentifier>{4d51a5ea-4afd-460f-b892-2afcaaf242f6}</UniqueIdentifier>
      <Extensions>h;hpp;hxx;hm;inl</Extensions>
    </Filter>
    <Filter Include="Lib">
      <UniqueIdentifier>{072c0cb1-7d45-4504-95ae-6f19fd458fa5}</UniqueIdentifier>
    </Filter>
    <Filter Include="Lib\debug">
      <UniqueIdentifier>{859ee956-ad47-4e6e-827c-903bd326fda0}</UniqueIdentifier>
    </Filter>
    <Filter Include="Lib\release">
      <UniqueIdentifier>{91025d22-662f-4dd5-95f7-c3c77d5fbea5}</UniqueIdentifier>
    </Filter>
    <Filter Include="Resource Files">
      <UniqueIdentifier>{cc2a49fe-ed3b-4ce5-8a90-232c0e06ba7d}</UniqueIdentifier>
      <Extensions>ico;cur;bmp;dlg;rc2;rct;bin;rgs;gif;jpg;jpeg;jpe</Extensions>
    </Filter>
  </ItemGroup>
  <ItemGroup>
    <ClCompile Include="GameServer.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="KSOServer.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="KTransferUnit.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="StdAfx.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
  </ItemGroup>
  <ItemGroup>
    <ClInclude Include="..\..\..\Headers\KRelayProtocol.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="KSOServer.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="KTransferUnit.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="StdAfx.h">
      <Filter>Header Files</Filter>
    </ClInclude>
  </ItemGroup>
  <ItemGroup>
    <Library Include="..\..\..\Lib\debug\engine.lib">
      <Filter>Lib\debug</Filter>
    </Library>
    <Library Include="..\..\..\Lib\debug\CoreServer.lib">
      <Filter>Lib\debug</Filter>
    </Library>
    <Library Include="..\..\..\Lib\debug\common.lib">
      <Filter>Lib\debug</Filter>
    </Library>
    <Library Include="..\..\..\Lib\release\engine.lib">
      <Filter>Lib\release</Filter>
    </Library>
    <Library Include="..\..\..\Lib\release\CoreServer.lib">
      <Filter>Lib\release</Filter>
    </Library>
    <Library Include="..\..\..\Lib\release\common.lib">
      <Filter>Lib\release</Filter>
    </Library>
  </ItemGroup>
  <ItemGroup>
    <CustomBuild Include="ReadMe.txt" />
  </ItemGroup>
</Project>