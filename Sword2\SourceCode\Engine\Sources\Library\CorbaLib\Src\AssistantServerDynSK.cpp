// This file is generated by omniidl (C++ backend)- omniORB_3_0. Do not edit.

#include "AssistantServer.h"
#include <omniORB3/tcDescriptor.h>

static const char* _0RL_library_version = omniORB_3_0;

static CORBA::PR_structMember _0RL_structmember_Message[] = {
  {"MsgId", CORBA::TypeCode::PR_long_tc()},
  {"PlayerId", CORBA::TypeCode::PR_long_tc()},
  {"MsgData", CORBA::TypeCode::PR_any_tc()}
};

static CORBA::TypeCode_ptr _0RL_tc_Message = CORBA::TypeCode::PR_struct_tc("IDL:Message:1.0", "Message", _0RL_structmember_Message, 3);
const CORBA::TypeCode_ptr _tc_Message = _0RL_tc_Message;

static CORBA::TypeCode_ptr _0RL_tc_MessageSeq = CORBA::TypeCode::PR_alias_tc("IDL:MessageSeq:1.0", "MessageSeq", CORBA::TypeCode::PR_sequence_tc(0, _0RL_tc_Message));

const CORBA::TypeCode_ptr _tc_MessageSeq = _0RL_tc_MessageSeq;

const CORBA::TypeCode_ptr _tc_AssistantServer = CORBA::TypeCode::PR_interface_tc("IDL:AssistantServer:1.0", "AssistantServer");

void _0RL_delete_Message(void* _data) {
  Message* _0RL_t = (Message*) _data;
  delete _0RL_t;
}

static CORBA::Boolean
_0RL_tcParser_getMemberDesc_Message(tcStructDesc *_desc, CORBA::ULong _index, tcDescriptor &_newdesc){
  switch (_index) {
  case 0:
    _0RL_buildDesc_clong(_newdesc, ((Message*)_desc->opq_struct)->MsgId);
    return 1;
  case 1:
    _0RL_buildDesc_clong(_newdesc, ((Message*)_desc->opq_struct)->PlayerId);
    return 1;
  case 2:
    _0RL_buildDesc_cany(_newdesc, ((Message*)_desc->opq_struct)->MsgData);
    return 1;
  
  default:
    return 0;
  };
}
static CORBA::ULong

_0RL_tcParser_getMemberCount_Message(tcStructDesc *_desc)
{
  return 3;
}

void _0RL_buildDesc_cMessage(tcDescriptor &_desc, const Message& _data)
{
  _desc.p_struct.getMemberDesc = _0RL_tcParser_getMemberDesc_Message;
  _desc.p_struct.getMemberCount = _0RL_tcParser_getMemberCount_Message;
  _desc.p_struct.opq_struct = (void *)&_data;
}

void operator<<=(CORBA::Any& _a, const Message& _s) {
  tcDescriptor _0RL_tcdesc;
  _0RL_buildDesc_cMessage(_0RL_tcdesc, _s);
  _a.PR_packFrom(_0RL_tc_Message, &_0RL_tcdesc);
}

void operator<<=(CORBA::Any& _a, Message* _sp) {
  tcDescriptor _0RL_tcdesc;
  _0RL_buildDesc_cMessage(_0RL_tcdesc, *_sp);
  _a.PR_packFrom(_0RL_tc_Message, &_0RL_tcdesc);
  delete _sp;
}

CORBA::Boolean operator>>=(const CORBA::Any& _a, Message*& _sp) {
  return _a >>= (const Message*&) _sp;
}

CORBA::Boolean operator>>=(const CORBA::Any& _a, const Message*& _sp) {
  _sp = (Message *) _a.PR_getCachedData();
  if (_sp == 0) {
    tcDescriptor _0RL_tcdesc;
    _sp = new Message;
    _0RL_buildDesc_cMessage(_0RL_tcdesc, *_sp);
    if (_a.PR_unpackTo(_0RL_tc_Message, &_0RL_tcdesc)) {
      ((CORBA::Any *)&_a)->PR_setCachedData((void*)_sp, _0RL_delete_Message);
      return 1;
    } else {
      delete (Message *)_sp; _sp = 0;
      return 0;
    }
  } else {
    CORBA::TypeCode_var _0RL_tctmp = _a.type();
    if (_0RL_tctmp->equivalent(_0RL_tc_Message)) return 1;
    _sp = 0;
    return 0;
  }
}
#ifndef __0RL_tcParser_buildDesc_s0_cMessage__
#define __0RL_tcParser_buildDesc_s0_cMessage__
static void
_0RL_tcParser_setElementCount_s0_cMessage(tcSequenceDesc* _desc, CORBA::ULong _len)
{
  ((_CORBA_Unbounded_Sequence< Message> *)_desc->opq_seq)->length(_len);
}

static CORBA::ULong
_0RL_tcParser_getElementCount_s0_cMessage(tcSequenceDesc* _desc)
{
  return ((_CORBA_Unbounded_Sequence< Message> *)_desc->opq_seq)->length();
}

static CORBA::Boolean
_0RL_tcParser_getElementDesc_s0_cMessage(tcSequenceDesc* _desc, CORBA::ULong _index, tcDescriptor& _newdesc, _CORBA_ULong& _contiguous)
{
  _0RL_buildDesc_cMessage(_newdesc, (*((_CORBA_Unbounded_Sequence< Message> *)_desc->opq_seq))[_index]);
  
  return 1;
}

static void
_0RL_buildDesc_s0_cMessage(tcDescriptor &_desc, const _CORBA_Unbounded_Sequence< Message> & _data)
{
  _desc.p_sequence.opq_seq = (void*) &_data;
  _desc.p_sequence.setElementCount =
    _0RL_tcParser_setElementCount_s0_cMessage;
  _desc.p_sequence.getElementCount =
    _0RL_tcParser_getElementCount_s0_cMessage;
  _desc.p_sequence.getElementDesc =
    _0RL_tcParser_getElementDesc_s0_cMessage;
  }
#endif

void operator <<= (CORBA::Any& _a, const MessageSeq& _s)
{
  tcDescriptor tcdesc;
  _0RL_buildDesc_s0_cMessage(tcdesc, _s);
  _a.PR_packFrom(_tc_MessageSeq, &tcdesc);
}

void _0RL_seq_delete_MessageSeq(void* _data)
{
  delete (MessageSeq*)_data;
}

CORBA::Boolean operator >>= (const CORBA::Any& _a, MessageSeq*& _s_out)
{
  return _a >>= (const MessageSeq*&) _s_out;
}

CORBA::Boolean operator >>= (const CORBA::Any& _a, const MessageSeq*& _s_out)
{
  _s_out = 0;
  MessageSeq* stmp = (MessageSeq*) _a.PR_getCachedData();
  if( stmp == 0 ) {
    tcDescriptor tcdesc;
    stmp = new MessageSeq;
    _0RL_buildDesc_s0_cMessage(tcdesc, *stmp);
    if( _a.PR_unpackTo(_tc_MessageSeq, &tcdesc)) {
      ((CORBA::Any*)&_a)->PR_setCachedData((void*)stmp, _0RL_seq_delete_MessageSeq);
      _s_out = stmp;
      return 1;
    } else {
      delete (MessageSeq *)stmp;
      return 0;
    }
  } else {
    CORBA::TypeCode_var tctmp = _a.type();
    if( tctmp->equivalent(_tc_MessageSeq) ) {
      _s_out = stmp;
      return 1;
    } else {
      return 0;
    }
  }
}

static void
_0RL_tcParser_setObjectPtr_AssistantServer(tcObjrefDesc *_desc, CORBA::Object_ptr _ptr)
{
  AssistantServer_ptr _p = AssistantServer::_narrow(_ptr);
  AssistantServer_ptr* pp = (AssistantServer_ptr*)_desc->opq_objref;
  if (_desc->opq_release && !CORBA::is_nil(*pp)) CORBA::release(*pp);
  *pp = _p;
  CORBA::release(_ptr);
}

static CORBA::Object_ptr
_0RL_tcParser_getObjectPtr_AssistantServer(tcObjrefDesc *_desc)
{
  return (CORBA::Object_ptr) *((AssistantServer_ptr*)_desc->opq_objref);
}

void _0RL_buildDesc_cAssistantServer(tcDescriptor& _desc, const _CORBA_ObjRef_tcDesc_arg< _objref_AssistantServer, AssistantServer_Helper> & _d)
{
  _desc.p_objref.opq_objref = (void*) &_d._data;
  _desc.p_objref.opq_release = _d._rel;
  _desc.p_objref.setObjectPtr = _0RL_tcParser_setObjectPtr_AssistantServer;
  _desc.p_objref.getObjectPtr = _0RL_tcParser_getObjectPtr_AssistantServer;
}

void _0RL_delete_AssistantServer(void* _data) {
  CORBA::release((AssistantServer_ptr) _data);
}

void operator<<=(CORBA::Any& _a, AssistantServer_ptr _s) {
  tcDescriptor tcd;
  _CORBA_ObjRef_tcDesc_arg< _objref_AssistantServer, AssistantServer_Helper>  tmp(_s,0);
  _0RL_buildDesc_cAssistantServer(tcd, tmp);
  _a.PR_packFrom(_tc_AssistantServer, &tcd);
}

void operator<<=(CORBA::Any& _a, AssistantServer_ptr* _sp) {
  _a <<= *_sp;
  CORBA::release(*_sp);
  *_sp = AssistantServer::_nil();
}

CORBA::Boolean operator>>=(const CORBA::Any& _a, AssistantServer_ptr& _s) {
  AssistantServer_ptr sp = (AssistantServer_ptr) _a.PR_getCachedData();
  if (sp == 0) {
    tcDescriptor tcd;
    AssistantServer_var tmp;
    _0RL_buildDesc_cAssistantServer(tcd, tmp);
    if( _a.PR_unpackTo(_tc_AssistantServer, &tcd) ) {
      if (!omniORB::omniORB_27_CompatibleAnyExtraction) {
        ((CORBA::Any*)&_a)->PR_setCachedData((void*)(AssistantServer_ptr)tmp,_0RL_delete_AssistantServer);
      }
      _s = tmp._retn();
      return 1;
    } else {
      _s = AssistantServer::_nil(); return 0;
    }
  }
  else {
    CORBA::TypeCode_var tc = _a.type();
    if (tc->equivalent(_tc_AssistantServer)) {
    _s = sp; return 1;
    }
    else {
    _s = AssistantServer::_nil(); return 0;
    }
  }
}

