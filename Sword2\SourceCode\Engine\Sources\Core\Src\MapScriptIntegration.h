//---------------------------------------------------------------------------
// Sword2 Map Script Integration (c) 2024
//
// File:	MapScriptIntegration.h
// Date:	2024.07.02
// Code:	AI Assistant
// Desc:	Integration layer between map system and Lua scripts
//---------------------------------------------------------------------------
#ifndef MAP_SCRIPT_INTEGRATION_H
#define MAP_SCRIPT_INTEGRATION_H

#include "MapManager.h"
#include "LuaScriptEngine.h"
#include "PlayerManager.h"
#include "UnifiedLoggingSystem.h"
#include <string>
#include <unordered_map>
#include <vector>
#include <functional>

namespace sword2 {

// 地图脚本API扩展
namespace MapScriptAPI {
    
    // 地图基础API
    int GetMapName(lua_State* L);
    int GetMapType(lua_State* L);
    int GetMapPlayerCount(lua_State* L);
    int GetMapMaxPlayers(lua_State* L);
    int IsMapPvPEnabled(lua_State* L);
    int GetMapTimeLimit(lua_State* L);
    
    // 地图实例API
    int CreateMapInstance(lua_State* L);
    int DestroyMapInstance(lua_State* L);
    int GetMapInstanceInfo(lua_State* L);
    int GetMapInstances(lua_State* L);
    
    // 玩家地图API
    int PlayerEnterMap(lua_State* L);
    int PlayerLeaveMap(lua_State* L);
    int GetPlayerMap(lua_State* L);
    int GetPlayersInMap(lua_State* L);
    int TeleportPlayerToMap(lua_State* L);
    
    // 场景对象API
    int CreateSceneObject(lua_State* L);
    int RemoveSceneObject(lua_State* L);
    int GetSceneObject(lua_State* L);
    int GetSceneObjectsInRange(lua_State* L);
    int SetSceneObjectPosition(lua_State* L);
    int GetSceneObjectPosition(lua_State* L);
    
    // 触发器API
    int CreateTrigger(lua_State* L);
    int SetTriggerCondition(lua_State* L);
    int SetTriggerEffect(lua_State* L);
    int ActivateTrigger(lua_State* L);
    int DeactivateTrigger(lua_State* L);
    
    // 传送门API
    int CreatePortal(lua_State* L);
    int SetPortalTarget(lua_State* L);
    int SetPortalRequirement(lua_State* L);
    int UsePortal(lua_State* L);
    
    // 资源点API
    int CreateResource(lua_State* L);
    int SetResourceType(lua_State* L);
    int SetResourceDrops(lua_State* L);
    int HarvestResource(lua_State* L);
    int GetResourceStatus(lua_State* L);
    
    // 地图事件API
    int RegisterMapEventHandler(lua_State* L);
    int UnregisterMapEventHandler(lua_State* L);
    int TriggerMapEvent(lua_State* L);
    
    // 碰撞检测API
    int CheckCollision(lua_State* L);
    int GetCollisionObjects(lua_State* L);
    int SetObjectCollidable(lua_State* L);
    
    // 地图效果API
    int PlayMapEffect(lua_State* L);
    int ShowMapMessage(lua_State* L);
    int SetMapWeather(lua_State* L);
    int SetMapTime(lua_State* L);
}

// 地图事件处理器
class MapEventHandler
{
public:
    struct EventHandlerData
    {
        std::string functionName;   // 处理函数名
        std::string condition;      // 触发条件
        uint32_t priority = 0;      // 优先级
        bool isActive = true;       // 是否激活
        
        EventHandlerData() = default;
        EventHandlerData(const std::string& func, const std::string& cond = "", uint32_t prio = 0)
            : functionName(func), condition(cond), priority(prio) {}
    };
    
    // 注册事件处理器
    bool RegisterHandler(MapEvent event, const EventHandlerData& handler)
    {
        std::lock_guard<std::mutex> lock(m_handlerMutex);
        m_eventHandlers[event].push_back(handler);
        
        // 按优先级排序
        auto& handlers = m_eventHandlers[event];
        std::sort(handlers.begin(), handlers.end(),
            [](const EventHandlerData& a, const EventHandlerData& b) {
                return a.priority > b.priority;
            });
        
        LOG_DEBUG("MAP_EVENT", "Registered event handler for " + GetEventName(event) + ": " + handler.functionName);
        return true;
    }
    
    // 注销事件处理器
    bool UnregisterHandler(MapEvent event, const std::string& functionName)
    {
        std::lock_guard<std::mutex> lock(m_handlerMutex);
        auto it = m_eventHandlers.find(event);
        if (it != m_eventHandlers.end())
        {
            auto& handlers = it->second;
            handlers.erase(std::remove_if(handlers.begin(), handlers.end(),
                [&functionName](const EventHandlerData& handler) {
                    return handler.functionName == functionName;
                }), handlers.end());
            
            LOG_DEBUG("MAP_EVENT", "Unregistered event handler for " + GetEventName(event) + ": " + functionName);
            return true;
        }
        return false;
    }
    
    // 触发事件
    void TriggerEvent(MapEvent event, uint32_t instanceId, uint32_t playerId = 0, uint32_t objectId = 0)
    {
        std::lock_guard<std::mutex> lock(m_handlerMutex);
        auto it = m_eventHandlers.find(event);
        if (it == m_eventHandlers.end())
            return;
        
        for (const auto& handler : it->second)
        {
            if (!handler.isActive)
                continue;
            
            // 检查条件
            if (!handler.condition.empty())
            {
                // 这里可以执行条件检查脚本
                // 暂时简化处理
            }
            
            // 执行处理函数
            std::vector<LuaValue> args = {
                LuaValue(static_cast<double>(instanceId)),
                LuaValue(static_cast<double>(playerId)),
                LuaValue(static_cast<double>(objectId))
            };
            
            ScriptResult result = EXECUTE_FUNCTION(handler.functionName, args);
            if (result != ScriptResult::Success)
            {
                LOG_WARNING("MAP_EVENT", "Failed to execute event handler: " + handler.functionName);
            }
        }
    }

private:
    std::mutex m_handlerMutex;
    std::unordered_map<MapEvent, std::vector<EventHandlerData>> m_eventHandlers;
    
    std::string GetEventName(MapEvent event)
    {
        switch (event)
        {
        case MapEvent::PlayerEnter: return "PlayerEnter";
        case MapEvent::PlayerLeave: return "PlayerLeave";
        case MapEvent::ObjectInteract: return "ObjectInteract";
        case MapEvent::TriggerActivate: return "TriggerActivate";
        case MapEvent::ResourceHarvest: return "ResourceHarvest";
        case MapEvent::PortalUse: return "PortalUse";
        case MapEvent::MapCreate: return "MapCreate";
        case MapEvent::MapDestroy: return "MapDestroy";
        default: return "Unknown";
        }
    }
};

// 地图效果管理器
class MapEffectManager
{
public:
    struct MapEffect
    {
        uint32_t effectId = 0;      // 效果ID
        std::string effectType;     // 效果类型
        uint32_t instanceId = 0;    // 地图实例ID
        int32_t x = 0, y = 0;      // 位置
        uint32_t duration = 0;      // 持续时间(毫秒)
        std::chrono::system_clock::time_point startTime; // 开始时间
        bool isActive = true;       // 是否激活
        
        MapEffect() = default;
        MapEffect(uint32_t id, const std::string& type, uint32_t instId, int32_t posX, int32_t posY, uint32_t dur)
            : effectId(id), effectType(type), instanceId(instId), x(posX), y(posY), duration(dur)
        {
            startTime = std::chrono::system_clock::now();
        }
        
        // 检查是否过期
        bool IsExpired() const
        {
            if (duration == 0) return false; // 永久效果
            
            auto now = std::chrono::system_clock::now();
            auto elapsed = std::chrono::duration_cast<std::chrono::milliseconds>(now - startTime);
            return elapsed.count() >= duration;
        }
    };
    
    // 播放地图效果
    uint32_t PlayEffect(const std::string& effectType, uint32_t instanceId, int32_t x, int32_t y, uint32_t duration = 0)
    {
        std::lock_guard<std::mutex> lock(m_effectMutex);
        
        uint32_t effectId = m_nextEffectId++;
        MapEffect effect(effectId, effectType, instanceId, x, y, duration);
        
        m_activeEffects[effectId] = effect;
        
        LOG_INFO("MAP_EFFECT", "Playing effect " + effectType + " at (" + std::to_string(x) + 
                ", " + std::to_string(y) + ") in instance " + std::to_string(instanceId));
        
        return effectId;
    }
    
    // 停止效果
    bool StopEffect(uint32_t effectId)
    {
        std::lock_guard<std::mutex> lock(m_effectMutex);
        auto it = m_activeEffects.find(effectId);
        if (it != m_activeEffects.end())
        {
            it->second.isActive = false;
            LOG_DEBUG("MAP_EFFECT", "Stopped effect " + std::to_string(effectId));
            return true;
        }
        return false;
    }
    
    // 更新效果
    void Update()
    {
        std::lock_guard<std::mutex> lock(m_effectMutex);
        
        auto it = m_activeEffects.begin();
        while (it != m_activeEffects.end())
        {
            if (!it->second.isActive || it->second.IsExpired())
            {
                LOG_DEBUG("MAP_EFFECT", "Removing expired effect " + std::to_string(it->first));
                it = m_activeEffects.erase(it);
            }
            else
            {
                ++it;
            }
        }
    }
    
    // 获取地图实例的所有效果
    std::vector<MapEffect> GetInstanceEffects(uint32_t instanceId)
    {
        std::lock_guard<std::mutex> lock(m_effectMutex);
        std::vector<MapEffect> effects;
        
        for (const auto& [effectId, effect] : m_activeEffects)
        {
            if (effect.instanceId == instanceId && effect.isActive)
            {
                effects.push_back(effect);
            }
        }
        
        return effects;
    }

private:
    std::mutex m_effectMutex;
    std::unordered_map<uint32_t, MapEffect> m_activeEffects;
    std::atomic<uint32_t> m_nextEffectId{1};
};

// 地图脚本集成管理器
class MapScriptIntegration : public Singleton<MapScriptIntegration>
{
public:
    MapScriptIntegration() = default;
    ~MapScriptIntegration() = default;
    
    // 初始化地图脚本集成
    bool Initialize()
    {
        // 注册地图相关的Lua API函数
        RegisterMapScriptAPI();
        
        LOG_INFO("MAP_SCRIPT", "Map script integration initialized");
        return true;
    }
    
    // 加载地图脚本
    bool LoadMapScript(const std::string& scriptPath)
    {
        ScriptResult result = LOAD_SCRIPT(scriptPath, ScriptType::Map);
        if (result == ScriptResult::Success)
        {
            LOG_INFO("MAP_SCRIPT", "Loaded map script: " + scriptPath);
            return true;
        }
        else
        {
            LOG_ERROR("MAP_SCRIPT", "Failed to load map script: " + scriptPath);
            return false;
        }
    }
    
    // 执行地图脚本函数
    bool ExecuteMapFunction(const std::string& functionName, uint32_t instanceId, uint32_t playerId = 0, uint32_t objectId = 0)
    {
        std::vector<LuaValue> args;
        args.push_back(LuaValue(static_cast<double>(instanceId)));
        if (playerId != 0)
        {
            args.push_back(LuaValue(static_cast<double>(playerId)));
        }
        if (objectId != 0)
        {
            args.push_back(LuaValue(static_cast<double>(objectId)));
        }
        
        ScriptResult result = EXECUTE_FUNCTION(functionName, args);
        return result == ScriptResult::Success;
    }
    
    // 注册地图事件处理器
    bool RegisterEventHandler(MapEvent event, const std::string& functionName, const std::string& condition = "", uint32_t priority = 0)
    {
        MapEventHandler::EventHandlerData handler(functionName, condition, priority);
        return m_eventHandler.RegisterHandler(event, handler);
    }
    
    // 注销地图事件处理器
    bool UnregisterEventHandler(MapEvent event, const std::string& functionName)
    {
        return m_eventHandler.UnregisterHandler(event, functionName);
    }
    
    // 触发地图事件
    void TriggerMapEvent(MapEvent event, uint32_t instanceId, uint32_t playerId = 0, uint32_t objectId = 0)
    {
        m_eventHandler.TriggerEvent(event, instanceId, playerId, objectId);
    }
    
    // 播放地图效果
    uint32_t PlayMapEffect(const std::string& effectType, uint32_t instanceId, int32_t x, int32_t y, uint32_t duration = 0)
    {
        return m_effectManager.PlayEffect(effectType, instanceId, x, y, duration);
    }
    
    // 停止地图效果
    bool StopMapEffect(uint32_t effectId)
    {
        return m_effectManager.StopEffect(effectId);
    }
    
    // 更新系统
    void Update()
    {
        m_effectManager.Update();
    }
    
    // 获取事件处理器
    MapEventHandler& GetEventHandler() { return m_eventHandler; }
    
    // 获取效果管理器
    MapEffectManager& GetEffectManager() { return m_effectManager; }

private:
    MapEventHandler m_eventHandler;
    MapEffectManager m_effectManager;
    
    void RegisterMapScriptAPI()
    {
        // 注册地图基础API
        REGISTER_LUA_FUNCTION("GetMapName", MapScriptAPI::GetMapName);
        REGISTER_LUA_FUNCTION("GetMapType", MapScriptAPI::GetMapType);
        REGISTER_LUA_FUNCTION("GetMapPlayerCount", MapScriptAPI::GetMapPlayerCount);
        REGISTER_LUA_FUNCTION("GetMapMaxPlayers", MapScriptAPI::GetMapMaxPlayers);
        REGISTER_LUA_FUNCTION("IsMapPvPEnabled", MapScriptAPI::IsMapPvPEnabled);
        
        // 注册地图实例API
        REGISTER_LUA_FUNCTION("CreateMapInstance", MapScriptAPI::CreateMapInstance);
        REGISTER_LUA_FUNCTION("DestroyMapInstance", MapScriptAPI::DestroyMapInstance);
        REGISTER_LUA_FUNCTION("GetMapInstanceInfo", MapScriptAPI::GetMapInstanceInfo);
        
        // 注册玩家地图API
        REGISTER_LUA_FUNCTION("PlayerEnterMap", MapScriptAPI::PlayerEnterMap);
        REGISTER_LUA_FUNCTION("PlayerLeaveMap", MapScriptAPI::PlayerLeaveMap);
        REGISTER_LUA_FUNCTION("GetPlayerMap", MapScriptAPI::GetPlayerMap);
        REGISTER_LUA_FUNCTION("TeleportPlayerToMap", MapScriptAPI::TeleportPlayerToMap);
        
        // 注册场景对象API
        REGISTER_LUA_FUNCTION("CreateSceneObject", MapScriptAPI::CreateSceneObject);
        REGISTER_LUA_FUNCTION("RemoveSceneObject", MapScriptAPI::RemoveSceneObject);
        REGISTER_LUA_FUNCTION("GetSceneObject", MapScriptAPI::GetSceneObject);
        REGISTER_LUA_FUNCTION("SetSceneObjectPosition", MapScriptAPI::SetSceneObjectPosition);
        
        // 注册触发器API
        REGISTER_LUA_FUNCTION("CreateTrigger", MapScriptAPI::CreateTrigger);
        REGISTER_LUA_FUNCTION("SetTriggerCondition", MapScriptAPI::SetTriggerCondition);
        REGISTER_LUA_FUNCTION("ActivateTrigger", MapScriptAPI::ActivateTrigger);
        
        // 注册传送门API
        REGISTER_LUA_FUNCTION("CreatePortal", MapScriptAPI::CreatePortal);
        REGISTER_LUA_FUNCTION("SetPortalTarget", MapScriptAPI::SetPortalTarget);
        REGISTER_LUA_FUNCTION("UsePortal", MapScriptAPI::UsePortal);
        
        // 注册资源点API
        REGISTER_LUA_FUNCTION("CreateResource", MapScriptAPI::CreateResource);
        REGISTER_LUA_FUNCTION("SetResourceType", MapScriptAPI::SetResourceType);
        REGISTER_LUA_FUNCTION("HarvestResource", MapScriptAPI::HarvestResource);
        
        // 注册地图事件API
        REGISTER_LUA_FUNCTION("RegisterMapEventHandler", MapScriptAPI::RegisterMapEventHandler);
        REGISTER_LUA_FUNCTION("TriggerMapEvent", MapScriptAPI::TriggerMapEvent);
        
        // 注册碰撞检测API
        REGISTER_LUA_FUNCTION("CheckCollision", MapScriptAPI::CheckCollision);
        REGISTER_LUA_FUNCTION("SetObjectCollidable", MapScriptAPI::SetObjectCollidable);
        
        // 注册地图效果API
        REGISTER_LUA_FUNCTION("PlayMapEffect", MapScriptAPI::PlayMapEffect);
        REGISTER_LUA_FUNCTION("ShowMapMessage", MapScriptAPI::ShowMapMessage);
        
        LOG_INFO("MAP_SCRIPT", "Registered map script API functions");
    }
};

} // namespace sword2

// 全局地图脚本集成访问
#define MAP_SCRIPT_INTEGRATION() sword2::MapScriptIntegration::getInstance()

// 便捷宏定义
#define INIT_MAP_SCRIPT_INTEGRATION() MAP_SCRIPT_INTEGRATION().Initialize()
#define LOAD_MAP_SCRIPT(scriptPath) MAP_SCRIPT_INTEGRATION().LoadMapScript(scriptPath)
#define EXECUTE_MAP_FUNCTION(functionName, instanceId, playerId, objectId) MAP_SCRIPT_INTEGRATION().ExecuteMapFunction(functionName, instanceId, playerId, objectId)

#define REGISTER_MAP_EVENT_HANDLER(event, functionName, condition, priority) MAP_SCRIPT_INTEGRATION().RegisterEventHandler(event, functionName, condition, priority)
#define TRIGGER_MAP_EVENT(event, instanceId, playerId, objectId) MAP_SCRIPT_INTEGRATION().TriggerMapEvent(event, instanceId, playerId, objectId)

#define PLAY_MAP_EFFECT(effectType, instanceId, x, y, duration) MAP_SCRIPT_INTEGRATION().PlayMapEffect(effectType, instanceId, x, y, duration)
#define STOP_MAP_EFFECT(effectId) MAP_SCRIPT_INTEGRATION().StopMapEffect(effectId)

#define MAP_EVENT_HANDLER() MAP_SCRIPT_INTEGRATION().GetEventHandler()
#define MAP_EFFECT_MANAGER() MAP_SCRIPT_INTEGRATION().GetEffectManager()

#endif // MAP_SCRIPT_INTEGRATION_H
