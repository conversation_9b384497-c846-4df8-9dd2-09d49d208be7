[1]
region=0,4
5=51104,102592
1=52448,104704
2=50304,100032
3=49376,103264
4=53184,101024

[11]
region=5,10
5=100608,162336
6=101824,165792
7=97504,159456
8=96160,163392
9=103840,160896
10=96672,162880

[20]
region=10,12
10=113472,199232
11=110656,197888
12=108064,200512

[13]
region=13,14
13=62336,157568
14=66240,156160

[25]
region=15,16
15=128896,165856
16=132800,163200

[54]
region=17,18
17=53280,100832
18=52416,96960

[53]
region=19,19
19=52032,101696

[55]
region=20,20
20=51008,102016

[59]
region=21,22
21=50144,103232
22=54016,99200

[37]
region=23,26
23=55232,99200
24=54272,102976
25=51904,96512
26=59392,94912

[49]
region=27,28
27=54016,101376
28=52608,102880

[78]
region=29,33
29=50464,103616
30=50336,107424
31=46528,104640
32=47168,101664
33=54528,103552

[80]
region=34,40
34=56256,96960
35=54912,103456
36=59648,97152
37=51776,101824
38=53024,96800
39=61056,91040
40=53632,96000

[81]
region=39,40
39=53376,100096
40=50944,102592

[95]
region=41,41
41=50976,102720

[96]
region=42,42
42=50976,102720

[99]
region=43,44
43=52160,102368
44=52928,104608

[100]
region=45,46
45=51616,101920 
46=54496,102848

[101]
region=47,48
47=54048,100896
48=50976,104256

[44]
region=49,50
49=28352,143968
50=50720,102880

[103]
region=51,52
51=59040,95456
52=51840,101120

[115]
region=53,54
53=48032,117504
54=48032,117504

[121]
region=55,56
55=64000,143200
56=61952,144416

[131]
region=57,58
57=50624,101600
58=47616,98208

[153]
region=59,60
59=52256,103520
60=52256,103520

[154]
region=61,62
61=12896,43552
62=9056,41824

[162]
region=63,64
63=50528,100320
64=52128,100992

[174]
region=65,66
65=52960,105472
66=51808,102208

[176]
region=67,70
67=44640,106176
68=53920,105120
69=50208,94016
70=50944,93600

[183]
region=70,71
70=55872,85536
71=47712,101664

[2]
region=72,72
72=83360, 114912

[242]
region=1,1
1=51680, 102400

[243]
region=1,1
1=51680, 102400

[244]
region=1,1
1=51680, 102400

[245]
region=1,1
1=51680, 102400

[246]
region=1,1
1=51680, 102400

[247]
region=1,1
1=51680, 102400

[248]
region=1,1
1=51680, 102400

[323]
region=1,2
1=48928, 102272
2=50944, 98400

[324]
region=1,2
1=48928, 102272
2=50944, 98400

[325]
region=1,2
1=48928, 102272
2=50944, 98400

[336]
region=1,1
1=35584, 102048

[175]
region=1,1
1=53440, 101440

[396]
region=1,1
1=47744, 97440

[398]
region=1,1
1=47744, 97440

[400]
region=1,1
1=47744, 97440

[402]
region=1,1
1=47744, 97440

[404]
region=1,1
1=47744, 97440

[406]
region=1,1
1=47744, 97440

[408]
region=1,1
1=47744, 97440

[410]
region=1,1
1=47744, 97440

[412]
region=1,1
1=47744, 97440

[414]
region=1,1
1=47744, 97440

[518]
region=1,3
1=50624,101568
2=50816,101120
3=51328,100704

[519]
region=1,3
1=50624,101568
2=50816,101120
3=51328,100704

[527]
region=1,1
1=48736, 96768

[528]
region=1,1
1=48736, 96768

[529]
region=1,1
1=48736, 96768

[530]
region=1,1
1=48736, 96768

[540]
region=1,1
1=48736, 96768

[542]
region=1,1
1=48736, 96768

[544]
region=1,1
1=48736, 96768

[546]
region=1,1
1=48736, 96768

[548]
region=1,1
1=48736, 96768

[550]
region=1,1
1=48736, 96768

[552]
region=1,1
1=48736, 96768

[554]
region=1,1
1=48736, 96768

[556]
region=1,1
1=48736, 96768

[558]
region=1,1
1=48736, 96768

