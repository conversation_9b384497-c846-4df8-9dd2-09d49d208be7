//---------------------------------------------------------------------------
// Sword2 Map Script API Implementation (c) 2024
//
// File:	MapScriptAPI.cpp
// Date:	2024.07.02
// Code:	AI Assistant
// Desc:	Implementation of map-related Lua script API functions
//---------------------------------------------------------------------------

#include "MapScriptIntegration.h"
#include "MapManager.h"
#include "PlayerManager.h"
#include "LuaScriptEngine.h"

namespace sword2 {
namespace MapScriptAPI {

// 辅助函数：获取地图实例ID参数
uint32_t GetInstanceIdFromLua(lua_State* L, int index = 1)
{
    if (lua_isnumber(L, index))
    {
        return static_cast<uint32_t>(lua_tonumber(L, index));
    }
    return 0;
}

// 辅助函数：获取地图实例对象
std::shared_ptr<MapInstance> GetMapInstanceFromLua(lua_State* L, int index = 1)
{
    uint32_t instanceId = GetInstanceIdFromLua(L, index);
    if (instanceId == 0) return nullptr;
    
    return MAP_MANAGER().GetMapInstance(instanceId);
}

// 地图基础API实现
int GetMapName(lua_State* L)
{
    auto instance = GetMapInstanceFromLua(L);
    if (instance)
    {
        lua_pushstring(L, instance->mapName.c_str());
    }
    else
    {
        lua_pushstring(L, "");
    }
    return 1;
}

int GetMapType(lua_State* L)
{
    auto instance = GetMapInstanceFromLua(L);
    if (instance)
    {
        lua_pushnumber(L, static_cast<int>(instance->mapData.mapType));
        lua_pushstring(L, instance->mapData.GetMapTypeDescription().c_str());
        return 2;
    }
    else
    {
        lua_pushnumber(L, 0);
        lua_pushstring(L, "Unknown");
        return 2;
    }
}

int GetMapPlayerCount(lua_State* L)
{
    auto instance = GetMapInstanceFromLua(L);
    if (instance)
    {
        lua_pushnumber(L, instance->GetPlayerCount());
    }
    else
    {
        lua_pushnumber(L, 0);
    }
    return 1;
}

int GetMapMaxPlayers(lua_State* L)
{
    auto instance = GetMapInstanceFromLua(L);
    if (instance)
    {
        lua_pushnumber(L, instance->maxPlayers);
    }
    else
    {
        lua_pushnumber(L, 0);
    }
    return 1;
}

int IsMapPvPEnabled(lua_State* L)
{
    auto instance = GetMapInstanceFromLua(L);
    if (instance)
    {
        lua_pushboolean(L, instance->mapData.isPvpEnabled ? 1 : 0);
    }
    else
    {
        lua_pushboolean(L, 0);
    }
    return 1;
}

int GetMapTimeLimit(lua_State* L)
{
    auto instance = GetMapInstanceFromLua(L);
    if (instance)
    {
        lua_pushnumber(L, instance->timeLimit);
    }
    else
    {
        lua_pushnumber(L, 0);
    }
    return 1;
}

// 地图实例API实现
int CreateMapInstance(lua_State* L)
{
    if (lua_isnumber(L, 1))
    {
        uint32_t mapId = static_cast<uint32_t>(lua_tonumber(L, 1));
        bool isPrivate = lua_isboolean(L, 2) ? (lua_toboolean(L, 2) != 0) : false;
        
        uint32_t instanceId = CREATE_MAP_INSTANCE(mapId, isPrivate);
        lua_pushnumber(L, instanceId);
        
        if (instanceId != 0)
        {
            LOG_INFO("MAP_API", "Created map instance " + std::to_string(instanceId) + " for map " + std::to_string(mapId));
        }
    }
    else
    {
        lua_pushnumber(L, 0);
    }
    return 1;
}

int DestroyMapInstance(lua_State* L)
{
    if (lua_isnumber(L, 1))
    {
        uint32_t instanceId = static_cast<uint32_t>(lua_tonumber(L, 1));
        
        bool success = DESTROY_MAP_INSTANCE(instanceId);
        lua_pushboolean(L, success ? 1 : 0);
        
        if (success)
        {
            LOG_INFO("MAP_API", "Destroyed map instance " + std::to_string(instanceId));
        }
    }
    else
    {
        lua_pushboolean(L, 0);
    }
    return 1;
}

int GetMapInstanceInfo(lua_State* L)
{
    auto instance = GetMapInstanceFromLua(L);
    if (instance)
    {
        // 创建Lua表返回地图信息
        lua_newtable(L);
        
        lua_pushstring(L, "instanceId");
        lua_pushnumber(L, instance->instanceId);
        lua_settable(L, -3);
        
        lua_pushstring(L, "mapId");
        lua_pushnumber(L, instance->mapId);
        lua_settable(L, -3);
        
        lua_pushstring(L, "mapName");
        lua_pushstring(L, instance->mapName.c_str());
        lua_settable(L, -3);
        
        lua_pushstring(L, "playerCount");
        lua_pushnumber(L, instance->GetPlayerCount());
        lua_settable(L, -3);
        
        lua_pushstring(L, "maxPlayers");
        lua_pushnumber(L, instance->maxPlayers);
        lua_settable(L, -3);
        
        lua_pushstring(L, "isActive");
        lua_pushboolean(L, instance->isActive ? 1 : 0);
        lua_settable(L, -3);
        
        lua_pushstring(L, "timeLimit");
        lua_pushnumber(L, instance->timeLimit);
        lua_settable(L, -3);
    }
    else
    {
        lua_pushnil(L);
    }
    return 1;
}

int GetMapInstances(lua_State* L)
{
    if (lua_isnumber(L, 1))
    {
        uint32_t mapId = static_cast<uint32_t>(lua_tonumber(L, 1));
        
        auto instances = MAP_MANAGER().GetMapInstances(mapId);
        
        // 创建Lua表返回实例列表
        lua_newtable(L);
        for (size_t i = 0; i < instances.size(); ++i)
        {
            lua_pushnumber(L, i + 1);
            lua_pushnumber(L, instances[i]->instanceId);
            lua_settable(L, -3);
        }
        
        LOG_DEBUG("MAP_API", "Found " + std::to_string(instances.size()) + " instances for map " + std::to_string(mapId));
    }
    else
    {
        lua_newtable(L);
    }
    return 1;
}

// 玩家地图API实现
int PlayerEnterMap(lua_State* L)
{
    if (lua_isnumber(L, 1) && lua_isnumber(L, 2))
    {
        uint32_t playerId = static_cast<uint32_t>(lua_tonumber(L, 1));
        uint32_t mapId = static_cast<uint32_t>(lua_tonumber(L, 2));
        int32_t x = lua_isnumber(L, 3) ? static_cast<int32_t>(lua_tonumber(L, 3)) : -1;
        int32_t y = lua_isnumber(L, 4) ? static_cast<int32_t>(lua_tonumber(L, 4)) : -1;
        
        bool success = PLAYER_ENTER_MAP(playerId, mapId, x, y);
        lua_pushboolean(L, success ? 1 : 0);
        
        if (success)
        {
            LOG_INFO("MAP_API", "Player " + std::to_string(playerId) + " entered map " + std::to_string(mapId));
        }
    }
    else
    {
        lua_pushboolean(L, 0);
    }
    return 1;
}

int PlayerLeaveMap(lua_State* L)
{
    if (lua_isnumber(L, 1) && lua_isnumber(L, 2))
    {
        uint32_t playerId = static_cast<uint32_t>(lua_tonumber(L, 1));
        uint32_t instanceId = static_cast<uint32_t>(lua_tonumber(L, 2));
        
        bool success = PLAYER_LEAVE_MAP(playerId, instanceId);
        lua_pushboolean(L, success ? 1 : 0);
        
        if (success)
        {
            LOG_INFO("MAP_API", "Player " + std::to_string(playerId) + " left map instance " + std::to_string(instanceId));
        }
    }
    else
    {
        lua_pushboolean(L, 0);
    }
    return 1;
}

int GetPlayerMap(lua_State* L)
{
    if (lua_isnumber(L, 1))
    {
        uint32_t playerId = static_cast<uint32_t>(lua_tonumber(L, 1));
        
        // 这里应该从PlayerManager获取玩家当前地图信息
        // 暂时返回0
        lua_pushnumber(L, 0);
        lua_pushnumber(L, 0);
        return 2;
    }
    else
    {
        lua_pushnumber(L, 0);
        lua_pushnumber(L, 0);
        return 2;
    }
}

int GetPlayersInMap(lua_State* L)
{
    auto instance = GetMapInstanceFromLua(L);
    if (instance)
    {
        // 创建Lua表返回玩家列表
        lua_newtable(L);
        int index = 1;
        for (uint32_t playerId : instance->players)
        {
            lua_pushnumber(L, index++);
            lua_pushnumber(L, playerId);
            lua_settable(L, -3);
        }
        
        LOG_DEBUG("MAP_API", "Found " + std::to_string(instance->players.size()) + 
                 " players in map instance " + std::to_string(instance->instanceId));
    }
    else
    {
        lua_newtable(L);
    }
    return 1;
}

int TeleportPlayerToMap(lua_State* L)
{
    if (lua_isnumber(L, 1) && lua_isnumber(L, 2) && lua_isnumber(L, 3) && lua_isnumber(L, 4))
    {
        uint32_t playerId = static_cast<uint32_t>(lua_tonumber(L, 1));
        uint32_t mapId = static_cast<uint32_t>(lua_tonumber(L, 2));
        int32_t x = static_cast<int32_t>(lua_tonumber(L, 3));
        int32_t y = static_cast<int32_t>(lua_tonumber(L, 4));
        
        // 这里应该实现实际的传送逻辑
        // 1. 让玩家离开当前地图
        // 2. 让玩家进入目标地图
        bool success = PLAYER_ENTER_MAP(playerId, mapId, x, y);
        lua_pushboolean(L, success ? 1 : 0);
        
        if (success)
        {
            LOG_INFO("MAP_API", "Teleported player " + std::to_string(playerId) + 
                    " to map " + std::to_string(mapId) + " (" + std::to_string(x) + ", " + std::to_string(y) + ")");
        }
    }
    else
    {
        lua_pushboolean(L, 0);
    }
    return 1;
}

// 场景对象API实现
int CreateSceneObject(lua_State* L)
{
    if (lua_isnumber(L, 1) && lua_isnumber(L, 2) && lua_isstring(L, 3) && 
        lua_isnumber(L, 4) && lua_isnumber(L, 5))
    {
        uint32_t instanceId = static_cast<uint32_t>(lua_tonumber(L, 1));
        int objectType = static_cast<int>(lua_tonumber(L, 2));
        std::string name = lua_tostring(L, 3);
        int32_t x = static_cast<int32_t>(lua_tonumber(L, 4));
        int32_t y = static_cast<int32_t>(lua_tonumber(L, 5));
        std::string scriptPath = lua_isstring(L, 6) ? lua_tostring(L, 6) : "";
        
        uint32_t objectId = CREATE_SCENE_OBJECT(instanceId, static_cast<SceneObjectType>(objectType), 
                                               name, x, y, scriptPath);
        lua_pushnumber(L, objectId);
        
        if (objectId != 0)
        {
            LOG_INFO("MAP_API", "Created scene object " + name + " (ID: " + std::to_string(objectId) + 
                    ") in map instance " + std::to_string(instanceId));
        }
    }
    else
    {
        lua_pushnumber(L, 0);
    }
    return 1;
}

int RemoveSceneObject(lua_State* L)
{
    if (lua_isnumber(L, 1) && lua_isnumber(L, 2))
    {
        uint32_t instanceId = static_cast<uint32_t>(lua_tonumber(L, 1));
        uint32_t objectId = static_cast<uint32_t>(lua_tonumber(L, 2));
        
        bool success = REMOVE_SCENE_OBJECT(instanceId, objectId);
        lua_pushboolean(L, success ? 1 : 0);
        
        if (success)
        {
            LOG_INFO("MAP_API", "Removed scene object " + std::to_string(objectId) + 
                    " from map instance " + std::to_string(instanceId));
        }
    }
    else
    {
        lua_pushboolean(L, 0);
    }
    return 1;
}

int GetSceneObject(lua_State* L)
{
    if (lua_isnumber(L, 1) && lua_isnumber(L, 2))
    {
        uint32_t instanceId = static_cast<uint32_t>(lua_tonumber(L, 1));
        uint32_t objectId = static_cast<uint32_t>(lua_tonumber(L, 2));
        
        auto instance = GET_MAP_INSTANCE(instanceId);
        if (instance)
        {
            auto object = instance->GetSceneObject(objectId);
            if (object)
            {
                // 创建Lua表返回对象信息
                lua_newtable(L);
                
                lua_pushstring(L, "objectId");
                lua_pushnumber(L, object->objectId);
                lua_settable(L, -3);
                
                lua_pushstring(L, "name");
                lua_pushstring(L, object->name.c_str());
                lua_settable(L, -3);
                
                lua_pushstring(L, "type");
                lua_pushnumber(L, static_cast<int>(object->type));
                lua_settable(L, -3);
                
                lua_pushstring(L, "x");
                lua_pushnumber(L, object->x);
                lua_settable(L, -3);
                
                lua_pushstring(L, "y");
                lua_pushnumber(L, object->y);
                lua_settable(L, -3);
                
                lua_pushstring(L, "status");
                lua_pushnumber(L, static_cast<int>(object->status));
                lua_settable(L, -3);
                
                return 1;
            }
        }
    }
    
    lua_pushnil(L);
    return 1;
}

int GetSceneObjectsInRange(lua_State* L)
{
    if (lua_isnumber(L, 1) && lua_isnumber(L, 2) && lua_isnumber(L, 3) && lua_isnumber(L, 4))
    {
        uint32_t instanceId = static_cast<uint32_t>(lua_tonumber(L, 1));
        int32_t x = static_cast<int32_t>(lua_tonumber(L, 2));
        int32_t y = static_cast<int32_t>(lua_tonumber(L, 3));
        double range = lua_tonumber(L, 4);
        
        auto instance = GET_MAP_INSTANCE(instanceId);
        if (instance)
        {
            auto objects = instance->GetSceneObjectsInRange(x, y, range);
            
            // 创建Lua表返回对象列表
            lua_newtable(L);
            for (size_t i = 0; i < objects.size(); ++i)
            {
                lua_pushnumber(L, i + 1);
                lua_pushnumber(L, objects[i]->objectId);
                lua_settable(L, -3);
            }
            
            LOG_DEBUG("MAP_API", "Found " + std::to_string(objects.size()) + 
                     " objects in range at (" + std::to_string(x) + ", " + std::to_string(y) + ")");
            return 1;
        }
    }
    
    lua_newtable(L);
    return 1;
}

int SetSceneObjectPosition(lua_State* L)
{
    if (lua_isnumber(L, 1) && lua_isnumber(L, 2) && lua_isnumber(L, 3) && lua_isnumber(L, 4))
    {
        uint32_t instanceId = static_cast<uint32_t>(lua_tonumber(L, 1));
        uint32_t objectId = static_cast<uint32_t>(lua_tonumber(L, 2));
        int32_t x = static_cast<int32_t>(lua_tonumber(L, 3));
        int32_t y = static_cast<int32_t>(lua_tonumber(L, 4));
        
        auto instance = GET_MAP_INSTANCE(instanceId);
        if (instance)
        {
            auto object = instance->GetSceneObject(objectId);
            if (object)
            {
                object->SetPosition(instance->mapId, x, y);
                lua_pushboolean(L, 1);
                
                LOG_DEBUG("MAP_API", "Set scene object " + std::to_string(objectId) + 
                         " position to (" + std::to_string(x) + ", " + std::to_string(y) + ")");
                return 1;
            }
        }
    }
    
    lua_pushboolean(L, 0);
    return 1;
}

int GetSceneObjectPosition(lua_State* L)
{
    if (lua_isnumber(L, 1) && lua_isnumber(L, 2))
    {
        uint32_t instanceId = static_cast<uint32_t>(lua_tonumber(L, 1));
        uint32_t objectId = static_cast<uint32_t>(lua_tonumber(L, 2));
        
        auto instance = GET_MAP_INSTANCE(instanceId);
        if (instance)
        {
            auto object = instance->GetSceneObject(objectId);
            if (object)
            {
                lua_pushnumber(L, object->x);
                lua_pushnumber(L, object->y);
                return 2;
            }
        }
    }
    
    lua_pushnumber(L, 0);
    lua_pushnumber(L, 0);
    return 2;
}

// 触发器API实现
int CreateTrigger(lua_State* L)
{
    if (lua_isnumber(L, 1) && lua_isstring(L, 2) && lua_isnumber(L, 3) &&
        lua_isnumber(L, 4) && lua_isnumber(L, 5))
    {
        uint32_t instanceId = static_cast<uint32_t>(lua_tonumber(L, 1));
        std::string name = lua_tostring(L, 2);
        int triggerType = static_cast<int>(lua_tonumber(L, 3));
        int32_t x = static_cast<int32_t>(lua_tonumber(L, 4));
        int32_t y = static_cast<int32_t>(lua_tonumber(L, 5));
        uint32_t range = lua_isnumber(L, 6) ? static_cast<uint32_t>(lua_tonumber(L, 6)) : 50;

        auto instance = GET_MAP_INSTANCE(instanceId);
        if (instance)
        {
            auto trigger = std::make_shared<TriggerObject>(MAP_MANAGER().m_nextObjectId++, name,
                                                          static_cast<TriggerType>(triggerType));
            trigger->SetPosition(instance->mapId, x, y);
            trigger->triggerRange = range;

            if (instance->AddSceneObject(trigger))
            {
                lua_pushnumber(L, trigger->objectId);
                LOG_INFO("MAP_API", "Created trigger " + name + " (ID: " + std::to_string(trigger->objectId) +
                        ") in map instance " + std::to_string(instanceId));
                return 1;
            }
        }
    }

    lua_pushnumber(L, 0);
    return 1;
}

int SetTriggerCondition(lua_State* L)
{
    if (lua_isnumber(L, 1) && lua_isnumber(L, 2) && lua_isstring(L, 3))
    {
        uint32_t instanceId = static_cast<uint32_t>(lua_tonumber(L, 1));
        uint32_t triggerId = static_cast<uint32_t>(lua_tonumber(L, 2));
        std::string condition = lua_tostring(L, 3);

        auto instance = GET_MAP_INSTANCE(instanceId);
        if (instance)
        {
            auto it = instance->triggers.find(triggerId);
            if (it != instance->triggers.end())
            {
                it->second->condition = condition;
                lua_pushboolean(L, 1);
                LOG_DEBUG("MAP_API", "Set trigger " + std::to_string(triggerId) + " condition: " + condition);
                return 1;
            }
        }
    }

    lua_pushboolean(L, 0);
    return 1;
}

int SetTriggerEffect(lua_State* L)
{
    if (lua_isnumber(L, 1) && lua_isnumber(L, 2) && lua_isstring(L, 3))
    {
        uint32_t instanceId = static_cast<uint32_t>(lua_tonumber(L, 1));
        uint32_t triggerId = static_cast<uint32_t>(lua_tonumber(L, 2));
        std::string effect = lua_tostring(L, 3);

        auto instance = GET_MAP_INSTANCE(instanceId);
        if (instance)
        {
            auto it = instance->triggers.find(triggerId);
            if (it != instance->triggers.end())
            {
                it->second->effectScript = effect;
                lua_pushboolean(L, 1);
                LOG_DEBUG("MAP_API", "Set trigger " + std::to_string(triggerId) + " effect: " + effect);
                return 1;
            }
        }
    }

    lua_pushboolean(L, 0);
    return 1;
}

int ActivateTrigger(lua_State* L)
{
    if (lua_isnumber(L, 1) && lua_isnumber(L, 2))
    {
        uint32_t instanceId = static_cast<uint32_t>(lua_tonumber(L, 1));
        uint32_t triggerId = static_cast<uint32_t>(lua_tonumber(L, 2));

        auto instance = GET_MAP_INSTANCE(instanceId);
        if (instance)
        {
            auto it = instance->triggers.find(triggerId);
            if (it != instance->triggers.end())
            {
                it->second->Activate();
                lua_pushboolean(L, 1);
                LOG_DEBUG("MAP_API", "Activated trigger " + std::to_string(triggerId));
                return 1;
            }
        }
    }

    lua_pushboolean(L, 0);
    return 1;
}

int DeactivateTrigger(lua_State* L)
{
    if (lua_isnumber(L, 1) && lua_isnumber(L, 2))
    {
        uint32_t instanceId = static_cast<uint32_t>(lua_tonumber(L, 1));
        uint32_t triggerId = static_cast<uint32_t>(lua_tonumber(L, 2));

        auto instance = GET_MAP_INSTANCE(instanceId);
        if (instance)
        {
            auto it = instance->triggers.find(triggerId);
            if (it != instance->triggers.end())
            {
                it->second->Deactivate();
                lua_pushboolean(L, 1);
                LOG_DEBUG("MAP_API", "Deactivated trigger " + std::to_string(triggerId));
                return 1;
            }
        }
    }

    lua_pushboolean(L, 0);
    return 1;
}

// 传送门API实现
int CreatePortal(lua_State* L)
{
    if (lua_isnumber(L, 1) && lua_isstring(L, 2) && lua_isnumber(L, 3) &&
        lua_isnumber(L, 4) && lua_isnumber(L, 5) && lua_isnumber(L, 6) && lua_isnumber(L, 7))
    {
        uint32_t instanceId = static_cast<uint32_t>(lua_tonumber(L, 1));
        std::string name = lua_tostring(L, 2);
        int32_t x = static_cast<int32_t>(lua_tonumber(L, 3));
        int32_t y = static_cast<int32_t>(lua_tonumber(L, 4));
        uint32_t targetMapId = static_cast<uint32_t>(lua_tonumber(L, 5));
        int32_t targetX = static_cast<int32_t>(lua_tonumber(L, 6));
        int32_t targetY = static_cast<int32_t>(lua_tonumber(L, 7));

        auto instance = GET_MAP_INSTANCE(instanceId);
        if (instance)
        {
            auto portal = std::make_shared<PortalObject>(MAP_MANAGER().m_nextObjectId++, name,
                                                        targetMapId, targetX, targetY);
            portal->SetPosition(instance->mapId, x, y);

            if (instance->AddSceneObject(portal))
            {
                lua_pushnumber(L, portal->objectId);
                LOG_INFO("MAP_API", "Created portal " + name + " (ID: " + std::to_string(portal->objectId) +
                        ") in map instance " + std::to_string(instanceId));
                return 1;
            }
        }
    }

    lua_pushnumber(L, 0);
    return 1;
}

int SetPortalTarget(lua_State* L)
{
    if (lua_isnumber(L, 1) && lua_isnumber(L, 2) && lua_isnumber(L, 3) &&
        lua_isnumber(L, 4) && lua_isnumber(L, 5))
    {
        uint32_t instanceId = static_cast<uint32_t>(lua_tonumber(L, 1));
        uint32_t portalId = static_cast<uint32_t>(lua_tonumber(L, 2));
        uint32_t targetMapId = static_cast<uint32_t>(lua_tonumber(L, 3));
        int32_t targetX = static_cast<int32_t>(lua_tonumber(L, 4));
        int32_t targetY = static_cast<int32_t>(lua_tonumber(L, 5));

        auto instance = GET_MAP_INSTANCE(instanceId);
        if (instance)
        {
            auto it = instance->portals.find(portalId);
            if (it != instance->portals.end())
            {
                it->second->targetMapId = targetMapId;
                it->second->targetX = targetX;
                it->second->targetY = targetY;
                lua_pushboolean(L, 1);

                LOG_DEBUG("MAP_API", "Set portal " + std::to_string(portalId) + " target to map " +
                         std::to_string(targetMapId) + " (" + std::to_string(targetX) + ", " + std::to_string(targetY) + ")");
                return 1;
            }
        }
    }

    lua_pushboolean(L, 0);
    return 1;
}

int SetPortalRequirement(lua_State* L)
{
    if (lua_isnumber(L, 1) && lua_isnumber(L, 2) && lua_isnumber(L, 3))
    {
        uint32_t instanceId = static_cast<uint32_t>(lua_tonumber(L, 1));
        uint32_t portalId = static_cast<uint32_t>(lua_tonumber(L, 2));
        uint32_t requiredLevel = static_cast<uint32_t>(lua_tonumber(L, 3));
        uint32_t cost = lua_isnumber(L, 4) ? static_cast<uint32_t>(lua_tonumber(L, 4)) : 0;

        auto instance = GET_MAP_INSTANCE(instanceId);
        if (instance)
        {
            auto it = instance->portals.find(portalId);
            if (it != instance->portals.end())
            {
                it->second->requiredLevel = requiredLevel;
                it->second->cost = cost;
                lua_pushboolean(L, 1);

                LOG_DEBUG("MAP_API", "Set portal " + std::to_string(portalId) +
                         " requirements: level " + std::to_string(requiredLevel) + ", cost " + std::to_string(cost));
                return 1;
            }
        }
    }

    lua_pushboolean(L, 0);
    return 1;
}

int UsePortal(lua_State* L)
{
    if (lua_isnumber(L, 1) && lua_isnumber(L, 2) && lua_isnumber(L, 3))
    {
        uint32_t instanceId = static_cast<uint32_t>(lua_tonumber(L, 1));
        uint32_t portalId = static_cast<uint32_t>(lua_tonumber(L, 2));
        uint32_t playerId = static_cast<uint32_t>(lua_tonumber(L, 3));

        auto instance = GET_MAP_INSTANCE(instanceId);
        if (instance)
        {
            auto it = instance->portals.find(portalId);
            if (it != instance->portals.end())
            {
                auto portal = it->second;

                // 这里应该获取玩家对象进行检查和传送
                // 暂时简化处理
                LOG_INFO("MAP_API", "Player " + std::to_string(playerId) + " used portal " + portal->name +
                        " to map " + std::to_string(portal->targetMapId));

                // 触发传送
                bool success = PLAYER_ENTER_MAP(playerId, portal->targetMapId, portal->targetX, portal->targetY);
                lua_pushboolean(L, success ? 1 : 0);
                return 1;
            }
        }
    }

    lua_pushboolean(L, 0);
    return 1;
}

// 资源点API实现
int CreateResource(lua_State* L)
{
    if (lua_isnumber(L, 1) && lua_isstring(L, 2) && lua_isstring(L, 3) &&
        lua_isnumber(L, 4) && lua_isnumber(L, 5))
    {
        uint32_t instanceId = static_cast<uint32_t>(lua_tonumber(L, 1));
        std::string name = lua_tostring(L, 2);
        std::string resourceType = lua_tostring(L, 3);
        int32_t x = static_cast<int32_t>(lua_tonumber(L, 4));
        int32_t y = static_cast<int32_t>(lua_tonumber(L, 5));

        auto instance = GET_MAP_INSTANCE(instanceId);
        if (instance)
        {
            auto resource = std::make_shared<ResourceObject>(MAP_MANAGER().m_nextObjectId++, name, resourceType);
            resource->SetPosition(instance->mapId, x, y);

            if (instance->AddSceneObject(resource))
            {
                lua_pushnumber(L, resource->objectId);
                LOG_INFO("MAP_API", "Created resource " + name + " (ID: " + std::to_string(resource->objectId) +
                        ") in map instance " + std::to_string(instanceId));
                return 1;
            }
        }
    }

    lua_pushnumber(L, 0);
    return 1;
}

int SetResourceType(lua_State* L)
{
    if (lua_isnumber(L, 1) && lua_isnumber(L, 2) && lua_isstring(L, 3))
    {
        uint32_t instanceId = static_cast<uint32_t>(lua_tonumber(L, 1));
        uint32_t resourceId = static_cast<uint32_t>(lua_tonumber(L, 2));
        std::string resourceType = lua_tostring(L, 3);

        auto instance = GET_MAP_INSTANCE(instanceId);
        if (instance)
        {
            auto it = instance->resources.find(resourceId);
            if (it != instance->resources.end())
            {
                it->second->resourceType = resourceType;
                lua_pushboolean(L, 1);
                LOG_DEBUG("MAP_API", "Set resource " + std::to_string(resourceId) + " type to " + resourceType);
                return 1;
            }
        }
    }

    lua_pushboolean(L, 0);
    return 1;
}

int SetResourceDrops(lua_State* L)
{
    if (lua_isnumber(L, 1) && lua_isnumber(L, 2) && lua_istable(L, 3))
    {
        uint32_t instanceId = static_cast<uint32_t>(lua_tonumber(L, 1));
        uint32_t resourceId = static_cast<uint32_t>(lua_tonumber(L, 2));

        auto instance = GET_MAP_INSTANCE(instanceId);
        if (instance)
        {
            auto it = instance->resources.find(resourceId);
            if (it != instance->resources.end())
            {
                auto resource = it->second;
                resource->dropItems.clear();

                // 遍历Lua表获取掉落物品
                lua_pushnil(L);
                while (lua_next(L, 3) != 0)
                {
                    if (lua_isnumber(L, -2) && lua_isnumber(L, -1))
                    {
                        uint32_t itemId = static_cast<uint32_t>(lua_tonumber(L, -2));
                        uint32_t count = static_cast<uint32_t>(lua_tonumber(L, -1));
                        resource->dropItems.emplace_back(itemId, count);
                    }
                    lua_pop(L, 1);
                }

                lua_pushboolean(L, 1);
                LOG_DEBUG("MAP_API", "Set resource " + std::to_string(resourceId) +
                         " drops: " + std::to_string(resource->dropItems.size()) + " items");
                return 1;
            }
        }
    }

    lua_pushboolean(L, 0);
    return 1;
}

int HarvestResource(lua_State* L)
{
    if (lua_isnumber(L, 1) && lua_isnumber(L, 2) && lua_isnumber(L, 3))
    {
        uint32_t instanceId = static_cast<uint32_t>(lua_tonumber(L, 1));
        uint32_t resourceId = static_cast<uint32_t>(lua_tonumber(L, 2));
        uint32_t playerId = static_cast<uint32_t>(lua_tonumber(L, 3));

        auto instance = GET_MAP_INSTANCE(instanceId);
        if (instance)
        {
            auto it = instance->resources.find(resourceId);
            if (it != instance->resources.end())
            {
                // 这里应该获取玩家对象进行采集
                // 暂时简化处理
                auto resource = it->second;

                // 模拟采集结果
                lua_newtable(L);
                int index = 1;
                for (const auto& [itemId, count] : resource->dropItems)
                {
                    lua_pushnumber(L, index++);
                    lua_newtable(L);

                    lua_pushstring(L, "itemId");
                    lua_pushnumber(L, itemId);
                    lua_settable(L, -3);

                    lua_pushstring(L, "count");
                    lua_pushnumber(L, count);
                    lua_settable(L, -3);

                    lua_settable(L, -3);
                }

                LOG_INFO("MAP_API", "Player " + std::to_string(playerId) +
                        " harvested resource " + resource->name);
                return 1;
            }
        }
    }

    lua_newtable(L);
    return 1;
}

int GetResourceStatus(lua_State* L)
{
    if (lua_isnumber(L, 1) && lua_isnumber(L, 2))
    {
        uint32_t instanceId = static_cast<uint32_t>(lua_tonumber(L, 1));
        uint32_t resourceId = static_cast<uint32_t>(lua_tonumber(L, 2));

        auto instance = GET_MAP_INSTANCE(instanceId);
        if (instance)
        {
            auto it = instance->resources.find(resourceId);
            if (it != instance->resources.end())
            {
                auto resource = it->second;

                // 创建Lua表返回资源状态
                lua_newtable(L);

                lua_pushstring(L, "resourceType");
                lua_pushstring(L, resource->resourceType.c_str());
                lua_settable(L, -3);

                lua_pushstring(L, "currentQuantity");
                lua_pushnumber(L, resource->currentQuantity);
                lua_settable(L, -3);

                lua_pushstring(L, "maxQuantity");
                lua_pushnumber(L, resource->maxQuantity);
                lua_settable(L, -3);

                lua_pushstring(L, "status");
                lua_pushnumber(L, static_cast<int>(resource->status));
                lua_settable(L, -3);

                return 1;
            }
        }
    }

    lua_pushnil(L);
    return 1;
}

// 地图事件API实现
int RegisterMapEventHandler(lua_State* L)
{
    if (lua_isnumber(L, 1) && lua_isstring(L, 2))
    {
        int eventType = static_cast<int>(lua_tonumber(L, 1));
        std::string functionName = lua_tostring(L, 2);
        std::string condition = lua_isstring(L, 3) ? lua_tostring(L, 3) : "";
        uint32_t priority = lua_isnumber(L, 4) ? static_cast<uint32_t>(lua_tonumber(L, 4)) : 0;

        bool success = REGISTER_MAP_EVENT_HANDLER(static_cast<MapEvent>(eventType), functionName, condition, priority);
        lua_pushboolean(L, success ? 1 : 0);

        if (success)
        {
            LOG_INFO("MAP_API", "Registered map event handler: " + functionName + " for event " + std::to_string(eventType));
        }
    }
    else
    {
        lua_pushboolean(L, 0);
    }
    return 1;
}

int UnregisterMapEventHandler(lua_State* L)
{
    if (lua_isnumber(L, 1) && lua_isstring(L, 2))
    {
        int eventType = static_cast<int>(lua_tonumber(L, 1));
        std::string functionName = lua_tostring(L, 2);

        bool success = MAP_SCRIPT_INTEGRATION().UnregisterEventHandler(static_cast<MapEvent>(eventType), functionName);
        lua_pushboolean(L, success ? 1 : 0);

        if (success)
        {
            LOG_INFO("MAP_API", "Unregistered map event handler: " + functionName + " for event " + std::to_string(eventType));
        }
    }
    else
    {
        lua_pushboolean(L, 0);
    }
    return 1;
}

int TriggerMapEvent(lua_State* L)
{
    if (lua_isnumber(L, 1) && lua_isnumber(L, 2))
    {
        int eventType = static_cast<int>(lua_tonumber(L, 1));
        uint32_t instanceId = static_cast<uint32_t>(lua_tonumber(L, 2));
        uint32_t playerId = lua_isnumber(L, 3) ? static_cast<uint32_t>(lua_tonumber(L, 3)) : 0;
        uint32_t objectId = lua_isnumber(L, 4) ? static_cast<uint32_t>(lua_tonumber(L, 4)) : 0;

        TRIGGER_MAP_EVENT(static_cast<MapEvent>(eventType), instanceId, playerId, objectId);
        lua_pushboolean(L, 1);

        LOG_DEBUG("MAP_API", "Triggered map event " + std::to_string(eventType) +
                 " in instance " + std::to_string(instanceId));
    }
    else
    {
        lua_pushboolean(L, 0);
    }
    return 1;
}

// 碰撞检测API实现
int CheckCollision(lua_State* L)
{
    if (lua_isnumber(L, 1) && lua_isnumber(L, 2) && lua_isnumber(L, 3))
    {
        uint32_t instanceId = static_cast<uint32_t>(lua_tonumber(L, 1));
        int32_t x = static_cast<int32_t>(lua_tonumber(L, 2));
        int32_t y = static_cast<int32_t>(lua_tonumber(L, 3));
        int32_t width = lua_isnumber(L, 4) ? static_cast<int32_t>(lua_tonumber(L, 4)) : 32;
        int32_t height = lua_isnumber(L, 5) ? static_cast<int32_t>(lua_tonumber(L, 5)) : 32;

        auto instance = GET_MAP_INSTANCE(instanceId);
        if (instance)
        {
            bool hasCollision = instance->CheckCollision(x, y, width, height);
            lua_pushboolean(L, hasCollision ? 1 : 0);
            return 1;
        }
    }

    lua_pushboolean(L, 0);
    return 1;
}

int GetCollisionObjects(lua_State* L)
{
    if (lua_isnumber(L, 1) && lua_isnumber(L, 2) && lua_isnumber(L, 3))
    {
        uint32_t instanceId = static_cast<uint32_t>(lua_tonumber(L, 1));
        int32_t x = static_cast<int32_t>(lua_tonumber(L, 2));
        int32_t y = static_cast<int32_t>(lua_tonumber(L, 3));
        int32_t width = lua_isnumber(L, 4) ? static_cast<int32_t>(lua_tonumber(L, 4)) : 32;
        int32_t height = lua_isnumber(L, 5) ? static_cast<int32_t>(lua_tonumber(L, 5)) : 32;

        auto instance = GET_MAP_INSTANCE(instanceId);
        if (instance)
        {
            // 查找碰撞的对象
            lua_newtable(L);
            int index = 1;

            for (const auto& [objectId, object] : instance->sceneObjects)
            {
                if (object && object->CheckCollision(x, y, width, height))
                {
                    lua_pushnumber(L, index++);
                    lua_pushnumber(L, objectId);
                    lua_settable(L, -3);
                }
            }

            return 1;
        }
    }

    lua_newtable(L);
    return 1;
}

int SetObjectCollidable(lua_State* L)
{
    if (lua_isnumber(L, 1) && lua_isnumber(L, 2) && lua_isboolean(L, 3))
    {
        uint32_t instanceId = static_cast<uint32_t>(lua_tonumber(L, 1));
        uint32_t objectId = static_cast<uint32_t>(lua_tonumber(L, 2));
        bool collidable = lua_toboolean(L, 3) != 0;

        auto instance = GET_MAP_INSTANCE(instanceId);
        if (instance)
        {
            auto object = instance->GetSceneObject(objectId);
            if (object)
            {
                object->isCollidable = collidable;
                lua_pushboolean(L, 1);
                LOG_DEBUG("MAP_API", "Set object " + std::to_string(objectId) +
                         " collidable: " + (collidable ? "true" : "false"));
                return 1;
            }
        }
    }

    lua_pushboolean(L, 0);
    return 1;
}

// 地图效果API实现
int PlayMapEffect(lua_State* L)
{
    if (lua_isstring(L, 1) && lua_isnumber(L, 2) && lua_isnumber(L, 3) && lua_isnumber(L, 4))
    {
        std::string effectType = lua_tostring(L, 1);
        uint32_t instanceId = static_cast<uint32_t>(lua_tonumber(L, 2));
        int32_t x = static_cast<int32_t>(lua_tonumber(L, 3));
        int32_t y = static_cast<int32_t>(lua_tonumber(L, 4));
        uint32_t duration = lua_isnumber(L, 5) ? static_cast<uint32_t>(lua_tonumber(L, 5)) : 0;

        uint32_t effectId = PLAY_MAP_EFFECT(effectType, instanceId, x, y, duration);
        lua_pushnumber(L, effectId);

        if (effectId != 0)
        {
            LOG_INFO("MAP_API", "Playing map effect " + effectType + " at (" +
                    std::to_string(x) + ", " + std::to_string(y) + ") in instance " + std::to_string(instanceId));
        }
    }
    else
    {
        lua_pushnumber(L, 0);
    }
    return 1;
}

int ShowMapMessage(lua_State* L)
{
    if (lua_isnumber(L, 1) && lua_isstring(L, 2))
    {
        uint32_t instanceId = static_cast<uint32_t>(lua_tonumber(L, 1));
        std::string message = lua_tostring(L, 2);
        uint32_t duration = lua_isnumber(L, 3) ? static_cast<uint32_t>(lua_tonumber(L, 3)) : 5000;

        // 这里应该实现实际的地图消息显示逻辑
        LOG_INFO("MAP_API", "Map message in instance " + std::to_string(instanceId) + ": " + message);
        lua_pushboolean(L, 1);
    }
    else
    {
        lua_pushboolean(L, 0);
    }
    return 1;
}

int SetMapWeather(lua_State* L)
{
    if (lua_isnumber(L, 1) && lua_isstring(L, 2))
    {
        uint32_t instanceId = static_cast<uint32_t>(lua_tonumber(L, 1));
        std::string weather = lua_tostring(L, 2);

        // 这里应该实现实际的天气设置逻辑
        LOG_INFO("MAP_API", "Set weather in instance " + std::to_string(instanceId) + " to " + weather);
        lua_pushboolean(L, 1);
    }
    else
    {
        lua_pushboolean(L, 0);
    }
    return 1;
}

int SetMapTime(lua_State* L)
{
    if (lua_isnumber(L, 1) && lua_isnumber(L, 2))
    {
        uint32_t instanceId = static_cast<uint32_t>(lua_tonumber(L, 1));
        uint32_t timeOfDay = static_cast<uint32_t>(lua_tonumber(L, 2));

        // 这里应该实现实际的时间设置逻辑
        LOG_INFO("MAP_API", "Set time in instance " + std::to_string(instanceId) + " to " + std::to_string(timeOfDay));
        lua_pushboolean(L, 1);
    }
    else
    {
        lua_pushboolean(L, 0);
    }
    return 1;
}

} // namespace MapScriptAPI
} // namespace sword2
