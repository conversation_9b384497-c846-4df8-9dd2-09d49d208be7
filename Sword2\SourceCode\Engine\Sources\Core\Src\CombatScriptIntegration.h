//---------------------------------------------------------------------------
// Sword2 Combat Script Integration (c) 2024
//
// File:	CombatScriptIntegration.h
// Date:	2024.07.02
// Code:	AI Assistant
// Desc:	Integration layer between combat system and Lua scripts
//---------------------------------------------------------------------------
#ifndef COMBAT_SCRIPT_INTEGRATION_H
#define COMBAT_SCRIPT_INTEGRATION_H

#include "CombatManager.h"
#include "LuaScriptEngine.h"
#include "PlayerManager.h"
#include "NPCManager.h"
#include "SkillManager.h"
#include "UnifiedLoggingSystem.h"
#include <string>
#include <unordered_map>
#include <vector>
#include <functional>

namespace sword2 {

// 战斗脚本API扩展
namespace CombatScriptAPI {
    
    // 战斗基础API
    int GetCombatState(lua_State* L);
    int SetCombatState(lua_State* L);
    int IsInCombat(lua_State* L);
    int GetCombatDuration(lua_State* L);
    int GetCombatTarget(lua_State* L);
    int SetCombatTarget(lua_State* L);
    
    // 攻击API
    int StartAttack(lua_State* L);
    int PerformAttack(lua_State* L);
    int PerformSkillAttack(lua_State* L);
    int CalculateHit(lua_State* L);
    int CalculateCritical(lua_State* L);
    int CalculateDamage(lua_State* L);
    
    // 伤害和治疗API
    int ApplyDamage(lua_State* L);
    int ApplyHeal(lua_State* L);
    int GetDamageReduction(lua_State* L);
    int GetHealBonus(lua_State* L);
    int IsImmune(lua_State* L);
    
    // 状态效果API
    int ApplyStateEffect(lua_State* L);
    int RemoveStateEffect(lua_State* L);
    int HasEffectType(lua_State* L);
    int GetTargetEffects(lua_State* L);
    int ClearAllEffects(lua_State* L);
    int DispelEffects(lua_State* L);
    int GetEffectValue(lua_State* L);
    int GetEffectDuration(lua_State* L);
    
    // 战斗统计API
    int GetDamageDealt(lua_State* L);
    int GetDamageTaken(lua_State* L);
    int GetHealingDone(lua_State* L);
    int GetKillCount(lua_State* L);
    int GetDeathCount(lua_State* L);
    int GetDPS(lua_State* L);
    int GetHPS(lua_State* L);
    
    // 距离和范围API
    int GetDistance(lua_State* L);
    int IsInRange(lua_State* L);
    int IsInAttackRange(lua_State* L);
    int GetAttackRange(lua_State* L);
    int GetSkillRange(lua_State* L);
    
    // 属性计算API
    int GetTotalAttack(lua_State* L);
    int GetTotalDefense(lua_State* L);
    int GetCriticalRate(lua_State* L);
    int GetDodgeRate(lua_State* L);
    int GetHitRate(lua_State* L);
    int GetResistance(lua_State* L);
    
    // 战斗事件API
    int RegisterCombatEventHandler(lua_State* L);
    int UnregisterCombatEventHandler(lua_State* L);
    int TriggerCombatEvent(lua_State* L);
    int OnAttackHit(lua_State* L);
    int OnTakeDamage(lua_State* L);
    int OnDeath(lua_State* L);
    
    // PvP相关API
    int CanAttackPlayer(lua_State* L);
    int GetPKMode(lua_State* L);
    int SetPKMode(lua_State* L);
    int GetPKValue(lua_State* L);
    int AddPKValue(lua_State* L);
    
    // 团队战斗API
    int IsTeamMember(lua_State* L);
    int GetTeamMembers(lua_State* L);
    int GetTeamDamage(lua_State* L);
    int GetTeamHealing(lua_State* L);
    int ShareDamage(lua_State* L);
}

// 战斗事件处理器
class CombatEventHandler
{
public:
    struct CombatEventData
    {
        CombatEvent event;
        uint32_t entityId;
        uint32_t otherId;
        uint32_t skillId;
        uint32_t damage;
        uint32_t heal;
        std::chrono::system_clock::time_point timestamp;
        
        CombatEventData() = default;
        CombatEventData(CombatEvent evt, uint32_t entity, uint32_t other = 0)
            : event(evt), entityId(entity), otherId(other), skillId(0), damage(0), heal(0)
        {
            timestamp = std::chrono::system_clock::now();
        }
    };
    
    using EventCallback = std::function<void(const CombatEventData&)>;
    
    // 注册事件处理器
    void RegisterEventHandler(CombatEvent event, const std::string& handlerName, EventCallback callback)
    {
        std::lock_guard<std::mutex> lock(m_handlerMutex);
        m_eventHandlers[event][handlerName] = callback;
        
        LOG_DEBUG("COMBAT_EVENT", "Registered event handler: " + handlerName + " for event " + std::to_string(static_cast<int>(event)));
    }
    
    // 注销事件处理器
    void UnregisterEventHandler(CombatEvent event, const std::string& handlerName)
    {
        std::lock_guard<std::mutex> lock(m_handlerMutex);
        auto it = m_eventHandlers.find(event);
        if (it != m_eventHandlers.end())
        {
            it->second.erase(handlerName);
            LOG_DEBUG("COMBAT_EVENT", "Unregistered event handler: " + handlerName);
        }
    }
    
    // 触发事件
    void TriggerEvent(const CombatEventData& eventData)
    {
        std::lock_guard<std::mutex> lock(m_handlerMutex);
        
        auto it = m_eventHandlers.find(eventData.event);
        if (it != m_eventHandlers.end())
        {
            for (const auto& [handlerName, callback] : it->second)
            {
                try
                {
                    callback(eventData);
                }
                catch (const std::exception& e)
                {
                    LOG_ERROR("COMBAT_EVENT", "Error in event handler " + handlerName + ": " + e.what());
                }
            }
        }
        
        // 记录事件历史
        m_eventHistory.push_back(eventData);
        if (m_eventHistory.size() > 1000) // 限制历史记录数量
        {
            m_eventHistory.erase(m_eventHistory.begin());
        }
    }
    
    // 获取事件历史
    std::vector<CombatEventData> GetEventHistory(uint32_t entityId = 0, CombatEvent event = CombatEvent::AttackStart) const
    {
        std::lock_guard<std::mutex> lock(m_handlerMutex);
        std::vector<CombatEventData> history;
        
        for (const auto& eventData : m_eventHistory)
        {
            if ((entityId == 0 || eventData.entityId == entityId || eventData.otherId == entityId) &&
                (event == CombatEvent::AttackStart || eventData.event == event))
            {
                history.push_back(eventData);
            }
        }
        
        return history;
    }

private:
    mutable std::mutex m_handlerMutex;
    std::unordered_map<CombatEvent, std::unordered_map<std::string, EventCallback>> m_eventHandlers;
    std::vector<CombatEventData> m_eventHistory;
};

// 战斗AI助手
class CombatAIHelper
{
public:
    // 选择最佳攻击目标
    static uint32_t SelectBestTarget(uint32_t attackerId, const std::vector<uint32_t>& candidates)
    {
        auto attacker = GET_ONLINE_PLAYER(attackerId);
        if (!attacker || candidates.empty())
            return 0;
        
        uint32_t bestTarget = 0;
        float bestScore = -1.0f;
        
        for (uint32_t candidateId : candidates)
        {
            auto candidate = GET_ONLINE_PLAYER(candidateId);
            if (!candidate) continue;
            
            float score = CalculateTargetScore(*attacker, *candidate);
            if (score > bestScore)
            {
                bestScore = score;
                bestTarget = candidateId;
            }
        }
        
        return bestTarget;
    }
    
    // 计算目标评分
    static float CalculateTargetScore(const Player& attacker, const Player& target)
    {
        float score = 0.0f;
        
        // 生命值越低，优先级越高
        float healthRatio = static_cast<float>(target.attributes.currentLife) / target.attributes.maxLife;
        score += (1.0f - healthRatio) * 50.0f;
        
        // 防御力越低，优先级越高
        float defenseRatio = static_cast<float>(target.attributes.defense) / (target.attributes.defense + 100);
        score += (1.0f - defenseRatio) * 30.0f;
        
        // 距离越近，优先级越高
        float distance = CalculateDistance(attacker.position.x, attacker.position.y, 
                                         target.position.x, target.position.y);
        score += std::max(0.0f, 20.0f - distance);
        
        return score;
    }
    
    // 计算距离
    static float CalculateDistance(int32_t x1, int32_t y1, int32_t x2, int32_t y2)
    {
        float dx = static_cast<float>(x2 - x1);
        float dy = static_cast<float>(y2 - y1);
        return std::sqrt(dx * dx + dy * dy);
    }
    
    // 推荐技能使用
    static uint32_t RecommendSkill(uint32_t playerId, uint32_t targetId)
    {
        auto player = GET_ONLINE_PLAYER(playerId);
        auto target = GET_ONLINE_PLAYER(targetId);
        
        if (!player || !target)
            return 0;
        
        // 这里可以实现复杂的技能推荐逻辑
        // 暂时返回一个示例技能ID
        return 1001;
    }
};

// 战斗脚本集成管理器
class CombatScriptIntegration : public Singleton<CombatScriptIntegration>
{
public:
    CombatScriptIntegration() = default;
    ~CombatScriptIntegration() = default;
    
    // 初始化战斗脚本集成
    bool Initialize()
    {
        // 注册战斗相关的Lua API函数
        RegisterCombatScriptAPI();
        
        // 注册默认事件处理器
        RegisterDefaultEventHandlers();
        
        LOG_INFO("COMBAT_SCRIPT", "Combat script integration initialized");
        return true;
    }
    
    // 加载战斗脚本
    bool LoadCombatScript(const std::string& scriptPath)
    {
        ScriptResult result = LOAD_SCRIPT(scriptPath, ScriptType::Combat);
        if (result == ScriptResult::Success)
        {
            LOG_INFO("COMBAT_SCRIPT", "Loaded combat script: " + scriptPath);
            return true;
        }
        else
        {
            LOG_ERROR("COMBAT_SCRIPT", "Failed to load combat script: " + scriptPath);
            return false;
        }
    }
    
    // 执行战斗脚本函数
    bool ExecuteCombatFunction(const std::string& functionName, uint32_t entityId, uint32_t otherId = 0)
    {
        std::vector<LuaValue> args;
        args.push_back(LuaValue(static_cast<double>(entityId)));
        if (otherId != 0)
        {
            args.push_back(LuaValue(static_cast<double>(otherId)));
        }
        
        ScriptResult result = EXECUTE_FUNCTION(functionName, args);
        return result == ScriptResult::Success;
    }
    
    // 触发战斗事件
    void TriggerCombatEvent(CombatEvent event, uint32_t entityId, uint32_t otherId = 0)
    {
        CombatEventHandler::CombatEventData eventData(event, entityId, otherId);
        m_eventHandler.TriggerEvent(eventData);
        
        // 执行脚本事件处理
        std::string eventName = GetEventName(event);
        ExecuteCombatFunction("on_combat_" + eventName, entityId, otherId);
    }
    
    // 计算伤害修正
    uint32_t CalculateDamageModifier(uint32_t baseDamage, uint32_t attackerId, uint32_t targetId, uint32_t skillId = 0)
    {
        // 这里可以调用Lua脚本进行复杂的伤害计算
        std::vector<LuaValue> args = {
            LuaValue(static_cast<double>(baseDamage)),
            LuaValue(static_cast<double>(attackerId)),
            LuaValue(static_cast<double>(targetId)),
            LuaValue(static_cast<double>(skillId))
        };
        
        auto result = EXECUTE_FUNCTION_WITH_RETURN("calculate_damage_modifier", args);
        if (result.has_value() && result->type == LuaValueType::Number)
        {
            return static_cast<uint32_t>(result->numberValue);
        }
        
        return baseDamage;
    }
    
    // 计算治疗修正
    uint32_t CalculateHealModifier(uint32_t baseHeal, uint32_t healerId, uint32_t targetId, uint32_t skillId = 0)
    {
        std::vector<LuaValue> args = {
            LuaValue(static_cast<double>(baseHeal)),
            LuaValue(static_cast<double>(healerId)),
            LuaValue(static_cast<double>(targetId)),
            LuaValue(static_cast<double>(skillId))
        };
        
        auto result = EXECUTE_FUNCTION_WITH_RETURN("calculate_heal_modifier", args);
        if (result.has_value() && result->type == LuaValueType::Number)
        {
            return static_cast<uint32_t>(result->numberValue);
        }
        
        return baseHeal;
    }
    
    // 检查战斗条件
    bool CheckCombatCondition(const std::string& condition, uint32_t entityId, uint32_t otherId = 0)
    {
        std::vector<LuaValue> args = {
            LuaValue(condition),
            LuaValue(static_cast<double>(entityId)),
            LuaValue(static_cast<double>(otherId))
        };
        
        auto result = EXECUTE_FUNCTION_WITH_RETURN("check_combat_condition", args);
        if (result.has_value() && result->type == LuaValueType::Boolean)
        {
            return result->booleanValue;
        }
        
        return false;
    }
    
    // 获取事件处理器
    CombatEventHandler& GetEventHandler() { return m_eventHandler; }

private:
    CombatEventHandler m_eventHandler;
    
    void RegisterCombatScriptAPI()
    {
        // 注册战斗基础API
        REGISTER_LUA_FUNCTION("GetCombatState", CombatScriptAPI::GetCombatState);
        REGISTER_LUA_FUNCTION("SetCombatState", CombatScriptAPI::SetCombatState);
        REGISTER_LUA_FUNCTION("IsInCombat", CombatScriptAPI::IsInCombat);
        REGISTER_LUA_FUNCTION("GetCombatDuration", CombatScriptAPI::GetCombatDuration);
        REGISTER_LUA_FUNCTION("GetCombatTarget", CombatScriptAPI::GetCombatTarget);
        
        // 注册攻击API
        REGISTER_LUA_FUNCTION("StartAttack", CombatScriptAPI::StartAttack);
        REGISTER_LUA_FUNCTION("PerformAttack", CombatScriptAPI::PerformAttack);
        REGISTER_LUA_FUNCTION("PerformSkillAttack", CombatScriptAPI::PerformSkillAttack);
        REGISTER_LUA_FUNCTION("CalculateHit", CombatScriptAPI::CalculateHit);
        REGISTER_LUA_FUNCTION("CalculateCritical", CombatScriptAPI::CalculateCritical);
        REGISTER_LUA_FUNCTION("CalculateDamage", CombatScriptAPI::CalculateDamage);
        
        // 注册伤害和治疗API
        REGISTER_LUA_FUNCTION("ApplyDamage", CombatScriptAPI::ApplyDamage);
        REGISTER_LUA_FUNCTION("ApplyHeal", CombatScriptAPI::ApplyHeal);
        REGISTER_LUA_FUNCTION("GetDamageReduction", CombatScriptAPI::GetDamageReduction);
        REGISTER_LUA_FUNCTION("IsImmune", CombatScriptAPI::IsImmune);
        
        // 注册状态效果API
        REGISTER_LUA_FUNCTION("ApplyStateEffect", CombatScriptAPI::ApplyStateEffect);
        REGISTER_LUA_FUNCTION("RemoveStateEffect", CombatScriptAPI::RemoveStateEffect);
        REGISTER_LUA_FUNCTION("HasEffectType", CombatScriptAPI::HasEffectType);
        REGISTER_LUA_FUNCTION("GetTargetEffects", CombatScriptAPI::GetTargetEffects);
        REGISTER_LUA_FUNCTION("ClearAllEffects", CombatScriptAPI::ClearAllEffects);
        REGISTER_LUA_FUNCTION("DispelEffects", CombatScriptAPI::DispelEffects);
        
        // 注册战斗统计API
        REGISTER_LUA_FUNCTION("GetDamageDealt", CombatScriptAPI::GetDamageDealt);
        REGISTER_LUA_FUNCTION("GetDamageTaken", CombatScriptAPI::GetDamageTaken);
        REGISTER_LUA_FUNCTION("GetHealingDone", CombatScriptAPI::GetHealingDone);
        REGISTER_LUA_FUNCTION("GetDPS", CombatScriptAPI::GetDPS);
        REGISTER_LUA_FUNCTION("GetHPS", CombatScriptAPI::GetHPS);
        
        // 注册距离和范围API
        REGISTER_LUA_FUNCTION("GetDistance", CombatScriptAPI::GetDistance);
        REGISTER_LUA_FUNCTION("IsInRange", CombatScriptAPI::IsInRange);
        REGISTER_LUA_FUNCTION("IsInAttackRange", CombatScriptAPI::IsInAttackRange);
        REGISTER_LUA_FUNCTION("GetAttackRange", CombatScriptAPI::GetAttackRange);
        
        // 注册属性计算API
        REGISTER_LUA_FUNCTION("GetTotalAttack", CombatScriptAPI::GetTotalAttack);
        REGISTER_LUA_FUNCTION("GetTotalDefense", CombatScriptAPI::GetTotalDefense);
        REGISTER_LUA_FUNCTION("GetCriticalRate", CombatScriptAPI::GetCriticalRate);
        REGISTER_LUA_FUNCTION("GetDodgeRate", CombatScriptAPI::GetDodgeRate);
        
        // 注册战斗事件API
        REGISTER_LUA_FUNCTION("RegisterCombatEventHandler", CombatScriptAPI::RegisterCombatEventHandler);
        REGISTER_LUA_FUNCTION("TriggerCombatEvent", CombatScriptAPI::TriggerCombatEvent);
        REGISTER_LUA_FUNCTION("OnAttackHit", CombatScriptAPI::OnAttackHit);
        REGISTER_LUA_FUNCTION("OnTakeDamage", CombatScriptAPI::OnTakeDamage);
        
        // 注册PvP相关API
        REGISTER_LUA_FUNCTION("CanAttackPlayer", CombatScriptAPI::CanAttackPlayer);
        REGISTER_LUA_FUNCTION("GetPKMode", CombatScriptAPI::GetPKMode);
        REGISTER_LUA_FUNCTION("SetPKMode", CombatScriptAPI::SetPKMode);
        
        // 注册团队战斗API
        REGISTER_LUA_FUNCTION("IsTeamMember", CombatScriptAPI::IsTeamMember);
        REGISTER_LUA_FUNCTION("GetTeamMembers", CombatScriptAPI::GetTeamMembers);
        REGISTER_LUA_FUNCTION("GetTeamDamage", CombatScriptAPI::GetTeamDamage);
        
        LOG_INFO("COMBAT_SCRIPT", "Registered combat script API functions");
    }
    
    void RegisterDefaultEventHandlers()
    {
        // 注册默认的战斗事件处理器
        m_eventHandler.RegisterEventHandler(CombatEvent::AttackHit, "default_attack_hit", 
            [](const CombatEventHandler::CombatEventData& data) {
                LOG_DEBUG("COMBAT_EVENT", "Attack hit: " + std::to_string(data.entityId) + " -> " + std::to_string(data.otherId));
            });
        
        m_eventHandler.RegisterEventHandler(CombatEvent::TakeDamage, "default_take_damage", 
            [](const CombatEventHandler::CombatEventData& data) {
                LOG_DEBUG("COMBAT_EVENT", "Take damage: " + std::to_string(data.entityId) + " damage: " + std::to_string(data.damage));
            });
        
        m_eventHandler.RegisterEventHandler(CombatEvent::Death, "default_death", 
            [](const CombatEventHandler::CombatEventData& data) {
                LOG_INFO("COMBAT_EVENT", "Death: " + std::to_string(data.entityId) + " killed by: " + std::to_string(data.otherId));
            });
        
        m_eventHandler.RegisterEventHandler(CombatEvent::CombatStart, "default_combat_start", 
            [](const CombatEventHandler::CombatEventData& data) {
                LOG_INFO("COMBAT_EVENT", "Combat started: " + std::to_string(data.entityId) + " vs " + std::to_string(data.otherId));
            });
        
        m_eventHandler.RegisterEventHandler(CombatEvent::CombatEnd, "default_combat_end", 
            [](const CombatEventHandler::CombatEventData& data) {
                LOG_INFO("COMBAT_EVENT", "Combat ended: " + std::to_string(data.entityId));
            });
    }
    
    std::string GetEventName(CombatEvent event)
    {
        switch (event)
        {
        case CombatEvent::AttackStart: return "attack_start";
        case CombatEvent::AttackHit: return "attack_hit";
        case CombatEvent::AttackMiss: return "attack_miss";
        case CombatEvent::TakeDamage: return "take_damage";
        case CombatEvent::Heal: return "heal";
        case CombatEvent::Death: return "death";
        case CombatEvent::Revive: return "revive";
        case CombatEvent::StateApply: return "state_apply";
        case CombatEvent::StateRemove: return "state_remove";
        case CombatEvent::CombatStart: return "combat_start";
        case CombatEvent::CombatEnd: return "combat_end";
        default: return "unknown";
        }
    }
};

} // namespace sword2

// 全局战斗脚本集成访问
#define COMBAT_SCRIPT_INTEGRATION() sword2::CombatScriptIntegration::getInstance()

// 便捷宏定义
#define INIT_COMBAT_SCRIPT_INTEGRATION() COMBAT_SCRIPT_INTEGRATION().Initialize()
#define LOAD_COMBAT_SCRIPT(scriptPath) COMBAT_SCRIPT_INTEGRATION().LoadCombatScript(scriptPath)
#define EXECUTE_COMBAT_FUNCTION(functionName, entityId, otherId) COMBAT_SCRIPT_INTEGRATION().ExecuteCombatFunction(functionName, entityId, otherId)

#define TRIGGER_COMBAT_EVENT(event, entityId, otherId) COMBAT_SCRIPT_INTEGRATION().TriggerCombatEvent(event, entityId, otherId)
#define CALCULATE_DAMAGE_MODIFIER(baseDamage, attackerId, targetId, skillId) COMBAT_SCRIPT_INTEGRATION().CalculateDamageModifier(baseDamage, attackerId, targetId, skillId)
#define CALCULATE_HEAL_MODIFIER(baseHeal, healerId, targetId, skillId) COMBAT_SCRIPT_INTEGRATION().CalculateHealModifier(baseHeal, healerId, targetId, skillId)
#define CHECK_COMBAT_CONDITION(condition, entityId, otherId) COMBAT_SCRIPT_INTEGRATION().CheckCombatCondition(condition, entityId, otherId)

#define COMBAT_EVENT_HANDLER() COMBAT_SCRIPT_INTEGRATION().GetEventHandler()

#endif // COMBAT_SCRIPT_INTEGRATION_H
