//Microsoft Developer Studio generated resource script.
//
#include "resource.h"

#define APSTUDIO_READONLY_SYMBOLS
/////////////////////////////////////////////////////////////////////////////
//
// Generated from the TEXTINCLUDE 2 resource.
//
#include "windows.h"

/////////////////////////////////////////////////////////////////////////////
#undef APSTUDIO_READONLY_SYMBOLS

/////////////////////////////////////////////////////////////////////////////
// Chinese (P.R.C.) resources

#if !defined(AFX_RESOURCE_DLL) || defined(AFX_TARG_CHS)
#ifdef _WIN32
LANGUAGE LANG_CHINESE, SUBLANG_CHINESE_SIMPLIFIED
#pragma code_page(936)
#endif //_WIN32

#ifdef APSTUDIO_INVOKED
/////////////////////////////////////////////////////////////////////////////
//
// TEXTINCLUDE
//

1 TEXTINCLUDE DISCARDABLE 
BEGIN
    "resource.h\0"
END

2 TEXTINCLUDE DISCARDABLE 
BEGIN
    "#include ""windows.h""\r\n"
    "\0"
END

3 TEXTINCLUDE DISCARDABLE 
BEGIN
    "\r\n"
    "\0"
END

#endif    // APSTUDIO_INVOKED


#ifndef _MAC
/////////////////////////////////////////////////////////////////////////////
//
// Version
//

VS_VERSION_INFO VERSIONINFO
 FILEVERSION 1,0,0,1
 PRODUCTVERSION 1,0,0,1
 FILEFLAGSMASK 0x3fL
#ifdef _DEBUG
 FILEFLAGS 0x1L
#else
 FILEFLAGS 0x0L
#endif
 FILEOS 0x40004L
 FILETYPE 0x1L
 FILESUBTYPE 0x0L
BEGIN
    BLOCK "StringFileInfo"
    BEGIN
        BLOCK "080404b0"
        BEGIN
            VALUE "Comments", "\0"
            VALUE "CompanyName", "kingsoft\0"
            VALUE "FileDescription", "Bishop\0"
            VALUE "FileVersion", "1, 0, 0, 1\0"
            VALUE "InternalName", "Bishop\0"
            VALUE "LegalCopyright", "Copyright ? 2003\0"
            VALUE "LegalTrademarks", "\0"
            VALUE "OriginalFilename", "Bishop.exe\0"
            VALUE "PrivateBuild", "\0"
            VALUE "ProductName", "kingsoft Bishop\0"
            VALUE "ProductVersion", "1, 0, 0, 1\0"
            VALUE "SpecialBuild", "\0"
        END
    END
    BLOCK "VarFileInfo"
    BEGIN
        VALUE "Translation", 0x804, 1200
    END
END

#endif    // !_MAC


/////////////////////////////////////////////////////////////////////////////
//
// Dialog
//

IDD_DLG_INTERCESSOR DIALOGEX 0, 0, 409, 265
STYLE DS_SETFOREGROUND | DS_CENTER | WS_MINIMIZEBOX | WS_CAPTION | 
    WS_SYSMENU
CAPTION "Bishop - [Disable]"
FONT 8, "Tahoma", 0, 0, 0x1
BEGIN
	DEFPUSHBUTTON   "Startup",IDOK,12,110,197,24
    PUSHBUTTON      "Stop the server && Exit",IDCANCEL,322,244,80,14
    EDITTEXT        IDC_EDIT_ACCSVRIP,90,36,64,12,ES_CENTER | ES_LOWERCASE | 
                    ES_AUTOHSCROLL | WS_DISABLED | NOT WS_BORDER,
                    WS_EX_STATICEDGE
    EDITTEXT        IDC_EDIT_ACCSVRPORT,179,36,26,12,ES_CENTER | 
                    ES_AUTOHSCROLL | ES_NUMBER | WS_DISABLED | NOT WS_BORDER,
                    WS_EX_STATICEDGE
    EDITTEXT        IDC_EDIT_ROLESVRIP,90,52,64,12,ES_CENTER | ES_LOWERCASE | 
                    ES_AUTOHSCROLL | NOT WS_BORDER,WS_EX_STATICEDGE
    EDITTEXT        IDC_EDIT_ROLESVRPORT,179,52,26,12,ES_CENTER | 
                    ES_AUTOHSCROLL | ES_NUMBER | NOT WS_BORDER,
                    WS_EX_STATICEDGE
    EDITTEXT        IDC_EDIT_CLIENT_PORT,128,73,26,12,ES_CENTER | 
                    ES_AUTOHSCROLL | ES_NUMBER | NOT WS_BORDER,
                    WS_EX_STATICEDGE
    EDITTEXT        IDC_EDIT_GAMESVR_PORT,128,90,26,12,ES_CENTER | 
                    ES_AUTOHSCROLL | ES_NUMBER | NOT WS_BORDER,
                    WS_EX_STATICEDGE
    CHECKBOX        "",IDC_CHECK_ACCSVR,18,36,9,12,BS_LEFTTEXT | BS_FLAT | 
                    WS_DISABLED
    CHECKBOX        "",IDC_CHECK_ROLESVR,18,52,9,12,BS_LEFTTEXT | BS_FLAT | 
                    WS_DISABLED
    CHECKBOX        "",IDC_CHECK_PLAYER,18,73,9,12,BS_LEFTTEXT | BS_FLAT | 
                    WS_DISABLED
    CHECKBOX        "",IDC_CHECK_GAMESVR,18,90,9,12,BS_LEFTTEXT | BS_FLAT | 
                    WS_DISABLED
    CONTROL         "",IDC_STATIC,"Static",SS_ETCHEDFRAME,7,7,207,133,
                    WS_EX_DLGMODALFRAME
    LTEXT           "Port :",IDC_STATIC,157,36,20,12,SS_CENTERIMAGE
    LTEXT           "Port :",IDC_STATIC,157,52,20,12,SS_CENTERIMAGE
    LTEXT           "Client Port :",IDC_STATIC,47,73,40,12,SS_CENTERIMAGE
    LTEXT           "Game Server Port :",IDC_STATIC,47,90,64,12,
                    SS_CENTERIMAGE
    LTEXT           "for Open",IDC_STATIC,157,73,32,12,SS_CENTERIMAGE
    LTEXT           "for Open",IDC_STATIC,157,90,32,12,SS_CENTERIMAGE
    LTEXT           "AccSvr IP :",IDC_STATIC,47,36,37,12,SS_CENTERIMAGE
    LTEXT           "RoleSvr IP :",IDC_STATIC,47,52,40,12,SS_CENTERIMAGE
    CONTROL         "",IDC_STATIC,"Static",SS_ETCHEDHORZ,14,29,193,1
    LTEXT           "Status",IDC_STATIC,15,17,24,8,SS_CENTERIMAGE
    LTEXT           "Information",IDC_STATIC,47,17,154,8,SS_CENTERIMAGE
    LISTBOX         IDC_LIST_GAMESERVER,226,18,170,20,LBS_NOINTEGRALHEIGHT | 
                    WS_DISABLED | NOT WS_BORDER | WS_VSCROLL | WS_TABSTOP,
                    WS_EX_STATICEDGE
    GROUPBOX        " GameServer Information ",IDC_STATIC,219,7,183,88
    GROUPBOX        "",IDC_STATIC,226,44,170,25
    PUSHBUTTON      "Update",IDC_BTN_GAMESVRINFO,322,41,51,14,WS_DISABLED
    RTEXT           "Content :",IDC_STATIC,232,58,32,10,SS_CENTERIMAGE
    LTEXT           "Invalid",IDC_STATIC_GAMESERVER_CONTEXT,267,58,116,10,
                    SS_CENTERIMAGE
    LTEXT           "Task [performance per five second] :",IDC_STATIC,229,76,
                    118,12,SS_CENTERIMAGE
    LTEXT           "Invalid",IDC_STATIC_TASK,351,76,41,12,SS_CENTERIMAGE
    GROUPBOX        " GameServer Punish ",IDC_STATIC,219,96,183,44
    CONTROL         "Account",IDC_PUNISH_LOCK,"Button",
                    BS_AUTORADIOBUTTON | WS_DISABLED | WS_GROUP,300,108,42,
                    9
    CONTROL         "Speech",IDC_PUNISH_CHAT,"Button",
                    BS_AUTORADIOBUTTON | WS_DISABLED,300,122,42,9
    EDITTEXT        IDC_EDIT_ACCOUNTNAME,223,108,70,22,ES_MULTILINE | 
                    WS_DISABLED | NOT WS_BORDER,WS_EX_STATICEDGE
    PUSHBUTTON      "Punish",IDC_BTN_PUNISH,348,104,50,14,WS_DISABLED
    PUSHBUTTON      "Unpunish",IDC_BTN_UNPUNISH,348,120,50,14,WS_DISABLED
    GROUPBOX        " Announcer ",IDC_STATIC,7,142,395,97
    CONTROL         "Send to all gameserver",IDC_INDEX_SEND2GAMESERVER,
                    "Button",BS_AUTORADIOBUTTON | WS_DISABLED | WS_GROUP,202,
                    152,86,9
    CONTROL         "Send to the current gameserver that it is selected",
                    IDC_INDEX_SEND2GS_ISSEL,"Button",BS_AUTORADIOBUTTON | 
                    WS_DISABLED,202,166,193,9
    EDITTEXT        IDC_EDIT_ANNOUNCE_MSG,202,179,194,34,ES_MULTILINE | 
                    ES_AUTOVSCROLL | ES_WANTRETURN | WS_DISABLED | NOT 
                    WS_BORDER | WS_VSCROLL,WS_EX_STATICEDGE
    PUSHBUTTON      "Send",IDC_BTN_SEND_MSG,346,218,50,14,WS_DISABLED
    LTEXT           "( Max length of text is 260 )",IDC_STATIC,202,218,138,
                    14,SS_CENTERIMAGE
    LTEXT           "NOTE : The 'Stop the server && Exit' button will stop this service and exit the application.",
                    IDC_STATIC,7,244,309,14,SS_CENTERIMAGE
    CONTROL         "Notify all player",IDC_RADIO_ANNOUNCE_OPTION,"Button",
                    BS_AUTORADIOBUTTON | WS_DISABLED | WS_GROUP,19,166,164,9
    CONTROL         "Notify game server to close safely *",
                    IDC_INDEX_GS2CLOSE,"Button",BS_AUTORADIOBUTTON | 
                    WS_DISABLED,19,182,128,9
    CONTROL         "Notify and warning all player to quit this game *",
                    IDC_INDEX_WANINGGS2CLOSE,"Button",BS_AUTORADIOBUTTON | 
                    WS_DISABLED,19,198,169,9
    GROUPBOX        " Command type ",IDC_STATIC,15,153,176,61
END


/////////////////////////////////////////////////////////////////////////////
//
// Icon
//

// Icon with lowest ID value placed first to ensure application icon
// remains consistent on all systems.
IDI_LOCK                ICON    DISCARDABLE     "res\\lock.ico"

/////////////////////////////////////////////////////////////////////////////
//
// DESIGNINFO
//

#ifdef APSTUDIO_INVOKED
GUIDELINES DESIGNINFO DISCARDABLE 
BEGIN
    IDD_DLG_INTERCESSOR, DIALOG
    BEGIN
        LEFTMARGIN, 7
        RIGHTMARGIN, 402
        TOPMARGIN, 7
        BOTTOMMARGIN, 288
    END
END
#endif    // APSTUDIO_INVOKED


/////////////////////////////////////////////////////////////////////////////
//
// String Table
//

STRINGTABLE DISCARDABLE 
BEGIN
    IDS_NAMEPSWD_ERROR      "The user name or password is incorrect, Letters in passwords must be typed using the correct case. Make sure that Caps Lock is not accidentaly on."
    IDS_NETWORK_ERROR       "The system cannot log on to server now because the network is not available."
    IDS_APP_TITLE           "Bishop"
END

#endif    // Chinese (P.R.C.) resources
/////////////////////////////////////////////////////////////////////////////


/////////////////////////////////////////////////////////////////////////////
// English (U.S.) resources

#if !defined(AFX_RESOURCE_DLL) || defined(AFX_TARG_ENU)
#ifdef _WIN32
LANGUAGE LANG_ENGLISH, SUBLANG_ENGLISH_US
#pragma code_page(1252)
#endif //_WIN32

/////////////////////////////////////////////////////////////////////////////
//
// Dialog
//

IDD_DLG_LOGIN DIALOGEX 0, 0, 214, 95
STYLE DS_SYSMODAL | DS_SETFOREGROUND | DS_CENTER | WS_POPUP | WS_CAPTION
EXSTYLE WS_EX_TOOLWINDOW
CAPTION "Login"
FONT 8, "Tahoma", 0, 0, 0x1
BEGIN
    DEFPUSHBUTTON   "OK",IDOK,103,73,50,14
    PUSHBUTTON      "Cancel",IDCANCEL,157,73,50,14
    EDITTEXT        IDC_EDIT_USERNAME,51,7,155,12,ES_AUTOHSCROLL | NOT 
                    WS_BORDER,WS_EX_STATICEDGE
    EDITTEXT        IDC_EDIT_PASSWORD,51,23,155,12,ES_PASSWORD | 
                    ES_AUTOHSCROLL | NOT WS_BORDER,WS_EX_STATICEDGE
    EDITTEXT        IDC_EDIT_LOGONTO_IP,88,46,64,12,ES_CENTER | ES_LOWERCASE | 
                    ES_AUTOHSCROLL | NOT WS_BORDER,WS_EX_STATICEDGE
    EDITTEXT        IDC_EDIT_LOGONTO_PORT,174,46,26,12,ES_CENTER | 
                    ES_AUTOHSCROLL | ES_NUMBER | NOT WS_BORDER,
                    WS_EX_STATICEDGE
    LTEXT           "User name :",IDC_STATIC,7,7,42,12,SS_CENTERIMAGE
    LTEXT           "Password :",IDC_STATIC,7,23,42,12,SS_CENTERIMAGE
    LTEXT           "Log on to :",IDC_STATIC,7,39,42,12,SS_CENTERIMAGE
    RTEXT           "A.S.",IDC_STATIC,55,46,15,12,SS_CENTERIMAGE
    GROUPBOX        " Server ",IDC_STATIC,51,36,155,28
    RTEXT           "IP :",IDC_STATIC,73,46,12,12,SS_CENTERIMAGE
    RTEXT           "Port :",IDC_STATIC,153,46,19,12,SS_CENTERIMAGE
    ICON            IDI_LOCK,IDC_STATIC,7,67,21,20
END


/////////////////////////////////////////////////////////////////////////////
//
// DESIGNINFO
//

#ifdef APSTUDIO_INVOKED
GUIDELINES DESIGNINFO DISCARDABLE 
BEGIN
    IDD_DLG_LOGIN, DIALOG
    BEGIN
        LEFTMARGIN, 7
        RIGHTMARGIN, 207
        TOPMARGIN, 7
        BOTTOMMARGIN, 88
    END
END
#endif    // APSTUDIO_INVOKED

#endif    // English (U.S.) resources
/////////////////////////////////////////////////////////////////////////////



#ifndef APSTUDIO_INVOKED
/////////////////////////////////////////////////////////////////////////////
//
// Generated from the TEXTINCLUDE 3 resource.
//


/////////////////////////////////////////////////////////////////////////////
#endif    // not APSTUDIO_INVOKED

