﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="4.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <ItemGroup>
    <Filter Include="Source Files">
      <UniqueIdentifier>{8ae512ca-4c5d-4255-9b09-ad6d72181165}</UniqueIdentifier>
      <Extensions>cpp;c;cxx;rc;def;r;odl;idl;hpj;bat</Extensions>
    </Filter>
    <Filter Include="Header Files">
      <UniqueIdentifier>{343d1896-42f5-4a8d-b45a-d613baee6f83}</UniqueIdentifier>
      <Extensions>h;hpp;hxx;hm;inl</Extensions>
    </Filter>
    <Filter Include="Resource Files">
      <UniqueIdentifier>{32cd5e76-9726-420e-89d0-17f46ab34b28}</UniqueIdentifier>
      <Extensions>ico;cur;bmp;dlg;rc2;rct;bin;rgs;gif;jpg;jpeg;jpe</Extensions>
    </Filter>
    <Filter Include="Library">
      <UniqueIdentifier>{07f43986-aabe-4cc4-934a-2d1e3b44e486}</UniqueIdentifier>
    </Filter>
    <Filter Include="Library\debug">
      <UniqueIdentifier>{a786c000-2b1f-4cc5-88e6-ec4734ad1c43}</UniqueIdentifier>
    </Filter>
    <Filter Include="Library\release">
      <UniqueIdentifier>{89497afe-b1c8-40ee-b82c-2c3357c6875e}</UniqueIdentifier>
    </Filter>
    <Filter Include="°ï»á">
      <UniqueIdentifier>{e6dad517-9aaf-4ddf-a2b7-23ee3071c10a}</UniqueIdentifier>
    </Filter>
    <Filter Include="ProtocolHeaderFiles">
      <UniqueIdentifier>{a0f2dde4-d3e0-432b-9ed1-f76e38bfebe4}</UniqueIdentifier>
    </Filter>
  </ItemGroup>
  <ItemGroup>
    <ClCompile Include="ChannelMgr.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="ChatConnect.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="ChatServer.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="DBConnect.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="DBTable.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="DealRelay.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="DoScript.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="FriendMgr.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="GatewayCenter.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="GatewayClient.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="Global.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="HeavenLib.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="HostConnect.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="HostServer.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="KThread.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="Lock.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="LogFile.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="Memory.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="NetCenter.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="NetClient.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="NetConnect.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="NetServer.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="NetSockDupEx.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="RainbowLib.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="RelayCenter.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="RelayClient.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="RelayConnect.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="RelayServer.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="RootCenter.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="RootClient.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="S3Relay.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="SockThread.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="StdAfx.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="TongConnect.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="TongServer.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="KTongControl.cpp">
      <Filter>°ï»á</Filter>
    </ClCompile>
    <ClCompile Include="KTongSet.cpp">
      <Filter>°ï»á</Filter>
    </ClCompile>
    <ClCompile Include="TONGDB.CPP">
      <Filter>°ï»á</Filter>
    </ClCompile>
  </ItemGroup>
  <ItemGroup>
    <ResourceCompile Include="S3Relay.rc">
      <Filter>Source Files</Filter>
    </ResourceCompile>
  </ItemGroup>
  <ItemGroup>
    <ClInclude Include="ChannelMgr.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="ChatConnect.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="ChatServer.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="DBConnect.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="DBTable.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="DealRelay.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="DoScript.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="FriendMgr.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="GatewayCenter.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="GatewayClient.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="Global.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="HeavenLib.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="HostConnect.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="HostServer.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="KThread.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="Lock.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="LogFile.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="Memory.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="NetCenter.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="NetClient.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="NetConnect.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="NetServer.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="NetSockDupEx.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="RainbowLib.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="RelayCenter.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="RelayClient.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="RelayConnect.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="RelayServer.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="resource.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="RootCenter.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="RootClient.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="S3Relay.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="SockThread.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="StdAfx.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="TongConnect.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="TongServer.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="KTongControl.h">
      <Filter>°ï»á</Filter>
    </ClInclude>
    <ClInclude Include="KTongSet.h">
      <Filter>°ï»á</Filter>
    </ClInclude>
    <ClInclude Include="TONGDB.H">
      <Filter>°ï»á</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\Headers\KRelayProtocol.h">
      <Filter>ProtocolHeaderFiles</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\Headers\KTongProtocol.h">
      <Filter>ProtocolHeaderFiles</Filter>
    </ClInclude>
  </ItemGroup>
  <ItemGroup>
    <Library Include="..\..\..\Lib\debug\engine.lib">
      <Filter>Library\debug</Filter>
    </Library>
    <Library Include="..\..\..\Lib\release\engine.lib">
      <Filter>Library\release</Filter>
    </Library>
    <Library Include="..\..\..\Lib\libdb62d.lib">
      <Filter>Library\debug</Filter>
    </Library>
  </ItemGroup>
  <ItemGroup>
    <CustomBuild Include="S3Relay.ico">
      <Filter>Resource Files</Filter>
    </CustomBuild>
    <CustomBuild Include="small.ico">
      <Filter>Resource Files</Filter>
    </CustomBuild>
    <CustomBuild Include="ReadMe.txt" />
  </ItemGroup>
</Project>