//---------------------------------------------------------------------------
// Sword2 Social Script Integration (c) 2024
//
// File:	SocialScriptIntegration.h
// Date:	2024.07.02
// Code:	AI Assistant
// Desc:	Integration layer between social system and Lua scripts
//---------------------------------------------------------------------------
#ifndef SOCIAL_SCRIPT_INTEGRATION_H
#define SOCIAL_SCRIPT_INTEGRATION_H

#include "SocialManager.h"
#include "MailManager.h"
#include "LuaScriptEngine.h"
#include "PlayerManager.h"
#include "UnifiedLoggingSystem.h"
#include <string>
#include <unordered_map>
#include <vector>
#include <functional>

namespace sword2 {

// 社交脚本API扩展
namespace SocialScriptAPI {
    
    // 好友系统API
    int AddFriend(lua_State* L);
    int RemoveFriend(lua_State* L);
    int GetFriendList(lua_State* L);
    int GetOnlineFriends(lua_State* L);
    int IsFriend(lua_State* L);
    int SetFriendGroup(lua_State* L);
    int GetFriendInfo(lua_State* L);
    int AddToBlacklist(lua_State* L);
    int RemoveFromBlacklist(lua_State* L);
    int IsBlacklisted(lua_State* L);
    int GetBlacklist(lua_State* L);
    
    // 聊天系统API
    int SendMessage(lua_State* L);
    int SendPrivateMessage(lua_State* L);
    int GetPrivateMessages(lua_State* L);
    int GetChannelMessages(lua_State* L);
    int CreateChannel(lua_State* L);
    int JoinChannel(lua_State* L);
    int LeaveChannel(lua_State* L);
    int GetChannelList(lua_State* L);
    int GetChannelInfo(lua_State* L);
    int SetChannelPermission(lua_State* L);
    int BanFromChannel(lua_State* L);
    int UnbanFromChannel(lua_State* L);
    
    // 邮件系统API
    int SendMail(lua_State* L);
    int SendItemMail(lua_State* L);
    int SendSystemMail(lua_State* L);
    int SendBroadcastMail(lua_State* L);
    int ReadMail(lua_State* L);
    int DeleteMail(lua_State* L);
    int GetMailList(lua_State* L);
    int GetMailInfo(lua_State* L);
    int CollectAttachment(lua_State* L);
    int CollectAllAttachments(lua_State* L);
    int GetUnreadMailCount(lua_State* L);
    int HasMailAttachment(lua_State* L);
    
    // 社交状态API
    int SetPlayerStatus(lua_State* L);
    int GetPlayerStatus(lua_State* L);
    int GetPlayerLocation(lua_State* L);
    int SetPlayerLocation(lua_State* L);
    int IsPlayerOnline(lua_State* L);
    int GetLastOnlineTime(lua_State* L);
    
    // 社交统计API
    int GetFriendCount(lua_State* L);
    int GetChatCount(lua_State* L);
    int GetMailCount(lua_State* L);
    int GetIntimacy(lua_State* L);
    int AddIntimacy(lua_State* L);
    int GetSocialRank(lua_State* L);
    
    // 社交事件API
    int RegisterSocialEventHandler(lua_State* L);
    int UnregisterSocialEventHandler(lua_State* L);
    int TriggerSocialEvent(lua_State* L);
    int OnFriendAdded(lua_State* L);
    int OnMessageReceived(lua_State* L);
    int OnMailReceived(lua_State* L);
    int OnPlayerOnline(lua_State* L);
    int OnPlayerOffline(lua_State* L);
    
    // 社交过滤API
    int FilterMessage(lua_State* L);
    int IsSpamMessage(lua_State* L);
    int AddWordFilter(lua_State* L);
    int RemoveWordFilter(lua_State* L);
    int GetFilteredWords(lua_State* L);
    
    // 社交权限API
    int CanSendMessage(lua_State* L);
    int CanAddFriend(lua_State* L);
    int CanSendMail(lua_State* L);
    int GetSocialPermission(lua_State* L);
    int SetSocialPermission(lua_State* L);
}

// 社交事件类型
enum class SocialEvent : uint8_t
{
    FriendAdded = 0,        // 好友添加
    FriendRemoved,          // 好友删除
    FriendOnline,           // 好友上线
    FriendOffline,          // 好友下线
    MessageSent,            // 消息发送
    MessageReceived,        // 消息接收
    MailSent,               // 邮件发送
    MailReceived,           // 邮件接收
    MailRead,               // 邮件阅读
    ChannelJoined,          // 频道加入
    ChannelLeft,            // 频道离开
    PlayerStatusChanged,    // 玩家状态变更
    IntimacyChanged,        // 亲密度变更
    BlacklistAdded,         // 黑名单添加
    BlacklistRemoved        // 黑名单移除
};

// 社交事件处理器
class SocialEventHandler
{
public:
    struct SocialEventData
    {
        SocialEvent event;
        uint32_t playerId;
        uint32_t targetId;
        uint32_t channelId;
        uint32_t mailId;
        std::string message;
        std::string extraData;
        std::chrono::system_clock::time_point timestamp;
        
        SocialEventData() = default;
        SocialEventData(SocialEvent evt, uint32_t player, uint32_t target = 0)
            : event(evt), playerId(player), targetId(target), channelId(0), mailId(0)
        {
            timestamp = std::chrono::system_clock::now();
        }
    };
    
    using EventCallback = std::function<void(const SocialEventData&)>;
    
    // 注册事件处理器
    void RegisterEventHandler(SocialEvent event, const std::string& handlerName, EventCallback callback)
    {
        std::lock_guard<std::mutex> lock(m_handlerMutex);
        m_eventHandlers[event][handlerName] = callback;
        
        LOG_DEBUG("SOCIAL_EVENT", "Registered event handler: " + handlerName + " for event " + std::to_string(static_cast<int>(event)));
    }
    
    // 注销事件处理器
    void UnregisterEventHandler(SocialEvent event, const std::string& handlerName)
    {
        std::lock_guard<std::mutex> lock(m_handlerMutex);
        auto it = m_eventHandlers.find(event);
        if (it != m_eventHandlers.end())
        {
            it->second.erase(handlerName);
            LOG_DEBUG("SOCIAL_EVENT", "Unregistered event handler: " + handlerName);
        }
    }
    
    // 触发事件
    void TriggerEvent(const SocialEventData& eventData)
    {
        std::lock_guard<std::mutex> lock(m_handlerMutex);
        
        auto it = m_eventHandlers.find(eventData.event);
        if (it != m_eventHandlers.end())
        {
            for (const auto& [handlerName, callback] : it->second)
            {
                try
                {
                    callback(eventData);
                }
                catch (const std::exception& e)
                {
                    LOG_ERROR("SOCIAL_EVENT", "Error in event handler " + handlerName + ": " + e.what());
                }
            }
        }
        
        // 记录事件历史
        m_eventHistory.push_back(eventData);
        if (m_eventHistory.size() > 1000) // 限制历史记录数量
        {
            m_eventHistory.erase(m_eventHistory.begin());
        }
    }
    
    // 获取事件历史
    std::vector<SocialEventData> GetEventHistory(uint32_t playerId = 0, SocialEvent event = SocialEvent::FriendAdded) const
    {
        std::lock_guard<std::mutex> lock(m_handlerMutex);
        std::vector<SocialEventData> history;
        
        for (const auto& eventData : m_eventHistory)
        {
            if ((playerId == 0 || eventData.playerId == playerId || eventData.targetId == playerId) &&
                (event == SocialEvent::FriendAdded || eventData.event == event))
            {
                history.push_back(eventData);
            }
        }
        
        return history;
    }

private:
    mutable std::mutex m_handlerMutex;
    std::unordered_map<SocialEvent, std::unordered_map<std::string, EventCallback>> m_eventHandlers;
    std::vector<SocialEventData> m_eventHistory;
};

// 消息过滤系统
class MessageFilterSystem
{
public:
    MessageFilterSystem() = default;
    ~MessageFilterSystem() = default;
    
    // 添加过滤词
    void AddFilterWord(const std::string& word, bool isRegex = false)
    {
        std::lock_guard<std::mutex> lock(m_filterMutex);
        
        if (isRegex)
        {
            m_regexFilters.push_back(word);
        }
        else
        {
            m_wordFilters.insert(word);
        }
        
        LOG_DEBUG("SOCIAL_FILTER", "Added filter word: " + word);
    }
    
    // 移除过滤词
    void RemoveFilterWord(const std::string& word)
    {
        std::lock_guard<std::mutex> lock(m_filterMutex);
        
        m_wordFilters.erase(word);
        
        auto it = std::find(m_regexFilters.begin(), m_regexFilters.end(), word);
        if (it != m_regexFilters.end())
        {
            m_regexFilters.erase(it);
        }
        
        LOG_DEBUG("SOCIAL_FILTER", "Removed filter word: " + word);
    }
    
    // 过滤消息
    std::string FilterMessage(const std::string& message)
    {
        std::lock_guard<std::mutex> lock(m_filterMutex);
        
        std::string filtered = message;
        
        // 简单词汇过滤
        for (const auto& word : m_wordFilters)
        {
            size_t pos = 0;
            while ((pos = filtered.find(word, pos)) != std::string::npos)
            {
                filtered.replace(pos, word.length(), std::string(word.length(), '*'));
                pos += word.length();
            }
        }
        
        // 正则表达式过滤（这里简化处理）
        for (const auto& regex : m_regexFilters)
        {
            // 实际实现中应该使用正则表达式库
            // 这里简化为包含检查
            if (filtered.find(regex) != std::string::npos)
            {
                filtered = std::string(filtered.length(), '*');
                break;
            }
        }
        
        return filtered;
    }
    
    // 检查是否为垃圾消息
    bool IsSpamMessage(const std::string& message)
    {
        std::lock_guard<std::mutex> lock(m_filterMutex);
        
        // 检查是否包含过滤词
        for (const auto& word : m_wordFilters)
        {
            if (message.find(word) != std::string::npos)
                return true;
        }
        
        // 检查消息长度
        if (message.length() > 500)
            return true;
        
        // 检查重复字符
        if (HasRepeatedCharacters(message, 10))
            return true;
        
        return false;
    }
    
    // 获取过滤词列表
    std::vector<std::string> GetFilterWords() const
    {
        std::lock_guard<std::mutex> lock(m_filterMutex);
        
        std::vector<std::string> words;
        for (const auto& word : m_wordFilters)
        {
            words.push_back(word);
        }
        
        for (const auto& regex : m_regexFilters)
        {
            words.push_back(regex);
        }
        
        return words;
    }

private:
    mutable std::mutex m_filterMutex;
    std::unordered_set<std::string> m_wordFilters;
    std::vector<std::string> m_regexFilters;
    
    bool HasRepeatedCharacters(const std::string& message, int threshold)
    {
        if (message.length() < threshold) return false;
        
        std::unordered_map<char, int> charCount;
        for (char c : message)
        {
            charCount[c]++;
            if (charCount[c] >= threshold)
                return true;
        }
        
        return false;
    }
};

// 社交脚本集成管理器
class SocialScriptIntegration : public Singleton<SocialScriptIntegration>
{
public:
    SocialScriptIntegration() = default;
    ~SocialScriptIntegration() = default;
    
    // 初始化社交脚本集成
    bool Initialize()
    {
        // 注册社交相关的Lua API函数
        RegisterSocialScriptAPI();
        
        // 注册默认事件处理器
        RegisterDefaultEventHandlers();
        
        // 初始化默认过滤词
        InitializeDefaultFilters();
        
        LOG_INFO("SOCIAL_SCRIPT", "Social script integration initialized");
        return true;
    }
    
    // 加载社交脚本
    bool LoadSocialScript(const std::string& scriptPath)
    {
        ScriptResult result = LOAD_SCRIPT(scriptPath, ScriptType::Social);
        if (result == ScriptResult::Success)
        {
            LOG_INFO("SOCIAL_SCRIPT", "Loaded social script: " + scriptPath);
            return true;
        }
        else
        {
            LOG_ERROR("SOCIAL_SCRIPT", "Failed to load social script: " + scriptPath);
            return false;
        }
    }
    
    // 执行社交脚本函数
    bool ExecuteSocialFunction(const std::string& functionName, uint32_t playerId, uint32_t targetId = 0)
    {
        std::vector<LuaValue> args;
        args.push_back(LuaValue(static_cast<double>(playerId)));
        if (targetId != 0)
        {
            args.push_back(LuaValue(static_cast<double>(targetId)));
        }
        
        ScriptResult result = EXECUTE_FUNCTION(functionName, args);
        return result == ScriptResult::Success;
    }
    
    // 触发社交事件
    void TriggerSocialEvent(SocialEvent event, uint32_t playerId, uint32_t targetId = 0)
    {
        SocialEventHandler::SocialEventData eventData(event, playerId, targetId);
        m_eventHandler.TriggerEvent(eventData);
        
        // 执行脚本事件处理
        std::string eventName = GetEventName(event);
        ExecuteSocialFunction("on_social_" + eventName, playerId, targetId);
    }
    
    // 过滤消息
    std::string FilterMessage(const std::string& message)
    {
        return m_filterSystem.FilterMessage(message);
    }
    
    // 检查垃圾消息
    bool IsSpamMessage(const std::string& message)
    {
        return m_filterSystem.IsSpamMessage(message);
    }
    
    // 检查社交权限
    bool CheckSocialPermission(uint32_t playerId, const std::string& action, uint32_t targetId = 0)
    {
        std::vector<LuaValue> args = {
            LuaValue(static_cast<double>(playerId)),
            LuaValue(action),
            LuaValue(static_cast<double>(targetId))
        };
        
        auto result = EXECUTE_FUNCTION_WITH_RETURN("check_social_permission", args);
        if (result.has_value() && result->type == LuaValueType::Boolean)
        {
            return result->booleanValue;
        }
        
        return true; // 默认允许
    }
    
    // 获取事件处理器
    SocialEventHandler& GetEventHandler() { return m_eventHandler; }
    
    // 获取过滤系统
    MessageFilterSystem& GetFilterSystem() { return m_filterSystem; }

private:
    SocialEventHandler m_eventHandler;
    MessageFilterSystem m_filterSystem;
    
    void RegisterSocialScriptAPI();
    void RegisterDefaultEventHandlers();
    void InitializeDefaultFilters();
    std::string GetEventName(SocialEvent event);
};

} // namespace sword2

// 全局社交脚本集成访问
#define SOCIAL_SCRIPT_INTEGRATION() sword2::SocialScriptIntegration::getInstance()

// 便捷宏定义
#define INIT_SOCIAL_SCRIPT_INTEGRATION() SOCIAL_SCRIPT_INTEGRATION().Initialize()
#define LOAD_SOCIAL_SCRIPT(scriptPath) SOCIAL_SCRIPT_INTEGRATION().LoadSocialScript(scriptPath)
#define EXECUTE_SOCIAL_FUNCTION(functionName, playerId, targetId) SOCIAL_SCRIPT_INTEGRATION().ExecuteSocialFunction(functionName, playerId, targetId)

#define TRIGGER_SOCIAL_EVENT(event, playerId, targetId) SOCIAL_SCRIPT_INTEGRATION().TriggerSocialEvent(event, playerId, targetId)
#define FILTER_MESSAGE(message) SOCIAL_SCRIPT_INTEGRATION().FilterMessage(message)
#define IS_SPAM_MESSAGE(message) SOCIAL_SCRIPT_INTEGRATION().IsSpamMessage(message)
#define CHECK_SOCIAL_PERMISSION(playerId, action, targetId) SOCIAL_SCRIPT_INTEGRATION().CheckSocialPermission(playerId, action, targetId)

#define SOCIAL_EVENT_HANDLER() SOCIAL_SCRIPT_INTEGRATION().GetEventHandler()
#define MESSAGE_FILTER_SYSTEM() SOCIAL_SCRIPT_INTEGRATION().GetFilterSystem()

#endif // SOCIAL_SCRIPT_INTEGRATION_H
