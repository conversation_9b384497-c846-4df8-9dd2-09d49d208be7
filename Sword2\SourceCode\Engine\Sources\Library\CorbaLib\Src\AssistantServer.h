// This file is generated by omniidl (C++ backend)- omniORB_3_0. Do not edit.
#ifndef __AssistantServer_hh__
#define __AssistantServer_hh__

#ifndef USE_omniORB_logStream
#define USE_omniORB_logStream
#endif

#ifndef __CORBA_H_EXTERNAL_GUARD__
#include <omniORB3/CORBA.h>
#endif

#ifndef  USE_core_stub_in_nt_dll
# define USE_core_stub_in_nt_dll_NOT_DEFINED_AssistantServer
#endif
#ifndef  USE_dyn_stub_in_nt_dll
# define USE_dyn_stub_in_nt_dll_NOT_DEFINED_AssistantServer
#endif

#ifdef USE_stub_in_nt_dll
#ifndef USE_core_stub_in_nt_dll
#define USE_core_stub_in_nt_dll
#endif
#ifndef USE_dyn_stub_in_nt_dll
#define USE_dyn_stub_in_nt_dll
#endif
#endif

#ifdef _core_attr
# error "A local CPP macro _core_attr has already been defined."
#else
# ifdef  USE_core_stub_in_nt_dll
#  define _core_attr _OMNIORB_NTDLL_IMPORT
# else
#  define _core_attr
# endif
#endif

#ifdef _dyn_attr
# error "A local CPP macro _dyn_attr has already been defined."
#else
# ifdef  USE_dyn_stub_in_nt_dll
#  define _dyn_attr _OMNIORB_NTDLL_IMPORT
# else
#  define _dyn_attr
# endif
#endif

struct Message {
  typedef _CORBA_ConstrType_Variable_Var<Message> _var_type;

  CORBA::Long MsgId;

  CORBA::Long PlayerId;

  CORBA::Any MsgData;

  size_t _NP_alignedSize(size_t initialoffset) const;
  void operator>>= (NetBufferedStream &) const;
  void operator<<= (NetBufferedStream &);
  void operator>>= (MemBufferedStream &) const;
  void operator<<= (MemBufferedStream &);
};

typedef Message::_var_type Message_var;

typedef _CORBA_ConstrType_Variable_OUT_arg< Message,Message_var > Message_out;

_CORBA_GLOBAL_VAR _dyn_attr const CORBA::TypeCode_ptr _tc_Message;

_CORBA_GLOBAL_VAR _dyn_attr const CORBA::TypeCode_ptr _tc_MessageSeq;

class MessageSeq_var;

class MessageSeq : public _CORBA_Unbounded_Sequence< Message>  {
public:
  typedef MessageSeq_var _var_type;
  inline MessageSeq() {}
  inline MessageSeq(const MessageSeq& s)
    : _CORBA_Unbounded_Sequence< Message> (s) {}

  inline MessageSeq(_CORBA_ULong _max)
    : _CORBA_Unbounded_Sequence< Message> (_max) {}
  inline MessageSeq(_CORBA_ULong _max, _CORBA_ULong _len, Message* _val, _CORBA_Boolean _rel=0)
    : _CORBA_Unbounded_Sequence< Message> (_max, _len, _val, _rel) {}

  inline MessageSeq& operator = (const MessageSeq& s) {
    _CORBA_Unbounded_Sequence< Message> ::operator=(s);
    return *this;
  }
};

class MessageSeq_out;

class MessageSeq_var {
public:
  typedef MessageSeq T;
  typedef MessageSeq_var T_var;

  inline MessageSeq_var() : _pd_seq(0) {}
  inline MessageSeq_var(T* s) : _pd_seq(s) {}
  inline MessageSeq_var(const T_var& s) {
    if( s._pd_seq )  _pd_seq = new T(*s._pd_seq);
    else             _pd_seq = 0;
  }
  inline ~MessageSeq_var() { if( _pd_seq )  delete _pd_seq; }

  inline T_var& operator = (T* s) {
    if( _pd_seq )  delete _pd_seq;
    _pd_seq = s;
    return *this;
  }
  inline T_var& operator = (const T_var& s) {
    if( s._pd_seq ) {
      if( !_pd_seq )  _pd_seq = new T;
      *_pd_seq = *s._pd_seq;
    } else if( _pd_seq ) {
      delete _pd_seq;
      _pd_seq = 0;
    }
    return *this;
  }

  inline Message& operator [] (_CORBA_ULong s) {
    return (*_pd_seq)[s];
  }

  inline T* operator -> () { return _pd_seq; }
#if defined(__GNUG__) && __GNUG__ == 2 && __GNUC_MINOR__ == 7
  inline operator T& () const { return *_pd_seq; }
#else
  inline operator const T& () const { return *_pd_seq; }
  inline operator T& () { return *_pd_seq; }
#endif

  inline const T& in() const { return *_pd_seq; }
  inline T&       inout()    { return *_pd_seq; }
  inline T*&      out() {
    if( _pd_seq ) { delete _pd_seq; _pd_seq = 0; }
    return _pd_seq;
  }
  inline T* _retn() { T* tmp = _pd_seq; _pd_seq = 0; return tmp; }

  friend class MessageSeq_out;

private:
  T* _pd_seq;
};

class MessageSeq_out {
public:
  typedef MessageSeq T;
  typedef MessageSeq_var T_var;

  inline MessageSeq_out(T*& s) : _data(s) { _data = 0; }
  inline MessageSeq_out(T_var& s)
    : _data(s._pd_seq) { s = (T*) 0; }
  inline MessageSeq_out(const MessageSeq_out& s) : _data(s._data) {}
  inline MessageSeq_out& operator = (const MessageSeq_out& s) {
    _data = s._data;
    return *this;
  }  inline MessageSeq_out& operator = (T* s) {
    _data = s;
    return *this;
  }
  inline operator T*&()  { return _data; }
  inline T*& ptr()       { return _data; }
  inline T* operator->() { return _data; }

  inline Message& operator [] (_CORBA_ULong i) {
    return (*_data)[i];
  }

  T*& _data;

private:
  MessageSeq_out();
  MessageSeq_out& operator=(const T_var&);
};

#ifndef __AssistantServer__
#define __AssistantServer__

class AssistantServer;
class _objref_AssistantServer;
class _impl_AssistantServer;

typedef _objref_AssistantServer* AssistantServer_ptr;
typedef AssistantServer_ptr AssistantServerRef;

class AssistantServer_Helper {
public:
  typedef AssistantServer_ptr _ptr_type;

  static _ptr_type _nil();
  static _CORBA_Boolean is_nil(_ptr_type);
  static void release(_ptr_type);
  static void duplicate(_ptr_type);
  static size_t NP_alignedSize(_ptr_type, size_t);
  static void marshalObjRef(_ptr_type, NetBufferedStream&);
  static _ptr_type unmarshalObjRef(NetBufferedStream&);
  static void marshalObjRef(_ptr_type, MemBufferedStream&);
  static _ptr_type unmarshalObjRef(MemBufferedStream&);
};

typedef _CORBA_ObjRef_Var<_objref_AssistantServer, AssistantServer_Helper> AssistantServer_var;
typedef _CORBA_ObjRef_OUT_arg<_objref_AssistantServer,AssistantServer_Helper > AssistantServer_out;

#endif

class AssistantServer {
public:
  // Declarations for this interface type.
  typedef AssistantServer_ptr _ptr_type;
  typedef AssistantServer_var _var_type;

  static _ptr_type _duplicate(_ptr_type);
  static _ptr_type _narrow(CORBA::Object_ptr);
  static _ptr_type _nil();

  static inline size_t _alignedSize(_ptr_type, size_t);
  static inline void _marshalObjRef(_ptr_type, NetBufferedStream&);
  static inline void _marshalObjRef(_ptr_type, MemBufferedStream&);

  static inline _ptr_type _unmarshalObjRef(NetBufferedStream& s) {
    CORBA::Object_ptr obj = CORBA::UnMarshalObjRef(_PD_repoId, s);
    _ptr_type result = _narrow(obj);
    CORBA::release(obj);
    return result;
  }

  static inline _ptr_type _unmarshalObjRef(MemBufferedStream& s) {
    CORBA::Object_ptr obj = CORBA::UnMarshalObjRef(_PD_repoId, s);
    _ptr_type result = _narrow(obj);
    CORBA::release(obj);
    return result;
  }

  static _core_attr const char* _PD_repoId;

  // Other IDL defined within this scope.
  
};

class _objref_AssistantServer :
  public virtual CORBA::Object, public virtual omniObjRef
{
public:
  CORBA::Long ExecuteSeq(const MessageSeq& MsgSeq, const char* DataServerName);
  CORBA::Long Execute(const Message& Msg, const char* DataServerName);
  
  inline _objref_AssistantServer() { _PR_setobj(0); }  // nil
  _objref_AssistantServer(const char*, IOP::TaggedProfileList*, omniIdentity*, omniLocalIdentity*);

protected:
  virtual ~_objref_AssistantServer();

private:
  virtual void* _ptrToObjRef(const char*);

  _objref_AssistantServer(const _objref_AssistantServer&);
  _objref_AssistantServer& operator = (const _objref_AssistantServer&);
  // not implemented
};

class _pof_AssistantServer : public proxyObjectFactory {
public:
  inline _pof_AssistantServer() : proxyObjectFactory(AssistantServer::_PD_repoId) {}
  virtual ~_pof_AssistantServer();

  virtual omniObjRef* newObjRef(const char*, IOP::TaggedProfileList*,
                                omniIdentity*, omniLocalIdentity*);
  virtual _CORBA_Boolean is_a(const char*) const;
};

class _impl_AssistantServer :
  public virtual omniServant
{
public:
  virtual ~_impl_AssistantServer();

  virtual CORBA::Long ExecuteSeq(const MessageSeq& MsgSeq, const char* DataServerName) = 0;
  virtual CORBA::Long Execute(const Message& Msg, const char* DataServerName) = 0;
  
public:  // Really protected, workaround for xlC
  virtual _CORBA_Boolean _dispatch(GIOP_S&);

private:
  virtual void* _ptrToInterface(const char*);
  virtual const char* _mostDerivedRepoId();
};

_CORBA_GLOBAL_VAR _dyn_attr const CORBA::TypeCode_ptr _tc_AssistantServer;

class POA_AssistantServer :
  public virtual _impl_AssistantServer,
  public virtual PortableServer::ServantBase
{
public:
  virtual ~POA_AssistantServer();

  inline AssistantServer_ptr _this() {
    return (AssistantServer_ptr) _do_this(AssistantServer::_PD_repoId);
  }
};

#undef _core_attr
#undef _dyn_attr

extern void operator<<=(CORBA::Any& _a, const Message& _s);
extern void operator<<=(CORBA::Any& _a, Message* _sp);
extern CORBA::Boolean operator>>=(const CORBA::Any& _a, Message*& _sp);
extern CORBA::Boolean operator>>=(const CORBA::Any& _a, const Message*& _sp);

extern void operator <<= (CORBA::Any& _a, const MessageSeq& _s);
inline void operator <<= (CORBA::Any& _a, MessageSeq* _sp) {
  _a <<= *_sp;
  delete _sp;
}
extern _CORBA_Boolean operator >>= (const CORBA::Any& _a, MessageSeq*& _sp);
extern _CORBA_Boolean operator >>= (const CORBA::Any& _a, const MessageSeq*& _sp);

void operator<<=(CORBA::Any& _a, AssistantServer_ptr _s);
void operator<<=(CORBA::Any& _a, AssistantServer_ptr* _s);
CORBA::Boolean operator>>=(const CORBA::Any& _a, AssistantServer_ptr& _s);

inline size_t
AssistantServer::_alignedSize(AssistantServer_ptr obj, size_t offset) {
  return CORBA::AlignedObjRef(obj, _PD_repoId, 24, offset);
}

inline void
AssistantServer::_marshalObjRef(AssistantServer_ptr obj, NetBufferedStream& s) {
  CORBA::MarshalObjRef(obj, _PD_repoId, 24, s);
}

inline void
AssistantServer::_marshalObjRef(AssistantServer_ptr obj, MemBufferedStream& s) {
  CORBA::MarshalObjRef(obj, _PD_repoId, 24, s);
}

#ifdef   USE_core_stub_in_nt_dll_NOT_DEFINED_AssistantServer
# undef  USE_core_stub_in_nt_dll
# undef  USE_core_stub_in_nt_dll_NOT_DEFINED_AssistantServer
#endif
#ifdef   USE_dyn_stub_in_nt_dll_NOT_DEFINED_AssistantServer
# undef  USE_dyn_stub_in_nt_dll
# undef  USE_dyn_stub_in_nt_dll_NOT_DEFINED_AssistantServer
#endif

#endif  // __AssistantServer_hh__

