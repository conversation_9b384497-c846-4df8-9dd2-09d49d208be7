// This file is generated by omniidl (C++ backend)- omniORB_3_0. Do not edit.

#include "ReqServer.h"
#include <omniORB3/tcDescriptor.h>

static const char* _0RL_library_version = omniORB_3_0;

const CORBA::TypeCode_ptr _tc_ReqServer = CORBA::TypeCode::PR_interface_tc("IDL:ReqServer:1.0", "ReqServer");

static void
_0RL_tcParser_setObjectPtr_ReqServer(tcObjrefDesc *_desc, CORBA::Object_ptr _ptr)
{
  ReqServer_ptr _p = ReqServer::_narrow(_ptr);
  ReqServer_ptr* pp = (ReqServer_ptr*)_desc->opq_objref;
  if (_desc->opq_release && !CORBA::is_nil(*pp)) CORBA::release(*pp);
  *pp = _p;
  CORBA::release(_ptr);
}

static CORBA::Object_ptr
_0RL_tcParser_getObjectPtr_ReqServer(tcObjrefDesc *_desc)
{
  return (CORBA::Object_ptr) *((ReqServer_ptr*)_desc->opq_objref);
}

void _0RL_buildDesc_cReqServer(tcDescriptor& _desc, const _CORBA_ObjRef_tcDesc_arg< _objref_ReqServer, ReqServer_Helper> & _d)
{
  _desc.p_objref.opq_objref = (void*) &_d._data;
  _desc.p_objref.opq_release = _d._rel;
  _desc.p_objref.setObjectPtr = _0RL_tcParser_setObjectPtr_ReqServer;
  _desc.p_objref.getObjectPtr = _0RL_tcParser_getObjectPtr_ReqServer;
}

void _0RL_delete_ReqServer(void* _data) {
  CORBA::release((ReqServer_ptr) _data);
}

void operator<<=(CORBA::Any& _a, ReqServer_ptr _s) {
  tcDescriptor tcd;
  _CORBA_ObjRef_tcDesc_arg< _objref_ReqServer, ReqServer_Helper>  tmp(_s,0);
  _0RL_buildDesc_cReqServer(tcd, tmp);
  _a.PR_packFrom(_tc_ReqServer, &tcd);
}

void operator<<=(CORBA::Any& _a, ReqServer_ptr* _sp) {
  _a <<= *_sp;
  CORBA::release(*_sp);
  *_sp = ReqServer::_nil();
}

CORBA::Boolean operator>>=(const CORBA::Any& _a, ReqServer_ptr& _s) {
  ReqServer_ptr sp = (ReqServer_ptr) _a.PR_getCachedData();
  if (sp == 0) {
    tcDescriptor tcd;
    ReqServer_var tmp;
    _0RL_buildDesc_cReqServer(tcd, tmp);
    if( _a.PR_unpackTo(_tc_ReqServer, &tcd) ) {
      if (!omniORB::omniORB_27_CompatibleAnyExtraction) {
        ((CORBA::Any*)&_a)->PR_setCachedData((void*)(ReqServer_ptr)tmp,_0RL_delete_ReqServer);
      }
      _s = tmp._retn();
      return 1;
    } else {
      _s = ReqServer::_nil(); return 0;
    }
  }
  else {
    CORBA::TypeCode_var tc = _a.type();
    if (tc->equivalent(_tc_ReqServer)) {
    _s = sp; return 1;
    }
    else {
    _s = ReqServer::_nil(); return 0;
    }
  }
}

