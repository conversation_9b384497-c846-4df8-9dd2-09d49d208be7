//---------------------------------------------------------------------------
// Sword2 Strategy Pattern Implementation (c) 2024
//
// File:	StrategyPattern.h
// Date:	2024.07.02
// Code:	AI Assistant
// Desc:	Modern C++ strategy pattern for algorithm selection and behavior switching
//---------------------------------------------------------------------------
#ifndef STRATEGY_PATTERN_H
#define STRATEGY_PATTERN_H

#include "ModernCpp.h"
#include <unordered_map>
#include <functional>
#include <memory>
#include <string>

namespace sword2 {

// 策略接口基类
template<typename Context, typename Result = void>
class IStrategy
{
public:
    virtual ~IStrategy() = default;
    virtual Result Execute(Context& context) = 0;
    virtual std::string GetName() const = 0;
    virtual int GetPriority() const { return 0; }
};

// 函数式策略包装器
template<typename Context, typename Result = void>
class FunctionStrategy : public IStrategy<Context, Result>
{
public:
    using StrategyFunction = std::function<Result(Context&)>;
    
    FunctionStrategy(const std::string& name, StrategyFunction func, int priority = 0)
        : m_name(name), m_function(std::move(func)), m_priority(priority) {}
    
    Result Execute(Context& context) override
    {
        return m_function(context);
    }
    
    std::string GetName() const override { return m_name; }
    int GetPriority() const override { return m_priority; }

private:
    std::string m_name;
    StrategyFunction m_function;
    int m_priority;
};

// 策略管理器
template<typename Context, typename Result = void>
class StrategyManager
{
public:
    using StrategyPtr = std::unique_ptr<IStrategy<Context, Result>>;
    using StrategyFunction = std::function<Result(Context&)>;
    
    // 注册策略
    void RegisterStrategy(const std::string& name, StrategyPtr strategy)
    {
        m_strategies[name] = std::move(strategy);
    }
    
    // 注册函数式策略
    void RegisterStrategy(const std::string& name, StrategyFunction func, int priority = 0)
    {
        auto strategy = std::make_unique<FunctionStrategy<Context, Result>>(name, std::move(func), priority);
        RegisterStrategy(name, std::move(strategy));
    }
    
    // 执行策略
    Result ExecuteStrategy(const std::string& name, Context& context)
    {
        auto it = m_strategies.find(name);
        if (it != m_strategies.end())
        {
            return it->second->Execute(context);
        }
        
        if constexpr (std::is_void_v<Result>)
        {
            throw ModernException("Strategy not found: " + name);
        }
        else
        {
            throw ModernException("Strategy not found: " + name);
        }
    }
    
    // 获取策略
    IStrategy<Context, Result>* GetStrategy(const std::string& name)
    {
        auto it = m_strategies.find(name);
        return it != m_strategies.end() ? it->second.get() : nullptr;
    }
    
    // 获取所有策略名称
    std::vector<std::string> GetStrategyNames() const
    {
        std::vector<std::string> names;
        for (const auto& pair : m_strategies)
        {
            names.push_back(pair.first);
        }
        return names;
    }
    
    // 按优先级排序的策略
    std::vector<std::pair<std::string, IStrategy<Context, Result>*>> GetStrategiesByPriority() const
    {
        std::vector<std::pair<std::string, IStrategy<Context, Result>*>> strategies;
        for (const auto& pair : m_strategies)
        {
            strategies.emplace_back(pair.first, pair.second.get());
        }
        
        std::sort(strategies.begin(), strategies.end(),
            [](const auto& a, const auto& b) {
                return a.second->GetPriority() < b.second->GetPriority();
            });
        
        return strategies;
    }
    
    // 移除策略
    bool RemoveStrategy(const std::string& name)
    {
        return m_strategies.erase(name) > 0;
    }
    
    // 清空所有策略
    void Clear()
    {
        m_strategies.clear();
    }

private:
    std::unordered_map<std::string, StrategyPtr> m_strategies;
};

// AI行为策略系统
namespace AIBehavior {

struct AIContext
{
    float health = 100.0f;
    float mana = 100.0f;
    float distanceToPlayer = 0.0f;
    bool playerVisible = false;
    std::vector<std::string> availableSkills;
    std::string currentState = "idle";
};

// AI行为策略接口
class IAIBehaviorStrategy : public IStrategy<AIContext, std::string>
{
public:
    virtual bool CanExecute(const AIContext& context) const = 0;
};

// 攻击策略
class AttackStrategy : public IAIBehaviorStrategy
{
public:
    std::string Execute(AIContext& context) override
    {
        if (context.distanceToPlayer <= 50.0f && context.playerVisible)
        {
            context.mana -= 10.0f;
            return "attacking_player";
        }
        return "moving_to_player";
    }
    
    std::string GetName() const override { return "Attack"; }
    int GetPriority() const override { return 10; }
    
    bool CanExecute(const AIContext& context) const override
    {
        return context.health > 30.0f && context.mana > 10.0f && context.playerVisible;
    }
};

// 逃跑策略
class FleeStrategy : public IAIBehaviorStrategy
{
public:
    std::string Execute(AIContext& context) override
    {
        return "fleeing_from_player";
    }
    
    std::string GetName() const override { return "Flee"; }
    int GetPriority() const override { return 20; }
    
    bool CanExecute(const AIContext& context) const override
    {
        return context.health < 30.0f;
    }
};

// 巡逻策略
class PatrolStrategy : public IAIBehaviorStrategy
{
public:
    std::string Execute(AIContext& context) override
    {
        return "patrolling";
    }
    
    std::string GetName() const override { return "Patrol"; }
    int GetPriority() const override { return 1; }
    
    bool CanExecute(const AIContext& context) const override
    {
        return !context.playerVisible;
    }
};

} // namespace AIBehavior

// 渲染策略系统
namespace Rendering {

struct RenderContext
{
    int objectCount = 0;
    float frameTime = 0.0f;
    float targetFPS = 60.0f;
    std::string qualityLevel = "high";
    bool vsyncEnabled = true;
};

// 渲染质量策略
class IRenderQualityStrategy : public IStrategy<RenderContext>
{
public:
    virtual void AdjustQuality(RenderContext& context) = 0;
    void Execute(RenderContext& context) override { AdjustQuality(context); }
};

// 高质量渲染策略
class HighQualityStrategy : public IRenderQualityStrategy
{
public:
    void AdjustQuality(RenderContext& context) override
    {
        context.qualityLevel = "high";
        // 启用所有特效
    }
    
    std::string GetName() const override { return "HighQuality"; }
};

// 性能优先策略
class PerformanceStrategy : public IRenderQualityStrategy
{
public:
    void AdjustQuality(RenderContext& context) override
    {
        context.qualityLevel = "low";
        // 禁用部分特效以提升性能
    }
    
    std::string GetName() const override { return "Performance"; }
};

// 自适应策略
class AdaptiveStrategy : public IRenderQualityStrategy
{
public:
    void AdjustQuality(RenderContext& context) override
    {
        float currentFPS = 1000.0f / context.frameTime;
        
        if (currentFPS < context.targetFPS * 0.8f)
        {
            // 降低质量
            context.qualityLevel = "medium";
        }
        else if (currentFPS > context.targetFPS * 1.2f)
        {
            // 提升质量
            context.qualityLevel = "high";
        }
    }
    
    std::string GetName() const override { return "Adaptive"; }
};

} // namespace Rendering

// 网络通信策略系统
namespace Network {

struct NetworkContext
{
    std::string protocol = "tcp";
    int latency = 0;
    float packetLoss = 0.0f;
    int bandwidth = 1000; // KB/s
    bool isReliable = true;
};

// 网络传输策略
class INetworkStrategy : public IStrategy<NetworkContext, bool>
{
public:
    virtual bool SendData(NetworkContext& context, const std::vector<uint8_t>& data) = 0;
    bool Execute(NetworkContext& context) override { return true; }
};

// TCP策略
class TCPStrategy : public INetworkStrategy
{
public:
    bool SendData(NetworkContext& context, const std::vector<uint8_t>& data) override
    {
        context.protocol = "tcp";
        context.isReliable = true;
        // TCP发送逻辑
        return true;
    }
    
    std::string GetName() const override { return "TCP"; }
};

// UDP策略
class UDPStrategy : public INetworkStrategy
{
public:
    bool SendData(NetworkContext& context, const std::vector<uint8_t>& data) override
    {
        context.protocol = "udp";
        context.isReliable = false;
        // UDP发送逻辑
        return true;
    }
    
    std::string GetName() const override { return "UDP"; }
};

} // namespace Network

// 策略选择器 - 自动选择最佳策略
template<typename Context, typename Result = void>
class StrategySelector
{
public:
    using StrategyPtr = std::unique_ptr<IStrategy<Context, Result>>;
    using SelectionCriteria = std::function<bool(const IStrategy<Context, Result>*, const Context&)>;
    
    void AddStrategy(StrategyPtr strategy)
    {
        m_strategies.push_back(std::move(strategy));
    }
    
    void SetSelectionCriteria(SelectionCriteria criteria)
    {
        m_selectionCriteria = std::move(criteria);
    }
    
    IStrategy<Context, Result>* SelectBestStrategy(const Context& context)
    {
        IStrategy<Context, Result>* bestStrategy = nullptr;
        int highestPriority = std::numeric_limits<int>::min();
        
        for (const auto& strategy : m_strategies)
        {
            if (m_selectionCriteria && !m_selectionCriteria(strategy.get(), context))
                continue;
                
            if (strategy->GetPriority() > highestPriority)
            {
                highestPriority = strategy->GetPriority();
                bestStrategy = strategy.get();
            }
        }
        
        return bestStrategy;
    }
    
    Result ExecuteBestStrategy(Context& context)
    {
        auto* strategy = SelectBestStrategy(context);
        if (strategy)
        {
            return strategy->Execute(context);
        }
        
        if constexpr (std::is_void_v<Result>)
        {
            throw ModernException("No suitable strategy found");
        }
        else
        {
            throw ModernException("No suitable strategy found");
        }
    }

private:
    std::vector<StrategyPtr> m_strategies;
    SelectionCriteria m_selectionCriteria;
};

} // namespace sword2

// 全局策略管理器
extern sword2::StrategyManager<sword2::AIBehavior::AIContext, std::string> g_AIBehaviorManager;
extern sword2::StrategyManager<sword2::Rendering::RenderContext> g_RenderStrategyManager;
extern sword2::StrategyManager<sword2::Network::NetworkContext, bool> g_NetworkStrategyManager;

// 便捷宏定义
#define REGISTER_AI_STRATEGY(name, strategy) \
    g_AIBehaviorManager.RegisterStrategy(name, std::make_unique<strategy>())

#define REGISTER_RENDER_STRATEGY(name, strategy) \
    g_RenderStrategyManager.RegisterStrategy(name, std::make_unique<strategy>())

#define REGISTER_NETWORK_STRATEGY(name, strategy) \
    g_NetworkStrategyManager.RegisterStrategy(name, std::make_unique<strategy>())

#define EXECUTE_AI_STRATEGY(name, context) \
    g_AIBehaviorManager.ExecuteStrategy(name, context)

#define EXECUTE_RENDER_STRATEGY(name, context) \
    g_RenderStrategyManager.ExecuteStrategy(name, context)

#define EXECUTE_NETWORK_STRATEGY(name, context) \
    g_NetworkStrategyManager.ExecuteStrategy(name, context)

#endif // STRATEGY_PATTERN_H
