//---------------------------------------------------------------------------
// Sword2 Lua Game API Implementation (c) 2024
//
// File:	LuaGameAPI.cpp
// Date:	2024.07.02
// Code:	AI Assistant
// Desc:	Implementation of game API functions for Lua scripts
//---------------------------------------------------------------------------

#include "LuaScriptEngine.h"
#include "PlayerManager.h"
#include "GameDataSystem.h"
#include <ctime>
#include <random>

namespace sword2 {
namespace GameAPI {

// 辅助函数：获取玩家ID参数
uint32_t GetPlayerIdFromLua(lua_State* L, int index = 1)
{
    if (lua_isnumber(L, index))
    {
        return static_cast<uint32_t>(lua_tonumber(L, index));
    }
    return 0;
}

// 辅助函数：获取玩家对象
std::shared_ptr<Player> GetPlayerFromLua(lua_State* L, int index = 1)
{
    uint32_t playerId = GetPlayerIdFromLua(L, index);
    if (playerId == 0) return nullptr;
    
    return PLAYER_MANAGER().GetOnlinePlayer(playerId);
}

// 玩家相关API实现
int GetPlayerName(lua_State* L)
{
    auto player = GetPlayerFromLua(L);
    if (player)
    {
        lua_pushstring(L, player->playerName.c_str());
    }
    else
    {
        lua_pushstring(L, "");
    }
    return 1;
}

int GetPlayerLevel(lua_State* L)
{
    auto player = GetPlayerFromLua(L);
    if (player)
    {
        lua_pushnumber(L, player->level);
    }
    else
    {
        lua_pushnumber(L, 0);
    }
    return 1;
}

int GetPlayerMoney(lua_State* L)
{
    auto player = GetPlayerFromLua(L);
    if (player)
    {
        lua_pushnumber(L, static_cast<double>(player->money));
    }
    else
    {
        lua_pushnumber(L, 0);
    }
    return 1;
}

int SetPlayerMoney(lua_State* L)
{
    auto player = GetPlayerFromLua(L, 1);
    if (player && lua_isnumber(L, 2))
    {
        uint64_t money = static_cast<uint64_t>(lua_tonumber(L, 2));
        player->money = money;
        lua_pushboolean(L, 1);
        
        LOG_INFO("LUA_API", "Set player " + player->playerName + " money to " + std::to_string(money));
    }
    else
    {
        lua_pushboolean(L, 0);
    }
    return 1;
}

int AddPlayerExp(lua_State* L)
{
    auto player = GetPlayerFromLua(L, 1);
    if (player && lua_isnumber(L, 2))
    {
        uint64_t exp = static_cast<uint64_t>(lua_tonumber(L, 2));
        bool leveledUp = player->AddExperience(exp);
        lua_pushboolean(L, leveledUp ? 1 : 0);
        
        LOG_INFO("LUA_API", "Added " + std::to_string(exp) + " exp to player " + player->playerName);
    }
    else
    {
        lua_pushboolean(L, 0);
    }
    return 1;
}

int GetPlayerPosition(lua_State* L)
{
    auto player = GetPlayerFromLua(L);
    if (player)
    {
        lua_pushnumber(L, player->position.mapId);
        lua_pushnumber(L, player->position.x);
        lua_pushnumber(L, player->position.y);
        return 3;
    }
    else
    {
        lua_pushnumber(L, 0);
        lua_pushnumber(L, 0);
        lua_pushnumber(L, 0);
        return 3;
    }
}

int SetPlayerPosition(lua_State* L)
{
    auto player = GetPlayerFromLua(L, 1);
    if (player && lua_isnumber(L, 2) && lua_isnumber(L, 3) && lua_isnumber(L, 4))
    {
        uint32_t mapId = static_cast<uint32_t>(lua_tonumber(L, 2));
        int32_t x = static_cast<int32_t>(lua_tonumber(L, 3));
        int32_t y = static_cast<int32_t>(lua_tonumber(L, 4));
        
        player->SetPosition(mapId, x, y);
        lua_pushboolean(L, 1);
        
        LOG_INFO("LUA_API", "Set player " + player->playerName + " position to map " + 
                std::to_string(mapId) + " (" + std::to_string(x) + ", " + std::to_string(y) + ")");
    }
    else
    {
        lua_pushboolean(L, 0);
    }
    return 1;
}

int TeleportPlayer(lua_State* L)
{
    auto player = GetPlayerFromLua(L, 1);
    if (player && lua_isnumber(L, 2) && lua_isnumber(L, 3) && lua_isnumber(L, 4))
    {
        uint32_t mapId = static_cast<uint32_t>(lua_tonumber(L, 2));
        int32_t x = static_cast<int32_t>(lua_tonumber(L, 3));
        int32_t y = static_cast<int32_t>(lua_tonumber(L, 4));
        
        bool success = player->TeleportTo(mapId, x, y);
        lua_pushboolean(L, success ? 1 : 0);
        
        if (success)
        {
            LOG_INFO("LUA_API", "Teleported player " + player->playerName + " to map " + 
                    std::to_string(mapId) + " (" + std::to_string(x) + ", " + std::to_string(y) + ")");
        }
    }
    else
    {
        lua_pushboolean(L, 0);
    }
    return 1;
}

// 物品相关API实现
int GiveItem(lua_State* L)
{
    auto player = GetPlayerFromLua(L, 1);
    if (player && lua_isnumber(L, 2) && lua_isnumber(L, 3))
    {
        uint32_t itemId = static_cast<uint32_t>(lua_tonumber(L, 2));
        uint32_t count = static_cast<uint32_t>(lua_tonumber(L, 3));
        
        // 这里应该实现实际的物品给予逻辑
        // 暂时返回成功
        lua_pushboolean(L, 1);
        
        LOG_INFO("LUA_API", "Gave item " + std::to_string(itemId) + " x" + 
                std::to_string(count) + " to player " + player->playerName);
    }
    else
    {
        lua_pushboolean(L, 0);
    }
    return 1;
}

int RemoveItem(lua_State* L)
{
    auto player = GetPlayerFromLua(L, 1);
    if (player && lua_isnumber(L, 2) && lua_isnumber(L, 3))
    {
        uint32_t itemId = static_cast<uint32_t>(lua_tonumber(L, 2));
        uint32_t count = static_cast<uint32_t>(lua_tonumber(L, 3));
        
        // 这里应该实现实际的物品移除逻辑
        // 暂时返回成功
        lua_pushboolean(L, 1);
        
        LOG_INFO("LUA_API", "Removed item " + std::to_string(itemId) + " x" + 
                std::to_string(count) + " from player " + player->playerName);
    }
    else
    {
        lua_pushboolean(L, 0);
    }
    return 1;
}

int GetItemCount(lua_State* L)
{
    auto player = GetPlayerFromLua(L, 1);
    if (player && lua_isnumber(L, 2))
    {
        uint32_t itemId = static_cast<uint32_t>(lua_tonumber(L, 2));
        
        // 这里应该实现实际的物品数量查询逻辑
        // 暂时返回0
        lua_pushnumber(L, 0);
    }
    else
    {
        lua_pushnumber(L, 0);
    }
    return 1;
}

int HasItem(lua_State* L)
{
    auto player = GetPlayerFromLua(L, 1);
    if (player && lua_isnumber(L, 2))
    {
        uint32_t itemId = static_cast<uint32_t>(lua_tonumber(L, 2));
        
        // 这里应该实现实际的物品检查逻辑
        // 暂时返回false
        lua_pushboolean(L, 0);
    }
    else
    {
        lua_pushboolean(L, 0);
    }
    return 1;
}

// 任务相关API实现
int StartMission(lua_State* L)
{
    auto player = GetPlayerFromLua(L, 1);
    if (player && lua_isnumber(L, 2))
    {
        uint32_t missionId = static_cast<uint32_t>(lua_tonumber(L, 2));
        
        // 这里应该实现实际的任务开始逻辑
        lua_pushboolean(L, 1);
        
        LOG_INFO("LUA_API", "Started mission " + std::to_string(missionId) + 
                " for player " + player->playerName);
    }
    else
    {
        lua_pushboolean(L, 0);
    }
    return 1;
}

int CompleteMission(lua_State* L)
{
    auto player = GetPlayerFromLua(L, 1);
    if (player && lua_isnumber(L, 2))
    {
        uint32_t missionId = static_cast<uint32_t>(lua_tonumber(L, 2));
        
        // 这里应该实现实际的任务完成逻辑
        lua_pushboolean(L, 1);
        
        LOG_INFO("LUA_API", "Completed mission " + std::to_string(missionId) + 
                " for player " + player->playerName);
    }
    else
    {
        lua_pushboolean(L, 0);
    }
    return 1;
}

int GetMissionStatus(lua_State* L)
{
    auto player = GetPlayerFromLua(L, 1);
    if (player && lua_isnumber(L, 2))
    {
        uint32_t missionId = static_cast<uint32_t>(lua_tonumber(L, 2));
        
        // 这里应该实现实际的任务状态查询逻辑
        // 0=未接受, 1=进行中, 2=已完成
        lua_pushnumber(L, 0);
    }
    else
    {
        lua_pushnumber(L, 0);
    }
    return 1;
}

int SetTaskValue(lua_State* L)
{
    auto player = GetPlayerFromLua(L, 1);
    if (player && lua_isnumber(L, 2) && lua_isnumber(L, 3))
    {
        uint32_t taskId = static_cast<uint32_t>(lua_tonumber(L, 2));
        int32_t value = static_cast<int32_t>(lua_tonumber(L, 3));
        
        // 这里应该实现实际的任务变量设置逻辑
        lua_pushboolean(L, 1);
        
        LOG_DEBUG("LUA_API", "Set task " + std::to_string(taskId) + " = " + 
                 std::to_string(value) + " for player " + player->playerName);
    }
    else
    {
        lua_pushboolean(L, 0);
    }
    return 1;
}

int GetTaskValue(lua_State* L)
{
    auto player = GetPlayerFromLua(L, 1);
    if (player && lua_isnumber(L, 2))
    {
        uint32_t taskId = static_cast<uint32_t>(lua_tonumber(L, 2));
        
        // 这里应该实现实际的任务变量获取逻辑
        lua_pushnumber(L, 0);
    }
    else
    {
        lua_pushnumber(L, 0);
    }
    return 1;
}

// 对话相关API实现
int Talk(lua_State* L)
{
    if (lua_isstring(L, 1))
    {
        std::string message = lua_tostring(L, 1);
        
        // 这里应该实现实际的对话逻辑
        LOG_INFO("LUA_API", "Talk: " + message);
        lua_pushboolean(L, 1);
    }
    else
    {
        lua_pushboolean(L, 0);
    }
    return 1;
}

int Say(lua_State* L)
{
    if (lua_isstring(L, 1))
    {
        std::string message = lua_tostring(L, 1);
        
        // 这里应该实现实际的说话逻辑
        LOG_INFO("LUA_API", "Say: " + message);
        lua_pushboolean(L, 1);
    }
    else
    {
        lua_pushboolean(L, 0);
    }
    return 1;
}

int Ask(lua_State* L)
{
    if (lua_isstring(L, 1))
    {
        std::string question = lua_tostring(L, 1);
        
        // 这里应该实现实际的询问逻辑
        LOG_INFO("LUA_API", "Ask: " + question);
        lua_pushstring(L, "yes"); // 默认回答
    }
    else
    {
        lua_pushstring(L, "");
    }
    return 1;
}

int SelectOption(lua_State* L)
{
    if (lua_isnumber(L, 1))
    {
        int option = static_cast<int>(lua_tonumber(L, 1));
        
        // 这里应该实现实际的选项选择逻辑
        LOG_INFO("LUA_API", "Selected option: " + std::to_string(option));
        lua_pushboolean(L, 1);
    }
    else
    {
        lua_pushboolean(L, 0);
    }
    return 1;
}

// 系统相关API实现
int WriteLog(lua_State* L)
{
    if (lua_isstring(L, 1))
    {
        std::string message = lua_tostring(L, 1);
        LOG_INFO("LUA_SCRIPT", message);
        lua_pushboolean(L, 1);
    }
    else
    {
        lua_pushboolean(L, 0);
    }
    return 1;
}

int GetCurrentTime(lua_State* L)
{
    std::time_t currentTime = std::time(nullptr);
    lua_pushnumber(L, static_cast<double>(currentTime));
    return 1;
}

int Random(lua_State* L)
{
    static std::random_device rd;
    static std::mt19937 gen(rd());
    
    if (lua_isnumber(L, 1) && lua_isnumber(L, 2))
    {
        int min = static_cast<int>(lua_tonumber(L, 1));
        int max = static_cast<int>(lua_tonumber(L, 2));
        
        std::uniform_int_distribution<> dis(min, max);
        lua_pushnumber(L, dis(gen));
    }
    else if (lua_isnumber(L, 1))
    {
        int max = static_cast<int>(lua_tonumber(L, 1));
        std::uniform_int_distribution<> dis(1, max);
        lua_pushnumber(L, dis(gen));
    }
    else
    {
        std::uniform_real_distribution<> dis(0.0, 1.0);
        lua_pushnumber(L, dis(gen));
    }
    return 1;
}

int Broadcast(lua_State* L)
{
    if (lua_isstring(L, 1))
    {
        std::string message = lua_tostring(L, 1);
        BROADCAST_MESSAGE(message);
        lua_pushboolean(L, 1);
    }
    else
    {
        lua_pushboolean(L, 0);
    }
    return 1;
}

// 地图相关API实现
int GetMapId(lua_State* L)
{
    auto player = GetPlayerFromLua(L);
    if (player)
    {
        lua_pushnumber(L, player->position.mapId);
    }
    else
    {
        lua_pushnumber(L, 0);
    }
    return 1;
}

int GetMapName(lua_State* L)
{
    if (lua_isnumber(L, 1))
    {
        uint32_t mapId = static_cast<uint32_t>(lua_tonumber(L, 1));
        
        // 这里应该从地图数据中获取地图名称
        lua_pushstring(L, ("Map_" + std::to_string(mapId)).c_str());
    }
    else
    {
        lua_pushstring(L, "");
    }
    return 1;
}

int CreateNPC(lua_State* L)
{
    if (lua_isnumber(L, 1) && lua_isnumber(L, 2) && lua_isnumber(L, 3) && lua_isnumber(L, 4))
    {
        uint32_t npcId = static_cast<uint32_t>(lua_tonumber(L, 1));
        uint32_t mapId = static_cast<uint32_t>(lua_tonumber(L, 2));
        int32_t x = static_cast<int32_t>(lua_tonumber(L, 3));
        int32_t y = static_cast<int32_t>(lua_tonumber(L, 4));
        
        // 这里应该实现实际的NPC创建逻辑
        LOG_INFO("LUA_API", "Created NPC " + std::to_string(npcId) + " at map " + 
                std::to_string(mapId) + " (" + std::to_string(x) + ", " + std::to_string(y) + ")");
        lua_pushboolean(L, 1);
    }
    else
    {
        lua_pushboolean(L, 0);
    }
    return 1;
}

int RemoveNPC(lua_State* L)
{
    if (lua_isnumber(L, 1))
    {
        uint32_t npcId = static_cast<uint32_t>(lua_tonumber(L, 1));
        
        // 这里应该实现实际的NPC移除逻辑
        LOG_INFO("LUA_API", "Removed NPC " + std::to_string(npcId));
        lua_pushboolean(L, 1);
    }
    else
    {
        lua_pushboolean(L, 0);
    }
    return 1;
}

// 战斗相关API实现
int GetPlayerHP(lua_State* L)
{
    auto player = GetPlayerFromLua(L);
    if (player)
    {
        lua_pushnumber(L, player->attributes.currentLife);
        lua_pushnumber(L, player->attributes.maxLife);
        return 2;
    }
    else
    {
        lua_pushnumber(L, 0);
        lua_pushnumber(L, 0);
        return 2;
    }
}

int SetPlayerHP(lua_State* L)
{
    auto player = GetPlayerFromLua(L, 1);
    if (player && lua_isnumber(L, 2))
    {
        uint32_t hp = static_cast<uint32_t>(lua_tonumber(L, 2));
        player->attributes.currentLife = std::min(hp, player->attributes.maxLife);
        lua_pushboolean(L, 1);
    }
    else
    {
        lua_pushboolean(L, 0);
    }
    return 1;
}

int GetPlayerMP(lua_State* L)
{
    auto player = GetPlayerFromLua(L);
    if (player)
    {
        lua_pushnumber(L, player->attributes.currentMana);
        lua_pushnumber(L, player->attributes.maxMana);
        return 2;
    }
    else
    {
        lua_pushnumber(L, 0);
        lua_pushnumber(L, 0);
        return 2;
    }
}

int SetPlayerMP(lua_State* L)
{
    auto player = GetPlayerFromLua(L, 1);
    if (player && lua_isnumber(L, 2))
    {
        uint32_t mp = static_cast<uint32_t>(lua_tonumber(L, 2));
        player->attributes.currentMana = std::min(mp, player->attributes.maxMana);
        lua_pushboolean(L, 1);
    }
    else
    {
        lua_pushboolean(L, 0);
    }
    return 1;
}

int DamagePlayer(lua_State* L)
{
    auto player = GetPlayerFromLua(L, 1);
    if (player && lua_isnumber(L, 2))
    {
        uint32_t damage = static_cast<uint32_t>(lua_tonumber(L, 2));
        
        if (player->attributes.currentLife > damage)
        {
            player->attributes.currentLife -= damage;
        }
        else
        {
            player->attributes.currentLife = 0;
        }
        
        lua_pushboolean(L, 1);
        LOG_INFO("LUA_API", "Damaged player " + player->playerName + " for " + std::to_string(damage));
    }
    else
    {
        lua_pushboolean(L, 0);
    }
    return 1;
}

int HealPlayer(lua_State* L)
{
    auto player = GetPlayerFromLua(L, 1);
    if (player && lua_isnumber(L, 2))
    {
        uint32_t heal = static_cast<uint32_t>(lua_tonumber(L, 2));
        player->attributes.RestoreLife(heal);
        
        lua_pushboolean(L, 1);
        LOG_INFO("LUA_API", "Healed player " + player->playerName + " for " + std::to_string(heal));
    }
    else
    {
        lua_pushboolean(L, 0);
    }
    return 1;
}

} // namespace GameAPI
} // namespace sword2
