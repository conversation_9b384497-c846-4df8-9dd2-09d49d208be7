// SockThread.cpp: implementation of the CSockThread class.
//
//////////////////////////////////////////////////////////////////////

#include "stdafx.h"
#include "SockThread.h"
#include "S3Relay.h"
#include "Global.h"
#include "../../Engine/Src/KGLog.h"


//////////////////////////////////////////////////////////////////////
// Construction/Destruction
//////////////////////////////////////////////////////////////////////

CSockThread::CSockThread()
	: m_step(step_NONE), m_cnt<PERSON><PERSON>(0), m_tick<PERSON>oop(0)
{

}

CSockThread::~CSockThread()
{

}


DWORD CSockThread::Main(LPVOID lpParam)
{
	try
	{
		DEBUGDO( m_step = step_NONE );
		DEBUGDO( m_tickLoop = 0 );
		DEBUGDO( m_cntLoop = 0 );


		DEBUGDO( m_step = step_EnterLoop);
		EnterLoop();

		// 优化的服务器主循环
		DWORD dwLastTime = ::GetTickCount();
		DWORD dwFrameCount = 0;
		DWORD dwTotalProcessTime = 0;
		const DWORD TARGET_FRAME_TIME = breathe_interval;

		while (!IsAskingStop())
		{
			DWORD dwFrameStart = ::GetTickCount();
			DEBUGDO( m_tickLoop = dwFrameStart );
			DEBUGDO( ++ m_cntLoop );

			DEBUGDO( m_step = step_PrepareSock);
			PrepareSock();

			// 批量处理网络路由 - 按优先级排序
			DEBUGDO( m_step = step_RelayCenter);
			g_RelayCenter.Route();

			DEBUGDO( m_step = step_RelayServer);
			g_RelayServer.Route();

			// 核心服务器处理
			DEBUGDO( m_step = step_RootCenter);
			g_RootCenter.Route();

			DEBUGDO( m_step = step_GatewayCenter);
			g_GatewayCenter.Route();

			// 游戏服务器处理 - 可以并行化
			DEBUGDO( m_step = step_HostServer);
			g_HostServer.Route();

			DEBUGDO( m_step = step_TongServer);
			g_TongServer.Route();

			DEBUGDO( m_step = step_ChatServer);
			g_ChatServer.Route();

			DEBUGDO( m_step = step_UnprepareSock);
			UnprepareSock();

			// 智能休眠策略
			DWORD dwFrameEnd = ::GetTickCount();
			DWORD dwProcessTime = dwFrameEnd - dwFrameStart;
			dwTotalProcessTime += dwProcessTime;
			dwFrameCount++;

			DEBUGDO( m_step = step_Sleep);

			// 动态调整休眠时间
			if (dwProcessTime < TARGET_FRAME_TIME)
			{
				DWORD dwSleepTime = TARGET_FRAME_TIME - dwProcessTime;

				// 根据负载调整休眠时间
				if (dwFrameCount % 100 == 0) // 每100帧统计一次
				{
					DWORD dwAvgProcessTime = dwTotalProcessTime / dwFrameCount;
					if (dwAvgProcessTime > TARGET_FRAME_TIME / 2)
					{
						// 高负载，减少休眠
						dwSleepTime = max(1, dwSleepTime / 2);
					}

					// 重置统计
					dwTotalProcessTime = 0;
					dwFrameCount = 0;
				}

				::Sleep(dwSleepTime);
			}
			else
			{
				// 处理时间超过目标，立即让出CPU
				::Sleep(1);
			}
		}

		DEBUGDO( m_step = step_LeaveLoop);
		LeaveLoop();
	}
	catch (...)
	{
		KGLogPrintf(LOG_INFO,"fatal: sock thread except\n");
	}

	return 0;
}


HANDLE CSockThread::Start()
{
	if (m_hProcessor != NULL)
		return m_hProcessor;

	m_step = step_NONE;
	m_cntLoop = 0;
	m_tickLoop = 0;

	HANDLE reth = KThread::Start();
	if (!reth)
	{
		KGLogPrintf(LOG_INFO,"FAIL: SockThread start\n");
		return NULL;
	}

	KGLogPrintf(LOG_INFO,"SockThread start success\n");

	return reth;
}

BOOL CSockThread::Stop()
{
	if (!m_hProcessor)
		return TRUE;

	if (!KThread::Stop())
	{
		KGLogPrintf(LOG_INFO,"FAIL: SockThread stop\n");
		return FALSE;
	}

	m_step = step_NONE;
	m_cntLoop = 0;
	m_tickLoop = 0;

	KGLogPrintf(LOG_INFO,"SockThread stop success\n");

	return TRUE;
}


BOOL CSockThread::TraceInfo()
{
	static const char* stepdesc[] = {
			"NONE",
			"EnterLoop",
			"PrepareSock",
			"RelayCenter", 
			"RelayServer",
			"RootCenter",
			"GatewayCenter",
			"HostServer",
			"TongServer",
			"ChatServer",
			"UnprepareSock",
			"LeaveLoop",
			"Sleep",
	};

	KGLogPrintf(LOG_INFO,"message: [SockThread] tickLoop: %d, cntLoop: %d, step: %s\n", m_tickLoop, m_cntLoop, stepdesc[m_step]);

	return TRUE;
}
