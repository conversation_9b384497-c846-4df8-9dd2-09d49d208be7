//---------------------------------------------------------------------------
// Sword3 Engine (c) 1999-2000 by Kingsoft
//
// File:	KMp3HufTable.h
// Desc:	MP3 Decoder originally Copyright (C) 1995-1997 Xing Technology
//			Corp.  http://www.xingtech.com
//			Hu<PERSON>man Decode Table
//---------------------------------------------------------------------------
#ifndef KMp3HufTable_H
#define KMp3HufTable_H
//---------------------------------------------------------------------------
// huffman lookup tables
typedef union
{
	int ptr;
	struct
	{
		unsigned char signbits;
		unsigned char x;
		unsigned char y;
		unsigned char purgebits;	// 0 = esc
	}
	b;
}
HUFF_ELEMENT;

//---------------------------------------------------------------------------
/* TABLE  0    2 entries  maxbits  1  linbits  0 */
/* dummy must not use */
static HUFF_ELEMENT huff_table_0[4] = 
{
	{0}, {0}, {0}, {64}
};

/* TABLE  1    4 entries  maxbits  3  linbits  0 */
static HUFF_ELEMENT huff_table_1[] =
{
	{0xFF000003}, {0x03010102}, {0x03010001}, {0x02000101}, {0x02000101},	/*  4 */
	{0x01000000}, {0x01000000}, {0x01000000}, {0x01000000},
};

/* max table bits  3 */

/* TABLE  2    9 entries  maxbits  6  linbits  0 */
static HUFF_ELEMENT huff_table_2[] =
{
	{0xFF000006}, {0x06020202}, {0x06020001}, {0x05020102}, {0x05020102},	/*  4 */
	{0x05010202}, {0x05010202}, {0x05000201}, {0x05000201}, {0x03010102},	/*  9 */
	{0x03010102}, {0x03010102}, {0x03010102}, {0x03010102}, {0x03010102},	/* 14 */
	{0x03010102}, {0x03010102}, {0x03010001}, {0x03010001}, {0x03010001},	/* 19 */
	{0x03010001}, {0x03010001}, {0x03010001}, {0x03010001}, {0x03010001},	/* 24 */
	{0x03000101}, {0x03000101}, {0x03000101}, {0x03000101}, {0x03000101},	/* 29 */
	{0x03000101}, {0x03000101}, {0x03000101}, {0x01000000}, {0x01000000},	/* 34 */
	{0x01000000}, {0x01000000}, {0x01000000}, {0x01000000}, {0x01000000},	/* 39 */
	{0x01000000}, {0x01000000}, {0x01000000}, {0x01000000}, {0x01000000},	/* 44 */
	{0x01000000}, {0x01000000}, {0x01000000}, {0x01000000}, {0x01000000},	/* 49 */
	{0x01000000}, {0x01000000}, {0x01000000}, {0x01000000}, {0x01000000},	/* 54 */
	{0x01000000}, {0x01000000}, {0x01000000}, {0x01000000}, {0x01000000},	/* 59 */
	{0x01000000}, {0x01000000}, {0x01000000}, {0x01000000}, {0x01000000},	/* 64 */
};

/* max table bits  6 */

/* TABLE  3    9 entries  maxbits  6  linbits  0 */
static HUFF_ELEMENT huff_table_3[] =
{
	{0xFF000006}, {0x06020202}, {0x06020001}, {0x05020102}, {0x05020102},	/*  4 */
	{0x05010202}, {0x05010202}, {0x05000201}, {0x05000201}, {0x03000101},	/*  9 */
	{0x03000101}, {0x03000101}, {0x03000101}, {0x03000101}, {0x03000101},	/* 14 */
	{0x03000101}, {0x03000101}, {0x02010102}, {0x02010102}, {0x02010102},	/* 19 */
	{0x02010102}, {0x02010102}, {0x02010102}, {0x02010102}, {0x02010102},	/* 24 */
	{0x02010102}, {0x02010102}, {0x02010102}, {0x02010102}, {0x02010102},	/* 29 */
	{0x02010102}, {0x02010102}, {0x02010102}, {0x02010001}, {0x02010001},	/* 34 */
	{0x02010001}, {0x02010001}, {0x02010001}, {0x02010001}, {0x02010001},	/* 39 */
	{0x02010001}, {0x02010001}, {0x02010001}, {0x02010001}, {0x02010001},	/* 44 */
	{0x02010001}, {0x02010001}, {0x02010001}, {0x02010001}, {0x02000000},	/* 49 */
	{0x02000000}, {0x02000000}, {0x02000000}, {0x02000000}, {0x02000000},	/* 54 */
	{0x02000000}, {0x02000000}, {0x02000000}, {0x02000000}, {0x02000000},	/* 59 */
	{0x02000000}, {0x02000000}, {0x02000000}, {0x02000000}, {0x02000000}, /* 64 */
};

/* max table bits  6 */
/* NO XING TABLE 4 */

/* TABLE  5   16 entries  maxbits  8  linbits  0 */
static HUFF_ELEMENT huff_table_5[] =
{
	{0xFF000008}, {0x08030302}, {0x08030202}, {0x07020302}, {0x07020302},	/*  4 */
	{0x06010302}, {0x06010302}, {0x06010302}, {0x06010302}, {0x07030102},	/*  9 */
	{0x07030102}, {0x07030001}, {0x07030001}, {0x07000301}, {0x07000301},	/* 14 */
	{0x07020202}, {0x07020202}, {0x06020102}, {0x06020102}, {0x06020102},	/* 19 */
	{0x06020102}, {0x06010202}, {0x06010202}, {0x06010202}, {0x06010202},	/* 24 */
	{0x06020001}, {0x06020001}, {0x06020001}, {0x06020001}, {0x06000201},	/* 29 */
	{0x06000201}, {0x06000201}, {0x06000201}, {0x03010102}, {0x03010102},	/* 34 */
	{0x03010102}, {0x03010102}, {0x03010102}, {0x03010102}, {0x03010102},	/* 39 */
	{0x03010102}, {0x03010102}, {0x03010102}, {0x03010102}, {0x03010102},	/* 44 */
	{0x03010102}, {0x03010102}, {0x03010102}, {0x03010102}, {0x03010102},	/* 49 */
	{0x03010102}, {0x03010102}, {0x03010102}, {0x03010102}, {0x03010102},	/* 54 */
	{0x03010102}, {0x03010102}, {0x03010102}, {0x03010102}, {0x03010102},	/* 59 */
	{0x03010102}, {0x03010102}, {0x03010102}, {0x03010102}, {0x03010102},	/* 64 */
	{0x03010001}, {0x03010001}, {0x03010001}, {0x03010001}, {0x03010001},	/* 69 */
	{0x03010001}, {0x03010001}, {0x03010001}, {0x03010001}, {0x03010001},	/* 74 */
	{0x03010001}, {0x03010001}, {0x03010001}, {0x03010001}, {0x03010001},	/* 79 */
	{0x03010001}, {0x03010001}, {0x03010001}, {0x03010001}, {0x03010001},	/* 84 */
	{0x03010001}, {0x03010001}, {0x03010001}, {0x03010001}, {0x03010001},	/* 89 */
	{0x03010001}, {0x03010001}, {0x03010001}, {0x03010001}, {0x03010001},	/* 94 */
	{0x03010001}, {0x03010001}, {0x03000101}, {0x03000101}, {0x03000101},	/* 99 */
	{0x03000101}, {0x03000101}, {0x03000101}, {0x03000101}, {0x03000101},	/* 104 */
	{0x03000101}, {0x03000101}, {0x03000101}, {0x03000101}, {0x03000101},	/* 109 */
	{0x03000101}, {0x03000101}, {0x03000101}, {0x03000101}, {0x03000101},	/* 114 */
	{0x03000101}, {0x03000101}, {0x03000101}, {0x03000101}, {0x03000101},	/* 119 */
	{0x03000101}, {0x03000101}, {0x03000101}, {0x03000101}, {0x03000101},	/* 124 */
	{0x03000101}, {0x03000101}, {0x03000101}, {0x03000101}, {0x01000000},	/* 129 */
	{0x01000000}, {0x01000000}, {0x01000000}, {0x01000000}, {0x01000000},	/* 134 */
	{0x01000000}, {0x01000000}, {0x01000000}, {0x01000000}, {0x01000000},	/* 139 */
	{0x01000000}, {0x01000000}, {0x01000000}, {0x01000000}, {0x01000000},	/* 144 */
	{0x01000000}, {0x01000000}, {0x01000000}, {0x01000000}, {0x01000000},	/* 149 */
	{0x01000000}, {0x01000000}, {0x01000000}, {0x01000000}, {0x01000000},	/* 154 */
	{0x01000000}, {0x01000000}, {0x01000000}, {0x01000000}, {0x01000000},	/* 159 */
	{0x01000000}, {0x01000000}, {0x01000000}, {0x01000000}, {0x01000000},	/* 164 */
	{0x01000000}, {0x01000000}, {0x01000000}, {0x01000000}, {0x01000000},	/* 169 */
	{0x01000000}, {0x01000000}, {0x01000000}, {0x01000000}, {0x01000000},	/* 174 */
	{0x01000000}, {0x01000000}, {0x01000000}, {0x01000000}, {0x01000000},	/* 179 */
	{0x01000000}, {0x01000000}, {0x01000000}, {0x01000000}, {0x01000000},	/* 184 */
	{0x01000000}, {0x01000000}, {0x01000000}, {0x01000000}, {0x01000000},	/* 189 */
	{0x01000000}, {0x01000000}, {0x01000000}, {0x01000000}, {0x01000000},	/* 194 */
	{0x01000000}, {0x01000000}, {0x01000000}, {0x01000000}, {0x01000000},	/* 199 */
	{0x01000000}, {0x01000000}, {0x01000000}, {0x01000000}, {0x01000000},	/* 204 */
	{0x01000000}, {0x01000000}, {0x01000000}, {0x01000000}, {0x01000000},	/* 209 */
	{0x01000000}, {0x01000000}, {0x01000000}, {0x01000000}, {0x01000000},	/* 214 */
	{0x01000000}, {0x01000000}, {0x01000000}, {0x01000000}, {0x01000000},	/* 219 */
	{0x01000000}, {0x01000000}, {0x01000000}, {0x01000000}, {0x01000000},	/* 224 */
	{0x01000000}, {0x01000000}, {0x01000000}, {0x01000000}, {0x01000000},	/* 229 */
	{0x01000000}, {0x01000000}, {0x01000000}, {0x01000000}, {0x01000000},	/* 234 */
	{0x01000000}, {0x01000000}, {0x01000000}, {0x01000000}, {0x01000000},	/* 239 */
	{0x01000000}, {0x01000000}, {0x01000000}, {0x01000000}, {0x01000000},	/* 244 */
	{0x01000000}, {0x01000000}, {0x01000000}, {0x01000000}, {0x01000000},	/* 249 */
	{0x01000000}, {0x01000000}, {0x01000000}, {0x01000000}, {0x01000000},	/* 254 */
	{0x01000000}, {0x01000000},
};

/* max table bits  8 */

/* TABLE  6   16 entries  maxbits  7  linbits  0 */
static HUFF_ELEMENT huff_table_6[] =
{
	{0xFF000007}, {0x07030302}, {0x07030001}, {0x06030202}, {0x06030202},	/*  4 */
	{0x06020302}, {0x06020302}, {0x06000301}, {0x06000301}, {0x05030102},	/*  9 */
	{0x05030102}, {0x05030102}, {0x05030102}, {0x05010302}, {0x05010302},	/* 14 */
	{0x05010302}, {0x05010302}, {0x05020202}, {0x05020202}, {0x05020202},	/* 19 */
	{0x05020202}, {0x05020001}, {0x05020001}, {0x05020001}, {0x05020001},	/* 24 */
	{0x04020102}, {0x04020102}, {0x04020102}, {0x04020102}, {0x04020102},	/* 29 */
	{0x04020102}, {0x04020102}, {0x04020102}, {0x04010202}, {0x04010202},	/* 34 */
	{0x04010202}, {0x04010202}, {0x04010202}, {0x04010202}, {0x04010202},	/* 39 */
	{0x04010202}, {0x04000201}, {0x04000201}, {0x04000201}, {0x04000201},	/* 44 */
	{0x04000201}, {0x04000201}, {0x04000201}, {0x04000201}, {0x03010001},	/* 49 */
	{0x03010001}, {0x03010001}, {0x03010001}, {0x03010001}, {0x03010001},	/* 54 */
	{0x03010001}, {0x03010001}, {0x03010001}, {0x03010001}, {0x03010001},	/* 59 */
	{0x03010001}, {0x03010001}, {0x03010001}, {0x03010001}, {0x03010001},	/* 64 */
	{0x02010102}, {0x02010102}, {0x02010102}, {0x02010102}, {0x02010102},	/* 69 */
	{0x02010102}, {0x02010102}, {0x02010102}, {0x02010102}, {0x02010102},	/* 74 */
	{0x02010102}, {0x02010102}, {0x02010102}, {0x02010102}, {0x02010102},	/* 79 */
	{0x02010102}, {0x02010102}, {0x02010102}, {0x02010102}, {0x02010102},	/* 84 */
	{0x02010102}, {0x02010102}, {0x02010102}, {0x02010102}, {0x02010102},	/* 89 */
	{0x02010102}, {0x02010102}, {0x02010102}, {0x02010102}, {0x02010102},	/* 94 */
	{0x02010102}, {0x02010102}, {0x03000101}, {0x03000101}, {0x03000101},	/* 99 */
	{0x03000101}, {0x03000101}, {0x03000101}, {0x03000101}, {0x03000101},	/* 104 */
	{0x03000101}, {0x03000101}, {0x03000101}, {0x03000101}, {0x03000101},	/* 109 */
	{0x03000101}, {0x03000101}, {0x03000101}, {0x03000000}, {0x03000000},	/* 114 */
	{0x03000000}, {0x03000000}, {0x03000000}, {0x03000000}, {0x03000000},	/* 119 */
	{0x03000000}, {0x03000000}, {0x03000000}, {0x03000000}, {0x03000000},	/* 124 */
	{0x03000000}, {0x03000000}, {0x03000000}, {0x03000000},
};

/* max table bits  7 */

/* TABLE  7   36 entries  maxbits 10  linbits  0 */
static HUFF_ELEMENT huff_table_7[] =
{
	{0xFF000006}, {0x00000041}, {0x00000052}, {0x0000005B}, {0x00000060},	/*  4 */
	{0x00000063}, {0x00000068}, {0x0000006B}, {0x06020102}, {0x05010202},	/*  9 */
	{0x05010202}, {0x06020001}, {0x06000201}, {0x04010102}, {0x04010102},	/* 14 */
	{0x04010102}, {0x04010102}, {0x03010001}, {0x03010001}, {0x03010001},	/* 19 */
	{0x03010001}, {0x03010001}, {0x03010001}, {0x03010001}, {0x03010001},	/* 24 */
	{0x03000101}, {0x03000101}, {0x03000101}, {0x03000101}, {0x03000101},	/* 29 */
	{0x03000101}, {0x03000101}, {0x03000101}, {0x01000000}, {0x01000000},	/* 34 */
	{0x01000000}, {0x01000000}, {0x01000000}, {0x01000000}, {0x01000000},	/* 39 */
	{0x01000000}, {0x01000000}, {0x01000000}, {0x01000000}, {0x01000000},	/* 44 */
	{0x01000000}, {0x01000000}, {0x01000000}, {0x01000000}, {0x01000000},	/* 49 */
	{0x01000000}, {0x01000000}, {0x01000000}, {0x01000000}, {0x01000000},	/* 54 */
	{0x01000000}, {0x01000000}, {0x01000000}, {0x01000000}, {0x01000000},	/* 59 */
	{0x01000000}, {0x01000000}, {0x01000000}, {0x01000000}, {0x01000000},	/* 64 */
	{0xFF000004}, {0x04050502}, {0x04050402}, {0x04040502}, {0x04030502},	/* 69 */
	{0x03050302}, {0x03050302}, {0x03040402}, {0x03040402}, {0x03050202},	/* 74 */
	{0x03050202}, {0x03020502}, {0x03020502}, {0x02050102}, {0x02050102},	/* 79 */
	{0x02050102}, {0x02050102}, {0xFF000003}, {0x02010502}, {0x02010502},	/* 84 */
	{0x03050001}, {0x03040302}, {0x02000501}, {0x02000501}, {0x03030402},	/* 89 */
	{0x03030302}, {0xFF000002}, {0x02040202}, {0x02020402}, {0x01040102},	/* 94 */
	{0x01040102}, {0xFF000001}, {0x01010402}, {0x01000401}, {0xFF000002},	/* 99 */
	{0x02040001}, {0x02030202}, {0x02020302}, {0x02030001}, {0xFF000001},	/* 104 */
	{0x01030102}, {0x01010302}, {0xFF000001}, {0x01000301}, {0x01020202},	/* 109 */
};

/* max table bits  6 */

/* TABLE  8   36 entries  maxbits 11  linbits  0 */
static HUFF_ELEMENT huff_table_8[] =
{
	{0xFF000008}, {0x00000101}, {0x0000010A}, {0x0000010F}, {0x08050102},	/*  4 */
	{0x08010502}, {0x00000112}, {0x00000115}, {0x08040202}, {0x08020402},	/*  9 */
	{0x08040102}, {0x07010402}, {0x07010402}, {0x08040001}, {0x08000401},	/* 14 */
	{0x08030202}, {0x08020302}, {0x08030102}, {0x08010302}, {0x08030001},	/* 19 */
	{0x08000301}, {0x06020202}, {0x06020202}, {0x06020202}, {0x06020202},	/* 24 */
	{0x06020001}, {0x06020001}, {0x06020001}, {0x06020001}, {0x06000201},	/* 29 */
	{0x06000201}, {0x06000201}, {0x06000201}, {0x04020102}, {0x04020102},	/* 34 */
	{0x04020102}, {0x04020102}, {0x04020102}, {0x04020102}, {0x04020102},	/* 39 */
	{0x04020102}, {0x04020102}, {0x04020102}, {0x04020102}, {0x04020102},	/* 44 */
	{0x04020102}, {0x04020102}, {0x04020102}, {0x04020102}, {0x04010202},	/* 49 */
	{0x04010202}, {0x04010202}, {0x04010202}, {0x04010202}, {0x04010202},	/* 54 */
	{0x04010202}, {0x04010202}, {0x04010202}, {0x04010202}, {0x04010202},	/* 59 */
	{0x04010202}, {0x04010202}, {0x04010202}, {0x04010202}, {0x04010202},	/* 64 */
	{0x02010102}, {0x02010102}, {0x02010102}, {0x02010102}, {0x02010102},	/* 69 */
	{0x02010102}, {0x02010102}, {0x02010102}, {0x02010102}, {0x02010102},	/* 74 */
	{0x02010102}, {0x02010102}, {0x02010102}, {0x02010102}, {0x02010102},	/* 79 */
	{0x02010102}, {0x02010102}, {0x02010102}, {0x02010102}, {0x02010102},	/* 84 */
	{0x02010102}, {0x02010102}, {0x02010102}, {0x02010102}, {0x02010102},	/* 89 */
	{0x02010102}, {0x02010102}, {0x02010102}, {0x02010102}, {0x02010102},	/* 94 */
	{0x02010102}, {0x02010102}, {0x02010102}, {0x02010102}, {0x02010102},	/* 99 */
	{0x02010102}, {0x02010102}, {0x02010102}, {0x02010102}, {0x02010102},	/* 104 */
	{0x02010102}, {0x02010102}, {0x02010102}, {0x02010102}, {0x02010102},	/* 109 */
	{0x02010102}, {0x02010102}, {0x02010102}, {0x02010102}, {0x02010102},	/* 114 */
	{0x02010102}, {0x02010102}, {0x02010102}, {0x02010102}, {0x02010102},	/* 119 */
	{0x02010102}, {0x02010102}, {0x02010102}, {0x02010102}, {0x02010102},	/* 124 */
	{0x02010102}, {0x02010102}, {0x02010102}, {0x02010102}, {0x03010001},	/* 129 */
	{0x03010001}, {0x03010001}, {0x03010001}, {0x03010001}, {0x03010001},	/* 134 */
	{0x03010001}, {0x03010001}, {0x03010001}, {0x03010001}, {0x03010001},	/* 139 */
	{0x03010001}, {0x03010001}, {0x03010001}, {0x03010001}, {0x03010001},	/* 144 */
	{0x03010001}, {0x03010001}, {0x03010001}, {0x03010001}, {0x03010001},	/* 149 */
	{0x03010001}, {0x03010001}, {0x03010001}, {0x03010001}, {0x03010001},	/* 154 */
	{0x03010001}, {0x03010001}, {0x03010001}, {0x03010001}, {0x03010001},	/* 159 */
	{0x03010001}, {0x03000101}, {0x03000101}, {0x03000101}, {0x03000101},	/* 164 */
	{0x03000101}, {0x03000101}, {0x03000101}, {0x03000101}, {0x03000101},	/* 169 */
	{0x03000101}, {0x03000101}, {0x03000101}, {0x03000101}, {0x03000101},	/* 174 */
	{0x03000101}, {0x03000101}, {0x03000101}, {0x03000101}, {0x03000101},	/* 179 */
	{0x03000101}, {0x03000101}, {0x03000101}, {0x03000101}, {0x03000101},	/* 184 */
	{0x03000101}, {0x03000101}, {0x03000101}, {0x03000101}, {0x03000101},	/* 189 */
	{0x03000101}, {0x03000101}, {0x03000101}, {0x02000000}, {0x02000000},	/* 194 */
	{0x02000000}, {0x02000000}, {0x02000000}, {0x02000000}, {0x02000000},	/* 199 */
	{0x02000000}, {0x02000000}, {0x02000000}, {0x02000000}, {0x02000000},	/* 204 */
	{0x02000000}, {0x02000000}, {0x02000000}, {0x02000000}, {0x02000000},	/* 209 */
	{0x02000000}, {0x02000000}, {0x02000000}, {0x02000000}, {0x02000000},	/* 214 */
	{0x02000000}, {0x02000000}, {0x02000000}, {0x02000000}, {0x02000000},	/* 219 */
	{0x02000000}, {0x02000000}, {0x02000000}, {0x02000000}, {0x02000000},	/* 224 */
	{0x02000000}, {0x02000000}, {0x02000000}, {0x02000000}, {0x02000000},	/* 229 */
	{0x02000000}, {0x02000000}, {0x02000000}, {0x02000000}, {0x02000000},	/* 234 */
	{0x02000000}, {0x02000000}, {0x02000000}, {0x02000000}, {0x02000000},	/* 239 */
	{0x02000000}, {0x02000000}, {0x02000000}, {0x02000000}, {0x02000000},	/* 244 */
	{0x02000000}, {0x02000000}, {0x02000000}, {0x02000000}, {0x02000000},	/* 249 */
	{0x02000000}, {0x02000000}, {0x02000000}, {0x02000000}, {0x02000000},	/* 254 */
	{0x02000000}, {0x02000000}, {0xFF000003}, {0x03050502}, {0x03040502},	/* 259 */
	{0x02050402}, {0x02050402}, {0x01030502}, {0x01030502}, {0x01030502},	/* 264 */
	{0x01030502}, {0xFF000002}, {0x02050302}, {0x02040402}, {0x01050202},	/* 269 */
	{0x01050202}, {0xFF000001}, {0x01020502}, {0x01050001}, {0xFF000001},	/* 274 */
	{0x01040302}, {0x01030402}, {0xFF000001}, {0x01000501}, {0x01030302},	/* 279 */
};

/* max table bits  8 */

/* TABLE  9   36 entries  maxbits  9  linbits  0 */
static HUFF_ELEMENT huff_table_9[] =
{
	{0xFF000006}, {0x00000041}, {0x0000004A}, {0x0000004F}, {0x00000052},	/*  4 */
	{0x00000057}, {0x0000005A}, {0x06040102}, {0x06010402}, {0x06030202},	/*  9 */
	{0x06020302}, {0x05030102}, {0x05030102}, {0x05010302}, {0x05010302},	/* 14 */
	{0x06030001}, {0x06000301}, {0x05020202}, {0x05020202}, {0x05020001},	/* 19 */
	{0x05020001}, {0x04020102}, {0x04020102}, {0x04020102}, {0x04020102},	/* 24 */
	{0x04010202}, {0x04010202}, {0x04010202}, {0x04010202}, {0x04000201},	/* 29 */
	{0x04000201}, {0x04000201}, {0x04000201}, {0x03010102}, {0x03010102},	/* 34 */
	{0x03010102}, {0x03010102}, {0x03010102}, {0x03010102}, {0x03010102},	/* 39 */
	{0x03010102}, {0x03010001}, {0x03010001}, {0x03010001}, {0x03010001},	/* 44 */
	{0x03010001}, {0x03010001}, {0x03010001}, {0x03010001}, {0x03000101},	/* 49 */
	{0x03000101}, {0x03000101}, {0x03000101}, {0x03000101}, {0x03000101},	/* 54 */
	{0x03000101}, {0x03000101}, {0x03000000}, {0x03000000}, {0x03000000},	/* 59 */
	{0x03000000}, {0x03000000}, {0x03000000}, {0x03000000}, {0x03000000},	/* 64 */
	{0xFF000003}, {0x03050502}, {0x03050402}, {0x02050302}, {0x02050302},	/* 69 */
	{0x02030502}, {0x02030502}, {0x03040502}, {0x03050001}, {0xFF000002},	/* 74 */
	{0x02040402}, {0x02050202}, {0x02020502}, {0x02050102}, {0xFF000001},	/* 79 */
	{0x01010502}, {0x01040302}, {0xFF000002}, {0x01030402}, {0x01030402},	/* 84 */
	{0x02000501}, {0x02040001}, {0xFF000001}, {0x01040202}, {0x01020402},	/* 89 */
	{0xFF000001}, {0x01030302}, {0x01000401},
};

/* max table bits  6 */

/* TABLE 10   64 entries  maxbits 11  linbits  0 */
static HUFF_ELEMENT huff_table_10[] =
{
	{0xFF000008}, {0x00000101}, {0x0000010A}, {0x0000010F}, {0x00000118},	/*  4 */
	{0x0000011B}, {0x00000120}, {0x00000125}, {0x08070102}, {0x08010702},	/*  9 */
	{0x0000012A}, {0x0000012D}, {0x00000132}, {0x08060102}, {0x08010602},	/* 14 */
	{0x08000601}, {0x00000137}, {0x0000013A}, {0x0000013D}, {0x08040102},	/* 19 */
	{0x08010402}, {0x08000401}, {0x08030202}, {0x08020302}, {0x08030001},	/* 24 */
	{0x07030102}, {0x07030102}, {0x07010302}, {0x07010302}, {0x07000301},	/* 29 */
	{0x07000301}, {0x07020202}, {0x07020202}, {0x06020102}, {0x06020102},	/* 34 */
	{0x06020102}, {0x06020102}, {0x06010202}, {0x06010202}, {0x06010202},	/* 39 */
	{0x06010202}, {0x06020001}, {0x06020001}, {0x06020001}, {0x06020001},	/* 44 */
	{0x06000201}, {0x06000201}, {0x06000201}, {0x06000201}, {0x04010102},	/* 49 */
	{0x04010102}, {0x04010102}, {0x04010102}, {0x04010102}, {0x04010102},	/* 54 */
	{0x04010102}, {0x04010102}, {0x04010102}, {0x04010102}, {0x04010102},	/* 59 */
	{0x04010102}, {0x04010102}, {0x04010102}, {0x04010102}, {0x04010102},	/* 64 */
	{0x03010001}, {0x03010001}, {0x03010001}, {0x03010001}, {0x03010001},	/* 69 */
	{0x03010001}, {0x03010001}, {0x03010001}, {0x03010001}, {0x03010001},	/* 74 */
	{0x03010001}, {0x03010001}, {0x03010001}, {0x03010001}, {0x03010001},	/* 79 */
	{0x03010001}, {0x03010001}, {0x03010001}, {0x03010001}, {0x03010001},	/* 84 */
	{0x03010001}, {0x03010001}, {0x03010001}, {0x03010001}, {0x03010001},	/* 89 */
	{0x03010001}, {0x03010001}, {0x03010001}, {0x03010001}, {0x03010001},	/* 94 */
	{0x03010001}, {0x03010001}, {0x03000101}, {0x03000101}, {0x03000101},	/* 99 */
	{0x03000101}, {0x03000101}, {0x03000101}, {0x03000101}, {0x03000101},	/* 104 */
	{0x03000101}, {0x03000101}, {0x03000101}, {0x03000101}, {0x03000101},	/* 109 */
	{0x03000101}, {0x03000101}, {0x03000101}, {0x03000101}, {0x03000101},	/* 114 */
	{0x03000101}, {0x03000101}, {0x03000101}, {0x03000101}, {0x03000101},	/* 119 */
	{0x03000101}, {0x03000101}, {0x03000101}, {0x03000101}, {0x03000101},	/* 124 */
	{0x03000101}, {0x03000101}, {0x03000101}, {0x03000101}, {0x01000000},	/* 129 */
	{0x01000000}, {0x01000000}, {0x01000000}, {0x01000000}, {0x01000000},	/* 134 */
	{0x01000000}, {0x01000000}, {0x01000000}, {0x01000000}, {0x01000000},	/* 139 */
	{0x01000000}, {0x01000000}, {0x01000000}, {0x01000000}, {0x01000000},	/* 144 */
	{0x01000000}, {0x01000000}, {0x01000000}, {0x01000000}, {0x01000000},	/* 149 */
	{0x01000000}, {0x01000000}, {0x01000000}, {0x01000000}, {0x01000000},	/* 154 */
	{0x01000000}, {0x01000000}, {0x01000000}, {0x01000000}, {0x01000000},	/* 159 */
	{0x01000000}, {0x01000000}, {0x01000000}, {0x01000000}, {0x01000000},	/* 164 */
	{0x01000000}, {0x01000000}, {0x01000000}, {0x01000000}, {0x01000000},	/* 169 */
	{0x01000000}, {0x01000000}, {0x01000000}, {0x01000000}, {0x01000000},	/* 174 */
	{0x01000000}, {0x01000000}, {0x01000000}, {0x01000000}, {0x01000000},	/* 179 */
	{0x01000000}, {0x01000000}, {0x01000000}, {0x01000000}, {0x01000000},	/* 184 */
	{0x01000000}, {0x01000000}, {0x01000000}, {0x01000000}, {0x01000000},	/* 189 */
	{0x01000000}, {0x01000000}, {0x01000000}, {0x01000000}, {0x01000000},	/* 194 */
	{0x01000000}, {0x01000000}, {0x01000000}, {0x01000000}, {0x01000000},	/* 199 */
	{0x01000000}, {0x01000000}, {0x01000000}, {0x01000000}, {0x01000000},	/* 204 */
	{0x01000000}, {0x01000000}, {0x01000000}, {0x01000000}, {0x01000000},	/* 209 */
	{0x01000000}, {0x01000000}, {0x01000000}, {0x01000000}, {0x01000000},	/* 214 */
	{0x01000000}, {0x01000000}, {0x01000000}, {0x01000000}, {0x01000000},	/* 219 */
	{0x01000000}, {0x01000000}, {0x01000000}, {0x01000000}, {0x01000000},	/* 224 */
	{0x01000000}, {0x01000000}, {0x01000000}, {0x01000000}, {0x01000000},	/* 229 */
	{0x01000000}, {0x01000000}, {0x01000000}, {0x01000000}, {0x01000000},	/* 234 */
	{0x01000000}, {0x01000000}, {0x01000000}, {0x01000000}, {0x01000000},	/* 239 */
	{0x01000000}, {0x01000000}, {0x01000000}, {0x01000000}, {0x01000000},	/* 244 */
	{0x01000000}, {0x01000000}, {0x01000000}, {0x01000000}, {0x01000000},	/* 249 */
	{0x01000000}, {0x01000000}, {0x01000000}, {0x01000000}, {0x01000000},	/* 254 */
	{0x01000000}, {0x01000000}, {0xFF000003}, {0x03070702}, {0x03070602},	/* 259 */
	{0x03060702}, {0x03070502}, {0x03050702}, {0x03060602}, {0x02070402},	/* 264 */
	{0x02070402}, {0xFF000002}, {0x02040702}, {0x02060502}, {0x02050602},	/* 269 */
	{0x02070302}, {0xFF000003}, {0x02030702}, {0x02030702}, {0x02060402},	/* 274 */
	{0x02060402}, {0x03050502}, {0x03040502}, {0x02030602}, {0x02030602},	/* 279 */
	{0xFF000001}, {0x01070202}, {0x01020702}, {0xFF000002}, {0x02040602},	/* 284 */
	{0x02070001}, {0x01000701}, {0x01000701}, {0xFF000002}, {0x01020602},	/* 289 */
	{0x01020602}, {0x02050402}, {0x02050302}, {0xFF000002}, {0x01060001},	/* 294 */
	{0x01060001}, {0x02030502}, {0x02040402}, {0xFF000001}, {0x01060302},	/* 299 */
	{0x01060202}, {0xFF000002}, {0x02050202}, {0x02020502}, {0x01050102},	/* 304 */
	{0x01050102}, {0xFF000002}, {0x01010502}, {0x01010502}, {0x02040302},	/* 309 */
	{0x02030402}, {0xFF000001}, {0x01050001}, {0x01000501}, {0xFF000001},	/* 314 */
	{0x01040202}, {0x01020402}, {0xFF000001}, {0x01030302}, {0x01040001},	/* 319 */
};

/* max table bits  8 */

/* TABLE 11   64 entries  maxbits 11  linbits  0 */
static HUFF_ELEMENT huff_table_11[] =
{
	{0xFF000008}, {0x00000101}, {0x00000106}, {0x0000010F}, {0x00000114},	/*  4 */
	{0x00000117}, {0x08070202}, {0x08020702}, {0x0000011C}, {0x07010702},	/*  9 */
	{0x07010702}, {0x08070102}, {0x08000701}, {0x08060302}, {0x08030602},	/* 14 */
	{0x08000601}, {0x0000011F}, {0x00000122}, {0x08050102}, {0x07020602},	/* 19 */
	{0x07020602}, {0x08060202}, {0x08060001}, {0x07060102}, {0x07060102},	/* 24 */
	{0x07010602}, {0x07010602}, {0x08010502}, {0x08040302}, {0x08000501},	/* 29 */
	{0x00000125}, {0x08040202}, {0x08020402}, {0x08040102}, {0x08010402},	/* 34 */
	{0x08040001}, {0x08000401}, {0x07030202}, {0x07030202}, {0x07020302},	/* 39 */
	{0x07020302}, {0x06030102}, {0x06030102}, {0x06030102}, {0x06030102},	/* 44 */
	{0x06010302}, {0x06010302}, {0x06010302}, {0x06010302}, {0x07030001},	/* 49 */
	{0x07030001}, {0x07000301}, {0x07000301}, {0x06020202}, {0x06020202},	/* 54 */
	{0x06020202}, {0x06020202}, {0x05010202}, {0x05010202}, {0x05010202},	/* 59 */
	{0x05010202}, {0x05010202}, {0x05010202}, {0x05010202}, {0x05010202},	/* 64 */
	{0x04020102}, {0x04020102}, {0x04020102}, {0x04020102}, {0x04020102},	/* 69 */
	{0x04020102}, {0x04020102}, {0x04020102}, {0x04020102}, {0x04020102},	/* 74 */
	{0x04020102}, {0x04020102}, {0x04020102}, {0x04020102}, {0x04020102},	/* 79 */
	{0x04020102}, {0x05020001}, {0x05020001}, {0x05020001}, {0x05020001},	/* 84 */
	{0x05020001}, {0x05020001}, {0x05020001}, {0x05020001}, {0x05000201},	/* 89 */
	{0x05000201}, {0x05000201}, {0x05000201}, {0x05000201}, {0x05000201},	/* 94 */
	{0x05000201}, {0x05000201}, {0x03010102}, {0x03010102}, {0x03010102},	/* 99 */
	{0x03010102}, {0x03010102}, {0x03010102}, {0x03010102}, {0x03010102},	/* 104 */
	{0x03010102}, {0x03010102}, {0x03010102}, {0x03010102}, {0x03010102},	/* 109 */
	{0x03010102}, {0x03010102}, {0x03010102}, {0x03010102}, {0x03010102},	/* 114 */
	{0x03010102}, {0x03010102}, {0x03010102}, {0x03010102}, {0x03010102},	/* 119 */
	{0x03010102}, {0x03010102}, {0x03010102}, {0x03010102}, {0x03010102},	/* 124 */
	{0x03010102}, {0x03010102}, {0x03010102}, {0x03010102}, {0x03010001},	/* 129 */
	{0x03010001}, {0x03010001}, {0x03010001}, {0x03010001}, {0x03010001},	/* 134 */
	{0x03010001}, {0x03010001}, {0x03010001}, {0x03010001}, {0x03010001},	/* 139 */
	{0x03010001}, {0x03010001}, {0x03010001}, {0x03010001}, {0x03010001},	/* 144 */
	{0x03010001}, {0x03010001}, {0x03010001}, {0x03010001}, {0x03010001},	/* 149 */
	{0x03010001}, {0x03010001}, {0x03010001}, {0x03010001}, {0x03010001},	/* 154 */
	{0x03010001}, {0x03010001}, {0x03010001}, {0x03010001}, {0x03010001},	/* 159 */
	{0x03010001}, {0x03000101}, {0x03000101}, {0x03000101}, {0x03000101},	/* 164 */
	{0x03000101}, {0x03000101}, {0x03000101}, {0x03000101}, {0x03000101},	/* 169 */
	{0x03000101}, {0x03000101}, {0x03000101}, {0x03000101}, {0x03000101},	/* 174 */
	{0x03000101}, {0x03000101}, {0x03000101}, {0x03000101}, {0x03000101},	/* 179 */
	{0x03000101}, {0x03000101}, {0x03000101}, {0x03000101}, {0x03000101},	/* 184 */
	{0x03000101}, {0x03000101}, {0x03000101}, {0x03000101}, {0x03000101},	/* 189 */
	{0x03000101}, {0x03000101}, {0x03000101}, {0x02000000}, {0x02000000},	/* 194 */
	{0x02000000}, {0x02000000}, {0x02000000}, {0x02000000}, {0x02000000},	/* 199 */
	{0x02000000}, {0x02000000}, {0x02000000}, {0x02000000}, {0x02000000},	/* 204 */
	{0x02000000}, {0x02000000}, {0x02000000}, {0x02000000}, {0x02000000},	/* 209 */
	{0x02000000}, {0x02000000}, {0x02000000}, {0x02000000}, {0x02000000},	/* 214 */
	{0x02000000}, {0x02000000}, {0x02000000}, {0x02000000}, {0x02000000},	/* 219 */
	{0x02000000}, {0x02000000}, {0x02000000}, {0x02000000}, {0x02000000},	/* 224 */
	{0x02000000}, {0x02000000}, {0x02000000}, {0x02000000}, {0x02000000},	/* 229 */
	{0x02000000}, {0x02000000}, {0x02000000}, {0x02000000}, {0x02000000},	/* 234 */
	{0x02000000}, {0x02000000}, {0x02000000}, {0x02000000}, {0x02000000},	/* 239 */
	{0x02000000}, {0x02000000}, {0x02000000}, {0x02000000}, {0x02000000},	/* 244 */
	{0x02000000}, {0x02000000}, {0x02000000}, {0x02000000}, {0x02000000},	/* 249 */
	{0x02000000}, {0x02000000}, {0x02000000}, {0x02000000}, {0x02000000},	/* 254 */
	{0x02000000}, {0x02000000}, {0xFF000002}, {0x02070702}, {0x02070602},	/* 259 */
	{0x02060702}, {0x02050702}, {0xFF000003}, {0x02060602}, {0x02060602},	/* 264 */
	{0x02070402}, {0x02070402}, {0x02040702}, {0x02040702}, {0x03070502},	/* 269 */
	{0x03050502}, {0xFF000002}, {0x02060502}, {0x02050602}, {0x01070302},	/* 274 */
	{0x01070302}, {0xFF000001}, {0x01030702}, {0x01060402}, {0xFF000002},	/* 279 */
	{0x02050402}, {0x02040502}, {0x02050302}, {0x02030502}, {0xFF000001},	/* 284 */
	{0x01040602}, {0x01070001}, {0xFF000001}, {0x01040402}, {0x01050202},	/* 289 */
	{0xFF000001}, {0x01020502}, {0x01050001}, {0xFF000001}, {0x01030402},	/* 294 */
	{0x01030302},
};

/* max table bits  8 */

/* TABLE 12   64 entries  maxbits 10  linbits  0 */
static HUFF_ELEMENT huff_table_12[] =
{
	{0xFF000007}, {0x00000081}, {0x0000008A}, {0x0000008F}, {0x00000092},	/*  4 */
	{0x00000097}, {0x0000009A}, {0x0000009D}, {0x000000A2}, {0x000000A5},	/*  9 */
	{0x000000A8}, {0x07060202}, {0x07020602}, {0x07010602}, {0x000000AD},	/* 14 */
	{0x000000B0}, {0x000000B3}, {0x07050102}, {0x07010502}, {0x07040302},	/* 19 */
	{0x07030402}, {0x000000B6}, {0x07040202}, {0x07020402}, {0x07040102},	/* 24 */
	{0x06030302}, {0x06030302}, {0x06010402}, {0x06010402}, {0x06030202},	/* 29 */
	{0x06030202}, {0x06020302}, {0x06020302}, {0x07000401}, {0x07030001},	/* 34 */
	{0x06000301}, {0x06000301}, {0x05030102}, {0x05030102}, {0x05030102},	/* 39 */
	{0x05030102}, {0x05010302}, {0x05010302}, {0x05010302}, {0x05010302},	/* 44 */
	{0x05020202}, {0x05020202}, {0x05020202}, {0x05020202}, {0x04020102},	/* 49 */
	{0x04020102}, {0x04020102}, {0x04020102}, {0x04020102}, {0x04020102},	/* 54 */
	{0x04020102}, {0x04020102}, {0x04010202}, {0x04010202}, {0x04010202},	/* 59 */
	{0x04010202}, {0x04010202}, {0x04010202}, {0x04010202}, {0x04010202},	/* 64 */
	{0x05020001}, {0x05020001}, {0x05020001}, {0x05020001}, {0x05000201},	/* 69 */
	{0x05000201}, {0x05000201}, {0x05000201}, {0x04000000}, {0x04000000},	/* 74 */
	{0x04000000}, {0x04000000}, {0x04000000}, {0x04000000}, {0x04000000},	/* 79 */
	{0x04000000}, {0x03010102}, {0x03010102}, {0x03010102}, {0x03010102},	/* 84 */
	{0x03010102}, {0x03010102}, {0x03010102}, {0x03010102}, {0x03010102},	/* 89 */
	{0x03010102}, {0x03010102}, {0x03010102}, {0x03010102}, {0x03010102},	/* 94 */
	{0x03010102}, {0x03010102}, {0x03010001}, {0x03010001}, {0x03010001},	/* 99 */
	{0x03010001}, {0x03010001}, {0x03010001}, {0x03010001}, {0x03010001},	/* 104 */
	{0x03010001}, {0x03010001}, {0x03010001}, {0x03010001}, {0x03010001},	/* 109 */
	{0x03010001}, {0x03010001}, {0x03010001}, {0x03000101}, {0x03000101},	/* 114 */
	{0x03000101}, {0x03000101}, {0x03000101}, {0x03000101}, {0x03000101},	/* 119 */
	{0x03000101}, {0x03000101}, {0x03000101}, {0x03000101}, {0x03000101},	/* 124 */
	{0x03000101}, {0x03000101}, {0x03000101}, {0x03000101}, {0xFF000003},	/* 129 */
	{0x03070702}, {0x03070602}, {0x02060702}, {0x02060702}, {0x02070502},	/* 134 */
	{0x02070502}, {0x02050702}, {0x02050702}, {0xFF000002}, {0x02060602},	/* 139 */
	{0x02070402}, {0x02040702}, {0x02050602}, {0xFF000001}, {0x01060502},	/* 144 */
	{0x01070302}, {0xFF000002}, {0x02030702}, {0x02050502}, {0x01070202},	/* 149 */
	{0x01070202}, {0xFF000001}, {0x01020702}, {0x01060402}, {0xFF000001},	/* 154 */
	{0x01040602}, {0x01070102}, {0xFF000002}, {0x01010702}, {0x01010702},	/* 159 */
	{0x02070001}, {0x02000701}, {0xFF000001}, {0x01060302}, {0x01030602},	/* 164 */
	{0xFF000001}, {0x01050402}, {0x01040502}, {0xFF000002}, {0x01040402},	/* 169 */
	{0x01040402}, {0x02060001}, {0x02050001}, {0xFF000001}, {0x01060102},	/* 174 */
	{0x01000601}, {0xFF000001}, {0x01050302}, {0x01030502}, {0xFF000001},	/* 179 */
	{0x01050202}, {0x01020502}, {0xFF000001}, {0x01000501}, {0x01040001},	/* 184 */
};

/* max table bits  7 */

/* TABLE 13  256 entries  maxbits 19  linbits  0 */
static HUFF_ELEMENT huff_table_13[] =
{
	{0xFF000006}, {0x00000041}, {0x00000082}, {0x000000C3}, {0x000000E4},	/*  4 */
	{0x00000105}, {0x00000116}, {0x0000011F}, {0x00000130}, {0x00000139},	/*  9 */
	{0x0000013E}, {0x00000143}, {0x00000146}, {0x06020102}, {0x06010202},	/* 14 */
	{0x06020001}, {0x06000201}, {0x04010102}, {0x04010102}, {0x04010102},	/* 19 */
	{0x04010102}, {0x04010001}, {0x04010001}, {0x04010001}, {0x04010001},	/* 24 */
	{0x03000101}, {0x03000101}, {0x03000101}, {0x03000101}, {0x03000101},	/* 29 */
	{0x03000101}, {0x03000101}, {0x03000101}, {0x01000000}, {0x01000000},	/* 34 */
	{0x01000000}, {0x01000000}, {0x01000000}, {0x01000000}, {0x01000000},	/* 39 */
	{0x01000000}, {0x01000000}, {0x01000000}, {0x01000000}, {0x01000000},	/* 44 */
	{0x01000000}, {0x01000000}, {0x01000000}, {0x01000000}, {0x01000000},	/* 49 */
	{0x01000000}, {0x01000000}, {0x01000000}, {0x01000000}, {0x01000000},	/* 54 */
	{0x01000000}, {0x01000000}, {0x01000000}, {0x01000000}, {0x01000000},	/* 59 */
	{0x01000000}, {0x01000000}, {0x01000000}, {0x01000000}, {0x01000000},	/* 64 */
	{0xFF000006}, {0x00000108}, {0x00000111}, {0x0000011A}, {0x00000123},	/* 69 */
	{0x0000012C}, {0x00000131}, {0x00000136}, {0x0000013F}, {0x00000144},	/* 74 */
	{0x00000147}, {0x0000014C}, {0x00000151}, {0x00000156}, {0x0000015B},	/* 79 */
	{0x060F0102}, {0x06010F02}, {0x06000F01}, {0x00000160}, {0x00000163},	/* 84 */
	{0x00000166}, {0x06020E02}, {0x00000169}, {0x060E0102}, {0x06010E02},	/* 89 */
	{0x0000016C}, {0x0000016F}, {0x00000172}, {0x00000175}, {0x00000178},	/* 94 */
	{0x0000017B}, {0x06060C02}, {0x060D0302}, {0x0000017E}, {0x060D0202},	/* 99 */
	{0x06020D02}, {0x060D0102}, {0x06070B02}, {0x00000181}, {0x00000184},	/* 104 */
	{0x06030C02}, {0x00000187}, {0x060B0402}, {0x05010D02}, {0x05010D02},	/* 109 */
	{0x060D0001}, {0x06000D01}, {0x060A0802}, {0x06080A02}, {0x060C0402},	/* 114 */
	{0x06040C02}, {0x060B0602}, {0x06060B02}, {0x050C0302}, {0x050C0302},	/* 119 */
	{0x050C0202}, {0x050C0202}, {0x05020C02}, {0x05020C02}, {0x050B0502},	/* 124 */
	{0x050B0502}, {0x06050B02}, {0x06090802}, {0x050C0102}, {0x050C0102},	/* 129 */
	{0xFF000006}, {0x05010C02}, {0x05010C02}, {0x06080902}, {0x060C0001},	/* 134 */
	{0x05000C01}, {0x05000C01}, {0x06040B02}, {0x060A0602}, {0x06060A02},	/* 139 */
	{0x06090702}, {0x050B0302}, {0x050B0302}, {0x05030B02}, {0x05030B02},	/* 144 */
	{0x06080802}, {0x060A0502}, {0x050B0202}, {0x050B0202}, {0x06050A02},	/* 149 */
	{0x06090602}, {0x05040A02}, {0x05040A02}, {0x06080702}, {0x06070802},	/* 154 */
	{0x05040902}, {0x05040902}, {0x06070702}, {0x06060702}, {0x04020B02},	/* 159 */
	{0x04020B02}, {0x04020B02}, {0x04020B02}, {0x040B0102}, {0x040B0102},	/* 164 */
	{0x040B0102}, {0x040B0102}, {0x04010B02}, {0x04010B02}, {0x04010B02},	/* 169 */
	{0x04010B02}, {0x050B0001}, {0x050B0001}, {0x05000B01}, {0x05000B01},	/* 174 */
	{0x05060902}, {0x05060902}, {0x050A0402}, {0x050A0402}, {0x050A0302},	/* 179 */
	{0x050A0302}, {0x05030A02}, {0x05030A02}, {0x05090502}, {0x05090502},	/* 184 */
	{0x05050902}, {0x05050902}, {0x040A0202}, {0x040A0202}, {0x040A0202},	/* 189 */
	{0x040A0202}, {0x04020A02}, {0x04020A02}, {0x04020A02}, {0x04020A02},	/* 194 */
	{0xFF000005}, {0x040A0102}, {0x040A0102}, {0x04010A02}, {0x04010A02},	/* 199 */
	{0x050A0001}, {0x05080602}, {0x04000A01}, {0x04000A01}, {0x05060802},	/* 204 */
	{0x05090402}, {0x04030902}, {0x04030902}, {0x05090302}, {0x05080502},	/* 209 */
	{0x05050802}, {0x05070602}, {0x04090202}, {0x04090202}, {0x04020902},	/* 214 */
	{0x04020902}, {0x05070502}, {0x05050702}, {0x04080302}, {0x04080302},	/* 219 */
	{0x04030802}, {0x04030802}, {0x05060602}, {0x05070402}, {0x05040702},	/* 224 */
	{0x05060502}, {0x05050602}, {0x05030702}, {0xFF000005}, {0x03090102},	/* 229 */
	{0x03090102}, {0x03090102}, {0x03090102}, {0x03010902}, {0x03010902},	/* 234 */
	{0x03010902}, {0x03010902}, {0x04090001}, {0x04090001}, {0x04000901},	/* 239 */
	{0x04000901}, {0x04080402}, {0x04080402}, {0x04040802}, {0x04040802},	/* 244 */
	{0x04020702}, {0x04020702}, {0x05060402}, {0x05040602}, {0x03080202},	/* 249 */
	{0x03080202}, {0x03080202}, {0x03080202}, {0x03020802}, {0x03020802},	/* 254 */
	{0x03020802}, {0x03020802}, {0x03080102}, {0x03080102}, {0x03080102},	/* 259 */
	{0x03080102}, {0xFF000004}, {0x04070302}, {0x04070202}, {0x03070102},	/* 264 */
	{0x03070102}, {0x03010702}, {0x03010702}, {0x04050502}, {0x04070001},	/* 269 */
	{0x04000701}, {0x04060302}, {0x04030602}, {0x04050402}, {0x04040502},	/* 274 */
	{0x04060202}, {0x04020602}, {0x04050302}, {0xFF000003}, {0x02010802},	/* 279 */
	{0x02010802}, {0x03080001}, {0x03000801}, {0x03060102}, {0x03010602},	/* 284 */
	{0x03060001}, {0x03000601}, {0xFF000004}, {0x04030502}, {0x04040402},	/* 289 */
	{0x03050202}, {0x03050202}, {0x03020502}, {0x03020502}, {0x03050001},	/* 294 */
	{0x03050001}, {0x02050102}, {0x02050102}, {0x02050102}, {0x02050102},	/* 299 */
	{0x02010502}, {0x02010502}, {0x02010502}, {0x02010502}, {0xFF000003},	/* 304 */
	{0x03040302}, {0x03030402}, {0x03000501}, {0x03040202}, {0x03020402},	/* 309 */
	{0x03030302}, {0x02040102}, {0x02040102}, {0xFF000002}, {0x01010402},	/* 314 */
	{0x01010402}, {0x02040001}, {0x02000401}, {0xFF000002}, {0x02030202},	/* 319 */
	{0x02020302}, {0x01030102}, {0x01030102}, {0xFF000001}, {0x01010302},	/* 324 */
	{0x01030001}, {0xFF000001}, {0x01000301}, {0x01020202}, {0xFF000003},	/* 329 */
	{0x00000082}, {0x0000008B}, {0x0000008E}, {0x00000091}, {0x00000094},	/* 334 */
	{0x00000097}, {0x030C0E02}, {0x030D0D02}, {0xFF000003}, {0x00000093},	/* 339 */
	{0x030E0B02}, {0x030B0E02}, {0x030F0902}, {0x03090F02}, {0x030A0E02},	/* 344 */
	{0x030D0B02}, {0x030B0D02}, {0xFF000003}, {0x030F0802}, {0x03080F02},	/* 349 */
	{0x030C0C02}, {0x0000008D}, {0x030E0802}, {0x00000090}, {0x02070F02},	/* 354 */
	{0x02070F02}, {0xFF000003}, {0x020A0D02}, {0x020A0D02}, {0x030D0A02},	/* 359 */
	{0x030C0B02}, {0x030B0C02}, {0x03060F02}, {0x020F0602}, {0x020F0602},	/* 364 */
	{0xFF000002}, {0x02080E02}, {0x020F0502}, {0x020D0902}, {0x02090D02},	/* 369 */
	{0xFF000002}, {0x02050F02}, {0x02070E02}, {0x020C0A02}, {0x020B0B02},	/* 374 */
	{0xFF000003}, {0x020F0402}, {0x020F0402}, {0x02040F02}, {0x02040F02},	/* 379 */
	{0x030A0C02}, {0x03060E02}, {0x02030F02}, {0x02030F02}, {0xFF000002},	/* 384 */
	{0x010F0302}, {0x010F0302}, {0x020D0802}, {0x02080D02}, {0xFF000001},	/* 389 */
	{0x010F0202}, {0x01020F02}, {0xFF000002}, {0x020E0602}, {0x020C0902},	/* 394 */
	{0x010F0001}, {0x010F0001}, {0xFF000002}, {0x02090C02}, {0x020E0502},	/* 399 */
	{0x010B0A02}, {0x010B0A02}, {0xFF000002}, {0x020D0702}, {0x02070D02},	/* 404 */
	{0x010E0402}, {0x010E0402}, {0xFF000002}, {0x02080C02}, {0x02060D02},	/* 409 */
	{0x010E0302}, {0x010E0302}, {0xFF000002}, {0x01090B02}, {0x01090B02},	/* 414 */
	{0x020B0902}, {0x020A0A02}, {0xFF000001}, {0x010A0B02}, {0x01050E02},	/* 419 */
	{0xFF000001}, {0x01040E02}, {0x010C0802}, {0xFF000001}, {0x010D0602},	/* 424 */
	{0x01030E02}, {0xFF000001}, {0x010E0202}, {0x010E0001}, {0xFF000001},	/* 429 */
	{0x01000E01}, {0x010D0502}, {0xFF000001}, {0x01050D02}, {0x010C0702},	/* 434 */
	{0xFF000001}, {0x01070C02}, {0x010D0402}, {0xFF000001}, {0x010B0802},	/* 439 */
	{0x01080B02}, {0xFF000001}, {0x01040D02}, {0x010A0902}, {0xFF000001},	/* 444 */
	{0x01090A02}, {0x010C0602}, {0xFF000001}, {0x01030D02}, {0x010B0702},	/* 449 */
	{0xFF000001}, {0x010C0502}, {0x01050C02}, {0xFF000001}, {0x01090902},	/* 454 */
	{0x010A0702}, {0xFF000001}, {0x01070A02}, {0x01070902}, {0xFF000003},	/* 459 */
	{0x00000023}, {0x030D0F02}, {0x020D0E02}, {0x020D0E02}, {0x010F0F02},	/* 464 */
	{0x010F0F02}, {0x010F0F02}, {0x010F0F02}, {0xFF000001}, {0x010F0E02},	/* 469 */
	{0x010F0D02}, {0xFF000001}, {0x010E0E02}, {0x010F0C02}, {0xFF000001},	/* 474 */
	{0x010E0D02}, {0x010F0B02}, {0xFF000001}, {0x010B0F02}, {0x010E0C02},	/* 479 */
	{0xFF000002}, {0x010C0D02}, {0x010C0D02}, {0x020F0A02}, {0x02090E02},	/* 484 */
	{0xFF000001}, {0x010A0F02}, {0x010D0C02}, {0xFF000001}, {0x010E0A02},	/* 489 */
	{0x010E0902}, {0xFF000001}, {0x010F0702}, {0x010E0702}, {0xFF000001},	/* 494 */
	{0x010E0F02}, {0x010C0F02},
};

/* max table bits  6 */
/* NO XING TABLE 14 */

/* TABLE 15  256 entries  maxbits 13  linbits  0 */
static HUFF_ELEMENT huff_table_15[] =
{
	{0xFF000008}, {0x00000101}, {0x00000122}, {0x00000143}, {0x00000154},	/*  4 */
	{0x00000165}, {0x00000176}, {0x0000017F}, {0x00000188}, {0x00000199},	/*  9 */
	{0x000001A2}, {0x000001AB}, {0x000001B4}, {0x000001BD}, {0x000001C2},	/* 14 */
	{0x000001CB}, {0x000001D4}, {0x000001D9}, {0x000001DE}, {0x000001E3},	/* 19 */
	{0x000001E8}, {0x000001ED}, {0x000001F2}, {0x000001F7}, {0x000001FC},	/* 24 */
	{0x00000201}, {0x00000204}, {0x00000207}, {0x0000020A}, {0x0000020F},	/* 29 */
	{0x00000212}, {0x00000215}, {0x0000021A}, {0x0000021D}, {0x00000220},	/* 34 */
	{0x08010902}, {0x00000223}, {0x00000226}, {0x00000229}, {0x0000022C},	/* 39 */
	{0x0000022F}, {0x08080202}, {0x08020802}, {0x08080102}, {0x08010802},	/* 44 */
	{0x00000232}, {0x00000235}, {0x00000238}, {0x0000023B}, {0x08070202},	/* 49 */
	{0x08020702}, {0x08040602}, {0x08070102}, {0x08050502}, {0x08010702},	/* 54 */
	{0x0000023E}, {0x08060302}, {0x08030602}, {0x08050402}, {0x08040502},	/* 59 */
	{0x08060202}, {0x08020602}, {0x08060102}, {0x00000241}, {0x08050302},	/* 64 */
	{0x07010602}, {0x07010602}, {0x08030502}, {0x08040402}, {0x07050202},	/* 69 */
	{0x07050202}, {0x07020502}, {0x07020502}, {0x07050102}, {0x07050102},	/* 74 */
	{0x07010502}, {0x07010502}, {0x08050001}, {0x08000501}, {0x07040302},	/* 79 */
	{0x07040302}, {0x07030402}, {0x07030402}, {0x07040202}, {0x07040202},	/* 84 */
	{0x07020402}, {0x07020402}, {0x07030302}, {0x07030302}, {0x06010402},	/* 89 */
	{0x06010402}, {0x06010402}, {0x06010402}, {0x07040102}, {0x07040102},	/* 94 */
	{0x07040001}, {0x07040001}, {0x06030202}, {0x06030202}, {0x06030202},	/* 99 */
	{0x06030202}, {0x06020302}, {0x06020302}, {0x06020302}, {0x06020302},	/* 104 */
	{0x07000401}, {0x07000401}, {0x07030001}, {0x07030001}, {0x06030102},	/* 109 */
	{0x06030102}, {0x06030102}, {0x06030102}, {0x06010302}, {0x06010302},	/* 114 */
	{0x06010302}, {0x06010302}, {0x06000301}, {0x06000301}, {0x06000301},	/* 119 */
	{0x06000301}, {0x05020202}, {0x05020202}, {0x05020202}, {0x05020202},	/* 124 */
	{0x05020202}, {0x05020202}, {0x05020202}, {0x05020202}, {0x05020102},	/* 129 */
	{0x05020102}, {0x05020102}, {0x05020102}, {0x05020102}, {0x05020102},	/* 134 */
	{0x05020102}, {0x05020102}, {0x05010202}, {0x05010202}, {0x05010202},	/* 139 */
	{0x05010202}, {0x05010202}, {0x05010202}, {0x05010202}, {0x05010202},	/* 144 */
	{0x05020001}, {0x05020001}, {0x05020001}, {0x05020001}, {0x05020001},	/* 149 */
	{0x05020001}, {0x05020001}, {0x05020001}, {0x05000201}, {0x05000201},	/* 154 */
	{0x05000201}, {0x05000201}, {0x05000201}, {0x05000201}, {0x05000201},	/* 159 */
	{0x05000201}, {0x03010102}, {0x03010102}, {0x03010102}, {0x03010102},	/* 164 */
	{0x03010102}, {0x03010102}, {0x03010102}, {0x03010102}, {0x03010102},	/* 169 */
	{0x03010102}, {0x03010102}, {0x03010102}, {0x03010102}, {0x03010102},	/* 174 */
	{0x03010102}, {0x03010102}, {0x03010102}, {0x03010102}, {0x03010102},	/* 179 */
	{0x03010102}, {0x03010102}, {0x03010102}, {0x03010102}, {0x03010102},	/* 184 */
	{0x03010102}, {0x03010102}, {0x03010102}, {0x03010102}, {0x03010102},	/* 189 */
	{0x03010102}, {0x03010102}, {0x03010102}, {0x04010001}, {0x04010001},	/* 194 */
	{0x04010001}, {0x04010001}, {0x04010001}, {0x04010001}, {0x04010001},	/* 199 */
	{0x04010001}, {0x04010001}, {0x04010001}, {0x04010001}, {0x04010001},	/* 204 */
	{0x04010001}, {0x04010001}, {0x04010001}, {0x04010001}, {0x04000101},	/* 209 */
	{0x04000101}, {0x04000101}, {0x04000101}, {0x04000101}, {0x04000101},	/* 214 */
	{0x04000101}, {0x04000101}, {0x04000101}, {0x04000101}, {0x04000101},	/* 219 */
	{0x04000101}, {0x04000101}, {0x04000101}, {0x04000101}, {0x04000101},	/* 224 */
	{0x03000000}, {0x03000000}, {0x03000000}, {0x03000000}, {0x03000000},	/* 229 */
	{0x03000000}, {0x03000000}, {0x03000000}, {0x03000000}, {0x03000000},	/* 234 */
	{0x03000000}, {0x03000000}, {0x03000000}, {0x03000000}, {0x03000000},	/* 239 */
	{0x03000000}, {0x03000000}, {0x03000000}, {0x03000000}, {0x03000000},	/* 244 */
	{0x03000000}, {0x03000000}, {0x03000000}, {0x03000000}, {0x03000000},	/* 249 */
	{0x03000000}, {0x03000000}, {0x03000000}, {0x03000000}, {0x03000000},	/* 254 */
	{0x03000000}, {0x03000000}, {0xFF000005}, {0x050F0F02}, {0x050F0E02},	/* 259 */
	{0x050E0F02}, {0x050F0D02}, {0x040E0E02}, {0x040E0E02}, {0x050D0F02},	/* 264 */
	{0x050F0C02}, {0x050C0F02}, {0x050E0D02}, {0x050D0E02}, {0x050F0B02},	/* 269 */
	{0x040B0F02}, {0x040B0F02}, {0x050E0C02}, {0x050C0E02}, {0x040D0D02},	/* 274 */
	{0x040D0D02}, {0x040F0A02}, {0x040F0A02}, {0x040A0F02}, {0x040A0F02},	/* 279 */
	{0x040E0B02}, {0x040E0B02}, {0x040B0E02}, {0x040B0E02}, {0x040D0C02},	/* 284 */
	{0x040D0C02}, {0x040C0D02}, {0x040C0D02}, {0x040F0902}, {0x040F0902},	/* 289 */
	{0xFF000005}, {0x04090F02}, {0x04090F02}, {0x040A0E02}, {0x040A0E02},	/* 294 */
	{0x040D0B02}, {0x040D0B02}, {0x040B0D02}, {0x040B0D02}, {0x040F0802},	/* 299 */
	{0x040F0802}, {0x04080F02}, {0x04080F02}, {0x040C0C02}, {0x040C0C02},	/* 304 */
	{0x040E0902}, {0x040E0902}, {0x04090E02}, {0x04090E02}, {0x040F0702},	/* 309 */
	{0x040F0702}, {0x04070F02}, {0x04070F02}, {0x040D0A02}, {0x040D0A02},	/* 314 */
	{0x040A0D02}, {0x040A0D02}, {0x040C0B02}, {0x040C0B02}, {0x040F0602},	/* 319 */
	{0x040F0602}, {0x050E0A02}, {0x050F0001}, {0xFF000004}, {0x030B0C02},	/* 324 */
	{0x030B0C02}, {0x03060F02}, {0x03060F02}, {0x040E0802}, {0x04080E02},	/* 329 */
	{0x040F0502}, {0x040D0902}, {0x03050F02}, {0x03050F02}, {0x030E0702},	/* 334 */
	{0x030E0702}, {0x03070E02}, {0x03070E02}, {0x030C0A02}, {0x030C0A02},	/* 339 */
	{0xFF000004}, {0x030A0C02}, {0x030A0C02}, {0x030B0B02}, {0x030B0B02},	/* 344 */
	{0x04090D02}, {0x040D0802}, {0x030F0402}, {0x030F0402}, {0x03040F02},	/* 349 */
	{0x03040F02}, {0x030F0302}, {0x030F0302}, {0x03030F02}, {0x03030F02},	/* 354 */
	{0x03080D02}, {0x03080D02}, {0xFF000004}, {0x03060E02}, {0x03060E02},	/* 359 */
	{0x030F0202}, {0x030F0202}, {0x03020F02}, {0x03020F02}, {0x040E0602},	/* 364 */
	{0x04000F01}, {0x030F0102}, {0x030F0102}, {0x03010F02}, {0x03010F02},	/* 369 */
	{0x030C0902}, {0x030C0902}, {0x03090C02}, {0x03090C02}, {0xFF000003},	/* 374 */
	{0x030E0502}, {0x030B0A02}, {0x030A0B02}, {0x03050E02}, {0x030D0702},	/* 379 */
	{0x03070D02}, {0x030E0402}, {0x03040E02}, {0xFF000003}, {0x030C0802},	/* 384 */
	{0x03080C02}, {0x030E0302}, {0x030D0602}, {0x03060D02}, {0x03030E02},	/* 389 */
	{0x030B0902}, {0x03090B02}, {0xFF000004}, {0x030E0202}, {0x030E0202},	/* 394 */
	{0x030A0A02}, {0x030A0A02}, {0x03020E02}, {0x03020E02}, {0x030E0102},	/* 399 */
	{0x030E0102}, {0x03010E02}, {0x03010E02}, {0x040E0001}, {0x04000E01},	/* 404 */
	{0x030D0502}, {0x030D0502}, {0x03050D02}, {0x03050D02}, {0xFF000003},	/* 409 */
	{0x030C0702}, {0x03070C02}, {0x030D0402}, {0x030B0802}, {0x02040D02},	/* 414 */
	{0x02040D02}, {0x03080B02}, {0x030A0902}, {0xFF000003}, {0x03090A02},	/* 419 */
	{0x030C0602}, {0x03060C02}, {0x030D0302}, {0x02030D02}, {0x02030D02},	/* 424 */
	{0x02020D02}, {0x02020D02}, {0xFF000003}, {0x030D0202}, {0x030D0001},	/* 429 */
	{0x020D0102}, {0x020D0102}, {0x020B0702}, {0x020B0702}, {0x02070B02},	/* 434 */
	{0x02070B02}, {0xFF000003}, {0x02010D02}, {0x02010D02}, {0x030C0502},	/* 439 */
	{0x03000D01}, {0x02050C02}, {0x02050C02}, {0x020A0802}, {0x020A0802},	/* 444 */
	{0xFF000002}, {0x02080A02}, {0x020C0402}, {0x02040C02}, {0x020B0602},	/* 449 */
	{0xFF000003}, {0x02060B02}, {0x02060B02}, {0x03090902}, {0x030C0001},	/* 454 */
	{0x020C0302}, {0x020C0302}, {0x02030C02}, {0x02030C02}, {0xFF000003},	/* 459 */
	{0x020A0702}, {0x020A0702}, {0x02070A02}, {0x02070A02}, {0x02060A02},	/* 464 */
	{0x02060A02}, {0x03000C01}, {0x030B0001}, {0xFF000002}, {0x01020C02},	/* 469 */
	{0x01020C02}, {0x020C0202}, {0x020B0502}, {0xFF000002}, {0x02050B02},	/* 474 */
	{0x020C0102}, {0x02090802}, {0x02080902}, {0xFF000002}, {0x02010C02},	/* 479 */
	{0x020B0402}, {0x02040B02}, {0x020A0602}, {0xFF000002}, {0x020B0302},	/* 484 */
	{0x02090702}, {0x01030B02}, {0x01030B02}, {0xFF000002}, {0x02070902},	/* 489 */
	{0x02080802}, {0x020B0202}, {0x020A0502}, {0xFF000002}, {0x01020B02},	/* 494 */
	{0x01020B02}, {0x02050A02}, {0x020B0102}, {0xFF000002}, {0x01010B02},	/* 499 */
	{0x01010B02}, {0x02000B01}, {0x02090602}, {0xFF000002}, {0x02060902},	/* 504 */
	{0x020A0402}, {0x02040A02}, {0x02080702}, {0xFF000002}, {0x02070802},	/* 509 */
	{0x020A0302}, {0x01030A02}, {0x01030A02}, {0xFF000001}, {0x01090502},	/* 514 */
	{0x01050902}, {0xFF000001}, {0x010A0202}, {0x01020A02}, {0xFF000001},	/* 519 */
	{0x010A0102}, {0x01010A02}, {0xFF000002}, {0x020A0001}, {0x02000A01},	/* 524 */
	{0x01080602}, {0x01080602}, {0xFF000001}, {0x01060802}, {0x01090402},	/* 529 */
	{0xFF000001}, {0x01040902}, {0x01090302}, {0xFF000002}, {0x01030902},	/* 534 */
	{0x01030902}, {0x02070702}, {0x02090001}, {0xFF000001}, {0x01080502},	/* 539 */
	{0x01050802}, {0xFF000001}, {0x01090202}, {0x01070602}, {0xFF000001},	/* 544 */
	{0x01060702}, {0x01020902}, {0xFF000001}, {0x01090102}, {0x01000901},	/* 549 */
	{0xFF000001}, {0x01080402}, {0x01040802}, {0xFF000001}, {0x01070502},	/* 554 */
	{0x01050702}, {0xFF000001}, {0x01080302}, {0x01030802}, {0xFF000001},	/* 559 */
	{0x01060602}, {0x01070402}, {0xFF000001}, {0x01040702}, {0x01080001},	/* 564 */
	{0xFF000001}, {0x01000801}, {0x01060502}, {0xFF000001}, {0x01050602},	/* 569 */
	{0x01070302}, {0xFF000001}, {0x01030702}, {0x01060402}, {0xFF000001},	/* 574 */
	{0x01070001}, {0x01000701}, {0xFF000001}, {0x01060001}, {0x01000601},	/* 579 */
};

/* max table bits  8 */

/* TABLE 16  256 entries  maxbits 17  linbits  0 */
static HUFF_ELEMENT huff_table_16[] =
{
	{0xFF000008}, {0x00000101}, {0x0000010A}, {0x00000113}, {0x080F0F02},	/*  4 */
	{0x00000118}, {0x0000011D}, {0x00000120}, {0x08020F02}, {0x00000131},	/*  9 */
	{0x080F0102}, {0x08010F02}, {0x00000134}, {0x00000145}, {0x00000156},	/* 14 */
	{0x00000167}, {0x00000178}, {0x00000189}, {0x0000019A}, {0x000001A3},	/* 19 */
	{0x000001AC}, {0x000001B5}, {0x000001BE}, {0x000001C7}, {0x000001D0},	/* 24 */
	{0x000001D9}, {0x000001DE}, {0x000001E3}, {0x000001E6}, {0x000001EB},	/* 29 */
	{0x000001F0}, {0x08010502}, {0x000001F3}, {0x000001F6}, {0x000001F9},	/* 34 */
	{0x000001FC}, {0x08040102}, {0x08010402}, {0x000001FF}, {0x08030202},	/* 39 */
	{0x08020302}, {0x07030102}, {0x07030102}, {0x07010302}, {0x07010302},	/* 44 */
	{0x08030001}, {0x08000301}, {0x07020202}, {0x07020202}, {0x06020102},	/* 49 */
	{0x06020102}, {0x06020102}, {0x06020102}, {0x06010202}, {0x06010202},	/* 54 */
	{0x06010202}, {0x06010202}, {0x06020001}, {0x06020001}, {0x06020001},	/* 59 */
	{0x06020001}, {0x06000201}, {0x06000201}, {0x06000201}, {0x06000201},	/* 64 */
	{0x04010102}, {0x04010102}, {0x04010102}, {0x04010102}, {0x04010102},	/* 69 */
	{0x04010102}, {0x04010102}, {0x04010102}, {0x04010102}, {0x04010102},	/* 74 */
	{0x04010102}, {0x04010102}, {0x04010102}, {0x04010102}, {0x04010102},	/* 79 */
	{0x04010102}, {0x04010001}, {0x04010001}, {0x04010001}, {0x04010001},	/* 84 */
	{0x04010001}, {0x04010001}, {0x04010001}, {0x04010001}, {0x04010001},	/* 89 */
	{0x04010001}, {0x04010001}, {0x04010001}, {0x04010001}, {0x04010001},	/* 94 */
	{0x04010001}, {0x04010001}, {0x03000101}, {0x03000101}, {0x03000101},	/* 99 */
	{0x03000101}, {0x03000101}, {0x03000101}, {0x03000101}, {0x03000101},	/* 104 */
	{0x03000101}, {0x03000101}, {0x03000101}, {0x03000101}, {0x03000101},	/* 109 */
	{0x03000101}, {0x03000101}, {0x03000101}, {0x03000101}, {0x03000101},	/* 114 */
	{0x03000101}, {0x03000101}, {0x03000101}, {0x03000101}, {0x03000101},	/* 119 */
	{0x03000101}, {0x03000101}, {0x03000101}, {0x03000101}, {0x03000101},	/* 124 */
	{0x03000101}, {0x03000101}, {0x03000101}, {0x03000101}, {0x01000000},	/* 129 */
	{0x01000000}, {0x01000000}, {0x01000000}, {0x01000000}, {0x01000000},	/* 134 */
	{0x01000000}, {0x01000000}, {0x01000000}, {0x01000000}, {0x01000000},	/* 139 */
	{0x01000000}, {0x01000000}, {0x01000000}, {0x01000000}, {0x01000000},	/* 144 */
	{0x01000000}, {0x01000000}, {0x01000000}, {0x01000000}, {0x01000000},	/* 149 */
	{0x01000000}, {0x01000000}, {0x01000000}, {0x01000000}, {0x01000000},	/* 154 */
	{0x01000000}, {0x01000000}, {0x01000000}, {0x01000000}, {0x01000000},	/* 159 */
	{0x01000000}, {0x01000000}, {0x01000000}, {0x01000000}, {0x01000000},	/* 164 */
	{0x01000000}, {0x01000000}, {0x01000000}, {0x01000000}, {0x01000000},	/* 169 */
	{0x01000000}, {0x01000000}, {0x01000000}, {0x01000000}, {0x01000000},	/* 174 */
	{0x01000000}, {0x01000000}, {0x01000000}, {0x01000000}, {0x01000000},	/* 179 */
	{0x01000000}, {0x01000000}, {0x01000000}, {0x01000000}, {0x01000000},	/* 184 */
	{0x01000000}, {0x01000000}, {0x01000000}, {0x01000000}, {0x01000000},	/* 189 */
	{0x01000000}, {0x01000000}, {0x01000000}, {0x01000000}, {0x01000000},	/* 194 */
	{0x01000000}, {0x01000000}, {0x01000000}, {0x01000000}, {0x01000000},	/* 199 */
	{0x01000000}, {0x01000000}, {0x01000000}, {0x01000000}, {0x01000000},	/* 204 */
	{0x01000000}, {0x01000000}, {0x01000000}, {0x01000000}, {0x01000000},	/* 209 */
	{0x01000000}, {0x01000000}, {0x01000000}, {0x01000000}, {0x01000000},	/* 214 */
	{0x01000000}, {0x01000000}, {0x01000000}, {0x01000000}, {0x01000000},	/* 219 */
	{0x01000000}, {0x01000000}, {0x01000000}, {0x01000000}, {0x01000000},	/* 224 */
	{0x01000000}, {0x01000000}, {0x01000000}, {0x01000000}, {0x01000000},	/* 229 */
	{0x01000000}, {0x01000000}, {0x01000000}, {0x01000000}, {0x01000000},	/* 234 */
	{0x01000000}, {0x01000000}, {0x01000000}, {0x01000000}, {0x01000000},	/* 239 */
	{0x01000000}, {0x01000000}, {0x01000000}, {0x01000000}, {0x01000000},	/* 244 */
	{0x01000000}, {0x01000000}, {0x01000000}, {0x01000000}, {0x01000000},	/* 249 */
	{0x01000000}, {0x01000000}, {0x01000000}, {0x01000000}, {0x01000000},	/* 254 */
	{0x01000000}, {0x01000000}, {0xFF000003}, {0x030F0E02}, {0x030E0F02},	/* 259 */
	{0x030F0D02}, {0x030D0F02}, {0x030F0C02}, {0x030C0F02}, {0x030F0B02},	/* 264 */
	{0x030B0F02}, {0xFF000003}, {0x020F0A02}, {0x020F0A02}, {0x030A0F02},	/* 269 */
	{0x030F0902}, {0x03090F02}, {0x03080F02}, {0x020F0802}, {0x020F0802},	/* 274 */
	{0xFF000002}, {0x020F0702}, {0x02070F02}, {0x020F0602}, {0x02060F02},	/* 279 */
	{0xFF000002}, {0x020F0502}, {0x02050F02}, {0x010F0402}, {0x010F0402},	/* 284 */
	{0xFF000001}, {0x01040F02}, {0x01030F02}, {0xFF000004}, {0x01000F01},	/* 289 */
	{0x01000F01}, {0x01000F01}, {0x01000F01}, {0x01000F01}, {0x01000F01},	/* 294 */
	{0x01000F01}, {0x01000F01}, {0x020F0302}, {0x020F0302}, {0x020F0302},	/* 299 */
	{0x020F0302}, {0x000000E2}, {0x000000F3}, {0x000000FC}, {0x00000105},	/* 304 */
	{0xFF000001}, {0x010F0202}, {0x010F0001}, {0xFF000004}, {0x000000FA},	/* 309 */
	{0x000000FF}, {0x00000104}, {0x00000109}, {0x0000010C}, {0x00000111},	/* 314 */
	{0x00000116}, {0x00000119}, {0x0000011E}, {0x00000123}, {0x00000128},	/* 319 */
	{0x04030E02}, {0x0000012D}, {0x00000130}, {0x00000133}, {0x00000136},	/* 324 */
	{0xFF000004}, {0x00000128}, {0x0000012B}, {0x0000012E}, {0x040D0001},	/* 329 */
	{0x00000131}, {0x00000134}, {0x00000137}, {0x040C0302}, {0x0000013A},	/* 334 */
	{0x040C0102}, {0x04000C01}, {0x0000013D}, {0x03020E02}, {0x03020E02},	/* 339 */
	{0x040E0202}, {0x040E0102}, {0xFF000004}, {0x04030D02}, {0x040D0202},	/* 344 */
	{0x04020D02}, {0x04010D02}, {0x040B0302}, {0x0000012F}, {0x030D0102},	/* 349 */
	{0x030D0102}, {0x04040C02}, {0x040B0602}, {0x04030C02}, {0x04070A02},	/* 354 */
	{0x030C0202}, {0x030C0202}, {0x04020C02}, {0x04050B02}, {0xFF000004},	/* 359 */
	{0x04010C02}, {0x040C0001}, {0x040B0402}, {0x04040B02}, {0x040A0602},	/* 364 */
	{0x04060A02}, {0x03030B02}, {0x03030B02}, {0x040A0502}, {0x04050A02},	/* 369 */
	{0x030B0202}, {0x030B0202}, {0x03020B02}, {0x03020B02}, {0x030B0102},	/* 374 */
	{0x030B0102}, {0xFF000004}, {0x03010B02}, {0x03010B02}, {0x040B0001},	/* 379 */
	{0x04000B01}, {0x04090602}, {0x04060902}, {0x040A0402}, {0x04040A02},	/* 384 */
	{0x04080702}, {0x04070802}, {0x03030A02}, {0x03030A02}, {0x040A0302},	/* 389 */
	{0x04090502}, {0x030A0202}, {0x030A0202}, {0xFF000004}, {0x04050902},	/* 394 */
	{0x04080602}, {0x03010A02}, {0x03010A02}, {0x04060802}, {0x04070702},	/* 399 */
	{0x03040902}, {0x03040902}, {0x04090402}, {0x04070502}, {0x03070602},	/* 404 */
	{0x03070602}, {0x02020A02}, {0x02020A02}, {0x02020A02}, {0x02020A02},	/* 409 */
	{0xFF000003}, {0x020A0102}, {0x020A0102}, {0x030A0001}, {0x03000A01},	/* 414 */
	{0x03090302}, {0x03030902}, {0x03080502}, {0x03050802}, {0xFF000003},	/* 419 */
	{0x02090202}, {0x02090202}, {0x02020902}, {0x02020902}, {0x03060702},	/* 424 */
	{0x03090001}, {0x02090102}, {0x02090102}, {0xFF000003}, {0x02010902},	/* 429 */
	{0x02010902}, {0x03000901}, {0x03080402}, {0x03040802}, {0x03050702},	/* 434 */
	{0x03080302}, {0x03030802}, {0xFF000003}, {0x03060602}, {0x03080202},	/* 439 */
	{0x02020802}, {0x02020802}, {0x03070402}, {0x03040702}, {0x02080102},	/* 444 */
	{0x02080102}, {0xFF000003}, {0x02010802}, {0x02010802}, {0x02000801},	/* 449 */
	{0x02000801}, {0x03080001}, {0x03060502}, {0x02070302}, {0x02070302},	/* 454 */
	{0xFF000003}, {0x02030702}, {0x02030702}, {0x03050602}, {0x03060402},	/* 459 */
	{0x02070202}, {0x02070202}, {0x02020702}, {0x02020702}, {0xFF000003},	/* 464 */
	{0x03040602}, {0x03050502}, {0x02070001}, {0x02070001}, {0x01070102},	/* 469 */
	{0x01070102}, {0x01070102}, {0x01070102}, {0xFF000002}, {0x01010702},	/* 474 */
	{0x01010702}, {0x02000701}, {0x02060302}, {0xFF000002}, {0x02030602},	/* 479 */
	{0x02050402}, {0x02040502}, {0x02060202}, {0xFF000001}, {0x01020602},	/* 484 */
	{0x01060102}, {0xFF000002}, {0x01010602}, {0x01010602}, {0x02060001},	/* 489 */
	{0x02000601}, {0xFF000002}, {0x01030502}, {0x01030502}, {0x02050302},	/* 494 */
	{0x02040402}, {0xFF000001}, {0x01050202}, {0x01020502}, {0xFF000001},	/* 499 */
	{0x01050102}, {0x01050001}, {0xFF000001}, {0x01040302}, {0x01030402},	/* 504 */
	{0xFF000001}, {0x01000501}, {0x01040202}, {0xFF000001}, {0x01020402},	/* 509 */
	{0x01030302}, {0xFF000001}, {0x01040001}, {0x01000401}, {0xFF000004},	/* 514 */
	{0x040E0C02}, {0x00000086}, {0x030E0D02}, {0x030E0D02}, {0x03090E02},	/* 519 */
	{0x03090E02}, {0x040A0E02}, {0x04090D02}, {0x020E0E02}, {0x020E0E02},	/* 524 */
	{0x020E0E02}, {0x020E0E02}, {0x030D0E02}, {0x030D0E02}, {0x030B0E02},	/* 529 */
	{0x030B0E02}, {0xFF000003}, {0x020E0B02}, {0x020E0B02}, {0x020D0C02},	/* 534 */
	{0x020D0C02}, {0x030C0D02}, {0x030B0D02}, {0x020E0A02}, {0x020E0A02},	/* 539 */
	{0xFF000003}, {0x020C0C02}, {0x020C0C02}, {0x030D0A02}, {0x030A0D02},	/* 544 */
	{0x030E0702}, {0x030C0A02}, {0x020A0C02}, {0x020A0C02}, {0xFF000003},	/* 549 */
	{0x03090C02}, {0x030D0702}, {0x020E0502}, {0x020E0502}, {0x010D0B02},	/* 554 */
	{0x010D0B02}, {0x010D0B02}, {0x010D0B02}, {0xFF000002}, {0x010E0902},	/* 559 */
	{0x010E0902}, {0x020C0B02}, {0x020B0C02}, {0xFF000002}, {0x020E0802},	/* 564 */
	{0x02080E02}, {0x020D0902}, {0x02070E02}, {0xFF000002}, {0x020B0B02},	/* 569 */
	{0x020D0802}, {0x02080D02}, {0x020E0602}, {0xFF000001}, {0x01060E02},	/* 574 */
	{0x010C0902}, {0xFF000002}, {0x020B0A02}, {0x020A0B02}, {0x02050E02},	/* 579 */
	{0x02070D02}, {0xFF000002}, {0x010E0402}, {0x010E0402}, {0x02040E02},	/* 584 */
	{0x020C0802}, {0xFF000001}, {0x01080C02}, {0x010E0302}, {0xFF000002},	/* 589 */
	{0x010D0602}, {0x010D0602}, {0x02060D02}, {0x020B0902}, {0xFF000002},	/* 594 */
	{0x02090B02}, {0x020A0A02}, {0x01010E02}, {0x01010E02}, {0xFF000002},	/* 599 */
	{0x01040D02}, {0x01040D02}, {0x02080B02}, {0x02090A02}, {0xFF000002},	/* 604 */
	{0x010B0702}, {0x010B0702}, {0x02070B02}, {0x02000D01}, {0xFF000001},	/* 609 */
	{0x010E0001}, {0x01000E01}, {0xFF000001}, {0x010D0502}, {0x01050D02},	/* 614 */
	{0xFF000001}, {0x010C0702}, {0x01070C02}, {0xFF000001}, {0x010D0402},	/* 619 */
	{0x010B0802}, {0xFF000001}, {0x010A0902}, {0x010C0602}, {0xFF000001},	/* 624 */
	{0x01060C02}, {0x010D0302}, {0xFF000001}, {0x010C0502}, {0x01050C02},	/* 629 */
	{0xFF000001}, {0x010A0802}, {0x01080A02}, {0xFF000001}, {0x01090902},	/* 634 */
	{0x010C0402}, {0xFF000001}, {0x01060B02}, {0x010A0702}, {0xFF000001},	/* 639 */
	{0x010B0502}, {0x01090802}, {0xFF000001}, {0x01080902}, {0x01090702},	/* 644 */
	{0xFF000001}, {0x01070902}, {0x01080802}, {0xFF000001}, {0x010C0E02},	/* 649 */
	{0x010D0D02},
};

/* max table bits  8 */
/* NO XING TABLE 17 */
/* NO XING TABLE 18 */
/* NO XING TABLE 19 */
/* NO XING TABLE 20 */
/* NO XING TABLE 21 */
/* NO XING TABLE 22 */
/* NO XING TABLE 23 */

/* TABLE 24  256 entries  maxbits 12  linbits  0 */
static HUFF_ELEMENT huff_table_24[] =
{
	{0xFF000009}, {0x080F0E02}, {0x080F0E02}, {0x080E0F02}, {0x080E0F02},	/*  4 */
	{0x080F0D02}, {0x080F0D02}, {0x080D0F02}, {0x080D0F02}, {0x080F0C02},	/*  9 */
	{0x080F0C02}, {0x080C0F02}, {0x080C0F02}, {0x080F0B02}, {0x080F0B02},	/* 14 */
	{0x080B0F02}, {0x080B0F02}, {0x070A0F02}, {0x070A0F02}, {0x070A0F02},	/* 19 */
	{0x070A0F02}, {0x080F0A02}, {0x080F0A02}, {0x080F0902}, {0x080F0902},	/* 24 */
	{0x07090F02}, {0x07090F02}, {0x07090F02}, {0x07090F02}, {0x07080F02},	/* 29 */
	{0x07080F02}, {0x07080F02}, {0x07080F02}, {0x080F0802}, {0x080F0802},	/* 34 */
	{0x080F0702}, {0x080F0702}, {0x07070F02}, {0x07070F02}, {0x07070F02},	/* 39 */
	{0x07070F02}, {0x070F0602}, {0x070F0602}, {0x070F0602}, {0x070F0602},	/* 44 */
	{0x07060F02}, {0x07060F02}, {0x07060F02}, {0x07060F02}, {0x070F0502},	/* 49 */
	{0x070F0502}, {0x070F0502}, {0x070F0502}, {0x07050F02}, {0x07050F02},	/* 54 */
	{0x07050F02}, {0x07050F02}, {0x070F0402}, {0x070F0402}, {0x070F0402},	/* 59 */
	{0x070F0402}, {0x07040F02}, {0x07040F02}, {0x07040F02}, {0x07040F02},	/* 64 */
	{0x070F0302}, {0x070F0302}, {0x070F0302}, {0x070F0302}, {0x07030F02},	/* 69 */
	{0x07030F02}, {0x07030F02}, {0x07030F02}, {0x070F0202}, {0x070F0202},	/* 74 */
	{0x070F0202}, {0x070F0202}, {0x07020F02}, {0x07020F02}, {0x07020F02},	/* 79 */
	{0x07020F02}, {0x07010F02}, {0x07010F02}, {0x07010F02}, {0x07010F02},	/* 84 */
	{0x080F0102}, {0x080F0102}, {0x08000F01}, {0x08000F01}, {0x090F0001},	/* 89 */
	{0x00000201}, {0x00000206}, {0x0000020B}, {0x00000210}, {0x00000215},	/* 94 */
	{0x0000021A}, {0x0000021F}, {0x040F0F02}, {0x040F0F02}, {0x040F0F02},	/* 99 */
	{0x040F0F02}, {0x040F0F02}, {0x040F0F02}, {0x040F0F02}, {0x040F0F02},	/* 104 */
	{0x040F0F02}, {0x040F0F02}, {0x040F0F02}, {0x040F0F02}, {0x040F0F02},	/* 109 */
	{0x040F0F02}, {0x040F0F02}, {0x040F0F02}, {0x040F0F02}, {0x040F0F02},	/* 114 */
	{0x040F0F02}, {0x040F0F02}, {0x040F0F02}, {0x040F0F02}, {0x040F0F02},	/* 119 */
	{0x040F0F02}, {0x040F0F02}, {0x040F0F02}, {0x040F0F02}, {0x040F0F02},	/* 124 */
	{0x040F0F02}, {0x040F0F02}, {0x040F0F02}, {0x040F0F02}, {0x00000224},	/* 129 */
	{0x00000229}, {0x00000232}, {0x00000237}, {0x0000023A}, {0x0000023F},	/* 134 */
	{0x00000242}, {0x00000245}, {0x0000024A}, {0x0000024D}, {0x00000250},	/* 139 */
	{0x00000253}, {0x00000256}, {0x00000259}, {0x0000025C}, {0x0000025F},	/* 144 */
	{0x00000262}, {0x00000265}, {0x00000268}, {0x0000026B}, {0x0000026E},	/* 149 */
	{0x00000271}, {0x00000274}, {0x00000277}, {0x0000027A}, {0x0000027D},	/* 154 */
	{0x00000280}, {0x00000283}, {0x00000288}, {0x0000028B}, {0x0000028E},	/* 159 */
	{0x00000291}, {0x00000294}, {0x00000297}, {0x0000029A}, {0x0000029F},	/* 164 */
	{0x09040B02}, {0x000002A4}, {0x000002A7}, {0x000002AA}, {0x09030B02},	/* 169 */
	{0x09080802}, {0x000002AF}, {0x09020B02}, {0x000002B2}, {0x000002B5},	/* 174 */
	{0x09060902}, {0x09040A02}, {0x000002B8}, {0x09070802}, {0x090A0302},	/* 179 */
	{0x09030A02}, {0x09090502}, {0x09050902}, {0x090A0202}, {0x09020A02},	/* 184 */
	{0x09010A02}, {0x09080602}, {0x09060802}, {0x09070702}, {0x09090402},	/* 189 */
	{0x09040902}, {0x09090302}, {0x09030902}, {0x09080502}, {0x09050802},	/* 194 */
	{0x09090202}, {0x09070602}, {0x09060702}, {0x09020902}, {0x09090102},	/* 199 */
	{0x09010902}, {0x09080402}, {0x09040802}, {0x09070502}, {0x09050702},	/* 204 */
	{0x09080302}, {0x09030802}, {0x09060602}, {0x09080202}, {0x09020802},	/* 209 */
	{0x09080102}, {0x09070402}, {0x09040702}, {0x09010802}, {0x000002BB},	/* 214 */
	{0x09060502}, {0x09050602}, {0x09070102}, {0x000002BE}, {0x08030702},	/* 219 */
	{0x08030702}, {0x09070302}, {0x09070202}, {0x08020702}, {0x08020702},	/* 224 */
	{0x08060402}, {0x08060402}, {0x08040602}, {0x08040602}, {0x08050502},	/* 229 */
	{0x08050502}, {0x08010702}, {0x08010702}, {0x08060302}, {0x08060302},	/* 234 */
	{0x08030602}, {0x08030602}, {0x08050402}, {0x08050402}, {0x08040502},	/* 239 */
	{0x08040502}, {0x08060202}, {0x08060202}, {0x08020602}, {0x08020602},	/* 244 */
	{0x08060102}, {0x08060102}, {0x08010602}, {0x08010602}, {0x09060001},	/* 249 */
	{0x09000601}, {0x08050302}, {0x08050302}, {0x08030502}, {0x08030502},	/* 254 */
	{0x08040402}, {0x08040402}, {0x08050202}, {0x08050202}, {0x08020502},	/* 259 */
	{0x08020502}, {0x08050102}, {0x08050102}, {0x09050001}, {0x09000501},	/* 264 */
	{0x07010502}, {0x07010502}, {0x07010502}, {0x07010502}, {0x08040302},	/* 269 */
	{0x08040302}, {0x08030402}, {0x08030402}, {0x07040202}, {0x07040202},	/* 274 */
	{0x07040202}, {0x07040202}, {0x07020402}, {0x07020402}, {0x07020402},	/* 279 */
	{0x07020402}, {0x07030302}, {0x07030302}, {0x07030302}, {0x07030302},	/* 284 */
	{0x07040102}, {0x07040102}, {0x07040102}, {0x07040102}, {0x07010402},	/* 289 */
	{0x07010402}, {0x07010402}, {0x07010402}, {0x08040001}, {0x08040001},	/* 294 */
	{0x08000401}, {0x08000401}, {0x07030202}, {0x07030202}, {0x07030202},	/* 299 */
	{0x07030202}, {0x07020302}, {0x07020302}, {0x07020302}, {0x07020302},	/* 304 */
	{0x06030102}, {0x06030102}, {0x06030102}, {0x06030102}, {0x06030102},	/* 309 */
	{0x06030102}, {0x06030102}, {0x06030102}, {0x06010302}, {0x06010302},	/* 314 */
	{0x06010302}, {0x06010302}, {0x06010302}, {0x06010302}, {0x06010302},	/* 319 */
	{0x06010302}, {0x07030001}, {0x07030001}, {0x07030001}, {0x07030001},	/* 324 */
	{0x07000301}, {0x07000301}, {0x07000301}, {0x07000301}, {0x06020202},	/* 329 */
	{0x06020202}, {0x06020202}, {0x06020202}, {0x06020202}, {0x06020202},	/* 334 */
	{0x06020202}, {0x06020202}, {0x05020102}, {0x05020102}, {0x05020102},	/* 339 */
	{0x05020102}, {0x05020102}, {0x05020102}, {0x05020102}, {0x05020102},	/* 344 */
	{0x05020102}, {0x05020102}, {0x05020102}, {0x05020102}, {0x05020102},	/* 349 */
	{0x05020102}, {0x05020102}, {0x05020102}, {0x05010202}, {0x05010202},	/* 354 */
	{0x05010202}, {0x05010202}, {0x05010202}, {0x05010202}, {0x05010202},	/* 359 */
	{0x05010202}, {0x05010202}, {0x05010202}, {0x05010202}, {0x05010202},	/* 364 */
	{0x05010202}, {0x05010202}, {0x05010202}, {0x05010202}, {0x06020001},	/* 369 */
	{0x06020001}, {0x06020001}, {0x06020001}, {0x06020001}, {0x06020001},	/* 374 */
	{0x06020001}, {0x06020001}, {0x06000201}, {0x06000201}, {0x06000201},	/* 379 */
	{0x06000201}, {0x06000201}, {0x06000201}, {0x06000201}, {0x06000201},	/* 384 */
	{0x04010102}, {0x04010102}, {0x04010102}, {0x04010102}, {0x04010102},	/* 389 */
	{0x04010102}, {0x04010102}, {0x04010102}, {0x04010102}, {0x04010102},	/* 394 */
	{0x04010102}, {0x04010102}, {0x04010102}, {0x04010102}, {0x04010102},	/* 399 */
	{0x04010102}, {0x04010102}, {0x04010102}, {0x04010102}, {0x04010102},	/* 404 */
	{0x04010102}, {0x04010102}, {0x04010102}, {0x04010102}, {0x04010102},	/* 409 */
	{0x04010102}, {0x04010102}, {0x04010102}, {0x04010102}, {0x04010102},	/* 414 */
	{0x04010102}, {0x04010102}, {0x04010001}, {0x04010001}, {0x04010001},	/* 419 */
	{0x04010001}, {0x04010001}, {0x04010001}, {0x04010001}, {0x04010001},	/* 424 */
	{0x04010001}, {0x04010001}, {0x04010001}, {0x04010001}, {0x04010001},	/* 429 */
	{0x04010001}, {0x04010001}, {0x04010001}, {0x04010001}, {0x04010001},	/* 434 */
	{0x04010001}, {0x04010001}, {0x04010001}, {0x04010001}, {0x04010001},	/* 439 */
	{0x04010001}, {0x04010001}, {0x04010001}, {0x04010001}, {0x04010001},	/* 444 */
	{0x04010001}, {0x04010001}, {0x04010001}, {0x04010001}, {0x04000101},	/* 449 */
	{0x04000101}, {0x04000101}, {0x04000101}, {0x04000101}, {0x04000101},	/* 454 */
	{0x04000101}, {0x04000101}, {0x04000101}, {0x04000101}, {0x04000101},	/* 459 */
	{0x04000101}, {0x04000101}, {0x04000101}, {0x04000101}, {0x04000101},	/* 464 */
	{0x04000101}, {0x04000101}, {0x04000101}, {0x04000101}, {0x04000101},	/* 469 */
	{0x04000101}, {0x04000101}, {0x04000101}, {0x04000101}, {0x04000101},	/* 474 */
	{0x04000101}, {0x04000101}, {0x04000101}, {0x04000101}, {0x04000101},	/* 479 */
	{0x04000101}, {0x04000000}, {0x04000000}, {0x04000000}, {0x04000000},	/* 484 */
	{0x04000000}, {0x04000000}, {0x04000000}, {0x04000000}, {0x04000000},	/* 489 */
	{0x04000000}, {0x04000000}, {0x04000000}, {0x04000000}, {0x04000000},	/* 494 */
	{0x04000000}, {0x04000000}, {0x04000000}, {0x04000000}, {0x04000000},	/* 499 */
	{0x04000000}, {0x04000000}, {0x04000000}, {0x04000000}, {0x04000000},	/* 504 */
	{0x04000000}, {0x04000000}, {0x04000000}, {0x04000000}, {0x04000000},	/* 509 */
	{0x04000000}, {0x04000000}, {0x04000000}, {0xFF000002}, {0x020E0E02},	/* 514 */
	{0x020E0D02}, {0x020D0E02}, {0x020E0C02}, {0xFF000002}, {0x020C0E02},	/* 519 */
	{0x020D0D02}, {0x020E0B02}, {0x020B0E02}, {0xFF000002}, {0x020D0C02},	/* 524 */
	{0x020C0D02}, {0x020E0A02}, {0x020A0E02}, {0xFF000002}, {0x020D0B02},	/* 529 */
	{0x020B0D02}, {0x020C0C02}, {0x020E0902}, {0xFF000002}, {0x02090E02},	/* 534 */
	{0x020D0A02}, {0x020A0D02}, {0x020C0B02}, {0xFF000002}, {0x020B0C02},	/* 539 */
	{0x020E0802}, {0x02080E02}, {0x020D0902}, {0xFF000002}, {0x02090D02},	/* 544 */
	{0x020E0702}, {0x02070E02}, {0x020C0A02}, {0xFF000002}, {0x020A0C02},	/* 549 */
	{0x020B0B02}, {0x020D0802}, {0x02080D02}, {0xFF000003}, {0x030E0001},	/* 554 */
	{0x03000E01}, {0x020D0001}, {0x020D0001}, {0x01060E02}, {0x01060E02},	/* 559 */
	{0x01060E02}, {0x01060E02}, {0xFF000002}, {0x020E0602}, {0x020C0902},	/* 564 */
	{0x01090C02}, {0x01090C02}, {0xFF000001}, {0x010E0502}, {0x010A0B02},	/* 569 */
	{0xFF000002}, {0x01050E02}, {0x01050E02}, {0x020B0A02}, {0x020D0702},	/* 574 */
	{0xFF000001}, {0x01070D02}, {0x01040E02}, {0xFF000001}, {0x010C0802},	/* 579 */
	{0x01080C02}, {0xFF000002}, {0x020E0402}, {0x020E0202}, {0x010E0302},	/* 584 */
	{0x010E0302}, {0xFF000001}, {0x010D0602}, {0x01060D02}, {0xFF000001},	/* 589 */
	{0x01030E02}, {0x010B0902}, {0xFF000001}, {0x01090B02}, {0x010A0A02},	/* 594 */
	{0xFF000001}, {0x01020E02}, {0x010E0102}, {0xFF000001}, {0x01010E02},	/* 599 */
	{0x010D0502}, {0xFF000001}, {0x01050D02}, {0x010C0702}, {0xFF000001},	/* 604 */
	{0x01070C02}, {0x010D0402}, {0xFF000001}, {0x010B0802}, {0x01080B02},	/* 609 */
	{0xFF000001}, {0x01040D02}, {0x010A0902}, {0xFF000001}, {0x01090A02},	/* 614 */
	{0x010C0602}, {0xFF000001}, {0x01060C02}, {0x010D0302}, {0xFF000001},	/* 619 */
	{0x01030D02}, {0x010D0202}, {0xFF000001}, {0x01020D02}, {0x010D0102},	/* 624 */
	{0xFF000001}, {0x010B0702}, {0x01070B02}, {0xFF000001}, {0x01010D02},	/* 629 */
	{0x010C0502}, {0xFF000001}, {0x01050C02}, {0x010A0802}, {0xFF000001},	/* 634 */
	{0x01080A02}, {0x01090902}, {0xFF000001}, {0x010C0402}, {0x01040C02},	/* 639 */
	{0xFF000001}, {0x010B0602}, {0x01060B02}, {0xFF000002}, {0x02000D01},	/* 644 */
	{0x020C0001}, {0x010C0302}, {0x010C0302}, {0xFF000001}, {0x01030C02},	/* 649 */
	{0x010A0702}, {0xFF000001}, {0x01070A02}, {0x010C0202}, {0xFF000001},	/* 654 */
	{0x01020C02}, {0x010B0502}, {0xFF000001}, {0x01050B02}, {0x010C0102},	/* 659 */
	{0xFF000001}, {0x01090802}, {0x01080902}, {0xFF000001}, {0x01010C02},	/* 664 */
	{0x010B0402}, {0xFF000002}, {0x02000C01}, {0x020B0001}, {0x010B0302},	/* 669 */
	{0x010B0302}, {0xFF000002}, {0x02000B01}, {0x020A0001}, {0x010A0102},	/* 674 */
	{0x010A0102}, {0xFF000001}, {0x010A0602}, {0x01060A02}, {0xFF000001},	/* 679 */
	{0x01090702}, {0x01070902}, {0xFF000002}, {0x02000A01}, {0x02090001},	/* 684 */
	{0x01000901}, {0x01000901}, {0xFF000001}, {0x010B0202}, {0x010A0502},	/* 689 */
	{0xFF000001}, {0x01050A02}, {0x010B0102}, {0xFF000001}, {0x01010B02},	/* 694 */
	{0x01090602}, {0xFF000001}, {0x010A0402}, {0x01080702}, {0xFF000001},	/* 699 */
	{0x01080001}, {0x01000801}, {0xFF000001}, {0x01070001}, {0x01000701},	/* 704 */
};

/* max table bits  9 */
/* NO XING TABLE 25 */
/* NO XING TABLE 26 */
/* NO XING TABLE 27 */
/* NO XING TABLE 28 */
/* NO XING TABLE 29 */
/* NO XING TABLE 30 */
/* NO XING TABLE 31 */
/* done */
//---------------------------------------------------------------------------
#endif // KMp3HufTable_H