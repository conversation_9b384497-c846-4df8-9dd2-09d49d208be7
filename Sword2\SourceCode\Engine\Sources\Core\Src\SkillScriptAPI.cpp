//---------------------------------------------------------------------------
// Sword2 Skill Script API Implementation (c) 2024
//
// File:	SkillScriptAPI.cpp
// Date:	2024.07.02
// Code:	AI Assistant
// Desc:	Implementation of skill-related Lua script API functions
//---------------------------------------------------------------------------

#include "SkillScriptIntegration.h"
#include "SkillManager.h"
#include "PlayerManager.h"
#include "LuaScriptEngine.h"

namespace sword2 {
namespace SkillScriptAPI {

// 辅助函数：获取技能ID参数
uint32_t GetSkillIdFromLua(lua_State* L, int index = 1)
{
    if (lua_isnumber(L, index))
    {
        return static_cast<uint32_t>(lua_tonumber(L, index));
    }
    return 0;
}

// 辅助函数：获取技能模板
std::shared_ptr<SkillTemplate> GetSkillTemplateFromLua(lua_State* L, int index = 1)
{
    uint32_t skillId = GetSkillIdFromLua(L, index);
    if (skillId == 0) return nullptr;
    
    return SKILL_MANAGER().GetSkillTemplate(skillId);
}

// 技能基础API实现
int GetSkillName(lua_State* L)
{
    auto skillTemplate = GetSkillTemplateFromLua(L);
    if (skillTemplate)
    {
        lua_pushstring(L, skillTemplate->name.c_str());
    }
    else
    {
        lua_pushstring(L, "");
    }
    return 1;
}

int GetSkillLevel(lua_State* L)
{
    if (lua_isnumber(L, 1) && lua_isnumber(L, 2))
    {
        uint32_t playerId = static_cast<uint32_t>(lua_tonumber(L, 1));
        uint32_t skillId = static_cast<uint32_t>(lua_tonumber(L, 2));
        
        // 这里应该从玩家数据中获取技能等级
        // 暂时返回1
        lua_pushnumber(L, 1);
    }
    else
    {
        lua_pushnumber(L, 0);
    }
    return 1;
}

int GetSkillMaxLevel(lua_State* L)
{
    auto skillTemplate = GetSkillTemplateFromLua(L);
    if (skillTemplate)
    {
        lua_pushnumber(L, skillTemplate->maxLevel);
    }
    else
    {
        lua_pushnumber(L, 0);
    }
    return 1;
}

int GetSkillType(lua_State* L)
{
    auto skillTemplate = GetSkillTemplateFromLua(L);
    if (skillTemplate)
    {
        lua_pushnumber(L, static_cast<int>(skillTemplate->type));
        lua_pushstring(L, skillTemplate->GetTypeDescription().c_str());
        return 2;
    }
    else
    {
        lua_pushnumber(L, 0);
        lua_pushstring(L, "Unknown");
        return 2;
    }
}

int GetSkillSeries(lua_State* L)
{
    auto skillTemplate = GetSkillTemplateFromLua(L);
    if (skillTemplate)
    {
        lua_pushnumber(L, static_cast<int>(skillTemplate->series));
        lua_pushstring(L, skillTemplate->GetSeriesDescription().c_str());
        return 2;
    }
    else
    {
        lua_pushnumber(L, 0);
        lua_pushstring(L, "Unknown");
        return 2;
    }
}

int GetSkillDescription(lua_State* L)
{
    auto skillTemplate = GetSkillTemplateFromLua(L);
    if (skillTemplate)
    {
        lua_pushstring(L, skillTemplate->description.c_str());
    }
    else
    {
        lua_pushstring(L, "");
    }
    return 1;
}

// 技能学习API实现
int LearnSkill(lua_State* L)
{
    if (lua_isnumber(L, 1) && lua_isnumber(L, 2))
    {
        uint32_t playerId = static_cast<uint32_t>(lua_tonumber(L, 1));
        uint32_t skillId = static_cast<uint32_t>(lua_tonumber(L, 2));
        
        bool success = LEARN_SKILL(playerId, skillId);
        lua_pushboolean(L, success ? 1 : 0);
        
        if (success)
        {
            LOG_INFO("SKILL_API", "Player " + std::to_string(playerId) + " learned skill " + std::to_string(skillId));
        }
    }
    else
    {
        lua_pushboolean(L, 0);
    }
    return 1;
}

int LevelUpSkill(lua_State* L)
{
    if (lua_isnumber(L, 1) && lua_isnumber(L, 2))
    {
        uint32_t playerId = static_cast<uint32_t>(lua_tonumber(L, 1));
        uint32_t skillId = static_cast<uint32_t>(lua_tonumber(L, 2));
        
        bool success = LEVELUP_SKILL(playerId, skillId);
        lua_pushboolean(L, success ? 1 : 0);
        
        if (success)
        {
            LOG_INFO("SKILL_API", "Player " + std::to_string(playerId) + " leveled up skill " + std::to_string(skillId));
        }
    }
    else
    {
        lua_pushboolean(L, 0);
    }
    return 1;
}

int ForgetSkill(lua_State* L)
{
    if (lua_isnumber(L, 1) && lua_isnumber(L, 2))
    {
        uint32_t playerId = static_cast<uint32_t>(lua_tonumber(L, 1));
        uint32_t skillId = static_cast<uint32_t>(lua_tonumber(L, 2));
        
        bool success = FORGET_SKILL(playerId, skillId);
        lua_pushboolean(L, success ? 1 : 0);
        
        if (success)
        {
            LOG_INFO("SKILL_API", "Player " + std::to_string(playerId) + " forgot skill " + std::to_string(skillId));
        }
    }
    else
    {
        lua_pushboolean(L, 0);
    }
    return 1;
}

int CanLearnSkill(lua_State* L)
{
    if (lua_isnumber(L, 1) && lua_isnumber(L, 2))
    {
        uint32_t playerId = static_cast<uint32_t>(lua_tonumber(L, 1));
        uint32_t skillId = static_cast<uint32_t>(lua_tonumber(L, 2));
        
        // 这里应该检查玩家是否可以学习该技能
        // 暂时返回true
        lua_pushboolean(L, 1);
    }
    else
    {
        lua_pushboolean(L, 0);
    }
    return 1;
}

int GetSkillRequirement(lua_State* L)
{
    auto skillTemplate = GetSkillTemplateFromLua(L);
    if (skillTemplate)
    {
        // 创建Lua表返回技能需求
        lua_newtable(L);
        
        lua_pushstring(L, "requiredLevel");
        lua_pushnumber(L, skillTemplate->requirement.requiredLevel);
        lua_settable(L, -3);
        
        lua_pushstring(L, "requiredMoney");
        lua_pushnumber(L, skillTemplate->requirement.requiredMoney);
        lua_settable(L, -3);
        
        lua_pushstring(L, "requiredSeries");
        lua_pushnumber(L, static_cast<int>(skillTemplate->requirement.requiredSeries));
        lua_settable(L, -3);
        
        // 前置技能列表
        lua_pushstring(L, "prerequisiteSkills");
        lua_newtable(L);
        for (size_t i = 0; i < skillTemplate->requirement.prerequisiteSkills.size(); ++i)
        {
            lua_pushnumber(L, i + 1);
            lua_pushnumber(L, skillTemplate->requirement.prerequisiteSkills[i]);
            lua_settable(L, -3);
        }
        lua_settable(L, -3);
    }
    else
    {
        lua_pushnil(L);
    }
    return 1;
}

int GetSkillUpgradeExp(lua_State* L)
{
    if (lua_isnumber(L, 1) && lua_isnumber(L, 2))
    {
        uint32_t skillId = static_cast<uint32_t>(lua_tonumber(L, 1));
        uint32_t currentLevel = static_cast<uint32_t>(lua_tonumber(L, 2));
        
        uint32_t expRequired = SKILL_MANAGER().GetSkillUpgradeExp(skillId, currentLevel);
        lua_pushnumber(L, expRequired);
    }
    else
    {
        lua_pushnumber(L, 0);
    }
    return 1;
}

// 技能使用API实现
int UseSkill(lua_State* L)
{
    if (lua_isnumber(L, 1) && lua_isnumber(L, 2))
    {
        uint32_t playerId = static_cast<uint32_t>(lua_tonumber(L, 1));
        uint32_t skillId = static_cast<uint32_t>(lua_tonumber(L, 2));
        uint32_t targetId = lua_isnumber(L, 3) ? static_cast<uint32_t>(lua_tonumber(L, 3)) : 0;
        int32_t x = lua_isnumber(L, 4) ? static_cast<int32_t>(lua_tonumber(L, 4)) : 0;
        int32_t y = lua_isnumber(L, 5) ? static_cast<int32_t>(lua_tonumber(L, 5)) : 0;
        
        SkillUseResult result = USE_SKILL(playerId, skillId, targetId, x, y);
        lua_pushnumber(L, static_cast<int>(result));
        
        if (result == SkillUseResult::Success)
        {
            LOG_INFO("SKILL_API", "Player " + std::to_string(playerId) + " used skill " + std::to_string(skillId));
        }
    }
    else
    {
        lua_pushnumber(L, static_cast<int>(SkillUseResult::Failed));
    }
    return 1;
}

int CanUseSkill(lua_State* L)
{
    if (lua_isnumber(L, 1) && lua_isnumber(L, 2))
    {
        uint32_t playerId = static_cast<uint32_t>(lua_tonumber(L, 1));
        uint32_t skillId = static_cast<uint32_t>(lua_tonumber(L, 2));
        
        // 这里应该检查玩家是否可以使用该技能
        // 暂时返回true
        lua_pushboolean(L, 1);
    }
    else
    {
        lua_pushboolean(L, 0);
    }
    return 1;
}

int GetSkillCooldown(lua_State* L)
{
    if (lua_isnumber(L, 1) && lua_isnumber(L, 2))
    {
        uint32_t playerId = static_cast<uint32_t>(lua_tonumber(L, 1));
        uint32_t skillId = static_cast<uint32_t>(lua_tonumber(L, 2));
        
        // 这里应该获取玩家技能的冷却时间
        // 暂时返回0
        lua_pushnumber(L, 0);
    }
    else
    {
        lua_pushnumber(L, 0);
    }
    return 1;
}

int GetSkillRange(lua_State* L)
{
    if (lua_isnumber(L, 1) && lua_isnumber(L, 2))
    {
        uint32_t skillId = static_cast<uint32_t>(lua_tonumber(L, 1));
        uint32_t level = lua_isnumber(L, 2) ? static_cast<uint32_t>(lua_tonumber(L, 2)) : 1;
        
        auto skillTemplate = GET_SKILL_TEMPLATE(skillId);
        if (skillTemplate)
        {
            const auto* levelData = skillTemplate->GetLevelData(level);
            if (levelData)
            {
                lua_pushnumber(L, levelData->range);
                return 1;
            }
        }
    }
    
    lua_pushnumber(L, 0);
    return 1;
}

int GetSkillCost(lua_State* L)
{
    if (lua_isnumber(L, 1) && lua_isnumber(L, 2))
    {
        uint32_t skillId = static_cast<uint32_t>(lua_tonumber(L, 1));
        uint32_t level = lua_isnumber(L, 2) ? static_cast<uint32_t>(lua_tonumber(L, 2)) : 1;
        
        auto skillTemplate = GET_SKILL_TEMPLATE(skillId);
        if (skillTemplate)
        {
            const auto* levelData = skillTemplate->GetLevelData(level);
            if (levelData && !levelData->costs.empty())
            {
                // 创建Lua表返回消耗信息
                lua_newtable(L);
                for (size_t i = 0; i < levelData->costs.size(); ++i)
                {
                    const auto& cost = levelData->costs[i];
                    
                    lua_pushnumber(L, i + 1);
                    lua_newtable(L);
                    
                    lua_pushstring(L, "type");
                    lua_pushnumber(L, static_cast<int>(cost.type));
                    lua_settable(L, -3);
                    
                    lua_pushstring(L, "amount");
                    lua_pushnumber(L, cost.amount);
                    lua_settable(L, -3);
                    
                    if (cost.itemId != 0)
                    {
                        lua_pushstring(L, "itemId");
                        lua_pushnumber(L, cost.itemId);
                        lua_settable(L, -3);
                    }
                    
                    lua_settable(L, -3);
                }
                return 1;
            }
        }
    }
    
    lua_newtable(L);
    return 1;
}

int GetSkillDamage(lua_State* L)
{
    if (lua_isnumber(L, 1) && lua_isnumber(L, 2))
    {
        uint32_t skillId = static_cast<uint32_t>(lua_tonumber(L, 1));
        uint32_t level = lua_isnumber(L, 2) ? static_cast<uint32_t>(lua_tonumber(L, 2)) : 1;
        
        auto skillTemplate = GET_SKILL_TEMPLATE(skillId);
        if (skillTemplate)
        {
            const auto* levelData = skillTemplate->GetLevelData(level);
            if (levelData)
            {
                lua_pushnumber(L, levelData->damage);
                return 1;
            }
        }
    }
    
    lua_pushnumber(L, 0);
    return 1;
}

// 技能效果API实现
int ApplySkillEffect(lua_State* L)
{
    if (lua_isnumber(L, 1) && lua_isnumber(L, 2) && lua_isnumber(L, 3) &&
        lua_isnumber(L, 4) && lua_isnumber(L, 5))
    {
        uint32_t skillId = static_cast<uint32_t>(lua_tonumber(L, 1));
        uint32_t casterId = static_cast<uint32_t>(lua_tonumber(L, 2));
        uint32_t targetId = static_cast<uint32_t>(lua_tonumber(L, 3));
        int effectType = static_cast<int>(lua_tonumber(L, 4));
        uint32_t value = static_cast<uint32_t>(lua_tonumber(L, 5));
        uint32_t duration = lua_isnumber(L, 6) ? static_cast<uint32_t>(lua_tonumber(L, 6)) : 0;

        uint32_t effectId = APPLY_SKILL_EFFECT(skillId, casterId, targetId,
                                              static_cast<SkillEffectType>(effectType), value, duration);
        lua_pushnumber(L, effectId);

        if (effectId != 0)
        {
            LOG_INFO("SKILL_API", "Applied skill effect " + std::to_string(effectId) +
                    " from skill " + std::to_string(skillId));
        }
    }
    else
    {
        lua_pushnumber(L, 0);
    }
    return 1;
}

int RemoveSkillEffect(lua_State* L)
{
    if (lua_isnumber(L, 1))
    {
        uint32_t effectId = static_cast<uint32_t>(lua_tonumber(L, 1));

        bool success = REMOVE_SKILL_EFFECT(effectId);
        lua_pushboolean(L, success ? 1 : 0);

        if (success)
        {
            LOG_INFO("SKILL_API", "Removed skill effect " + std::to_string(effectId));
        }
    }
    else
    {
        lua_pushboolean(L, 0);
    }
    return 1;
}

int GetSkillEffects(lua_State* L)
{
    if (lua_isnumber(L, 1))
    {
        uint32_t targetId = static_cast<uint32_t>(lua_tonumber(L, 1));

        auto effects = SKILL_EFFECT_MANAGER().GetTargetEffects(targetId);

        // 创建Lua表返回效果列表
        lua_newtable(L);
        for (size_t i = 0; i < effects.size(); ++i)
        {
            const auto& effect = effects[i];

            lua_pushnumber(L, i + 1);
            lua_newtable(L);

            lua_pushstring(L, "effectId");
            lua_pushnumber(L, effect.effectId);
            lua_settable(L, -3);

            lua_pushstring(L, "skillId");
            lua_pushnumber(L, effect.skillId);
            lua_settable(L, -3);

            lua_pushstring(L, "type");
            lua_pushnumber(L, static_cast<int>(effect.type));
            lua_settable(L, -3);

            lua_pushstring(L, "value");
            lua_pushnumber(L, effect.value);
            lua_settable(L, -3);

            lua_pushstring(L, "remainingTime");
            lua_pushnumber(L, effect.GetRemainingTime());
            lua_settable(L, -3);

            lua_settable(L, -3);
        }

        LOG_DEBUG("SKILL_API", "Found " + std::to_string(effects.size()) +
                 " effects for target " + std::to_string(targetId));
    }
    else
    {
        lua_newtable(L);
    }
    return 1;
}

int CalculateSkillDamage(lua_State* L)
{
    if (lua_isnumber(L, 1) && lua_isnumber(L, 2) && lua_isnumber(L, 3))
    {
        uint32_t skillId = static_cast<uint32_t>(lua_tonumber(L, 1));
        uint32_t level = static_cast<uint32_t>(lua_tonumber(L, 2));
        uint32_t casterId = static_cast<uint32_t>(lua_tonumber(L, 3));
        uint32_t targetId = lua_isnumber(L, 4) ? static_cast<uint32_t>(lua_tonumber(L, 4)) : 0;

        // 这里应该获取施法者和目标的Player对象进行计算
        // 暂时简化处理
        auto caster = GET_ONLINE_PLAYER(casterId);
        if (caster)
        {
            uint32_t damage = CALCULATE_SKILL_DAMAGE(skillId, level, *caster, nullptr);
            lua_pushnumber(L, damage);
        }
        else
        {
            lua_pushnumber(L, 0);
        }
    }
    else
    {
        lua_pushnumber(L, 0);
    }
    return 1;
}

int CalculateSkillHeal(lua_State* L)
{
    if (lua_isnumber(L, 1) && lua_isnumber(L, 2) && lua_isnumber(L, 3))
    {
        uint32_t skillId = static_cast<uint32_t>(lua_tonumber(L, 1));
        uint32_t level = static_cast<uint32_t>(lua_tonumber(L, 2));
        uint32_t casterId = static_cast<uint32_t>(lua_tonumber(L, 3));

        // 治疗量计算逻辑类似于伤害计算
        auto skillTemplate = GET_SKILL_TEMPLATE(skillId);
        if (skillTemplate)
        {
            const auto* levelData = skillTemplate->GetLevelData(level);
            if (levelData)
            {
                // 查找治疗效果
                for (const auto& effect : levelData->effects)
                {
                    if (effect.type == SkillEffectType::Heal)
                    {
                        lua_pushnumber(L, effect.value);
                        return 1;
                    }
                }
            }
        }
    }

    lua_pushnumber(L, 0);
    return 1;
}

// 技能树API实现
int GetFactionSkills(lua_State* L)
{
    if (lua_isnumber(L, 1))
    {
        int faction = static_cast<int>(lua_tonumber(L, 1));

        auto skills = GET_FACTION_SKILLS(static_cast<PlayerSeries>(faction));

        // 创建Lua表返回技能列表
        lua_newtable(L);
        for (size_t i = 0; i < skills.size(); ++i)
        {
            lua_pushnumber(L, i + 1);
            lua_pushnumber(L, skills[i]);
            lua_settable(L, -3);
        }

        LOG_DEBUG("SKILL_API", "Found " + std::to_string(skills.size()) +
                 " skills for faction " + std::to_string(faction));
    }
    else
    {
        lua_newtable(L);
    }
    return 1;
}

int GetSeriesSkills(lua_State* L)
{
    if (lua_isnumber(L, 1))
    {
        int series = static_cast<int>(lua_tonumber(L, 1));

        auto skills = GET_SERIES_SKILLS(static_cast<SkillSeries>(series));

        // 创建Lua表返回技能列表
        lua_newtable(L);
        for (size_t i = 0; i < skills.size(); ++i)
        {
            lua_pushnumber(L, i + 1);
            lua_pushnumber(L, skills[i]);
            lua_settable(L, -3);
        }

        LOG_DEBUG("SKILL_API", "Found " + std::to_string(skills.size()) +
                 " skills for series " + std::to_string(series));
    }
    else
    {
        lua_newtable(L);
    }
    return 1;
}

int GetSkillTree(lua_State* L)
{
    if (lua_isnumber(L, 1))
    {
        int faction = static_cast<int>(lua_tonumber(L, 1));

        auto skillTree = SKILL_MANAGER().GetFactionSkillTree(static_cast<PlayerSeries>(faction));
        if (skillTree)
        {
            // 创建Lua表返回技能树信息
            lua_newtable(L);

            lua_pushstring(L, "factionName");
            lua_pushstring(L, skillTree->factionName.c_str());
            lua_settable(L, -3);

            lua_pushstring(L, "maxTier");
            lua_pushnumber(L, skillTree->GetMaxTier());
            lua_settable(L, -3);

            // 按层级返回技能
            lua_pushstring(L, "tiers");
            lua_newtable(L);
            for (uint32_t tier = 1; tier <= skillTree->GetMaxTier(); ++tier)
            {
                auto tierSkills = skillTree->GetTierSkills(tier);

                lua_pushnumber(L, tier);
                lua_newtable(L);
                for (size_t i = 0; i < tierSkills.size(); ++i)
                {
                    lua_pushnumber(L, i + 1);
                    lua_pushnumber(L, tierSkills[i]);
                    lua_settable(L, -3);
                }
                lua_settable(L, -3);
            }
            lua_settable(L, -3);
        }
        else
        {
            lua_pushnil(L);
        }
    }
    else
    {
        lua_pushnil(L);
    }
    return 1;
}

int GetSkillPrerequisites(lua_State* L)
{
    if (lua_isnumber(L, 1))
    {
        uint32_t skillId = static_cast<uint32_t>(lua_tonumber(L, 1));

        auto skillTemplate = GET_SKILL_TEMPLATE(skillId);
        if (skillTemplate)
        {
            // 创建Lua表返回前置技能
            lua_newtable(L);
            for (size_t i = 0; i < skillTemplate->requirement.prerequisiteSkills.size(); ++i)
            {
                lua_pushnumber(L, i + 1);
                lua_pushnumber(L, skillTemplate->requirement.prerequisiteSkills[i]);
                lua_settable(L, -3);
            }
        }
        else
        {
            lua_newtable(L);
        }
    }
    else
    {
        lua_newtable(L);
    }
    return 1;
}

int GetUnlockedSkills(lua_State* L)
{
    if (lua_isnumber(L, 1) && lua_isnumber(L, 2))
    {
        uint32_t playerId = static_cast<uint32_t>(lua_tonumber(L, 1));
        int faction = static_cast<int>(lua_tonumber(L, 2));

        // 这里应该根据玩家已学技能返回可解锁的技能
        // 暂时返回空表
        lua_newtable(L);
    }
    else
    {
        lua_newtable(L);
    }
    return 1;
}

// 技能状态API实现
int SetSkillCooldown(lua_State* L)
{
    if (lua_isnumber(L, 1) && lua_isnumber(L, 2) && lua_isnumber(L, 3))
    {
        uint32_t playerId = static_cast<uint32_t>(lua_tonumber(L, 1));
        uint32_t skillId = static_cast<uint32_t>(lua_tonumber(L, 2));
        uint32_t cooldownMs = static_cast<uint32_t>(lua_tonumber(L, 3));

        // 这里应该设置玩家技能的冷却时间
        // 暂时返回成功
        lua_pushboolean(L, 1);

        LOG_DEBUG("SKILL_API", "Set skill " + std::to_string(skillId) +
                 " cooldown to " + std::to_string(cooldownMs) + "ms for player " + std::to_string(playerId));
    }
    else
    {
        lua_pushboolean(L, 0);
    }
    return 1;
}

int ResetSkillCooldown(lua_State* L)
{
    if (lua_isnumber(L, 1) && lua_isnumber(L, 2))
    {
        uint32_t playerId = static_cast<uint32_t>(lua_tonumber(L, 1));
        uint32_t skillId = static_cast<uint32_t>(lua_tonumber(L, 2));

        // 这里应该重置玩家技能的冷却时间
        // 暂时返回成功
        lua_pushboolean(L, 1);

        LOG_DEBUG("SKILL_API", "Reset skill " + std::to_string(skillId) +
                 " cooldown for player " + std::to_string(playerId));
    }
    else
    {
        lua_pushboolean(L, 0);
    }
    return 1;
}

int EnableSkill(lua_State* L)
{
    if (lua_isnumber(L, 1) && lua_isnumber(L, 2))
    {
        uint32_t playerId = static_cast<uint32_t>(lua_tonumber(L, 1));
        uint32_t skillId = static_cast<uint32_t>(lua_tonumber(L, 2));

        // 这里应该启用玩家技能
        // 暂时返回成功
        lua_pushboolean(L, 1);

        LOG_DEBUG("SKILL_API", "Enabled skill " + std::to_string(skillId) +
                 " for player " + std::to_string(playerId));
    }
    else
    {
        lua_pushboolean(L, 0);
    }
    return 1;
}

int DisableSkill(lua_State* L)
{
    if (lua_isnumber(L, 1) && lua_isnumber(L, 2))
    {
        uint32_t playerId = static_cast<uint32_t>(lua_tonumber(L, 1));
        uint32_t skillId = static_cast<uint32_t>(lua_tonumber(L, 2));

        // 这里应该禁用玩家技能
        // 暂时返回成功
        lua_pushboolean(L, 1);

        LOG_DEBUG("SKILL_API", "Disabled skill " + std::to_string(skillId) +
                 " for player " + std::to_string(playerId));
    }
    else
    {
        lua_pushboolean(L, 0);
    }
    return 1;
}

int GetSkillStatus(lua_State* L)
{
    if (lua_isnumber(L, 1) && lua_isnumber(L, 2))
    {
        uint32_t playerId = static_cast<uint32_t>(lua_tonumber(L, 1));
        uint32_t skillId = static_cast<uint32_t>(lua_tonumber(L, 2));

        // 这里应该获取玩家技能状态
        // 暂时返回可用状态
        lua_pushnumber(L, static_cast<int>(SkillStatus::Available));
        lua_pushstring(L, "Available");
        return 2;
    }
    else
    {
        lua_pushnumber(L, static_cast<int>(SkillStatus::Locked));
        lua_pushstring(L, "Locked");
        return 2;
    }
}

} // namespace SkillScriptAPI
} // namespace sword2
