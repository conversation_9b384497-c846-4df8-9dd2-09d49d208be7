#ifndef __KMEMCLASS1_H__
#define __KMEMCLASS1_H__

#include "KEngine.h"
#include <memory>

// 现代化的内存管理类
class ENGINE_API KMemClass1
{
private:
	DWORD m_lpMemLen;
	PVOID m_lpMemPtr;
	std::unique_ptr<BYTE[]> m_memoryBuffer; // 现代化的内存管理

public:
	// 构造函数和析构函数
	KMemClass1() : m_lpMemLen(0), m_lpMemPtr(nullptr) {}
	~KMemClass1() { Free(); }

	// 禁止拷贝，允许移动
	KMemClass1(const KMemClass1&) = delete;
	KMemClass1& operator=(const KMemClass1&) = delete;
	KMemClass1(KMemClass1&&) = default;
	KMemClass1& operator=(KMemClass1&&) = default;

	// 内存操作接口
	PVOID		Alloc(DWORD dwSize);
	void		Free();
	void		Zero();
	void		Fill(BYTE byFill);
	void		Fill(WORD wFill);
	void		Fill(DWORD dwFill);

	// 现代化的访问器
	PVOID		GetMemPtr() const noexcept { return m_lpMemPtr; }
	DWORD		GetMemLen() const noexcept { return m_lpMemLen; }
	bool		IsValid() const noexcept { return m_memoryBuffer != nullptr; }

	// 类型安全的访问器
	template<typename T>
	T* GetAs() const noexcept
	{
		return static_cast<T*>(m_lpMemPtr);
	}
};

#endif //__KMEMCLASS1_H__
