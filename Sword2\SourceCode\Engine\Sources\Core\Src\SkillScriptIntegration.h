//---------------------------------------------------------------------------
// Sword2 Skill Script Integration (c) 2024
//
// File:	SkillScriptIntegration.h
// Date:	2024.07.02
// Code:	AI Assistant
// Desc:	Integration layer between skill system and Lua scripts
//---------------------------------------------------------------------------
#ifndef SKILL_SCRIPT_INTEGRATION_H
#define SKILL_SCRIPT_INTEGRATION_H

#include "SkillManager.h"
#include "LuaScriptEngine.h"
#include "PlayerManager.h"
#include "UnifiedLoggingSystem.h"
#include <string>
#include <unordered_map>
#include <vector>
#include <functional>

namespace sword2 {

// 技能脚本API扩展
namespace SkillScriptAPI {
    
    // 技能基础API
    int GetSkillName(lua_State* L);
    int GetSkillLevel(lua_State* L);
    int GetSkillMaxLevel(lua_State* L);
    int GetSkillType(lua_State* L);
    int GetSkillSeries(lua_State* L);
    int GetSkillDescription(lua_State* L);
    
    // 技能学习API
    int LearnSkill(lua_State* L);
    int LevelUpSkill(lua_State* L);
    int ForgetSkill(lua_State* L);
    int CanLearnSkill(lua_State* L);
    int GetSkillRequirement(lua_State* L);
    int GetSkillUpgradeExp(lua_State* L);
    
    // 技能使用API
    int UseSkill(lua_State* L);
    int CanUseSkill(lua_State* L);
    int GetSkillCooldown(lua_State* L);
    int GetSkillRange(lua_State* L);
    int GetSkillCost(lua_State* L);
    int GetSkillDamage(lua_State* L);
    
    // 技能效果API
    int ApplySkillEffect(lua_State* L);
    int RemoveSkillEffect(lua_State* L);
    int GetSkillEffects(lua_State* L);
    int CalculateSkillDamage(lua_State* L);
    int CalculateSkillHeal(lua_State* L);
    
    // 技能树API
    int GetFactionSkills(lua_State* L);
    int GetSeriesSkills(lua_State* L);
    int GetSkillTree(lua_State* L);
    int GetSkillPrerequisites(lua_State* L);
    int GetUnlockedSkills(lua_State* L);
    
    // 技能状态API
    int SetSkillCooldown(lua_State* L);
    int ResetSkillCooldown(lua_State* L);
    int EnableSkill(lua_State* L);
    int DisableSkill(lua_State* L);
    int GetSkillStatus(lua_State* L);
    
    // 技能增强API
    int AddSkillLevel(lua_State* L);
    int SetSkillLevel(lua_State* L);
    int EnhanceSkill(lua_State* L);
    int GetSkillEnhancement(lua_State* L);
    
    // 技能组合API
    int CreateSkillCombo(lua_State* L);
    int TriggerSkillCombo(lua_State* L);
    int GetSkillCombos(lua_State* L);
    
    // 技能统计API
    int GetPlayerSkills(lua_State* L);
    int GetSkillStatistics(lua_State* L);
    int GetSkillUsageCount(lua_State* L);
}

// 技能效果管理器
class SkillEffectManager
{
public:
    struct ActiveEffect
    {
        uint32_t effectId = 0;      // 效果ID
        uint32_t skillId = 0;       // 技能ID
        uint32_t casterId = 0;      // 施法者ID
        uint32_t targetId = 0;      // 目标ID
        SkillEffectType type = SkillEffectType::Damage;
        uint32_t value = 0;         // 效果值
        uint32_t duration = 0;      // 持续时间(毫秒)
        std::chrono::system_clock::time_point startTime; // 开始时间
        bool isActive = true;       // 是否激活
        
        ActiveEffect() = default;
        ActiveEffect(uint32_t id, uint32_t skill, uint32_t caster, uint32_t target, 
                    SkillEffectType effectType, uint32_t effectValue, uint32_t effectDuration)
            : effectId(id), skillId(skill), casterId(caster), targetId(target), 
              type(effectType), value(effectValue), duration(effectDuration)
        {
            startTime = std::chrono::system_clock::now();
        }
        
        // 检查是否过期
        bool IsExpired() const
        {
            if (duration == 0) return false; // 永久效果
            
            auto now = std::chrono::system_clock::now();
            auto elapsed = std::chrono::duration_cast<std::chrono::milliseconds>(now - startTime);
            return elapsed.count() >= duration;
        }
        
        // 获取剩余时间
        uint32_t GetRemainingTime() const
        {
            if (duration == 0) return UINT32_MAX;
            
            auto now = std::chrono::system_clock::now();
            auto elapsed = std::chrono::duration_cast<std::chrono::milliseconds>(now - startTime);
            uint32_t elapsedMs = static_cast<uint32_t>(elapsed.count());
            
            return (elapsedMs >= duration) ? 0 : (duration - elapsedMs);
        }
    };
    
    // 应用技能效果
    uint32_t ApplyEffect(uint32_t skillId, uint32_t casterId, uint32_t targetId, 
                        SkillEffectType type, uint32_t value, uint32_t duration = 0)
    {
        std::lock_guard<std::mutex> lock(m_effectMutex);
        
        uint32_t effectId = m_nextEffectId++;
        ActiveEffect effect(effectId, skillId, casterId, targetId, type, value, duration);
        
        m_activeEffects[effectId] = effect;
        m_targetEffects[targetId].push_back(effectId);
        
        LOG_DEBUG("SKILL_EFFECT", "Applied effect " + std::to_string(effectId) + 
                 " from skill " + std::to_string(skillId) + " to target " + std::to_string(targetId));
        
        return effectId;
    }
    
    // 移除技能效果
    bool RemoveEffect(uint32_t effectId)
    {
        std::lock_guard<std::mutex> lock(m_effectMutex);
        
        auto it = m_activeEffects.find(effectId);
        if (it != m_activeEffects.end())
        {
            uint32_t targetId = it->second.targetId;
            
            // 从目标效果列表中移除
            auto& targetEffects = m_targetEffects[targetId];
            targetEffects.erase(std::remove(targetEffects.begin(), targetEffects.end(), effectId), 
                               targetEffects.end());
            
            m_activeEffects.erase(it);
            
            LOG_DEBUG("SKILL_EFFECT", "Removed effect " + std::to_string(effectId));
            return true;
        }
        
        return false;
    }
    
    // 获取目标的所有效果
    std::vector<ActiveEffect> GetTargetEffects(uint32_t targetId)
    {
        std::lock_guard<std::mutex> lock(m_effectMutex);
        std::vector<ActiveEffect> effects;
        
        auto it = m_targetEffects.find(targetId);
        if (it != m_targetEffects.end())
        {
            for (uint32_t effectId : it->second)
            {
                auto effectIt = m_activeEffects.find(effectId);
                if (effectIt != m_activeEffects.end() && effectIt->second.isActive)
                {
                    effects.push_back(effectIt->second);
                }
            }
        }
        
        return effects;
    }
    
    // 更新效果
    void Update()
    {
        std::lock_guard<std::mutex> lock(m_effectMutex);
        
        std::vector<uint32_t> expiredEffects;
        
        for (auto& [effectId, effect] : m_activeEffects)
        {
            if (effect.IsExpired())
            {
                expiredEffects.push_back(effectId);
            }
        }
        
        // 移除过期效果
        for (uint32_t effectId : expiredEffects)
        {
            RemoveEffect(effectId);
        }
    }

private:
    std::mutex m_effectMutex;
    std::unordered_map<uint32_t, ActiveEffect> m_activeEffects;
    std::unordered_map<uint32_t, std::vector<uint32_t>> m_targetEffects; // targetId -> effectIds
    std::atomic<uint32_t> m_nextEffectId{1};
};

// 技能组合管理器
class SkillComboManager
{
public:
    struct SkillCombo
    {
        uint32_t comboId = 0;       // 组合ID
        std::string name;           // 组合名称
        std::vector<uint32_t> skills; // 技能序列
        uint32_t timeWindow = 3000; // 时间窗口(毫秒)
        uint32_t bonusDamage = 0;   // 额外伤害
        std::string bonusEffect;    // 额外效果
        
        SkillCombo() = default;
        SkillCombo(uint32_t id, const std::string& comboName, const std::vector<uint32_t>& skillList)
            : comboId(id), name(comboName), skills(skillList) {}
    };
    
    struct ComboProgress
    {
        uint32_t comboId = 0;
        uint32_t currentStep = 0;
        std::chrono::system_clock::time_point lastSkillTime;
        
        ComboProgress() = default;
        ComboProgress(uint32_t id) : comboId(id), currentStep(0)
        {
            lastSkillTime = std::chrono::system_clock::now();
        }
    };
    
    // 注册技能组合
    bool RegisterCombo(const SkillCombo& combo)
    {
        std::lock_guard<std::mutex> lock(m_comboMutex);
        
        if (m_combos.find(combo.comboId) != m_combos.end())
        {
            LOG_WARNING("SKILL_COMBO", "Combo already exists: " + std::to_string(combo.comboId));
            return false;
        }
        
        m_combos[combo.comboId] = combo;
        
        LOG_DEBUG("SKILL_COMBO", "Registered skill combo: " + combo.name + " (ID: " + std::to_string(combo.comboId) + ")");
        return true;
    }
    
    // 检查技能组合
    bool CheckCombo(uint32_t playerId, uint32_t skillId)
    {
        std::lock_guard<std::mutex> lock(m_comboMutex);
        
        auto& progress = m_playerProgress[playerId];
        auto now = std::chrono::system_clock::now();
        
        // 检查所有可能的组合
        for (const auto& [comboId, combo] : m_combos)
        {
            // 检查是否是组合的第一个技能
            if (combo.skills.empty() || combo.skills[0] != skillId)
                continue;
            
            // 开始新的组合
            progress[comboId] = ComboProgress(comboId);
            progress[comboId].currentStep = 1;
            progress[comboId].lastSkillTime = now;
            
            LOG_DEBUG("SKILL_COMBO", "Player " + std::to_string(playerId) + " started combo " + combo.name);
        }
        
        // 检查现有组合的进度
        for (auto& [comboId, comboProgress] : progress)
        {
            auto comboIt = m_combos.find(comboId);
            if (comboIt == m_combos.end()) continue;
            
            const auto& combo = comboIt->second;
            
            // 检查时间窗口
            auto elapsed = std::chrono::duration_cast<std::chrono::milliseconds>(now - comboProgress.lastSkillTime);
            if (elapsed.count() > combo.timeWindow)
            {
                // 超时，重置进度
                comboProgress.currentStep = 0;
                continue;
            }
            
            // 检查是否是下一个技能
            if (comboProgress.currentStep < combo.skills.size() && 
                combo.skills[comboProgress.currentStep] == skillId)
            {
                comboProgress.currentStep++;
                comboProgress.lastSkillTime = now;
                
                // 检查是否完成组合
                if (comboProgress.currentStep >= combo.skills.size())
                {
                    TriggerCombo(playerId, comboId);
                    comboProgress.currentStep = 0; // 重置
                    return true;
                }
            }
        }
        
        return false;
    }
    
    // 触发技能组合
    void TriggerCombo(uint32_t playerId, uint32_t comboId)
    {
        auto it = m_combos.find(comboId);
        if (it == m_combos.end()) return;
        
        const auto& combo = it->second;
        
        LOG_INFO("SKILL_COMBO", "Player " + std::to_string(playerId) + " triggered combo: " + combo.name);
        
        // 这里可以应用组合效果
        // 例如额外伤害、特殊效果等
    }

private:
    std::mutex m_comboMutex;
    std::unordered_map<uint32_t, SkillCombo> m_combos;
    std::unordered_map<uint32_t, std::unordered_map<uint32_t, ComboProgress>> m_playerProgress; // playerId -> comboId -> progress
};

// 技能脚本集成管理器
class SkillScriptIntegration : public Singleton<SkillScriptIntegration>
{
public:
    SkillScriptIntegration() = default;
    ~SkillScriptIntegration() = default;
    
    // 初始化技能脚本集成
    bool Initialize()
    {
        // 注册技能相关的Lua API函数
        RegisterSkillScriptAPI();
        
        LOG_INFO("SKILL_SCRIPT", "Skill script integration initialized");
        return true;
    }
    
    // 加载技能脚本
    bool LoadSkillScript(const std::string& scriptPath)
    {
        ScriptResult result = LOAD_SCRIPT(scriptPath, ScriptType::Skill);
        if (result == ScriptResult::Success)
        {
            LOG_INFO("SKILL_SCRIPT", "Loaded skill script: " + scriptPath);
            return true;
        }
        else
        {
            LOG_ERROR("SKILL_SCRIPT", "Failed to load skill script: " + scriptPath);
            return false;
        }
    }
    
    // 执行技能脚本函数
    bool ExecuteSkillFunction(const std::string& functionName, uint32_t skillId, uint32_t playerId, uint32_t targetId = 0)
    {
        std::vector<LuaValue> args;
        args.push_back(LuaValue(static_cast<double>(skillId)));
        args.push_back(LuaValue(static_cast<double>(playerId)));
        if (targetId != 0)
        {
            args.push_back(LuaValue(static_cast<double>(targetId)));
        }
        
        ScriptResult result = EXECUTE_FUNCTION(functionName, args);
        return result == ScriptResult::Success;
    }
    
    // 计算技能伤害（脚本版本）
    uint32_t CalculateSkillDamageScript(uint32_t skillId, uint32_t level, uint32_t casterId, uint32_t targetId = 0)
    {
        std::vector<LuaValue> args = {
            LuaValue(static_cast<double>(skillId)),
            LuaValue(static_cast<double>(level)),
            LuaValue(static_cast<double>(casterId)),
            LuaValue(static_cast<double>(targetId))
        };
        
        auto result = CALL_FUNCTION_WITH_RETURN("CalculateSkillDamage", args);
        if (result.has_value() && std::holds_alternative<double>(result.value()))
        {
            return static_cast<uint32_t>(std::get<double>(result.value()));
        }
        
        return 0;
    }
    
    // 应用技能效果
    uint32_t ApplySkillEffect(uint32_t skillId, uint32_t casterId, uint32_t targetId, 
                             SkillEffectType type, uint32_t value, uint32_t duration = 0)
    {
        return m_effectManager.ApplyEffect(skillId, casterId, targetId, type, value, duration);
    }
    
    // 移除技能效果
    bool RemoveSkillEffect(uint32_t effectId)
    {
        return m_effectManager.RemoveEffect(effectId);
    }
    
    // 注册技能组合
    bool RegisterSkillCombo(uint32_t comboId, const std::string& name, const std::vector<uint32_t>& skills)
    {
        SkillComboManager::SkillCombo combo(comboId, name, skills);
        return m_comboManager.RegisterCombo(combo);
    }
    
    // 检查技能组合
    bool CheckSkillCombo(uint32_t playerId, uint32_t skillId)
    {
        return m_comboManager.CheckCombo(playerId, skillId);
    }
    
    // 更新系统
    void Update()
    {
        m_effectManager.Update();
    }
    
    // 获取效果管理器
    SkillEffectManager& GetEffectManager() { return m_effectManager; }
    
    // 获取组合管理器
    SkillComboManager& GetComboManager() { return m_comboManager; }

private:
    SkillEffectManager m_effectManager;
    SkillComboManager m_comboManager;
    
    void RegisterSkillScriptAPI()
    {
        // 注册技能基础API
        REGISTER_LUA_FUNCTION("GetSkillName", SkillScriptAPI::GetSkillName);
        REGISTER_LUA_FUNCTION("GetSkillLevel", SkillScriptAPI::GetSkillLevel);
        REGISTER_LUA_FUNCTION("GetSkillMaxLevel", SkillScriptAPI::GetSkillMaxLevel);
        REGISTER_LUA_FUNCTION("GetSkillType", SkillScriptAPI::GetSkillType);
        REGISTER_LUA_FUNCTION("GetSkillSeries", SkillScriptAPI::GetSkillSeries);
        
        // 注册技能学习API
        REGISTER_LUA_FUNCTION("LearnSkill", SkillScriptAPI::LearnSkill);
        REGISTER_LUA_FUNCTION("LevelUpSkill", SkillScriptAPI::LevelUpSkill);
        REGISTER_LUA_FUNCTION("ForgetSkill", SkillScriptAPI::ForgetSkill);
        REGISTER_LUA_FUNCTION("CanLearnSkill", SkillScriptAPI::CanLearnSkill);
        
        // 注册技能使用API
        REGISTER_LUA_FUNCTION("UseSkill", SkillScriptAPI::UseSkill);
        REGISTER_LUA_FUNCTION("CanUseSkill", SkillScriptAPI::CanUseSkill);
        REGISTER_LUA_FUNCTION("GetSkillCooldown", SkillScriptAPI::GetSkillCooldown);
        REGISTER_LUA_FUNCTION("GetSkillRange", SkillScriptAPI::GetSkillRange);
        
        // 注册技能效果API
        REGISTER_LUA_FUNCTION("ApplySkillEffect", SkillScriptAPI::ApplySkillEffect);
        REGISTER_LUA_FUNCTION("RemoveSkillEffect", SkillScriptAPI::RemoveSkillEffect);
        REGISTER_LUA_FUNCTION("CalculateSkillDamage", SkillScriptAPI::CalculateSkillDamage);
        
        // 注册技能树API
        REGISTER_LUA_FUNCTION("GetFactionSkills", SkillScriptAPI::GetFactionSkills);
        REGISTER_LUA_FUNCTION("GetSeriesSkills", SkillScriptAPI::GetSeriesSkills);
        REGISTER_LUA_FUNCTION("GetSkillTree", SkillScriptAPI::GetSkillTree);
        
        // 注册技能状态API
        REGISTER_LUA_FUNCTION("SetSkillCooldown", SkillScriptAPI::SetSkillCooldown);
        REGISTER_LUA_FUNCTION("ResetSkillCooldown", SkillScriptAPI::ResetSkillCooldown);
        REGISTER_LUA_FUNCTION("EnableSkill", SkillScriptAPI::EnableSkill);
        REGISTER_LUA_FUNCTION("DisableSkill", SkillScriptAPI::DisableSkill);
        
        // 注册技能增强API
        REGISTER_LUA_FUNCTION("AddSkillLevel", SkillScriptAPI::AddSkillLevel);
        REGISTER_LUA_FUNCTION("EnhanceSkill", SkillScriptAPI::EnhanceSkill);
        
        // 注册技能组合API
        REGISTER_LUA_FUNCTION("CreateSkillCombo", SkillScriptAPI::CreateSkillCombo);
        REGISTER_LUA_FUNCTION("TriggerSkillCombo", SkillScriptAPI::TriggerSkillCombo);
        
        // 注册技能统计API
        REGISTER_LUA_FUNCTION("GetPlayerSkills", SkillScriptAPI::GetPlayerSkills);
        REGISTER_LUA_FUNCTION("GetSkillStatistics", SkillScriptAPI::GetSkillStatistics);
        
        LOG_INFO("SKILL_SCRIPT", "Registered skill script API functions");
    }
};

} // namespace sword2

// 全局技能脚本集成访问
#define SKILL_SCRIPT_INTEGRATION() sword2::SkillScriptIntegration::getInstance()

// 便捷宏定义
#define INIT_SKILL_SCRIPT_INTEGRATION() SKILL_SCRIPT_INTEGRATION().Initialize()
#define LOAD_SKILL_SCRIPT(scriptPath) SKILL_SCRIPT_INTEGRATION().LoadSkillScript(scriptPath)
#define EXECUTE_SKILL_FUNCTION(functionName, skillId, playerId, targetId) SKILL_SCRIPT_INTEGRATION().ExecuteSkillFunction(functionName, skillId, playerId, targetId)

#define APPLY_SKILL_EFFECT(skillId, casterId, targetId, type, value, duration) SKILL_SCRIPT_INTEGRATION().ApplySkillEffect(skillId, casterId, targetId, type, value, duration)
#define REMOVE_SKILL_EFFECT(effectId) SKILL_SCRIPT_INTEGRATION().RemoveSkillEffect(effectId)

#define REGISTER_SKILL_COMBO(comboId, name, skills) SKILL_SCRIPT_INTEGRATION().RegisterSkillCombo(comboId, name, skills)
#define CHECK_SKILL_COMBO(playerId, skillId) SKILL_SCRIPT_INTEGRATION().CheckSkillCombo(playerId, skillId)

#define SKILL_EFFECT_MANAGER() SKILL_SCRIPT_INTEGRATION().GetEffectManager()
#define SKILL_COMBO_MANAGER() SKILL_SCRIPT_INTEGRATION().GetComboManager()

#endif // SKILL_SCRIPT_INTEGRATION_H
