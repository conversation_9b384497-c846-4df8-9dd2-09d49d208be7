//---------------------------------------------------------------------------
// Sword2 Auction System (c) 2024
//
// File:	AuctionSystem.h
// Date:	2024.07.02
// Code:	AI Assistant
// Desc:	Comprehensive auction house system
//---------------------------------------------------------------------------
#ifndef AUCTION_SYSTEM_H
#define AUCTION_SYSTEM_H

#include "ShopSystem.h"
#include "PlayerSystem.h"
#include "ItemSystem.h"
#include "UnifiedLoggingSystem.h"
#include <string>
#include <unordered_map>
#include <vector>
#include <memory>
#include <chrono>
#include <atomic>
#include <mutex>
#include <algorithm>

namespace sword2 {

// 拍卖状态
enum class AuctionState : uint8_t
{
    Active = 0,         // 进行中
    Completed,          // 已完成
    Cancelled,          // 已取消
    Expired,            // 已过期
    Withdrawn           // 已撤回
};

// 拍卖类型
enum class AuctionType : uint8_t
{
    FixedPrice = 0,     // 一口价
    Bidding,            // 竞价
    Dutch,              // 荷兰式拍卖（降价）
    Reserve             // 保留价拍卖
};

// 竞价记录
struct BidRecord
{
    uint32_t bidId = 0;             // 竞价ID
    uint32_t auctionId = 0;         // 拍卖ID
    uint32_t bidderId = 0;          // 竞价者ID
    std::string bidderName;         // 竞价者名称
    uint32_t bidAmount = 0;         // 竞价金额
    std::chrono::system_clock::time_point bidTime; // 竞价时间
    bool isWinning = false;         // 是否为当前最高价
    bool isRefunded = false;        // 是否已退款
    
    BidRecord() = default;
    BidRecord(uint32_t auction, uint32_t bidder, const std::string& name, uint32_t amount)
        : auctionId(auction), bidderId(bidder), bidderName(name), bidAmount(amount)
    {
        bidTime = std::chrono::system_clock::now();
    }
};

// 拍卖物品
struct AuctionItem
{
    uint32_t auctionId = 0;         // 拍卖ID
    uint32_t sellerId = 0;          // 卖家ID
    std::string sellerName;         // 卖家名称
    
    uint32_t itemInstanceId = 0;    // 物品实例ID
    uint32_t itemTemplateId = 0;    // 物品模板ID
    std::string itemName;           // 物品名称
    uint32_t quantity = 1;          // 数量
    
    AuctionType type = AuctionType::FixedPrice;
    AuctionState state = AuctionState::Active;
    
    // 价格信息
    uint32_t startPrice = 0;        // 起始价格
    uint32_t buyoutPrice = 0;       // 一口价
    uint32_t reservePrice = 0;      // 保留价
    uint32_t currentPrice = 0;      // 当前价格
    uint32_t bidIncrement = 1;      // 最小加价幅度
    
    // 时间信息
    std::chrono::system_clock::time_point startTime;   // 开始时间
    std::chrono::system_clock::time_point endTime;     // 结束时间
    uint32_t duration = 0;          // 持续时间（秒）
    
    // 竞价信息
    uint32_t totalBids = 0;         // 总竞价次数
    uint32_t highestBidderId = 0;   // 最高竞价者ID
    std::string highestBidderName;  // 最高竞价者名称
    std::vector<BidRecord> bidHistory; // 竞价历史
    
    // 费用信息
    uint32_t listingFee = 0;        // 上架费用
    uint32_t commissionRate = 5;    // 佣金率（百分比）
    uint32_t commissionFee = 0;     // 佣金费用
    
    // 统计信息
    uint32_t viewCount = 0;         // 查看次数
    std::chrono::system_clock::time_point lastViewTime; // 最后查看时间
    
    AuctionItem() = default;
    AuctionItem(uint32_t seller, uint32_t itemInstance, uint32_t itemTemplate, 
               uint32_t price, AuctionType auctionType, uint32_t durationSec)
        : sellerId(seller), itemInstanceId(itemInstance), itemTemplateId(itemTemplate),
          startPrice(price), currentPrice(price), type(auctionType), duration(durationSec)
    {
        startTime = std::chrono::system_clock::now();
        endTime = startTime + std::chrono::seconds(duration);
        lastViewTime = startTime;
    }
    
    // 检查是否过期
    bool IsExpired() const
    {
        return std::chrono::system_clock::now() > endTime;
    }
    
    // 获取剩余时间
    uint32_t GetRemainingTime() const
    {
        auto now = std::chrono::system_clock::now();
        if (now >= endTime) return 0;
        
        auto remaining = std::chrono::duration_cast<std::chrono::seconds>(endTime - now);
        return static_cast<uint32_t>(remaining.count());
    }
    
    // 检查是否可以竞价
    bool CanBid() const
    {
        return state == AuctionState::Active && !IsExpired() && 
               (type == AuctionType::Bidding || type == AuctionType::Reserve);
    }
    
    // 检查是否可以一口价购买
    bool CanBuyout() const
    {
        return state == AuctionState::Active && !IsExpired() && buyoutPrice > 0;
    }
    
    // 添加竞价
    bool AddBid(uint32_t bidderId, const std::string& bidderName, uint32_t amount)
    {
        if (!CanBid()) return false;
        
        // 检查竞价金额
        if (amount < currentPrice + bidIncrement) return false;
        
        // 检查是否为卖家自己
        if (bidderId == sellerId) return false;
        
        // 标记之前的最高竞价为非获胜
        if (!bidHistory.empty())
        {
            bidHistory.back().isWinning = false;
        }
        
        // 添加新竞价
        BidRecord bid(auctionId, bidderId, bidderName, amount);
        bid.isWinning = true;
        bidHistory.push_back(bid);
        
        // 更新当前价格和最高竞价者
        currentPrice = amount;
        highestBidderId = bidderId;
        highestBidderName = bidderName;
        totalBids++;
        
        // 如果达到一口价，立即结束拍卖
        if (buyoutPrice > 0 && amount >= buyoutPrice)
        {
            state = AuctionState::Completed;
        }
        
        return true;
    }
    
    // 获取最高竞价
    uint32_t GetHighestBid() const
    {
        return currentPrice;
    }
    
    // 获取下一个有效竞价金额
    uint32_t GetNextBidAmount() const
    {
        return currentPrice + bidIncrement;
    }
    
    // 计算佣金
    uint32_t CalculateCommission() const
    {
        if (state != AuctionState::Completed) return 0;
        return (currentPrice * commissionRate) / 100;
    }
    
    // 计算卖家收入
    uint32_t GetSellerIncome() const
    {
        if (state != AuctionState::Completed) return 0;
        return currentPrice - CalculateCommission();
    }
    
    // 增加查看次数
    void IncrementView()
    {
        viewCount++;
        lastViewTime = std::chrono::system_clock::now();
    }
    
    // 获取状态描述
    std::string GetStateDescription() const
    {
        switch (state)
        {
        case AuctionState::Active: return "进行中";
        case AuctionState::Completed: return "已完成";
        case AuctionState::Cancelled: return "已取消";
        case AuctionState::Expired: return "已过期";
        case AuctionState::Withdrawn: return "已撤回";
        default: return "未知";
        }
    }
    
    // 获取类型描述
    std::string GetTypeDescription() const
    {
        switch (type)
        {
        case AuctionType::FixedPrice: return "一口价";
        case AuctionType::Bidding: return "竞价";
        case AuctionType::Dutch: return "荷兰式";
        case AuctionType::Reserve: return "保留价";
        default: return "未知";
        }
    }
};

// 拍卖搜索条件
struct AuctionSearchCriteria
{
    std::string itemName;           // 物品名称
    uint32_t itemTemplateId = 0;    // 物品模板ID
    uint32_t minPrice = 0;          // 最低价格
    uint32_t maxPrice = UINT32_MAX; // 最高价格
    uint32_t minLevel = 0;          // 最低等级
    uint32_t maxLevel = 999;        // 最高等级
    AuctionType type = AuctionType::FixedPrice; // 拍卖类型
    bool activeOnly = true;         // 仅显示进行中的
    uint32_t sellerId = 0;          // 卖家ID
    
    // 排序选项
    enum class SortBy
    {
        TimeLeft,       // 剩余时间
        Price,          // 价格
        ItemName,       // 物品名称
        BidCount        // 竞价次数
    };
    
    SortBy sortBy = SortBy::TimeLeft;
    bool ascending = true;          // 升序
    
    uint32_t pageSize = 20;         // 每页数量
    uint32_t pageIndex = 0;         // 页索引
};

// 拍卖统计
struct AuctionStatistics
{
    uint32_t totalAuctions = 0;     // 总拍卖数
    uint32_t activeAuctions = 0;    // 进行中拍卖数
    uint32_t completedAuctions = 0; // 已完成拍卖数
    uint32_t totalVolume = 0;       // 总交易额
    uint32_t dailyVolume = 0;       // 日交易额
    uint32_t totalCommission = 0;   // 总佣金
    uint32_t dailyCommission = 0;   // 日佣金
    
    // 热门物品
    std::vector<std::pair<uint32_t, uint32_t>> popularItems; // itemId, count
    
    // 价格趋势
    std::unordered_map<uint32_t, std::vector<uint32_t>> priceTrends; // itemId -> prices
    
    std::chrono::system_clock::time_point lastUpdate;
    
    AuctionStatistics()
    {
        lastUpdate = std::chrono::system_clock::now();
    }
};

// 拍卖邮件
struct AuctionMail
{
    uint32_t mailId = 0;            // 邮件ID
    uint32_t recipientId = 0;       // 收件人ID
    uint32_t auctionId = 0;         // 拍卖ID
    std::string subject;            // 主题
    std::string content;            // 内容
    
    uint32_t attachedMoney = 0;     // 附加金钱
    uint32_t attachedItemId = 0;    // 附加物品ID
    uint32_t attachedQuantity = 0;  // 附加数量
    
    bool isRead = false;            // 是否已读
    bool isCollected = false;       // 是否已收取
    
    std::chrono::system_clock::time_point sendTime;
    std::chrono::system_clock::time_point expireTime;
    
    AuctionMail() = default;
    AuctionMail(uint32_t recipient, uint32_t auction, const std::string& subj, const std::string& cont)
        : recipientId(recipient), auctionId(auction), subject(subj), content(cont)
    {
        sendTime = std::chrono::system_clock::now();
        expireTime = sendTime + std::chrono::hours(72); // 72小时过期
    }
    
    // 检查是否过期
    bool IsExpired() const
    {
        return std::chrono::system_clock::now() > expireTime;
    }
};

} // namespace sword2

#endif // AUCTION_SYSTEM_H
