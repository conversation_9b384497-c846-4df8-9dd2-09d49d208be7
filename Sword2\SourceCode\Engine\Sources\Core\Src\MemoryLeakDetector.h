//---------------------------------------------------------------------------
// Sword2 Memory Leak Detector (c) 2024
//
// File:	MemoryLeakDetector.h
// Date:	2024.07.02
// Code:	AI Assistant
// Desc:	Advanced memory leak detection and debugging system
//---------------------------------------------------------------------------
#ifndef MEMORY_LEAK_DETECTOR_H
#define MEMORY_LEAK_DETECTOR_H

#include <windows.h>
#include <map>
#include <vector>

// 内存分配信息
struct MemoryAllocation
{
    void* pAddress;             // 内存地址
    size_t nSize;               // 分配大小
    const char* pFile;          // 分配文件
    int nLine;                  // 分配行号
    const char* pFunction;      // 分配函数
    DWORD dwThreadId;           // 分配线程ID
    DWORD dwTimestamp;          // 分配时间戳
    DWORD dwCallStack[16];      // 调用栈
    int nCallStackDepth;        // 调用栈深度
    DWORD dwSequenceNumber;     // 序列号
};

// 内存泄漏统计
struct MemoryLeakStats
{
    DWORD dwTotalAllocations;   // 总分配次数
    DWORD dwTotalDeallocations; // 总释放次数
    DWORD dwCurrentAllocations; // 当前分配数
    size_t nTotalAllocatedBytes; // 总分配字节数
    size_t nTotalFreedBytes;    // 总释放字节数
    size_t nCurrentAllocatedBytes; // 当前分配字节数
    size_t nPeakAllocatedBytes; // 峰值分配字节数
    DWORD dwLeakCount;          // 泄漏数量
    size_t nLeakBytes;          // 泄漏字节数
};

// 内存泄漏检测器
class CMemoryLeakDetector
{
public:
    CMemoryLeakDetector();
    ~CMemoryLeakDetector();

    BOOL Initialize();
    void Cleanup();

    // 内存跟踪
    void RecordAllocation(void* pAddress, size_t nSize, 
                         const char* pFile, int nLine, const char* pFunction);
    void RecordDeallocation(void* pAddress);

    // 泄漏检测
    void CheckForLeaks();
    void DumpLeaks();
    void DumpLeaksToFile(const char* pFilename);

    // 统计信息
    void GetStats(MemoryLeakStats* pStats);
    void ResetStats();
    void LogStats();

    // 配置
    void SetEnabled(BOOL bEnabled) { m_bEnabled = bEnabled; }
    void SetCallStackDepth(int nDepth) { m_nCallStackDepth = min(nDepth, 16); }
    void SetBreakOnLeak(BOOL bBreak) { m_bBreakOnLeak = bBreak; }

    // 调试功能
    void SetBreakOnAllocation(DWORD dwSequenceNumber);
    void SetBreakOnSize(size_t nSize);
    void DumpAllocation(void* pAddress);

private:
    std::map<void*, MemoryAllocation> m_Allocations; // 分配记录
    MemoryLeakStats m_Stats;                        // 统计信息
    
    BOOL m_bEnabled;
    BOOL m_bBreakOnLeak;
    int m_nCallStackDepth;
    DWORD m_dwSequenceCounter;
    DWORD m_dwBreakOnAllocation;
    size_t m_nBreakOnSize;
    
    CRITICAL_SECTION m_CriticalSection;

    void CaptureCallStack(DWORD* pCallStack, int nMaxDepth);
    void FormatCallStack(const DWORD* pCallStack, int nDepth, char* pBuffer, int nBufferSize);
    const char* GetFileName(const char* pFullPath);
};

// 内存边界检查器
class CMemoryBoundsChecker
{
public:
    CMemoryBoundsChecker();
    ~CMemoryBoundsChecker();

    // 边界保护
    void* AllocateWithGuards(size_t nSize);
    void FreeWithGuards(void* pAddress);
    
    // 边界检查
    BOOL CheckBounds(void* pAddress);
    void CheckAllBounds();

    // 统计
    DWORD GetBoundsViolationCount() const { return m_dwViolationCount; }

private:
    static const DWORD GUARD_PATTERN = 0xDEADBEEF;
    static const size_t GUARD_SIZE = 16;

    struct GuardedAllocation
    {
        void* pUserAddress;
        void* pActualAddress;
        size_t nUserSize;
        size_t nActualSize;
    };

    std::map<void*, GuardedAllocation> m_GuardedAllocations;
    DWORD m_dwViolationCount;
    CRITICAL_SECTION m_CriticalSection;

    void SetupGuards(void* pAddress, size_t nSize);
    BOOL VerifyGuards(const GuardedAllocation& allocation);
};

// 内存使用分析器
class CMemoryUsageAnalyzer
{
public:
    CMemoryUsageAnalyzer();
    ~CMemoryUsageAnalyzer();

    // 使用分析
    void AnalyzeUsagePatterns();
    void GenerateUsageReport();
    
    // 分配模式分析
    void AnalyzeAllocationPatterns();
    void DetectMemoryHogs();
    
    // 建议生成
    void GenerateOptimizationSuggestions();

private:
    struct AllocationPattern
    {
        const char* pFile;
        int nLine;
        DWORD dwCount;
        size_t nTotalSize;
        size_t nAverageSize;
        DWORD dwFrequency;
    };

    std::vector<AllocationPattern> m_Patterns;
    
    void CollectPatterns();
    void AnalyzePatterns();
};

// 智能内存调试器
class CSmartMemoryDebugger
{
public:
    CSmartMemoryDebugger();
    ~CSmartMemoryDebugger();

    BOOL Initialize();
    void Cleanup();

    // 自动检测
    void EnableAutoDetection(BOOL bEnable) { m_bAutoDetection = bEnable; }
    void RunDiagnostics();

    // 实时监控
    void StartRealTimeMonitoring();
    void StopRealTimeMonitoring();

    // 报告生成
    void GenerateFullReport();
    void GenerateSummaryReport();

private:
    CMemoryLeakDetector m_LeakDetector;
    CMemoryBoundsChecker m_BoundsChecker;
    CMemoryUsageAnalyzer m_UsageAnalyzer;
    
    BOOL m_bAutoDetection;
    BOOL m_bRealTimeMonitoring;
    HANDLE m_hMonitorThread;
    HANDLE m_hStopEvent;

    static DWORD WINAPI MonitorThreadProc(LPVOID pParam);
    void PerformRealTimeChecks();
};

// 全局实例
extern CMemoryLeakDetector g_MemoryLeakDetector;
extern CMemoryBoundsChecker g_MemoryBoundsChecker;
extern CSmartMemoryDebugger g_SmartMemoryDebugger;

// 调试宏定义
#ifdef _DEBUG
    #define DEBUG_NEW new(__FILE__, __LINE__)
    #define DEBUG_MALLOC(size) DebugMalloc(size, __FILE__, __LINE__, __FUNCTION__)
    #define DEBUG_FREE(ptr) DebugFree(ptr)
    
    // 重载new/delete操作符
    void* operator new(size_t nSize, const char* pFile, int nLine);
    void* operator new[](size_t nSize, const char* pFile, int nLine);
    void operator delete(void* pAddress, const char* pFile, int nLine);
    void operator delete[](void* pAddress, const char* pFile, int nLine);
    
    // 调试版本的malloc/free
    void* DebugMalloc(size_t nSize, const char* pFile, int nLine, const char* pFunction);
    void DebugFree(void* pAddress);
    
    // 内存检查宏
    #define CHECK_MEMORY_LEAKS()    g_MemoryLeakDetector.CheckForLeaks()
    #define DUMP_MEMORY_LEAKS()     g_MemoryLeakDetector.DumpLeaks()
    #define CHECK_MEMORY_BOUNDS()   g_MemoryBoundsChecker.CheckAllBounds()
    
    // 自动内存检查
    class CAutoMemoryChecker
    {
    public:
        CAutoMemoryChecker(const char* pScope) : m_pScope(pScope)
        {
            g_MemoryLeakDetector.GetStats(&m_StartStats);
        }
        
        ~CAutoMemoryChecker()
        {
            MemoryLeakStats endStats;
            g_MemoryLeakDetector.GetStats(&endStats);
            
            if (endStats.dwCurrentAllocations > m_StartStats.dwCurrentAllocations)
            {
                char szMsg[256];
                sprintf_s(szMsg, sizeof(szMsg), 
                    "[MEMORY] Potential leak in %s: %d allocations not freed\n",
                    m_pScope, 
                    endStats.dwCurrentAllocations - m_StartStats.dwCurrentAllocations);
                OutputDebugStringA(szMsg);
            }
        }
        
    private:
        const char* m_pScope;
        MemoryLeakStats m_StartStats;
    };
    
    #define AUTO_MEMORY_CHECK(scope) CAutoMemoryChecker _memChecker(scope)
    
#else
    #define DEBUG_NEW new
    #define DEBUG_MALLOC(size) malloc(size)
    #define DEBUG_FREE(ptr) free(ptr)
    #define CHECK_MEMORY_LEAKS()
    #define DUMP_MEMORY_LEAKS()
    #define CHECK_MEMORY_BOUNDS()
    #define AUTO_MEMORY_CHECK(scope)
#endif

// 内存安全包装器
template<typename T>
class CSafePtr
{
public:
    CSafePtr() : m_pPtr(nullptr), m_bValid(false) {}
    explicit CSafePtr(T* pPtr) : m_pPtr(pPtr), m_bValid(pPtr != nullptr) {}
    ~CSafePtr() { Reset(); }

    T* Get() const { return m_bValid ? m_pPtr : nullptr; }
    T* operator->() const { return Get(); }
    T& operator*() const { return *Get(); }
    
    void Reset(T* pPtr = nullptr)
    {
        if (m_pPtr && m_bValid)
        {
            delete m_pPtr;
        }
        m_pPtr = pPtr;
        m_bValid = (pPtr != nullptr);
    }
    
    T* Release()
    {
        T* pTemp = m_pPtr;
        m_pPtr = nullptr;
        m_bValid = false;
        return pTemp;
    }
    
    bool IsValid() const { return m_bValid && m_pPtr != nullptr; }

private:
    T* m_pPtr;
    bool m_bValid;
    
    // 禁止拷贝
    CSafePtr(const CSafePtr&);
    CSafePtr& operator=(const CSafePtr&);
};

#endif // MEMORY_LEAK_DETECTOR_H
