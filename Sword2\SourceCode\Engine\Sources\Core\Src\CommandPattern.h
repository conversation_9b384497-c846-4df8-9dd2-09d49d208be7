//---------------------------------------------------------------------------
// Sword2 Command Pattern Implementation (c) 2024
//
// File:	CommandPattern.h
// Date:	2024.07.02
// Code:	AI Assistant
// Desc:	Modern C++ command pattern for operation encapsulation and undo/redo
//---------------------------------------------------------------------------
#ifndef COMMAND_PATTERN_H
#define COMMAND_PATTERN_H

#include "ModernCpp.h"
#include <stack>
#include <queue>
#include <chrono>
#include <functional>
#include <memory>
#include <string>

namespace sword2 {

// 命令执行结果
enum class CommandResult
{
    Success,
    Failed,
    Cancelled,
    Pending
};

// 命令接口
class ICommand
{
public:
    virtual ~ICommand() = default;
    
    // 执行命令
    virtual CommandResult Execute() = 0;
    
    // 撤销命令
    virtual CommandResult Undo() = 0;
    
    // 重做命令
    virtual CommandResult Redo() { return Execute(); }
    
    // 命令信息
    virtual std::string GetName() const = 0;
    virtual std::string GetDescription() const { return GetName(); }
    virtual bool CanUndo() const { return true; }
    virtual bool CanRedo() const { return true; }
    
    // 命令状态
    virtual bool IsExecuted() const { return m_executed; }
    virtual std::chrono::steady_clock::time_point GetExecutionTime() const { return m_executionTime; }

protected:
    bool m_executed = false;
    std::chrono::steady_clock::time_point m_executionTime;
};

// 函数式命令
class FunctionCommand : public ICommand
{
public:
    using ExecuteFunc = std::function<CommandResult()>;
    using UndoFunc = std::function<CommandResult()>;
    
    FunctionCommand(const std::string& name, ExecuteFunc executeFunc, UndoFunc undoFunc = nullptr)
        : m_name(name), m_executeFunc(std::move(executeFunc)), m_undoFunc(std::move(undoFunc)) {}
    
    CommandResult Execute() override
    {
        if (m_executeFunc)
        {
            auto result = m_executeFunc();
            if (result == CommandResult::Success)
            {
                m_executed = true;
                m_executionTime = std::chrono::steady_clock::now();
            }
            return result;
        }
        return CommandResult::Failed;
    }
    
    CommandResult Undo() override
    {
        if (m_undoFunc && m_executed)
        {
            auto result = m_undoFunc();
            if (result == CommandResult::Success)
            {
                m_executed = false;
            }
            return result;
        }
        return CommandResult::Failed;
    }
    
    std::string GetName() const override { return m_name; }
    bool CanUndo() const override { return m_undoFunc != nullptr; }

private:
    std::string m_name;
    ExecuteFunc m_executeFunc;
    UndoFunc m_undoFunc;
};

// 宏命令 - 组合多个命令
class MacroCommand : public ICommand
{
public:
    explicit MacroCommand(const std::string& name) : m_name(name) {}
    
    void AddCommand(std::unique_ptr<ICommand> command)
    {
        m_commands.push_back(std::move(command));
    }
    
    CommandResult Execute() override
    {
        m_executedCommands.clear();
        
        for (auto& command : m_commands)
        {
            auto result = command->Execute();
            if (result == CommandResult::Success)
            {
                m_executedCommands.push_back(command.get());
            }
            else
            {
                // 失败时回滚已执行的命令
                RollbackExecutedCommands();
                return result;
            }
        }
        
        m_executed = true;
        m_executionTime = std::chrono::steady_clock::now();
        return CommandResult::Success;
    }
    
    CommandResult Undo() override
    {
        if (!m_executed) return CommandResult::Failed;
        
        // 逆序撤销
        for (auto it = m_executedCommands.rbegin(); it != m_executedCommands.rend(); ++it)
        {
            (*it)->Undo();
        }
        
        m_executed = false;
        m_executedCommands.clear();
        return CommandResult::Success;
    }
    
    std::string GetName() const override { return m_name; }
    
    bool CanUndo() const override
    {
        return std::all_of(m_commands.begin(), m_commands.end(),
            [](const auto& cmd) { return cmd->CanUndo(); });
    }

private:
    std::string m_name;
    std::vector<std::unique_ptr<ICommand>> m_commands;
    std::vector<ICommand*> m_executedCommands;
    
    void RollbackExecutedCommands()
    {
        for (auto it = m_executedCommands.rbegin(); it != m_executedCommands.rend(); ++it)
        {
            (*it)->Undo();
        }
        m_executedCommands.clear();
    }
};

// 命令调用器 - 支持撤销重做
class CommandInvoker
{
public:
    CommandInvoker() = default;
    
    // 执行命令
    CommandResult ExecuteCommand(std::unique_ptr<ICommand> command)
    {
        auto result = command->Execute();
        
        if (result == CommandResult::Success && command->CanUndo())
        {
            // 清空重做栈
            while (!m_redoStack.empty())
            {
                m_redoStack.pop();
            }
            
            // 添加到撤销栈
            m_undoStack.push(std::move(command));
            
            // 限制撤销栈大小
            if (m_undoStack.size() > m_maxUndoLevels)
            {
                // 移除最底层的命令
                std::stack<std::unique_ptr<ICommand>> tempStack;
                for (size_t i = 0; i < m_maxUndoLevels - 1; ++i)
                {
                    tempStack.push(std::move(const_cast<std::unique_ptr<ICommand>&>(m_undoStack.top())));
                    m_undoStack.pop();
                }
                
                m_undoStack = std::stack<std::unique_ptr<ICommand>>();
                while (!tempStack.empty())
                {
                    m_undoStack.push(std::move(const_cast<std::unique_ptr<ICommand>&>(tempStack.top())));
                    tempStack.pop();
                }
            }
        }
        
        return result;
    }
    
    // 撤销
    CommandResult Undo()
    {
        if (m_undoStack.empty())
            return CommandResult::Failed;
        
        auto command = std::move(const_cast<std::unique_ptr<ICommand>&>(m_undoStack.top()));
        m_undoStack.pop();
        
        auto result = command->Undo();
        if (result == CommandResult::Success)
        {
            m_redoStack.push(std::move(command));
        }
        
        return result;
    }
    
    // 重做
    CommandResult Redo()
    {
        if (m_redoStack.empty())
            return CommandResult::Failed;
        
        auto command = std::move(const_cast<std::unique_ptr<ICommand>&>(m_redoStack.top()));
        m_redoStack.pop();
        
        auto result = command->Redo();
        if (result == CommandResult::Success)
        {
            m_undoStack.push(std::move(command));
        }
        
        return result;
    }
    
    // 状态查询
    bool CanUndo() const { return !m_undoStack.empty(); }
    bool CanRedo() const { return !m_redoStack.empty(); }
    
    // 获取命令历史
    std::vector<std::string> GetUndoHistory() const
    {
        std::vector<std::string> history;
        std::stack<std::unique_ptr<ICommand>> tempStack = m_undoStack;
        
        while (!tempStack.empty())
        {
            history.push_back(tempStack.top()->GetName());
            tempStack.pop();
        }
        
        std::reverse(history.begin(), history.end());
        return history;
    }
    
    std::vector<std::string> GetRedoHistory() const
    {
        std::vector<std::string> history;
        std::stack<std::unique_ptr<ICommand>> tempStack = m_redoStack;
        
        while (!tempStack.empty())
        {
            history.push_back(tempStack.top()->GetName());
            tempStack.pop();
        }
        
        return history;
    }
    
    // 清空历史
    void ClearHistory()
    {
        while (!m_undoStack.empty()) m_undoStack.pop();
        while (!m_redoStack.empty()) m_redoStack.pop();
    }
    
    // 设置最大撤销级别
    void SetMaxUndoLevels(size_t levels) { m_maxUndoLevels = levels; }

private:
    std::stack<std::unique_ptr<ICommand>> m_undoStack;
    std::stack<std::unique_ptr<ICommand>> m_redoStack;
    size_t m_maxUndoLevels = 50;
};

// 异步命令执行器
class AsyncCommandExecutor
{
public:
    AsyncCommandExecutor() = default;
    ~AsyncCommandExecutor() { Stop(); }
    
    // 添加命令到队列
    void QueueCommand(std::unique_ptr<ICommand> command)
    {
        std::lock_guard<std::mutex> lock(m_mutex);
        m_commandQueue.push(std::move(command));
        m_condition.notify_one();
    }
    
    // 启动执行线程
    void Start()
    {
        if (m_running) return;
        
        m_running = true;
        m_workerThread = std::thread(&AsyncCommandExecutor::WorkerLoop, this);
    }
    
    // 停止执行
    void Stop()
    {
        {
            std::lock_guard<std::mutex> lock(m_mutex);
            m_running = false;
        }
        m_condition.notify_all();
        
        if (m_workerThread.joinable())
        {
            m_workerThread.join();
        }
    }
    
    // 等待所有命令完成
    void WaitForCompletion()
    {
        std::unique_lock<std::mutex> lock(m_mutex);
        m_condition.wait(lock, [this] { return m_commandQueue.empty(); });
    }
    
    // 获取队列大小
    size_t GetQueueSize() const
    {
        std::lock_guard<std::mutex> lock(m_mutex);
        return m_commandQueue.size();
    }

private:
    std::queue<std::unique_ptr<ICommand>> m_commandQueue;
    std::thread m_workerThread;
    std::mutex m_mutex;
    std::condition_variable m_condition;
    std::atomic<bool> m_running{false};
    
    void WorkerLoop()
    {
        while (m_running)
        {
            std::unique_ptr<ICommand> command;
            
            {
                std::unique_lock<std::mutex> lock(m_mutex);
                m_condition.wait(lock, [this] { return !m_commandQueue.empty() || !m_running; });
                
                if (!m_running) break;
                
                if (!m_commandQueue.empty())
                {
                    command = std::move(m_commandQueue.front());
                    m_commandQueue.pop();
                }
            }
            
            if (command)
            {
                try
                {
                    command->Execute();
                }
                catch (const std::exception& e)
                {
                    OutputDebugStringA(("[COMMAND] Exception in async command: " + std::string(e.what()) + "\n").c_str());
                }
            }
        }
    }
};

// 游戏特定命令示例

// 移动命令
class MoveCommand : public ICommand
{
public:
    MoveCommand(GameObject* object, const TransformComponent::Vector3& delta)
        : m_object(object), m_delta(delta) {}
    
    CommandResult Execute() override
    {
        if (!m_object || !m_object->Transform()) return CommandResult::Failed;
        
        m_oldPosition = m_object->Transform()->position;
        m_object->Transform()->Translate(m_delta);
        
        m_executed = true;
        m_executionTime = std::chrono::steady_clock::now();
        return CommandResult::Success;
    }
    
    CommandResult Undo() override
    {
        if (!m_executed || !m_object || !m_object->Transform()) return CommandResult::Failed;
        
        m_object->Transform()->SetPosition(m_oldPosition);
        m_executed = false;
        return CommandResult::Success;
    }
    
    std::string GetName() const override { return "Move Object"; }

private:
    GameObject* m_object;
    TransformComponent::Vector3 m_delta;
    TransformComponent::Vector3 m_oldPosition;
};

// 创建对象命令
class CreateObjectCommand : public ICommand
{
public:
    explicit CreateObjectCommand(const std::string& objectName)
        : m_objectName(objectName) {}
    
    CommandResult Execute() override
    {
        m_createdObject = GAME_OBJECTS().CreateGameObject(m_objectName);
        if (m_createdObject)
        {
            m_executed = true;
            m_executionTime = std::chrono::steady_clock::now();
            return CommandResult::Success;
        }
        return CommandResult::Failed;
    }
    
    CommandResult Undo() override
    {
        if (!m_executed || !m_createdObject) return CommandResult::Failed;
        
        GAME_OBJECTS().DestroyGameObject(m_createdObject);
        m_createdObject = nullptr;
        m_executed = false;
        return CommandResult::Success;
    }
    
    std::string GetName() const override { return "Create Object: " + m_objectName; }

private:
    std::string m_objectName;
    GameObject* m_createdObject = nullptr;
};

} // namespace sword2

// 全局命令调用器
extern sword2::CommandInvoker g_CommandInvoker;
extern sword2::AsyncCommandExecutor g_AsyncCommandExecutor;

// 便捷宏定义
#define EXECUTE_COMMAND(command) g_CommandInvoker.ExecuteCommand(std::move(command))
#define UNDO_COMMAND() g_CommandInvoker.Undo()
#define REDO_COMMAND() g_CommandInvoker.Redo()
#define CAN_UNDO() g_CommandInvoker.CanUndo()
#define CAN_REDO() g_CommandInvoker.CanRedo()

#define QUEUE_ASYNC_COMMAND(command) g_AsyncCommandExecutor.QueueCommand(std::move(command))

// 便捷的命令创建宏
#define MAKE_FUNCTION_COMMAND(name, executeFunc, undoFunc) \
    std::make_unique<sword2::FunctionCommand>(name, executeFunc, undoFunc)

#define MAKE_MOVE_COMMAND(object, delta) \
    std::make_unique<sword2::MoveCommand>(object, delta)

#define MAKE_CREATE_COMMAND(name) \
    std::make_unique<sword2::CreateObjectCommand>(name)

#endif // COMMAND_PATTERN_H
