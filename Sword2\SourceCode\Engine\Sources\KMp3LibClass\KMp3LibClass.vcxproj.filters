﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="4.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <ItemGroup>
    <Filter Include="Source Files">
      <UniqueIdentifier>{3d915b60-d943-4a99-b945-9f238dad4771}</UniqueIdentifier>
      <Extensions>cpp;c;cxx;rc;def;r;odl;idl;hpj;bat</Extensions>
    </Filter>
    <Filter Include="Header Files">
      <UniqueIdentifier>{fd94d2c9-d408-4a26-bf45-175f6b6643fb}</UniqueIdentifier>
      <Extensions>h;hpp;hxx;hm;inl</Extensions>
    </Filter>
    <Filter Include="asm_x86">
      <UniqueIdentifier>{00509aa5-08ad-4a0b-9a85-8f6156d4ce52}</UniqueIdentifier>
    </Filter>
  </ItemGroup>
  <ItemGroup>
    <ClCompile Include="src\KMp3DecodeClass.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\KMp3FdctClass.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\KMp3HeadClass.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\KMp3HufClass.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\KMp3HybridClass.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\KMp3InitClass.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\KMp3LibClass.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\KMp3MdctClass.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\KMp3MsisClass.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\KMp3QuantClass.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\KMp3SbtClass.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\KMp3ScaleClass.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\KMp3WinBClass.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\KMp3WinClass.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
  </ItemGroup>
  <ItemGroup>
    <ResourceCompile Include="KMp3LibClass.rc">
      <Filter>Source Files</Filter>
    </ResourceCompile>
  </ItemGroup>
  <ItemGroup>
    <ClInclude Include="src\KMp3DecodeClass.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="src\KMp3FdctClass.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="src\KMp3HeadClass.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="src\KMp3HufClass.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="src\KMp3HufTable.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="src\KMp3HybridClass.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="src\KMp3InitClass.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="src\KMp3LibClass.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="src\KMp3MdctClass.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="src\KMp3MsisClass.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="src\KMp3QuantClass.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="src\KMp3SbtClass.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="src\KMp3ScaleClass.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="src\KMp3Type.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="src\KMp3WinBClass.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="src\KMp3WinClass.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="src\KMp3WinTable.h">
      <Filter>Header Files</Filter>
    </ClInclude>
  </ItemGroup>
  <ItemGroup>
    <CustomBuild Include="src\asm_x86\mp3_fdct.asm">
      <Filter>asm_x86</Filter>
    </CustomBuild>
    <CustomBuild Include="src\asm_x86\mp3_mdct.asm">
      <Filter>asm_x86</Filter>
    </CustomBuild>
    <CustomBuild Include="src\asm_x86\mp3_msis.asm">
      <Filter>asm_x86</Filter>
    </CustomBuild>
    <CustomBuild Include="src\asm_x86\mp3_win.asm">
      <Filter>asm_x86</Filter>
    </CustomBuild>
    <CustomBuild Include="src\asm_x86\mp3_winb.asm">
      <Filter>asm_x86</Filter>
    </CustomBuild>
  </ItemGroup>
</Project>