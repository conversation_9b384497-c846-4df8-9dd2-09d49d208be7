// This file is generated by omniidl (C++ backend)- omniORB_3_0. Do not edit.

#include "AssistantServer.h"
#include <omniORB3/callDescriptor.h>

static const char* _0RL_library_version = omniORB_3_0;

size_t
Message::_NP_alignedSize(size_t _initialoffset) const
{
  CORBA::ULong _msgsize = _initialoffset;
  _msgsize = omni::align_to(_msgsize, omni::ALIGN_4) + 4;

  _msgsize = omni::align_to(_msgsize, omni::ALIGN_4) + 4;

  _msgsize = MsgData._NP_alignedSize(_msgsize);

  return _msgsize;
}

void
Message::operator>>= (NetBufferedStream &_n) const
{
  MsgId >>= _n;
  PlayerId >>= _n;
  MsgData >>= _n;
  
}

void
Message::operator<<= (NetBufferedStream &_n)
{
  MsgId <<= _n;
  PlayerId <<= _n;
  MsgData <<= _n;
  
}

void
Message::operator>>= (MemBufferedStream &_n) const
{
  MsgId >>= _n;
  PlayerId >>= _n;
  MsgData >>= _n;
  
}

void
Message::operator<<= (MemBufferedStream &_n)
{
  MsgId <<= _n;
  PlayerId <<= _n;
  MsgData <<= _n;
  
}

AssistantServer_ptr AssistantServer_Helper::_nil() {
  return AssistantServer::_nil();
}

CORBA::Boolean AssistantServer_Helper::is_nil(AssistantServer_ptr p) {
  return CORBA::is_nil(p);

}

void AssistantServer_Helper::release(AssistantServer_ptr p) {
  CORBA::release(p);
}

void AssistantServer_Helper::duplicate(AssistantServer_ptr p) {
  if( p && !p->_NP_is_nil() )  omni::duplicateObjRef(p);
}

size_t AssistantServer_Helper::NP_alignedSize(AssistantServer_ptr obj, size_t offset) {
  return AssistantServer::_alignedSize(obj, offset);
}

void AssistantServer_Helper::marshalObjRef(AssistantServer_ptr obj, NetBufferedStream& s) {
  AssistantServer::_marshalObjRef(obj, s);
}

AssistantServer_ptr AssistantServer_Helper::unmarshalObjRef(NetBufferedStream& s) {
  return AssistantServer::_unmarshalObjRef(s);
}

void AssistantServer_Helper::marshalObjRef(AssistantServer_ptr obj, MemBufferedStream& s) {
  AssistantServer::_marshalObjRef(obj, s);
}

AssistantServer_ptr AssistantServer_Helper::unmarshalObjRef(MemBufferedStream& s) {
  return AssistantServer::_unmarshalObjRef(s);
}

AssistantServer_ptr
AssistantServer::_duplicate(AssistantServer_ptr obj)
{
  if( obj && !obj->_NP_is_nil() )  omni::duplicateObjRef(obj);

  return obj;
}

AssistantServer_ptr
AssistantServer::_narrow(CORBA::Object_ptr obj)
{
  if( !obj || obj->_NP_is_nil() || obj->_NP_is_pseudo() ) return _nil();
  _ptr_type e = (_ptr_type) obj->_PR_getobj()->_realNarrow(_PD_repoId);
  return e ? e : _nil();
}

AssistantServer_ptr
AssistantServer::_nil()
{
  static _objref_AssistantServer* _the_nil_ptr = 0;
  if( !_the_nil_ptr ) {
    omni::nilRefLock().lock();
  if( !_the_nil_ptr )  _the_nil_ptr = new _objref_AssistantServer;
    omni::nilRefLock().unlock();
  }
  return _the_nil_ptr;
}

const char* AssistantServer::_PD_repoId = "IDL:AssistantServer:1.0";

_objref_AssistantServer::~_objref_AssistantServer() {}

_objref_AssistantServer::_objref_AssistantServer(const char* mdri,
   IOP::TaggedProfileList* p, omniIdentity* id, omniLocalIdentity* lid) :
   
   omniObjRef(AssistantServer::_PD_repoId, mdri, p, id, lid)
{
  _PR_setobj(this);
}

void*
_objref_AssistantServer::_ptrToObjRef(const char* id)
{
  if( !strcmp(id, CORBA::Object::_PD_repoId) )
    return (CORBA::Object_ptr) this;
  if( !strcmp(id, AssistantServer::_PD_repoId) )
    return (AssistantServer_ptr) this;
  
  return 0;
}

// Proxy call descriptor class. Mangled signature:
//  _clong_i_cMessageSeq_i_cstring
class _0RL_cd_7a99751ce44193ca_00000000
  : public omniCallDescriptor
{
public:
  inline _0RL_cd_7a99751ce44193ca_00000000(LocalCallFn lcfn, const char* op, size_t oplen, _CORBA_Boolean oneway, const MessageSeq& a_0, const char* a_1):
     omniCallDescriptor(lcfn, op, oplen, oneway),
     arg_0(a_0),
     arg_1(a_1) {}

  virtual CORBA::ULong alignedSize(CORBA::ULong size_in);
  virtual void marshalArguments(GIOP_C&);
  
  virtual void unmarshalReturnedValues(GIOP_C&);
    
  inline CORBA::Long result() { return pd_result; }
  const MessageSeq& arg_0;
  const char* arg_1;
  CORBA::Long pd_result;
};

CORBA::ULong _0RL_cd_7a99751ce44193ca_00000000::alignedSize(CORBA::ULong msgsize)
{
  msgsize = arg_0._NP_alignedSize(msgsize);
  
  msgsize = omni::align_to(msgsize, omni::ALIGN_4) + 4;
  msgsize += ((const char*) arg_1) ? strlen((const char*) arg_1) + 1 : 1;
  
  return msgsize;
}

void _0RL_cd_7a99751ce44193ca_00000000::marshalArguments(GIOP_C& giop_client)
{
  arg_0 >>= giop_client;
  {
    CORBA::ULong _len = (((const char*) arg_1)? strlen((const char*) arg_1) + 1 : 1);
    
    _len >>= giop_client;
    if (_len > 1)
      giop_client.put_char_array((const CORBA::Char *)((const char*)arg_1),_len);
    else {
      if ((const char*) arg_1 == 0 && omniORB::traceLevel > 1)
        _CORBA_null_string_ptr(0);
      CORBA::Char('\0') >>= giop_client;
    }
  }
  
}

void _0RL_cd_7a99751ce44193ca_00000000::unmarshalReturnedValues(GIOP_C& giop_client)
{
  
  pd_result <<= giop_client;
  
}

// Local call call-back function.
static void
_0RL_lcfn_7a99751ce44193ca_10000000(omniCallDescriptor* cd, omniServant* svnt)
{
  _0RL_cd_7a99751ce44193ca_00000000* tcd = (_0RL_cd_7a99751ce44193ca_00000000*) cd;
  _impl_AssistantServer* impl = (_impl_AssistantServer*) svnt->_ptrToInterface(AssistantServer::_PD_repoId);
  tcd->pd_result = impl->ExecuteSeq(tcd->arg_0, tcd->arg_1);
}

CORBA::Long _objref_AssistantServer::ExecuteSeq(const MessageSeq& MsgSeq, const char* DataServerName)
{
  _0RL_cd_7a99751ce44193ca_00000000 _call_desc(_0RL_lcfn_7a99751ce44193ca_10000000, "ExecuteSeq", 11, 0, MsgSeq, DataServerName);
  
  _invoke(_call_desc);
  return _call_desc.result();
}

// Proxy call descriptor class. Mangled signature:
//  _clong_i_cMessage_i_cstring
class _0RL_cd_7a99751ce44193ca_20000000
  : public omniCallDescriptor
{
public:
  inline _0RL_cd_7a99751ce44193ca_20000000(LocalCallFn lcfn, const char* op, size_t oplen, _CORBA_Boolean oneway, const Message& a_0, const char* a_1):
     omniCallDescriptor(lcfn, op, oplen, oneway),
     arg_0(a_0),
     arg_1(a_1) {}

  virtual CORBA::ULong alignedSize(CORBA::ULong size_in);
  virtual void marshalArguments(GIOP_C&);
  
  virtual void unmarshalReturnedValues(GIOP_C&);
    
  inline CORBA::Long result() { return pd_result; }
  const Message& arg_0;
  const char* arg_1;
  CORBA::Long pd_result;
};

CORBA::ULong _0RL_cd_7a99751ce44193ca_20000000::alignedSize(CORBA::ULong msgsize)
{
  msgsize = arg_0._NP_alignedSize(msgsize);
  
  msgsize = omni::align_to(msgsize, omni::ALIGN_4) + 4;
  msgsize += ((const char*) arg_1) ? strlen((const char*) arg_1) + 1 : 1;
  
  return msgsize;
}

void _0RL_cd_7a99751ce44193ca_20000000::marshalArguments(GIOP_C& giop_client)
{
  arg_0 >>= giop_client;
  {
    CORBA::ULong _len = (((const char*) arg_1)? strlen((const char*) arg_1) + 1 : 1);
    
    _len >>= giop_client;
    if (_len > 1)
      giop_client.put_char_array((const CORBA::Char *)((const char*)arg_1),_len);
    else {
      if ((const char*) arg_1 == 0 && omniORB::traceLevel > 1)
        _CORBA_null_string_ptr(0);
      CORBA::Char('\0') >>= giop_client;
    }
  }
  
}

void _0RL_cd_7a99751ce44193ca_20000000::unmarshalReturnedValues(GIOP_C& giop_client)
{
  
  pd_result <<= giop_client;
  
}

// Local call call-back function.
static void
_0RL_lcfn_7a99751ce44193ca_30000000(omniCallDescriptor* cd, omniServant* svnt)
{
  _0RL_cd_7a99751ce44193ca_20000000* tcd = (_0RL_cd_7a99751ce44193ca_20000000*) cd;
  _impl_AssistantServer* impl = (_impl_AssistantServer*) svnt->_ptrToInterface(AssistantServer::_PD_repoId);
  tcd->pd_result = impl->Execute(tcd->arg_0, tcd->arg_1);
}

CORBA::Long _objref_AssistantServer::Execute(const Message& Msg, const char* DataServerName)
{
  _0RL_cd_7a99751ce44193ca_20000000 _call_desc(_0RL_lcfn_7a99751ce44193ca_30000000, "Execute", 8, 0, Msg, DataServerName);
  
  _invoke(_call_desc);
  return _call_desc.result();
}

_pof_AssistantServer::~_pof_AssistantServer() {}

omniObjRef*
_pof_AssistantServer::newObjRef(const char* mdri, IOP::TaggedProfileList* p,
               omniIdentity* id, omniLocalIdentity* lid)
{
  return new _objref_AssistantServer(mdri, p, id, lid);
}

CORBA::Boolean
_pof_AssistantServer::is_a(const char* id) const
{
  if( !strcmp(id, AssistantServer::_PD_repoId) )
    return 1;
  
  return 0;
}

const _pof_AssistantServer _the_pof_AssistantServer;

_impl_AssistantServer::~_impl_AssistantServer() {}

CORBA::Boolean
_impl_AssistantServer::_dispatch(GIOP_S& giop_s)
{
  if( !strcmp(giop_s.operation(), "ExecuteSeq") ) {
    
    MessageSeq arg_MsgSeq;
    
    arg_MsgSeq <<= giop_s;
    CORBA::String_var arg_DataServerName;
    
    {
      CORBA::String_member _0RL_str_tmp;
      _0RL_str_tmp <<=  giop_s;
      arg_DataServerName = _0RL_str_tmp._ptr;
      _0RL_str_tmp._ptr = 0;
    }
    
    giop_s.RequestReceived();
    CORBA::Long result;
    
    result = this->ExecuteSeq(arg_MsgSeq, arg_DataServerName.in());
    
    if( giop_s.response_expected() ) {
      size_t msgsize = (size_t) GIOP_S::ReplyHeaderSize();
      msgsize = omni::align_to(msgsize, omni::ALIGN_4) + 4;
      
      giop_s.InitialiseReply(GIOP::NO_EXCEPTION, (CORBA::ULong) msgsize);
      result >>= giop_s;
      
    }
    giop_s.ReplyCompleted();
    return 1;
  }

  if( !strcmp(giop_s.operation(), "Execute") ) {
    
    Message arg_Msg;
    
    arg_Msg <<= giop_s;
    CORBA::String_var arg_DataServerName;
    
    {
      CORBA::String_member _0RL_str_tmp;
      _0RL_str_tmp <<=  giop_s;
      arg_DataServerName = _0RL_str_tmp._ptr;
      _0RL_str_tmp._ptr = 0;
    }
    
    giop_s.RequestReceived();
    CORBA::Long result;
    
    result = this->Execute(arg_Msg, arg_DataServerName.in());
    
    if( giop_s.response_expected() ) {
      size_t msgsize = (size_t) GIOP_S::ReplyHeaderSize();
      msgsize = omni::align_to(msgsize, omni::ALIGN_4) + 4;
      
      giop_s.InitialiseReply(GIOP::NO_EXCEPTION, (CORBA::ULong) msgsize);
      result >>= giop_s;
      
    }
    giop_s.ReplyCompleted();
    return 1;
  }

  return 0;
}

void*
_impl_AssistantServer::_ptrToInterface(const char* id)
{
  if( !strcmp(id, CORBA::Object::_PD_repoId) )
    return (void*) 1;
  if( !strcmp(id, AssistantServer::_PD_repoId) )
    return (_impl_AssistantServer*) this;
  
  return 0;
}

const char*
_impl_AssistantServer::_mostDerivedRepoId()
{
  return AssistantServer::_PD_repoId;
}

POA_AssistantServer::~POA_AssistantServer() {}

