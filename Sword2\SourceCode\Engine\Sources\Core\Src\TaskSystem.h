//---------------------------------------------------------------------------
// Sword2 Task System (c) 2024
//
// File:	TaskSystem.h
// Date:	2024.07.02
// Code:	AI Assistant
// Desc:	Comprehensive task system compatible with existing task configuration files
//---------------------------------------------------------------------------
#ifndef TASK_SYSTEM_H
#define TASK_SYSTEM_H

#include "ModernCpp.h"
#include "UnifiedLoggingSystem.h"
#include "PlayerSystem.h"
#include <string>
#include <unordered_map>
#include <vector>
#include <memory>
#include <chrono>
#include <atomic>
#include <mutex>
#include <functional>

namespace sword2 {

// 任务状态（对应原有的任务状态）
enum class TaskState : uint8_t
{
    NotStarted = 0,     // 未开始
    InProgress,         // 进行中
    Completed,          // 已完成
    Failed,             // 失败
    Cancelled,          // 已取消
    Expired,            // 已过期
    Submitted           // 已提交
};

// 任务类型（对应原有的任务类型）
enum class TaskType : uint8_t
{
    Main = 0,           // 主线任务
    Side,               // 支线任务
    Daily,              // 日常任务
    Weekly,             // 周常任务
    Chain,              // 连环任务
    Guild,              // 帮会任务
    Activity,           // 活动任务
    Random,             // 随机任务
    Global,             // 全局任务
    Collection,         // 收集任务
    Killer,             // 杀手任务
    Escort              // 护送任务
};

// 任务条件类型（对应原有的StartType）
enum class TaskConditionType : uint8_t
{
    None = 0,           // 无条件
    Level,              // 等级条件
    TaskComplete,       // 任务完成
    TaskStart,          // 任务开始
    ItemHave,           // 拥有物品
    Variable,           // 变量条件
    Reputation,         // 声望条件
    Money,              // 金钱条件
    Series,             // 门派条件
    Time,               // 时间条件
    Location,           // 位置条件
    Script              // 脚本条件
};

// 任务实体类型（对应原有的TaskType）
enum class TaskEntityType : uint8_t
{
    FindGoods = 0,      // 寻找物品
    KillNpc,            // 击杀NPC
    PayMoney,           // 支付金钱
    ChangeValue,        // 改变变量
    TalkNpc,            // 对话NPC
    ReachLocation,      // 到达地点
    UseItem,            // 使用物品
    CollectItem,        // 收集物品
    EscortNpc,          // 护送NPC
    DefendLocation,     // 守卫地点
    Script              // 脚本任务
};

// 任务奖励类型（对应原有的AwardType）
enum class TaskAwardType : uint8_t
{
    None = 0,           // 无奖励
    Money,              // 金钱
    Experience,         // 经验
    Reputation,         // 声望
    Item,               // 物品
    Skill,              // 技能
    Title,              // 称号
    TaskStart,          // 开始任务
    Variable,           // 变量
    Script              // 脚本奖励
};

// 任务条件
struct TaskCondition
{
    TaskConditionType type = TaskConditionType::None;
    std::string description;    // 条件描述
    int32_t value1 = 0;        // 条件值1
    int32_t value2 = 0;        // 条件值2
    int32_t value3 = 0;        // 条件值3
    std::string stringValue;   // 字符串值
    
    TaskCondition() = default;
    TaskCondition(TaskConditionType condType, const std::string& desc, int32_t val1 = 0)
        : type(condType), description(desc), value1(val1) {}
    
    // 检查条件是否满足
    bool CheckCondition(const Player& player) const;
    
    // 获取条件描述
    std::string GetDescription() const
    {
        switch (type)
        {
        case TaskConditionType::Level: return "等级达到 " + std::to_string(value1);
        case TaskConditionType::TaskComplete: return "完成任务: " + description;
        case TaskConditionType::ItemHave: return "拥有物品: " + description + " x" + std::to_string(value1);
        case TaskConditionType::Money: return "拥有金钱: " + std::to_string(value1);
        case TaskConditionType::Reputation: return "声望达到: " + std::to_string(value1);
        default: return description;
        }
    }
};

// 任务实体（任务目标）
struct TaskEntity
{
    TaskEntityType type = TaskEntityType::FindGoods;
    std::string description;    // 实体描述
    uint32_t genre = 0;        // 类别
    uint32_t detail = 0;       // 详细
    uint32_t particular = 0;   // 特殊
    uint32_t goodsNum = 1;     // 物品数量
    bool delGoods = true;      // 是否删除物品
    std::string npcName;       // NPC名称
    uint32_t npcNum = 1;       // NPC数量
    uint32_t payMoney = 0;     // 支付金钱
    std::string taskText;      // 任务文本
    
    // 当前进度
    uint32_t currentProgress = 0;  // 当前进度
    uint32_t targetProgress = 1;   // 目标进度
    
    TaskEntity() = default;
    TaskEntity(TaskEntityType entityType, const std::string& desc, uint32_t target = 1)
        : type(entityType), description(desc), targetProgress(target) {}
    
    // 检查是否完成
    bool IsCompleted() const
    {
        return currentProgress >= targetProgress;
    }
    
    // 更新进度
    bool UpdateProgress(uint32_t amount = 1)
    {
        currentProgress = std::min(currentProgress + amount, targetProgress);
        return IsCompleted();
    }
    
    // 获取进度描述
    std::string GetProgressDescription() const
    {
        return std::to_string(currentProgress) + "/" + std::to_string(targetProgress);
    }
};

// 任务奖励
struct TaskAward
{
    TaskAwardType type = TaskAwardType::None;
    uint32_t genre = 0;        // 类别
    uint32_t detail = 0;       // 详细
    uint32_t particular = 0;   // 特殊
    uint32_t goodsNum = 1;     // 物品数量
    uint32_t moneyNum = 0;     // 金钱数量
    uint32_t expNum = 0;       // 经验数量
    uint32_t reputeNum = 0;    // 声望数量
    std::string taskString;    // 任务字符串
    std::string awardText;     // 奖励文本
    
    TaskAward() = default;
    TaskAward(TaskAwardType awardType, uint32_t amount, const std::string& text = "")
        : type(awardType), awardText(text)
    {
        switch (awardType)
        {
        case TaskAwardType::Money: moneyNum = amount; break;
        case TaskAwardType::Experience: expNum = amount; break;
        case TaskAwardType::Reputation: reputeNum = amount; break;
        case TaskAwardType::Item: goodsNum = amount; break;
        default: break;
        }
    }
    
    // 获取奖励描述
    std::string GetDescription() const
    {
        switch (type)
        {
        case TaskAwardType::Money: return std::to_string(moneyNum) + " 金钱";
        case TaskAwardType::Experience: return std::to_string(expNum) + " 经验";
        case TaskAwardType::Reputation: return std::to_string(reputeNum) + " 声望";
        case TaskAwardType::Item: return awardText + " x" + std::to_string(goodsNum);
        case TaskAwardType::TaskStart: return "开始任务: " + taskString;
        default: return awardText;
        }
    }
};

// 任务对话
struct TaskTalk
{
    std::string taskName;      // 任务名称
    std::vector<std::string> dialogues; // 对话内容
    std::string acceptText;    // 接受文本
    std::string refuseText;    // 拒绝文本
    std::string completeText;  // 完成文本
    std::string incompleteText; // 未完成文本
    
    TaskTalk() = default;
    TaskTalk(const std::string& name) : taskName(name) {}
};

// 任务模板
class TaskTemplate
{
public:
    uint32_t taskId = 0;       // 任务ID
    std::string taskName;      // 任务名称
    std::string description;   // 任务描述
    TaskType type = TaskType::Main;
    uint32_t eventId = 0;      // 事件ID
    
    // 任务参与者
    std::string fromNpc;       // 发布NPC
    std::string toNpc;         // 完成NPC
    
    // 任务条件和实体
    std::vector<TaskCondition> startConditions;  // 开始条件
    std::vector<TaskCondition> finishConditions; // 完成条件
    std::vector<TaskEntity> entities;            // 任务实体
    std::vector<TaskAward> awards;               // 任务奖励
    TaskTalk talkData;                           // 对话数据
    
    // 任务属性
    uint32_t level = 1;        // 任务等级
    uint32_t timeLimit = 0;    // 时间限制（秒，0表示无限制）
    bool canShare = false;     // 是否可共享
    bool canCancel = true;     // 是否可取消
    bool canSkip = false;      // 是否可跳过
    uint32_t cancelCost = 0;   // 取消费用
    uint32_t skipCost = 0;     // 跳过费用
    
    // 任务链
    uint32_t prevTaskId = 0;   // 前置任务ID
    uint32_t nextTaskId = 0;   // 后续任务ID
    std::vector<uint32_t> branchTasks; // 分支任务
    
    // 脚本
    std::string scriptPath;    // 脚本路径
    
    TaskTemplate() = default;
    TaskTemplate(uint32_t id, const std::string& name, TaskType taskType)
        : taskId(id), taskName(name), type(taskType), talkData(name) {}
    
    // 检查是否可以接受
    bool CanAccept(const Player& player) const
    {
        for (const auto& condition : startConditions)
        {
            if (!condition.CheckCondition(player))
                return false;
        }
        return true;
    }
    
    // 检查是否可以完成
    bool CanComplete(const Player& player) const
    {
        for (const auto& condition : finishConditions)
        {
            if (!condition.CheckCondition(player))
                return false;
        }
        return true;
    }
    
    // 获取任务类型描述
    std::string GetTypeDescription() const
    {
        switch (type)
        {
        case TaskType::Main: return "主线任务";
        case TaskType::Side: return "支线任务";
        case TaskType::Daily: return "日常任务";
        case TaskType::Weekly: return "周常任务";
        case TaskType::Chain: return "连环任务";
        case TaskType::Guild: return "帮会任务";
        case TaskType::Activity: return "活动任务";
        case TaskType::Random: return "随机任务";
        case TaskType::Global: return "全局任务";
        case TaskType::Collection: return "收集任务";
        case TaskType::Killer: return "杀手任务";
        case TaskType::Escort: return "护送任务";
        default: return "未知任务";
        }
    }
};

// 任务实例
class TaskInstance
{
public:
    uint32_t instanceId = 0;   // 实例ID
    uint32_t templateId = 0;   // 模板ID
    uint32_t playerId = 0;     // 玩家ID
    
    TaskState state = TaskState::NotStarted;
    std::vector<TaskEntity> entities; // 任务实体副本
    
    std::chrono::system_clock::time_point acceptTime; // 接受时间
    std::chrono::system_clock::time_point completeTime; // 完成时间
    std::chrono::system_clock::time_point expireTime; // 过期时间
    
    // 任务变量
    std::unordered_map<std::string, int32_t> variables;
    
    TaskInstance() = default;
    TaskInstance(uint32_t instId, uint32_t templId, uint32_t player)
        : instanceId(instId), templateId(templId), playerId(player)
    {
        acceptTime = std::chrono::system_clock::now();
    }
    
    // 检查是否过期
    bool IsExpired() const
    {
        if (expireTime == std::chrono::system_clock::time_point{})
            return false; // 无时间限制
        
        return std::chrono::system_clock::now() > expireTime;
    }
    
    // 设置过期时间
    void SetExpireTime(uint32_t seconds)
    {
        if (seconds == 0)
        {
            expireTime = std::chrono::system_clock::time_point{}; // 无限制
        }
        else
        {
            expireTime = acceptTime + std::chrono::seconds(seconds);
        }
    }
    
    // 获取剩余时间
    uint32_t GetRemainingTime() const
    {
        if (expireTime == std::chrono::system_clock::time_point{})
            return UINT32_MAX; // 无限制
        
        auto now = std::chrono::system_clock::now();
        if (now >= expireTime)
            return 0;
        
        auto remaining = std::chrono::duration_cast<std::chrono::seconds>(expireTime - now);
        return static_cast<uint32_t>(remaining.count());
    }
    
    // 检查是否完成
    bool IsCompleted() const
    {
        for (const auto& entity : entities)
        {
            if (!entity.IsCompleted())
                return false;
        }
        return true;
    }
    
    // 更新实体进度
    bool UpdateEntityProgress(size_t entityIndex, uint32_t amount = 1)
    {
        if (entityIndex >= entities.size())
            return false;
        
        return entities[entityIndex].UpdateProgress(amount);
    }
    
    // 获取进度百分比
    float GetProgressPercentage() const
    {
        if (entities.empty())
            return 0.0f;
        
        uint32_t totalProgress = 0;
        uint32_t totalTarget = 0;
        
        for (const auto& entity : entities)
        {
            totalProgress += entity.currentProgress;
            totalTarget += entity.targetProgress;
        }
        
        return totalTarget > 0 ? static_cast<float>(totalProgress) / totalTarget : 0.0f;
    }
    
    // 设置变量
    void SetVariable(const std::string& name, int32_t value)
    {
        variables[name] = value;
    }
    
    // 获取变量
    int32_t GetVariable(const std::string& name) const
    {
        auto it = variables.find(name);
        return (it != variables.end()) ? it->second : 0;
    }
    
    // 获取状态描述
    std::string GetStateDescription() const
    {
        switch (state)
        {
        case TaskState::NotStarted: return "未开始";
        case TaskState::InProgress: return "进行中";
        case TaskState::Completed: return "已完成";
        case TaskState::Failed: return "失败";
        case TaskState::Cancelled: return "已取消";
        case TaskState::Expired: return "已过期";
        case TaskState::Submitted: return "已提交";
        default: return "未知状态";
        }
    }
};

} // namespace sword2

#endif // TASK_SYSTEM_H
