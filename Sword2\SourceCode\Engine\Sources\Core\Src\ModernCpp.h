//---------------------------------------------------------------------------
// Sword2 Modern C++ Infrastructure (c) 2024
//
// File:	ModernCpp.h
// Date:	2024.07.02
// Code:	AI Assistant
// Desc:	Modern C++ features and utilities for legacy code modernization
//---------------------------------------------------------------------------
#ifndef MODERN_CPP_H
#define MODERN_CPP_H

#include <memory>
#include <functional>
#include <type_traits>
#include <utility>
#include <exception>
#include <stdexcept>

// 智能指针类型别名
template<typename T>
using unique_ptr = std::unique_ptr<T>;

template<typename T>
using shared_ptr = std::shared_ptr<T>;

template<typename T>
using weak_ptr = std::weak_ptr<T>;

// 现代化的内存管理工具
namespace sword2 {

// RAII资源管理器
template<typename Resource, typename Deleter>
class RAIIWrapper
{
public:
    explicit RAIIWrapper(Resource resource, Deleter deleter)
        : m_resource(resource), m_deleter(deleter), m_valid(true) {}
    
    ~RAIIWrapper()
    {
        if (m_valid && m_resource)
        {
            m_deleter(m_resource);
        }
    }
    
    // 移动构造
    RAIIWrapper(RAIIWrapper&& other) noexcept
        : m_resource(other.m_resource), m_deleter(std::move(other.m_deleter)), m_valid(other.m_valid)
    {
        other.m_valid = false;
    }
    
    // 移动赋值
    RAIIWrapper& operator=(RAIIWrapper&& other) noexcept
    {
        if (this != &other)
        {
            if (m_valid && m_resource)
            {
                m_deleter(m_resource);
            }
            m_resource = other.m_resource;
            m_deleter = std::move(other.m_deleter);
            m_valid = other.m_valid;
            other.m_valid = false;
        }
        return *this;
    }
    
    // 禁止拷贝
    RAIIWrapper(const RAIIWrapper&) = delete;
    RAIIWrapper& operator=(const RAIIWrapper&) = delete;
    
    Resource get() const { return m_resource; }
    Resource operator->() const { return m_resource; }
    Resource& operator*() { return m_resource; }
    const Resource& operator*() const { return m_resource; }
    
    explicit operator bool() const { return m_valid && m_resource; }
    
    Resource release()
    {
        m_valid = false;
        return m_resource;
    }

private:
    Resource m_resource;
    Deleter m_deleter;
    bool m_valid;
};

// 便捷的RAII创建函数
template<typename Resource, typename Deleter>
auto make_raii(Resource resource, Deleter deleter)
{
    return RAIIWrapper<Resource, Deleter>(resource, deleter);
}

// 现代化的智能指针工厂
template<typename T, typename... Args>
std::unique_ptr<T> make_unique(Args&&... args)
{
    return std::unique_ptr<T>(new T(std::forward<Args>(args)...));
}

template<typename T, typename... Args>
std::shared_ptr<T> make_shared_safe(Args&&... args)
{
    try
    {
        return std::make_shared<T>(std::forward<Args>(args)...);
    }
    catch (const std::exception& e)
    {
        // 记录异常并返回空指针
        OutputDebugStringA(("[MODERN_CPP] Failed to create shared_ptr: " + std::string(e.what()) + "\n").c_str());
        return nullptr;
    }
}

// 异常安全的内存分配器
template<typename T>
class SafeAllocator
{
public:
    using value_type = T;
    using pointer = T*;
    using const_pointer = const T*;
    using reference = T&;
    using const_reference = const T&;
    using size_type = std::size_t;
    using difference_type = std::ptrdiff_t;

    template<typename U>
    struct rebind
    {
        using other = SafeAllocator<U>;
    };

    SafeAllocator() = default;
    
    template<typename U>
    SafeAllocator(const SafeAllocator<U>&) {}

    pointer allocate(size_type n)
    {
        if (n > max_size())
        {
            throw std::bad_alloc();
        }
        
        pointer p = static_cast<pointer>(g_MemoryPool.Allocate(n * sizeof(T)));
        if (!p)
        {
            throw std::bad_alloc();
        }
        return p;
    }

    void deallocate(pointer p, size_type)
    {
        g_MemoryPool.Free(p);
    }

    size_type max_size() const
    {
        return std::numeric_limits<size_type>::max() / sizeof(T);
    }

    template<typename U, typename... Args>
    void construct(U* p, Args&&... args)
    {
        new(p) U(std::forward<Args>(args)...);
    }

    template<typename U>
    void destroy(U* p)
    {
        p->~U();
    }
};

// 现代化的错误处理
class ModernException : public std::exception
{
public:
    explicit ModernException(const std::string& message)
        : m_message(message) {}
    
    const char* what() const noexcept override
    {
        return m_message.c_str();
    }

private:
    std::string m_message;
};

// 结果类型 - 用于错误处理
template<typename T, typename E = std::string>
class Result
{
public:
    // 成功构造
    static Result success(T value)
    {
        Result result;
        result.m_hasValue = true;
        result.m_value = std::move(value);
        return result;
    }
    
    // 失败构造
    static Result failure(E error)
    {
        Result result;
        result.m_hasValue = false;
        result.m_error = std::move(error);
        return result;
    }
    
    bool isSuccess() const { return m_hasValue; }
    bool isFailure() const { return !m_hasValue; }
    
    const T& value() const
    {
        if (!m_hasValue)
        {
            throw ModernException("Attempting to access value of failed Result");
        }
        return m_value;
    }
    
    const E& error() const
    {
        if (m_hasValue)
        {
            throw ModernException("Attempting to access error of successful Result");
        }
        return m_error;
    }
    
    // 链式操作
    template<typename F>
    auto map(F&& func) -> Result<decltype(func(m_value)), E>
    {
        if (m_hasValue)
        {
            return Result<decltype(func(m_value)), E>::success(func(m_value));
        }
        else
        {
            return Result<decltype(func(m_value)), E>::failure(m_error);
        }
    }

private:
    bool m_hasValue = false;
    T m_value;
    E m_error;
};

// 现代化的单例模式
template<typename T>
class Singleton
{
public:
    static T& getInstance()
    {
        static T instance;
        return instance;
    }

protected:
    Singleton() = default;
    virtual ~Singleton() = default;

public:
    Singleton(const Singleton&) = delete;
    Singleton& operator=(const Singleton&) = delete;
    Singleton(Singleton&&) = delete;
    Singleton& operator=(Singleton&&) = delete;
};

// 现代化的观察者模式
template<typename... Args>
class Signal
{
public:
    using SlotType = std::function<void(Args...)>;
    using ConnectionId = std::size_t;

    ConnectionId connect(SlotType slot)
    {
        ConnectionId id = m_nextId++;
        m_slots[id] = std::move(slot);
        return id;
    }

    void disconnect(ConnectionId id)
    {
        m_slots.erase(id);
    }

    void emit(Args... args)
    {
        for (const auto& pair : m_slots)
        {
            try
            {
                pair.second(args...);
            }
            catch (const std::exception& e)
            {
                OutputDebugStringA(("[SIGNAL] Exception in slot: " + std::string(e.what()) + "\n").c_str());
            }
        }
    }

    void clear()
    {
        m_slots.clear();
    }

private:
    std::map<ConnectionId, SlotType> m_slots;
    ConnectionId m_nextId = 0;
};

// 现代化的工厂模式
template<typename Base, typename Key = std::string>
class Factory
{
public:
    using CreatorFunc = std::function<std::unique_ptr<Base>()>;

    void registerCreator(const Key& key, CreatorFunc creator)
    {
        m_creators[key] = std::move(creator);
    }

    std::unique_ptr<Base> create(const Key& key)
    {
        auto it = m_creators.find(key);
        if (it != m_creators.end())
        {
            return it->second();
        }
        return nullptr;
    }

    bool isRegistered(const Key& key) const
    {
        return m_creators.find(key) != m_creators.end();
    }

private:
    std::map<Key, CreatorFunc> m_creators;
};

// 现代化的作用域守卫
template<typename F>
class ScopeGuard
{
public:
    explicit ScopeGuard(F&& func) : m_func(std::forward<F>(func)), m_active(true) {}
    
    ~ScopeGuard()
    {
        if (m_active)
        {
            try
            {
                m_func();
            }
            catch (...)
            {
                // 析构函数中不抛出异常
            }
        }
    }
    
    void dismiss() { m_active = false; }
    
    ScopeGuard(const ScopeGuard&) = delete;
    ScopeGuard& operator=(const ScopeGuard&) = delete;
    
    ScopeGuard(ScopeGuard&& other) noexcept
        : m_func(std::move(other.m_func)), m_active(other.m_active)
    {
        other.m_active = false;
    }

private:
    F m_func;
    bool m_active;
};

template<typename F>
auto make_scope_guard(F&& func)
{
    return ScopeGuard<F>(std::forward<F>(func));
}

} // namespace sword2

// 便捷宏定义
#define MAKE_UNIQUE(type, ...) sword2::make_unique<type>(__VA_ARGS__)
#define MAKE_SHARED(type, ...) sword2::make_shared_safe<type>(__VA_ARGS__)
#define MAKE_RAII(resource, deleter) sword2::make_raii(resource, deleter)
#define SCOPE_GUARD(func) auto _guard = sword2::make_scope_guard(func)

// 现代化的类型特征
namespace sword2 {

template<typename T>
struct is_smart_pointer : std::false_type {};

template<typename T>
struct is_smart_pointer<std::unique_ptr<T>> : std::true_type {};

template<typename T>
struct is_smart_pointer<std::shared_ptr<T>> : std::true_type {};

template<typename T>
struct is_smart_pointer<std::weak_ptr<T>> : std::true_type {};

template<typename T>
constexpr bool is_smart_pointer_v = is_smart_pointer<T>::value;

} // namespace sword2

#endif // MODERN_CPP_H
