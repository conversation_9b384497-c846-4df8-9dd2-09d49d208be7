//---------------------------------------------------------------------------
// Sword2 Player Data Persistence (c) 2024
//
// File:	PlayerDataPersistence.h
// Date:	2024.07.02
// Code:	AI Assistant
// Desc:	Player data serialization and persistence system
//---------------------------------------------------------------------------
#ifndef PLAYER_DATA_PERSISTENCE_H
#define PLAYER_DATA_PERSISTENCE_H

#include "PlayerSystem.h"
#include "UnifiedLoggingSystem.h"
#include <fstream>
#include <sstream>
#include <filesystem>
#include <chrono>

namespace sword2 {

// 数据序列化格式
enum class SerializationFormat : uint8_t
{
    Binary = 0,         // 二进制格式
    JSON,               // JSON格式
    XML                 // XML格式
};

// 玩家数据序列化器
class PlayerDataSerializer
{
public:
    // 序列化玩家数据到字符串
    static std::string SerializePlayer(const Player& player, SerializationFormat format = SerializationFormat::JSON)
    {
        switch (format)
        {
        case SerializationFormat::JSON:
            return SerializeToJSON(player);
        case SerializationFormat::XML:
            return SerializeToXML(player);
        case SerializationFormat::Binary:
            return SerializeToBinary(player);
        default:
            return SerializeToJSON(player);
        }
    }

    // 从字符串反序列化玩家数据
    static bool DeserializePlayer(const std::string& data, Player& player, SerializationFormat format = SerializationFormat::JSON)
    {
        try
        {
            switch (format)
            {
            case SerializationFormat::JSON:
                return DeserializeFromJSON(data, player);
            case SerializationFormat::XML:
                return DeserializeFromXML(data, player);
            case SerializationFormat::Binary:
                return DeserializeFromBinary(data, player);
            default:
                return DeserializeFromJSON(data, player);
            }
        }
        catch (const std::exception& e)
        {
            LOG_ERROR("PLAYER_PERSIST", std::string("Deserialization error: ") + e.what());
            return false;
        }
    }

private:
    // JSON序列化
    static std::string SerializeToJSON(const Player& player)
    {
        std::ostringstream json;
        json << "{\n";
        json << "  \"playerId\": " << player.playerId << ",\n";
        json << "  \"playerName\": \"" << EscapeString(player.playerName) << "\",\n";
        json << "  \"accountName\": \"" << EscapeString(player.accountName) << "\",\n";
        json << "  \"gender\": " << static_cast<int>(player.gender) << ",\n";
        json << "  \"series\": " << static_cast<int>(player.series) << ",\n";
        json << "  \"level\": " << player.level << ",\n";
        json << "  \"experience\": " << player.experience << ",\n";
        json << "  \"nextLevelExp\": " << player.nextLevelExp << ",\n";
        json << "  \"money\": " << player.money << ",\n";
        json << "  \"pkValue\": " << player.pkValue << ",\n";
        json << "  \"reputation\": " << player.reputation << ",\n";
        json << "  \"killCount\": " << player.killCount << ",\n";
        json << "  \"deathCount\": " << player.deathCount << ",\n";
        json << "  \"totalOnlineTime\": " << player.totalOnlineTime << ",\n";

        // 位置信息
        json << "  \"position\": {\n";
        json << "    \"mapId\": " << player.position.mapId << ",\n";
        json << "    \"x\": " << player.position.x << ",\n";
        json << "    \"y\": " << player.position.y << ",\n";
        json << "    \"direction\": " << player.position.direction << "\n";
        json << "  },\n";

        // 属性信息
        json << "  \"attributes\": {\n";
        json << "    \"strength\": " << player.attributes.strength << ",\n";
        json << "    \"agility\": " << player.attributes.agility << ",\n";
        json << "    \"vitality\": " << player.attributes.vitality << ",\n";
        json << "    \"energy\": " << player.attributes.energy << ",\n";
        json << "    \"maxLife\": " << player.attributes.maxLife << ",\n";
        json << "    \"currentLife\": " << player.attributes.currentLife << ",\n";
        json << "    \"maxMana\": " << player.attributes.maxMana << ",\n";
        json << "    \"currentMana\": " << player.attributes.currentMana << ",\n";
        json << "    \"maxStamina\": " << player.attributes.maxStamina << ",\n";
        json << "    \"currentStamina\": " << player.attributes.currentStamina << ",\n";
        json << "    \"minAttack\": " << player.attributes.minAttack << ",\n";
        json << "    \"maxAttack\": " << player.attributes.maxAttack << ",\n";
        json << "    \"defense\": " << player.attributes.defense << ",\n";
        json << "    \"magicDefense\": " << player.attributes.magicDefense << "\n";
        json << "  },\n";

        // 装备信息
        json << "  \"equipment\": [\n";
        bool firstEquip = true;
        for (size_t i = 0; i < player.equipment.size(); ++i)
        {
            if (player.equipment[i])
            {
                if (!firstEquip) json << ",\n";
                json << "    {\n";
                json << "      \"slot\": " << i << ",\n";
                json << "      \"itemId\": " << player.equipment[i]->itemId << ",\n";
                json << "      \"itemName\": \"" << EscapeString(player.equipment[i]->itemName) << "\",\n";
                json << "      \"durability\": " << player.equipment[i]->durability << ",\n";
                json << "      \"maxDurability\": " << player.equipment[i]->maxDurability << ",\n";
                json << "      \"enhanceLevel\": " << player.equipment[i]->enhanceLevel << "\n";
                json << "    }";
                firstEquip = false;
            }
        }
        json << "\n  ],\n";

        // 时间信息
        auto createTime = std::chrono::system_clock::to_time_t(player.createTime);
        auto lastLoginTime = std::chrono::system_clock::to_time_t(player.lastLoginTime);
        auto lastLogoutTime = std::chrono::system_clock::to_time_t(player.lastLogoutTime);

        json << "  \"createTime\": " << createTime << ",\n";
        json << "  \"lastLoginTime\": " << lastLoginTime << ",\n";
        json << "  \"lastLogoutTime\": " << lastLogoutTime << "\n";
        json << "}";

        return json.str();
    }

    // JSON反序列化
    static bool DeserializeFromJSON(const std::string& data, Player& player)
    {
        // 简化的JSON解析实现
        // 在实际项目中应该使用专业的JSON库如nlohmann/json

        try
        {
            // 解析基础字段
            player.playerId = ExtractIntValue(data, "playerId");
            player.playerName = ExtractStringValue(data, "playerName");
            player.accountName = ExtractStringValue(data, "accountName");
            player.gender = static_cast<PlayerGender>(ExtractIntValue(data, "gender"));
            player.series = static_cast<PlayerSeries>(ExtractIntValue(data, "series"));
            player.level = ExtractIntValue(data, "level");
            player.experience = ExtractLongValue(data, "experience");
            player.nextLevelExp = ExtractLongValue(data, "nextLevelExp");
            player.money = ExtractLongValue(data, "money");
            player.pkValue = ExtractIntValue(data, "pkValue");
            player.reputation = ExtractIntValue(data, "reputation");
            player.killCount = ExtractIntValue(data, "killCount");
            player.deathCount = ExtractIntValue(data, "deathCount");
            player.totalOnlineTime = ExtractLongValue(data, "totalOnlineTime");

            // 解析位置信息
            player.position.mapId = ExtractNestedIntValue(data, "position", "mapId");
            player.position.x = ExtractNestedIntValue(data, "position", "x");
            player.position.y = ExtractNestedIntValue(data, "position", "y");
            player.position.direction = ExtractNestedIntValue(data, "position", "direction");

            // 解析属性信息
            player.attributes.strength = ExtractNestedIntValue(data, "attributes", "strength");
            player.attributes.agility = ExtractNestedIntValue(data, "attributes", "agility");
            player.attributes.vitality = ExtractNestedIntValue(data, "attributes", "vitality");
            player.attributes.energy = ExtractNestedIntValue(data, "attributes", "energy");
            player.attributes.maxLife = ExtractNestedIntValue(data, "attributes", "maxLife");
            player.attributes.currentLife = ExtractNestedIntValue(data, "attributes", "currentLife");
            player.attributes.maxMana = ExtractNestedIntValue(data, "attributes", "maxMana");
            player.attributes.currentMana = ExtractNestedIntValue(data, "attributes", "currentMana");
            player.attributes.maxStamina = ExtractNestedIntValue(data, "attributes", "maxStamina");
            player.attributes.currentStamina = ExtractNestedIntValue(data, "attributes", "currentStamina");
            player.attributes.minAttack = ExtractNestedIntValue(data, "attributes", "minAttack");
            player.attributes.maxAttack = ExtractNestedIntValue(data, "attributes", "maxAttack");
            player.attributes.defense = ExtractNestedIntValue(data, "attributes", "defense");
            player.attributes.magicDefense = ExtractNestedIntValue(data, "attributes", "magicDefense");

            // 解析时间信息
            auto createTime = ExtractLongValue(data, "createTime");
            auto lastLoginTime = ExtractLongValue(data, "lastLoginTime");
            auto lastLogoutTime = ExtractLongValue(data, "lastLogoutTime");

            player.createTime = std::chrono::system_clock::from_time_t(createTime);
            player.lastLoginTime = std::chrono::system_clock::from_time_t(lastLoginTime);
            player.lastLogoutTime = std::chrono::system_clock::from_time_t(lastLogoutTime);

            return true;
        }
        catch (const std::exception& e)
        {
            LOG_ERROR("PLAYER_PERSIST", std::string("JSON parsing error: ") + e.what());
            return false;
        }
    }

    // XML序列化（简化实现）
    static std::string SerializeToXML(const Player& player)
    {
        std::ostringstream xml;
        xml << "<?xml version=\"1.0\" encoding=\"UTF-8\"?>\n";
        xml << "<Player>\n";
        xml << "  <PlayerId>" << player.playerId << "</PlayerId>\n";
        xml << "  <PlayerName>" << EscapeString(player.playerName) << "</PlayerName>\n";
        xml << "  <AccountName>" << EscapeString(player.accountName) << "</AccountName>\n";
        xml << "  <Gender>" << static_cast<int>(player.gender) << "</Gender>\n";
        xml << "  <Series>" << static_cast<int>(player.series) << "</Series>\n";
        xml << "  <Level>" << player.level << "</Level>\n";
        xml << "  <Experience>" << player.experience << "</Experience>\n";
        xml << "  <Money>" << player.money << "</Money>\n";
        xml << "</Player>";

        return xml.str();
    }

    // XML反序列化（简化实现）
    static bool DeserializeFromXML(const std::string& data, Player& player)
    {
        // 简化的XML解析实现
        // 在实际项目中应该使用专业的XML库
        return false; // 暂不实现
    }

    // 二进制序列化（简化实现）
    static std::string SerializeToBinary(const Player& player)
    {
        // 二进制序列化实现
        return ""; // 暂不实现
    }

    // 二进制反序列化（简化实现）
    static bool DeserializeFromBinary(const std::string& data, Player& player)
    {
        // 二进制反序列化实现
        return false; // 暂不实现
    }

    // 辅助函数
    static std::string EscapeString(const std::string& str)
    {
        std::string escaped = str;
        // 简单的转义处理
        size_t pos = 0;
        while ((pos = escaped.find("\"", pos)) != std::string::npos)
        {
            escaped.replace(pos, 1, "\\\"");
            pos += 2;
        }
        return escaped;
    }

    static uint32_t ExtractIntValue(const std::string& json, const std::string& key)
    {
        std::string searchKey = "\"" + key + "\": ";
        size_t pos = json.find(searchKey);
        if (pos == std::string::npos) return 0;

        pos += searchKey.length();
        size_t endPos = json.find_first_of(",\n}", pos);
        if (endPos == std::string::npos) return 0;

        std::string value = json.substr(pos, endPos - pos);
        return static_cast<uint32_t>(std::stoul(value));
    }

    static uint64_t ExtractLongValue(const std::string& json, const std::string& key)
    {
        std::string searchKey = "\"" + key + "\": ";
        size_t pos = json.find(searchKey);
        if (pos == std::string::npos) return 0;

        pos += searchKey.length();
        size_t endPos = json.find_first_of(",\n}", pos);
        if (endPos == std::string::npos) return 0;

        std::string value = json.substr(pos, endPos - pos);
        return std::stoull(value);
    }

    static std::string ExtractStringValue(const std::string& json, const std::string& key)
    {
        std::string searchKey = "\"" + key + "\": \"";
        size_t pos = json.find(searchKey);
        if (pos == std::string::npos) return "";

        pos += searchKey.length();
        size_t endPos = json.find("\"", pos);
        if (endPos == std::string::npos) return "";

        return json.substr(pos, endPos - pos);
    }

    static uint32_t ExtractNestedIntValue(const std::string& json, const std::string& parent, const std::string& key)
    {
        std::string parentKey = "\"" + parent + "\": {";
        size_t parentPos = json.find(parentKey);
        if (parentPos == std::string::npos) return 0;

        size_t parentEnd = json.find("}", parentPos);
        if (parentEnd == std::string::npos) return 0;

        std::string parentSection = json.substr(parentPos, parentEnd - parentPos);
        return ExtractIntValue(parentSection, key);
    }
};

// 玩家数据存储管理器
class PlayerDataStorage
{
public:
    PlayerDataStorage(const std::string& dataDirectory = "playerdata")
        : m_dataDirectory(dataDirectory), m_format(SerializationFormat::JSON)
    {
        // 确保数据目录存在
        std::filesystem::create_directories(m_dataDirectory);
    }

    // 设置序列化格式
    void SetSerializationFormat(SerializationFormat format)
    {
        m_format = format;
    }

    // 保存玩家数据
    bool SavePlayer(const Player& player)
    {
        try
        {
            std::string filename = GetPlayerFilename(player.playerId);
            std::string data = PlayerDataSerializer::SerializePlayer(player, m_format);

            std::ofstream file(filename, std::ios::binary);
            if (!file.is_open())
            {
                LOG_ERROR("PLAYER_STORAGE", "Failed to open file for writing: " + filename);
                return false;
            }

            file << data;
            file.close();

            LOG_DEBUG("PLAYER_STORAGE", "Saved player data: " + player.playerName + " to " + filename);
            return true;
        }
        catch (const std::exception& e)
        {
            LOG_ERROR("PLAYER_STORAGE", std::string("Error saving player: ") + e.what());
            return false;
        }
    }

    // 加载玩家数据
    bool LoadPlayer(uint32_t playerId, Player& player)
    {
        try
        {
            std::string filename = GetPlayerFilename(playerId);

            if (!std::filesystem::exists(filename))
            {
                LOG_WARNING("PLAYER_STORAGE", "Player file not found: " + filename);
                return false;
            }

            std::ifstream file(filename, std::ios::binary);
            if (!file.is_open())
            {
                LOG_ERROR("PLAYER_STORAGE", "Failed to open file for reading: " + filename);
                return false;
            }

            std::string data((std::istreambuf_iterator<char>(file)), std::istreambuf_iterator<char>());
            file.close();

            if (PlayerDataSerializer::DeserializePlayer(data, player, m_format))
            {
                LOG_DEBUG("PLAYER_STORAGE", "Loaded player data: " + player.playerName + " from " + filename);
                return true;
            }
            else
            {
                LOG_ERROR("PLAYER_STORAGE", "Failed to deserialize player data from: " + filename);
                return false;
            }
        }
        catch (const std::exception& e)
        {
            LOG_ERROR("PLAYER_STORAGE", std::string("Error loading player: ") + e.what());
            return false;
        }
    }

    // 删除玩家数据
    bool DeletePlayer(uint32_t playerId)
    {
        try
        {
            std::string filename = GetPlayerFilename(playerId);

            if (std::filesystem::exists(filename))
            {
                std::filesystem::remove(filename);
                LOG_INFO("PLAYER_STORAGE", "Deleted player file: " + filename);
                return true;
            }
            else
            {
                LOG_WARNING("PLAYER_STORAGE", "Player file not found for deletion: " + filename);
                return false;
            }
        }
        catch (const std::exception& e)
        {
            LOG_ERROR("PLAYER_STORAGE", std::string("Error deleting player: ") + e.what());
            return false;
        }
    }

    // 检查玩家数据是否存在
    bool PlayerExists(uint32_t playerId)
    {
        std::string filename = GetPlayerFilename(playerId);
        return std::filesystem::exists(filename);
    }

    // 备份玩家数据
    bool BackupPlayer(uint32_t playerId)
    {
        try
        {
            std::string filename = GetPlayerFilename(playerId);
            std::string backupFilename = GetPlayerBackupFilename(playerId);

            if (std::filesystem::exists(filename))
            {
                std::filesystem::copy_file(filename, backupFilename, std::filesystem::copy_options::overwrite_existing);
                LOG_INFO("PLAYER_STORAGE", "Backed up player file: " + filename + " to " + backupFilename);
                return true;
            }
            else
            {
                LOG_WARNING("PLAYER_STORAGE", "Player file not found for backup: " + filename);
                return false;
            }
        }
        catch (const std::exception& e)
        {
            LOG_ERROR("PLAYER_STORAGE", std::string("Error backing up player: ") + e.what());
            return false;
        }
    }

    // 恢复玩家数据
    bool RestorePlayer(uint32_t playerId)
    {
        try
        {
            std::string filename = GetPlayerFilename(playerId);
            std::string backupFilename = GetPlayerBackupFilename(playerId);

            if (std::filesystem::exists(backupFilename))
            {
                std::filesystem::copy_file(backupFilename, filename, std::filesystem::copy_options::overwrite_existing);
                LOG_INFO("PLAYER_STORAGE", "Restored player file: " + backupFilename + " to " + filename);
                return true;
            }
            else
            {
                LOG_WARNING("PLAYER_STORAGE", "Backup file not found for restore: " + backupFilename);
                return false;
            }
        }
        catch (const std::exception& e)
        {
            LOG_ERROR("PLAYER_STORAGE", std::string("Error restoring player: ") + e.what());
            return false;
        }
    }

    // 获取所有玩家ID列表
    std::vector<uint32_t> GetAllPlayerIds()
    {
        std::vector<uint32_t> playerIds;

        try
        {
            for (const auto& entry : std::filesystem::directory_iterator(m_dataDirectory))
            {
                if (entry.is_regular_file())
                {
                    std::string filename = entry.path().filename().string();
                    if (filename.starts_with("player_") && filename.ends_with(GetFileExtension()))
                    {
                        // 提取玩家ID
                        std::string idStr = filename.substr(7); // 移除"player_"前缀
                        idStr = idStr.substr(0, idStr.find('.')); // 移除扩展名

                        try
                        {
                            uint32_t playerId = std::stoul(idStr);
                            playerIds.push_back(playerId);
                        }
                        catch (...)
                        {
                            // 忽略无效的文件名
                        }
                    }
                }
            }
        }
        catch (const std::exception& e)
        {
            LOG_ERROR("PLAYER_STORAGE", std::string("Error scanning player directory: ") + e.what());
        }

        return playerIds;
    }

    // 获取存储统计信息
    struct StorageStatistics
    {
        size_t totalPlayers = 0;
        size_t totalFileSize = 0;
        size_t backupFiles = 0;
        std::string dataDirectory;
    };

    StorageStatistics GetStorageStatistics()
    {
        StorageStatistics stats;
        stats.dataDirectory = m_dataDirectory;

        try
        {
            for (const auto& entry : std::filesystem::directory_iterator(m_dataDirectory))
            {
                if (entry.is_regular_file())
                {
                    std::string filename = entry.path().filename().string();
                    size_t fileSize = std::filesystem::file_size(entry.path());
                    stats.totalFileSize += fileSize;

                    if (filename.starts_with("player_") && filename.ends_with(GetFileExtension()))
                    {
                        stats.totalPlayers++;
                    }
                    else if (filename.starts_with("player_") && filename.ends_with(".backup"))
                    {
                        stats.backupFiles++;
                    }
                }
            }
        }
        catch (const std::exception& e)
        {
            LOG_ERROR("PLAYER_STORAGE", std::string("Error calculating storage statistics: ") + e.what());
        }

        return stats;
    }

private:
    std::string m_dataDirectory;
    SerializationFormat m_format;

    std::string GetPlayerFilename(uint32_t playerId)
    {
        return m_dataDirectory + "/player_" + std::to_string(playerId) + GetFileExtension();
    }

    std::string GetPlayerBackupFilename(uint32_t playerId)
    {
        return m_dataDirectory + "/player_" + std::to_string(playerId) + ".backup";
    }

    std::string GetFileExtension()
    {
        switch (m_format)
        {
        case SerializationFormat::JSON: return ".json";
        case SerializationFormat::XML: return ".xml";
        case SerializationFormat::Binary: return ".dat";
        default: return ".json";
        }
    }
};

// 玩家数据持久化管理器
class PlayerPersistenceManager : public Singleton<PlayerPersistenceManager>
{
public:
    PlayerPersistenceManager() : m_autoSaveEnabled(true), m_autoSaveInterval(std::chrono::minutes(5)) {}

    ~PlayerPersistenceManager()
    {
        Stop();
    }

    // 启动持久化管理器
    bool Start(const std::string& dataDirectory = "playerdata")
    {
        if (m_running) return true;

        m_storage = std::make_unique<PlayerDataStorage>(dataDirectory);
        m_running = true;

        if (m_autoSaveEnabled)
        {
            m_autoSaveThread = std::thread(&PlayerPersistenceManager::AutoSaveLoop, this);
        }

        LOG_INFO("PLAYER_PERSIST", "Player persistence manager started");
        return true;
    }

    // 停止持久化管理器
    void Stop()
    {
        if (!m_running) return;

        m_running = false;
        if (m_autoSaveThread.joinable())
        {
            m_autoSaveThread.join();
        }

        LOG_INFO("PLAYER_PERSIST", "Player persistence manager stopped");
    }

    // 保存玩家数据
    bool SavePlayer(const Player& player)
    {
        if (!m_storage) return false;
        return m_storage->SavePlayer(player);
    }

    // 加载玩家数据
    bool LoadPlayer(uint32_t playerId, Player& player)
    {
        if (!m_storage) return false;
        return m_storage->LoadPlayer(playerId, player);
    }

    // 设置自动保存
    void SetAutoSave(bool enabled, std::chrono::seconds interval = std::chrono::minutes(5))
    {
        m_autoSaveEnabled = enabled;
        m_autoSaveInterval = interval;
        LOG_INFO("PLAYER_PERSIST", "Auto save " + std::string(enabled ? "enabled" : "disabled") +
                ", interval: " + std::to_string(interval.count()) + " seconds");
    }

    // 添加到自动保存队列
    void ScheduleSave(uint32_t playerId)
    {
        std::lock_guard<std::mutex> lock(m_saveQueueMutex);
        m_saveQueue.insert(playerId);
    }

    // 获取存储统计
    PlayerDataStorage::StorageStatistics GetStorageStatistics()
    {
        if (!m_storage) return {};
        return m_storage->GetStorageStatistics();
    }

private:
    std::unique_ptr<PlayerDataStorage> m_storage;
    std::atomic<bool> m_running{false};
    std::thread m_autoSaveThread;

    bool m_autoSaveEnabled;
    std::chrono::seconds m_autoSaveInterval;

    std::mutex m_saveQueueMutex;
    std::unordered_set<uint32_t> m_saveQueue;

    void AutoSaveLoop()
    {
        while (m_running)
        {
            try
            {
                std::this_thread::sleep_for(m_autoSaveInterval);

                if (!m_running) break;

                // 处理保存队列
                std::unordered_set<uint32_t> playersToSave;
                {
                    std::lock_guard<std::mutex> lock(m_saveQueueMutex);
                    playersToSave = std::move(m_saveQueue);
                    m_saveQueue.clear();
                }

                // 保存玩家数据
                for (uint32_t playerId : playersToSave)
                {
                    auto player = PLAYER_MANAGER().GetPlayer(playerId);
                    if (player)
                    {
                        SavePlayer(*player);
                    }
                }

                if (!playersToSave.empty())
                {
                    LOG_DEBUG("PLAYER_PERSIST", "Auto saved " + std::to_string(playersToSave.size()) + " players");
                }
            }
            catch (const std::exception& e)
            {
                LOG_ERROR("PLAYER_PERSIST", std::string("Error in auto save loop: ") + e.what());
            }
        }
    }
};

} // namespace sword2

// 全局玩家持久化管理器访问
#define PLAYER_PERSISTENCE() sword2::PlayerPersistenceManager::getInstance()

// 便捷宏定义
#define START_PLAYER_PERSISTENCE(dataDir) PLAYER_PERSISTENCE().Start(dataDir)
#define STOP_PLAYER_PERSISTENCE() PLAYER_PERSISTENCE().Stop()
#define SAVE_PLAYER(player) PLAYER_PERSISTENCE().SavePlayer(player)
#define LOAD_PLAYER(playerId, player) PLAYER_PERSISTENCE().LoadPlayer(playerId, player)
#define SCHEDULE_PLAYER_SAVE(playerId) PLAYER_PERSISTENCE().ScheduleSave(playerId)

#endif // PLAYER_DATA_PERSISTENCE_H