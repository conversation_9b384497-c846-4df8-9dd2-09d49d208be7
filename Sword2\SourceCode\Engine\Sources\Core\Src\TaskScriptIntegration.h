//---------------------------------------------------------------------------
// Sword2 Task Script Integration (c) 2024
//
// File:	TaskScriptIntegration.h
// Date:	2024.07.02
// Code:	AI Assistant
// Desc:	Integration layer between task system and Lua scripts
//---------------------------------------------------------------------------
#ifndef TASK_SCRIPT_INTEGRATION_H
#define TASK_SCRIPT_INTEGRATION_H

#include "TaskManager.h"
#include "LuaScriptEngine.h"
#include "PlayerManager.h"
#include "ItemManager.h"
#include "UnifiedLoggingSystem.h"
#include <string>
#include <unordered_map>
#include <vector>
#include <functional>

namespace sword2 {

// 任务脚本API扩展
namespace TaskScriptAPI {
    
    // 任务基础API
    int GetTaskName(lua_State* L);
    int GetTaskDescription(lua_State* L);
    int GetTaskType(lua_State* L);
    int GetTaskLevel(lua_State* L);
    int GetTaskState(lua_State* L);
    int GetTaskProgress(lua_State* L);
    int GetTaskTimeLimit(lua_State* L);
    int GetTaskRemainingTime(lua_State* L);
    
    // 任务操作API
    int AcceptTask(lua_State* L);
    int CompleteTask(lua_State* L);
    int SubmitTask(lua_State* L);
    int CancelTask(lua_State* L);
    int SkipTask(lua_State* L);
    int UpdateTaskProgress(lua_State* L);
    int SetTaskVariable(lua_State* L);
    int GetTaskVariable(lua_State* L);
    
    // 任务查询API
    int HasTask(lua_State* L);
    int GetPlayerTasks(lua_State* L);
    int GetAvailableTasks(lua_State* L);
    int GetCompletedTasks(lua_State* L);
    int GetTaskByName(lua_State* L);
    int GetTaskChain(lua_State* L);
    
    // 任务条件API
    int CheckTaskCondition(lua_State* L);
    int CheckAcceptCondition(lua_State* L);
    int CheckCompleteCondition(lua_State* L);
    int AddTaskCondition(lua_State* L);
    int RemoveTaskCondition(lua_State* L);
    
    // 任务实体API
    int GetTaskEntities(lua_State* L);
    int UpdateEntityProgress(lua_State* L);
    int CompleteEntity(lua_State* L);
    int GetEntityProgress(lua_State* L);
    int SetEntityTarget(lua_State* L);
    
    // 任务奖励API
    int GetTaskRewards(lua_State* L);
    int GiveTaskReward(lua_State* L);
    int AddTaskReward(lua_State* L);
    int RemoveTaskReward(lua_State* L);
    int CalculateRewardValue(lua_State* L);
    
    // 任务对话API
    int GetTaskDialogue(lua_State* L);
    int SetTaskDialogue(lua_State* L);
    int ShowTaskDialogue(lua_State* L);
    int GetAcceptText(lua_State* L);
    int GetCompleteText(lua_State* L);
    
    // 任务事件API
    int TriggerTaskEvent(lua_State* L);
    int RegisterTaskEventHandler(lua_State* L);
    int UnregisterTaskEventHandler(lua_State* L);
    int OnTaskAccept(lua_State* L);
    int OnTaskComplete(lua_State* L);
    int OnTaskSubmit(lua_State* L);
    
    // 任务统计API
    int GetTaskStatistics(lua_State* L);
    int GetPlayerTaskCount(lua_State* L);
    int GetTaskCompletionRate(lua_State* L);
    int GetTaskRanking(lua_State* L);
}

// 任务事件处理器
class TaskEventHandler
{
public:
    struct TaskEventData
    {
        TaskEvent event;
        uint32_t instanceId;
        uint32_t playerId;
        uint32_t taskId;
        std::string taskName;
        std::chrono::system_clock::time_point timestamp;
        
        TaskEventData() = default;
        TaskEventData(TaskEvent evt, uint32_t inst, uint32_t player, uint32_t task, const std::string& name)
            : event(evt), instanceId(inst), playerId(player), taskId(task), taskName(name)
        {
            timestamp = std::chrono::system_clock::now();
        }
    };
    
    using EventCallback = std::function<void(const TaskEventData&)>;
    
    // 注册事件处理器
    void RegisterEventHandler(TaskEvent event, const std::string& handlerName, EventCallback callback)
    {
        std::lock_guard<std::mutex> lock(m_handlerMutex);
        m_eventHandlers[event][handlerName] = callback;
        
        LOG_DEBUG("TASK_EVENT", "Registered event handler: " + handlerName + " for event " + std::to_string(static_cast<int>(event)));
    }
    
    // 注销事件处理器
    void UnregisterEventHandler(TaskEvent event, const std::string& handlerName)
    {
        std::lock_guard<std::mutex> lock(m_handlerMutex);
        auto it = m_eventHandlers.find(event);
        if (it != m_eventHandlers.end())
        {
            it->second.erase(handlerName);
            LOG_DEBUG("TASK_EVENT", "Unregistered event handler: " + handlerName);
        }
    }
    
    // 触发事件
    void TriggerEvent(const TaskEventData& eventData)
    {
        std::lock_guard<std::mutex> lock(m_handlerMutex);
        
        auto it = m_eventHandlers.find(eventData.event);
        if (it != m_eventHandlers.end())
        {
            for (const auto& [handlerName, callback] : it->second)
            {
                try
                {
                    callback(eventData);
                }
                catch (const std::exception& e)
                {
                    LOG_ERROR("TASK_EVENT", "Error in event handler " + handlerName + ": " + e.what());
                }
            }
        }
        
        // 记录事件历史
        m_eventHistory.push_back(eventData);
        if (m_eventHistory.size() > 1000) // 限制历史记录数量
        {
            m_eventHistory.erase(m_eventHistory.begin());
        }
    }
    
    // 获取事件历史
    std::vector<TaskEventData> GetEventHistory(uint32_t playerId = 0, TaskEvent event = TaskEvent::Accept) const
    {
        std::lock_guard<std::mutex> lock(m_handlerMutex);
        std::vector<TaskEventData> history;
        
        for (const auto& eventData : m_eventHistory)
        {
            if ((playerId == 0 || eventData.playerId == playerId) &&
                (event == TaskEvent::Accept || eventData.event == event))
            {
                history.push_back(eventData);
            }
        }
        
        return history;
    }

private:
    mutable std::mutex m_handlerMutex;
    std::unordered_map<TaskEvent, std::unordered_map<std::string, EventCallback>> m_eventHandlers;
    std::vector<TaskEventData> m_eventHistory;
};

// 任务条件检查器
class TaskConditionChecker
{
public:
    // 检查任务条件
    static bool CheckCondition(const TaskCondition& condition, const Player& player)
    {
        switch (condition.type)
        {
        case TaskConditionType::Level:
            return player.level >= condition.value1;
            
        case TaskConditionType::TaskComplete:
            {
                auto taskTemplate = TASK_MANAGER().GetTaskTemplate(condition.stringValue);
                if (taskTemplate)
                {
                    auto instance = TASK_MANAGER().GetPlayerTask(player.playerId, taskTemplate->taskId);
                    return instance && instance->state == TaskState::Submitted;
                }
                return false;
            }
            
        case TaskConditionType::ItemHave:
            {
                // 检查玩家是否拥有指定物品
                auto inventory = GET_PLAYER_INVENTORY(player.playerId);
                if (inventory)
                {
                    auto items = inventory->GetAllItems(InventoryType::Equipment);
                    uint32_t count = 0;
                    
                    for (uint32_t instanceId : items)
                    {
                        auto instance = GET_ITEM_INSTANCE(instanceId);
                        if (instance && instance->templateId == condition.value1)
                        {
                            count += instance->stackCount;
                        }
                    }
                    
                    return count >= condition.value2;
                }
                return false;
            }
            
        case TaskConditionType::Money:
            return player.money >= condition.value1;
            
        case TaskConditionType::Reputation:
            return player.reputation >= condition.value1;
            
        case TaskConditionType::Series:
            return static_cast<int>(player.series) == condition.value1;
            
        case TaskConditionType::Variable:
            {
                // 检查任务变量
                auto instance = TASK_MANAGER().GetPlayerTask(player.playerId, condition.value1);
                if (instance)
                {
                    return instance->GetVariable(condition.stringValue) >= condition.value2;
                }
                return false;
            }
            
        default:
            return true;
        }
    }
    
    // 批量检查条件
    static bool CheckConditions(const std::vector<TaskCondition>& conditions, const Player& player)
    {
        for (const auto& condition : conditions)
        {
            if (!CheckCondition(condition, player))
                return false;
        }
        return true;
    }
};

// 任务奖励发放器
class TaskRewardDispenser
{
public:
    // 发放任务奖励
    static TaskOperationResult GiveRewards(uint32_t playerId, const std::vector<TaskAward>& awards)
    {
        auto player = GET_ONLINE_PLAYER(playerId);
        if (!player)
            return TaskOperationResult::Failed;
        
        // 检查背包空间
        auto inventory = GET_PLAYER_INVENTORY(playerId);
        if (!inventory)
            return TaskOperationResult::Failed;
        
        uint32_t itemCount = 0;
        for (const auto& award : awards)
        {
            if (award.type == TaskAwardType::Item)
                itemCount++;
        }
        
        if (itemCount > 0 && inventory->GetFreeSlotCount(InventoryType::Equipment) < itemCount)
        {
            return TaskOperationResult::InventoryFull;
        }
        
        // 发放奖励
        for (const auto& award : awards)
        {
            switch (award.type)
            {
            case TaskAwardType::Money:
                player->money += award.moneyNum;
                LOG_INFO("TASK_REWARD", "Player " + std::to_string(playerId) + " received " + std::to_string(award.moneyNum) + " money");
                break;
                
            case TaskAwardType::Experience:
                player->experience += award.expNum;
                LOG_INFO("TASK_REWARD", "Player " + std::to_string(playerId) + " received " + std::to_string(award.expNum) + " experience");
                break;
                
            case TaskAwardType::Reputation:
                player->reputation += award.reputeNum;
                LOG_INFO("TASK_REWARD", "Player " + std::to_string(playerId) + " received " + std::to_string(award.reputeNum) + " reputation");
                break;
                
            case TaskAwardType::Item:
                {
                    uint32_t templateId = award.genre * 10000 + award.detail * 100 + award.particular;
                    bool success = GIVE_ITEM_TO_PLAYER(playerId, templateId, award.goodsNum, InventoryType::Equipment);
                    if (success)
                    {
                        LOG_INFO("TASK_REWARD", "Player " + std::to_string(playerId) + " received item " + std::to_string(templateId) + " x" + std::to_string(award.goodsNum));
                    }
                }
                break;
                
            case TaskAwardType::TaskStart:
                {
                    auto taskTemplate = TASK_MANAGER().GetTaskTemplate(award.taskString);
                    if (taskTemplate)
                    {
                        ACCEPT_TASK(playerId, taskTemplate->taskId);
                        LOG_INFO("TASK_REWARD", "Player " + std::to_string(playerId) + " started task " + award.taskString);
                    }
                }
                break;
                
            default:
                break;
            }
        }
        
        return TaskOperationResult::Success;
    }
    
    // 计算奖励价值
    static uint32_t CalculateRewardValue(const std::vector<TaskAward>& awards)
    {
        uint32_t totalValue = 0;
        
        for (const auto& award : awards)
        {
            switch (award.type)
            {
            case TaskAwardType::Money:
                totalValue += award.moneyNum;
                break;
            case TaskAwardType::Experience:
                totalValue += award.expNum / 10; // 经验转换为价值
                break;
            case TaskAwardType::Reputation:
                totalValue += award.reputeNum * 5; // 声望转换为价值
                break;
            case TaskAwardType::Item:
                totalValue += 100 * award.goodsNum; // 物品估值
                break;
            default:
                break;
            }
        }
        
        return totalValue;
    }
};

// 任务脚本集成管理器
class TaskScriptIntegration : public Singleton<TaskScriptIntegration>
{
public:
    TaskScriptIntegration() = default;
    ~TaskScriptIntegration() = default;
    
    // 初始化任务脚本集成
    bool Initialize()
    {
        // 注册任务相关的Lua API函数
        RegisterTaskScriptAPI();
        
        // 注册默认事件处理器
        RegisterDefaultEventHandlers();
        
        LOG_INFO("TASK_SCRIPT", "Task script integration initialized");
        return true;
    }
    
    // 加载任务脚本
    bool LoadTaskScript(const std::string& scriptPath)
    {
        ScriptResult result = LOAD_SCRIPT(scriptPath, ScriptType::Task);
        if (result == ScriptResult::Success)
        {
            LOG_INFO("TASK_SCRIPT", "Loaded task script: " + scriptPath);
            return true;
        }
        else
        {
            LOG_ERROR("TASK_SCRIPT", "Failed to load task script: " + scriptPath);
            return false;
        }
    }
    
    // 执行任务脚本函数
    bool ExecuteTaskFunction(const std::string& functionName, uint32_t taskId, uint32_t playerId)
    {
        std::vector<LuaValue> args;
        args.push_back(LuaValue(static_cast<double>(taskId)));
        args.push_back(LuaValue(static_cast<double>(playerId)));
        
        ScriptResult result = EXECUTE_FUNCTION(functionName, args);
        return result == ScriptResult::Success;
    }
    
    // 触发任务事件
    void TriggerTaskEvent(TaskEvent event, uint32_t instanceId, uint32_t playerId)
    {
        auto instance = TASK_MANAGER().GetTaskInstance(instanceId);
        if (!instance) return;
        
        auto taskTemplate = TASK_MANAGER().GetTaskTemplate(instance->templateId);
        if (!taskTemplate) return;
        
        TaskEventHandler::TaskEventData eventData(event, instanceId, playerId, instance->templateId, taskTemplate->taskName);
        m_eventHandler.TriggerEvent(eventData);
        
        // 执行脚本事件处理
        std::string eventName = GetEventName(event);
        ExecuteTaskFunction("on_task_" + eventName, instance->templateId, playerId);
    }
    
    // 检查任务条件
    bool CheckTaskCondition(const TaskCondition& condition, uint32_t playerId)
    {
        auto player = GET_ONLINE_PLAYER(playerId);
        if (!player) return false;
        
        return TaskConditionChecker::CheckCondition(condition, *player);
    }
    
    // 发放任务奖励
    TaskOperationResult GiveTaskRewards(uint32_t playerId, const std::vector<TaskAward>& awards)
    {
        return TaskRewardDispenser::GiveRewards(playerId, awards);
    }
    
    // 获取事件处理器
    TaskEventHandler& GetEventHandler() { return m_eventHandler; }

private:
    TaskEventHandler m_eventHandler;
    
    void RegisterTaskScriptAPI()
    {
        // 注册任务基础API
        REGISTER_LUA_FUNCTION("GetTaskName", TaskScriptAPI::GetTaskName);
        REGISTER_LUA_FUNCTION("GetTaskDescription", TaskScriptAPI::GetTaskDescription);
        REGISTER_LUA_FUNCTION("GetTaskType", TaskScriptAPI::GetTaskType);
        REGISTER_LUA_FUNCTION("GetTaskLevel", TaskScriptAPI::GetTaskLevel);
        REGISTER_LUA_FUNCTION("GetTaskState", TaskScriptAPI::GetTaskState);
        REGISTER_LUA_FUNCTION("GetTaskProgress", TaskScriptAPI::GetTaskProgress);
        
        // 注册任务操作API
        REGISTER_LUA_FUNCTION("AcceptTask", TaskScriptAPI::AcceptTask);
        REGISTER_LUA_FUNCTION("CompleteTask", TaskScriptAPI::CompleteTask);
        REGISTER_LUA_FUNCTION("SubmitTask", TaskScriptAPI::SubmitTask);
        REGISTER_LUA_FUNCTION("CancelTask", TaskScriptAPI::CancelTask);
        REGISTER_LUA_FUNCTION("SkipTask", TaskScriptAPI::SkipTask);
        REGISTER_LUA_FUNCTION("UpdateTaskProgress", TaskScriptAPI::UpdateTaskProgress);
        
        // 注册任务查询API
        REGISTER_LUA_FUNCTION("HasTask", TaskScriptAPI::HasTask);
        REGISTER_LUA_FUNCTION("GetPlayerTasks", TaskScriptAPI::GetPlayerTasks);
        REGISTER_LUA_FUNCTION("GetAvailableTasks", TaskScriptAPI::GetAvailableTasks);
        REGISTER_LUA_FUNCTION("GetCompletedTasks", TaskScriptAPI::GetCompletedTasks);
        
        // 注册任务条件API
        REGISTER_LUA_FUNCTION("CheckTaskCondition", TaskScriptAPI::CheckTaskCondition);
        REGISTER_LUA_FUNCTION("CheckAcceptCondition", TaskScriptAPI::CheckAcceptCondition);
        REGISTER_LUA_FUNCTION("CheckCompleteCondition", TaskScriptAPI::CheckCompleteCondition);
        
        // 注册任务实体API
        REGISTER_LUA_FUNCTION("GetTaskEntities", TaskScriptAPI::GetTaskEntities);
        REGISTER_LUA_FUNCTION("UpdateEntityProgress", TaskScriptAPI::UpdateEntityProgress);
        REGISTER_LUA_FUNCTION("GetEntityProgress", TaskScriptAPI::GetEntityProgress);
        
        // 注册任务奖励API
        REGISTER_LUA_FUNCTION("GetTaskRewards", TaskScriptAPI::GetTaskRewards);
        REGISTER_LUA_FUNCTION("GiveTaskReward", TaskScriptAPI::GiveTaskReward);
        REGISTER_LUA_FUNCTION("CalculateRewardValue", TaskScriptAPI::CalculateRewardValue);
        
        // 注册任务对话API
        REGISTER_LUA_FUNCTION("GetTaskDialogue", TaskScriptAPI::GetTaskDialogue);
        REGISTER_LUA_FUNCTION("ShowTaskDialogue", TaskScriptAPI::ShowTaskDialogue);
        REGISTER_LUA_FUNCTION("GetAcceptText", TaskScriptAPI::GetAcceptText);
        REGISTER_LUA_FUNCTION("GetCompleteText", TaskScriptAPI::GetCompleteText);
        
        // 注册任务事件API
        REGISTER_LUA_FUNCTION("TriggerTaskEvent", TaskScriptAPI::TriggerTaskEvent);
        REGISTER_LUA_FUNCTION("RegisterTaskEventHandler", TaskScriptAPI::RegisterTaskEventHandler);
        REGISTER_LUA_FUNCTION("OnTaskAccept", TaskScriptAPI::OnTaskAccept);
        REGISTER_LUA_FUNCTION("OnTaskComplete", TaskScriptAPI::OnTaskComplete);
        
        // 注册任务统计API
        REGISTER_LUA_FUNCTION("GetTaskStatistics", TaskScriptAPI::GetTaskStatistics);
        REGISTER_LUA_FUNCTION("GetPlayerTaskCount", TaskScriptAPI::GetPlayerTaskCount);
        REGISTER_LUA_FUNCTION("GetTaskCompletionRate", TaskScriptAPI::GetTaskCompletionRate);
        
        LOG_INFO("TASK_SCRIPT", "Registered task script API functions");
    }
    
    void RegisterDefaultEventHandlers()
    {
        // 注册默认的任务事件处理器
        m_eventHandler.RegisterEventHandler(TaskEvent::Accept, "default_accept", 
            [](const TaskEventHandler::TaskEventData& data) {
                LOG_INFO("TASK_EVENT", "Player " + std::to_string(data.playerId) + " accepted task: " + data.taskName);
            });
        
        m_eventHandler.RegisterEventHandler(TaskEvent::Complete, "default_complete", 
            [](const TaskEventHandler::TaskEventData& data) {
                LOG_INFO("TASK_EVENT", "Player " + std::to_string(data.playerId) + " completed task: " + data.taskName);
            });
        
        m_eventHandler.RegisterEventHandler(TaskEvent::Submit, "default_submit", 
            [](const TaskEventHandler::TaskEventData& data) {
                LOG_INFO("TASK_EVENT", "Player " + std::to_string(data.playerId) + " submitted task: " + data.taskName);
            });
        
        m_eventHandler.RegisterEventHandler(TaskEvent::Cancel, "default_cancel", 
            [](const TaskEventHandler::TaskEventData& data) {
                LOG_INFO("TASK_EVENT", "Player " + std::to_string(data.playerId) + " cancelled task: " + data.taskName);
            });
        
        m_eventHandler.RegisterEventHandler(TaskEvent::Expire, "default_expire", 
            [](const TaskEventHandler::TaskEventData& data) {
                LOG_WARNING("TASK_EVENT", "Task expired for player " + std::to_string(data.playerId) + ": " + data.taskName);
            });
    }
    
    std::string GetEventName(TaskEvent event)
    {
        switch (event)
        {
        case TaskEvent::Accept: return "accept";
        case TaskEvent::Complete: return "complete";
        case TaskEvent::Submit: return "submit";
        case TaskEvent::Cancel: return "cancel";
        case TaskEvent::Expire: return "expire";
        case TaskEvent::Progress: return "progress";
        case TaskEvent::Skip: return "skip";
        default: return "unknown";
        }
    }
};

} // namespace sword2

// 全局任务脚本集成访问
#define TASK_SCRIPT_INTEGRATION() sword2::TaskScriptIntegration::getInstance()

// 便捷宏定义
#define INIT_TASK_SCRIPT_INTEGRATION() TASK_SCRIPT_INTEGRATION().Initialize()
#define LOAD_TASK_SCRIPT(scriptPath) TASK_SCRIPT_INTEGRATION().LoadTaskScript(scriptPath)
#define EXECUTE_TASK_FUNCTION(functionName, taskId, playerId) TASK_SCRIPT_INTEGRATION().ExecuteTaskFunction(functionName, taskId, playerId)

#define TRIGGER_TASK_EVENT(event, instanceId, playerId) TASK_SCRIPT_INTEGRATION().TriggerTaskEvent(event, instanceId, playerId)
#define CHECK_TASK_CONDITION(condition, playerId) TASK_SCRIPT_INTEGRATION().CheckTaskCondition(condition, playerId)
#define GIVE_TASK_REWARDS(playerId, awards) TASK_SCRIPT_INTEGRATION().GiveTaskRewards(playerId, awards)

#define TASK_EVENT_HANDLER() TASK_SCRIPT_INTEGRATION().GetEventHandler()

#endif // TASK_SCRIPT_INTEGRATION_H
