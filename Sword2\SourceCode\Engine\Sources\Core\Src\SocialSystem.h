//---------------------------------------------------------------------------
// Sword2 Social System (c) 2024
//
// File:	SocialSystem.h
// Date:	2024.07.02
// Code:	AI Assistant
// Desc:	Comprehensive social system compatible with existing game mechanics
//---------------------------------------------------------------------------
#ifndef SOCIAL_SYSTEM_H
#define SOCIAL_SYSTEM_H

#include "ModernCpp.h"
#include "UnifiedLoggingSystem.h"
#include "PlayerSystem.h"
#include <string>
#include <unordered_map>
#include <vector>
#include <memory>
#include <chrono>
#include <atomic>
#include <mutex>
#include <functional>

namespace sword2 {

// 好友关系类型
enum class FriendshipType : uint8_t
{
    None = 0,           // 无关系
    Friend,             // 好友
    BestFriend,         // 挚友
    Blacklist,          // 黑名单
    Stranger,           // 陌生人
    Enemy,              // 仇人
    Mentor,             // 师父
    Apprentice,         // 徒弟
    Spouse,             // 配偶
    Sibling             // 结拜兄弟
};

// 好友状态
enum class FriendStatus : uint8_t
{
    Offline = 0,        // 离线
    Online,             // 在线
    Away,               // 离开
    Busy,               // 忙碌
    DoNotDisturb,       // 勿扰
    Invisible,          // 隐身
    InGame,             // 游戏中
    InCombat,           // 战斗中
    Trading,            // 交易中
    Chatting            // 聊天中
};

// 聊天频道类型
enum class ChatChannelType : uint8_t
{
    World = 0,          // 世界频道
    Region,             // 区域频道
    Team,               // 队伍频道
    Guild,              // 帮会频道
    Private,            // 私聊
    System,             // 系统频道
    Trade,              // 交易频道
    Newbie,             // 新手频道
    Custom,             // 自定义频道
    Broadcast           // 广播频道
};

// 消息类型
enum class MessageType : uint8_t
{
    Text = 0,           // 文本消息
    Emoji,              // 表情
    Item,               // 物品链接
    Coordinate,         // 坐标
    Voice,              // 语音
    Image,              // 图片
    System,             // 系统消息
    Achievement,        // 成就
    Skill,              // 技能链接
    Quest               // 任务链接
};

// 邮件类型
enum class MailType : uint8_t
{
    Personal = 0,       // 个人邮件
    System,             // 系统邮件
    Guild,              // 帮会邮件
    Auction,            // 拍卖邮件
    Trade,              // 交易邮件
    Achievement,        // 成就邮件
    Event,              // 活动邮件
    Notification        // 通知邮件
};

// 邮件状态
enum class MailStatus : uint8_t
{
    Unread = 0,         // 未读
    Read,               // 已读
    Replied,            // 已回复
    Forwarded,          // 已转发
    Deleted,            // 已删除
    Archived            // 已归档
};

// 好友信息
struct FriendInfo
{
    uint32_t playerId = 0;          // 玩家ID
    std::string playerName;         // 玩家名称
    std::string nickname;           // 昵称
    std::string groupName;          // 分组名称
    FriendshipType relationship = FriendshipType::Friend;
    FriendStatus status = FriendStatus::Offline;
    
    uint32_t level = 1;             // 等级
    PlayerSeries series = PlayerSeries::None; // 门派
    uint32_t mapId = 0;             // 地图ID
    std::string location;           // 位置描述
    
    std::chrono::system_clock::time_point addTime;     // 添加时间
    std::chrono::system_clock::time_point lastOnline;  // 最后在线时间
    std::chrono::system_clock::time_point lastChat;    // 最后聊天时间
    
    uint32_t intimacy = 0;          // 亲密度
    uint32_t chatCount = 0;         // 聊天次数
    uint32_t giftCount = 0;         // 赠送次数
    
    bool isOnline = false;          // 是否在线
    bool canSeeLocation = true;     // 是否可见位置
    bool canReceiveMessage = true;  // 是否接收消息
    bool isBlocked = false;         // 是否被屏蔽
    
    FriendInfo() = default;
    FriendInfo(uint32_t id, const std::string& name, FriendshipType type = FriendshipType::Friend)
        : playerId(id), playerName(name), relationship(type)
    {
        addTime = std::chrono::system_clock::now();
        lastOnline = addTime;
        lastChat = addTime;
    }
    
    // 更新在线状态
    void UpdateOnlineStatus(bool online, FriendStatus newStatus = FriendStatus::Online)
    {
        isOnline = online;
        status = online ? newStatus : FriendStatus::Offline;
        if (online)
        {
            lastOnline = std::chrono::system_clock::now();
        }
    }
    
    // 增加亲密度
    void AddIntimacy(uint32_t value)
    {
        intimacy += value;
        if (intimacy > 10000) intimacy = 10000; // 最大亲密度
    }
    
    // 获取关系描述
    std::string GetRelationshipDescription() const
    {
        switch (relationship)
        {
        case FriendshipType::Friend: return "好友";
        case FriendshipType::BestFriend: return "挚友";
        case FriendshipType::Blacklist: return "黑名单";
        case FriendshipType::Enemy: return "仇人";
        case FriendshipType::Mentor: return "师父";
        case FriendshipType::Apprentice: return "徒弟";
        case FriendshipType::Spouse: return "配偶";
        case FriendshipType::Sibling: return "结拜兄弟";
        default: return "陌生人";
        }
    }
    
    // 获取状态描述
    std::string GetStatusDescription() const
    {
        switch (status)
        {
        case FriendStatus::Online: return "在线";
        case FriendStatus::Away: return "离开";
        case FriendStatus::Busy: return "忙碌";
        case FriendStatus::DoNotDisturb: return "勿扰";
        case FriendStatus::Invisible: return "隐身";
        case FriendStatus::InGame: return "游戏中";
        case FriendStatus::InCombat: return "战斗中";
        case FriendStatus::Trading: return "交易中";
        case FriendStatus::Chatting: return "聊天中";
        default: return "离线";
        }
    }
};

// 聊天消息
struct ChatMessage
{
    uint32_t messageId = 0;         // 消息ID
    uint32_t senderId = 0;          // 发送者ID
    std::string senderName;         // 发送者名称
    uint32_t receiverId = 0;        // 接收者ID（私聊时使用）
    std::string receiverName;       // 接收者名称
    
    ChatChannelType channel = ChatChannelType::World;
    MessageType type = MessageType::Text;
    std::string content;            // 消息内容
    std::string extraData;          // 额外数据（物品链接、坐标等）
    
    std::chrono::system_clock::time_point timestamp;
    uint32_t mapId = 0;             // 发送地图
    int32_t x = 0, y = 0;           // 发送坐标
    
    bool isRead = false;            // 是否已读
    bool isFiltered = false;        // 是否被过滤
    bool isSystemMessage = false;   // 是否系统消息
    
    ChatMessage() = default;
    ChatMessage(uint32_t sender, const std::string& senderName, const std::string& msg, 
               ChatChannelType ch = ChatChannelType::World, MessageType msgType = MessageType::Text)
        : senderId(sender), senderName(senderName), content(msg), channel(ch), type(msgType)
    {
        timestamp = std::chrono::system_clock::now();
    }
    
    // 获取频道描述
    std::string GetChannelDescription() const
    {
        switch (channel)
        {
        case ChatChannelType::World: return "世界";
        case ChatChannelType::Region: return "区域";
        case ChatChannelType::Team: return "队伍";
        case ChatChannelType::Guild: return "帮会";
        case ChatChannelType::Private: return "私聊";
        case ChatChannelType::System: return "系统";
        case ChatChannelType::Trade: return "交易";
        case ChatChannelType::Newbie: return "新手";
        case ChatChannelType::Custom: return "自定义";
        case ChatChannelType::Broadcast: return "广播";
        default: return "未知";
        }
    }
    
    // 获取消息类型描述
    std::string GetTypeDescription() const
    {
        switch (type)
        {
        case MessageType::Text: return "文本";
        case MessageType::Emoji: return "表情";
        case MessageType::Item: return "物品";
        case MessageType::Coordinate: return "坐标";
        case MessageType::Voice: return "语音";
        case MessageType::Image: return "图片";
        case MessageType::System: return "系统";
        case MessageType::Achievement: return "成就";
        case MessageType::Skill: return "技能";
        case MessageType::Quest: return "任务";
        default: return "未知";
        }
    }
};

// 邮件附件
struct MailAttachment
{
    uint32_t attachmentId = 0;      // 附件ID
    std::string type;               // 附件类型
    std::string name;               // 附件名称
    std::string data;               // 附件数据
    uint32_t itemId = 0;            // 物品ID（如果是物品）
    uint32_t quantity = 0;          // 数量
    uint32_t money = 0;             // 金钱
    
    bool isCollected = false;       // 是否已收取
    
    MailAttachment() = default;
    MailAttachment(const std::string& attachType, const std::string& attachName, const std::string& attachData)
        : type(attachType), name(attachName), data(attachData) {}
};

// 邮件信息
struct MailInfo
{
    uint32_t mailId = 0;            // 邮件ID
    uint32_t senderId = 0;          // 发送者ID
    std::string senderName;         // 发送者名称
    uint32_t receiverId = 0;        // 接收者ID
    std::string receiverName;       // 接收者名称
    
    MailType type = MailType::Personal;
    MailStatus status = MailStatus::Unread;
    std::string subject;            // 主题
    std::string content;            // 内容
    
    std::vector<MailAttachment> attachments; // 附件列表
    
    std::chrono::system_clock::time_point sendTime;    // 发送时间
    std::chrono::system_clock::time_point readTime;    // 阅读时间
    std::chrono::system_clock::time_point expireTime;  // 过期时间
    
    bool hasAttachment = false;     // 是否有附件
    bool isImportant = false;       // 是否重要
    bool isDeleted = false;         // 是否已删除
    
    MailInfo() = default;
    MailInfo(uint32_t sender, const std::string& senderName, uint32_t receiver, 
            const std::string& receiverName, const std::string& subj, const std::string& cont)
        : senderId(sender), senderName(senderName), receiverId(receiver), 
          receiverName(receiverName), subject(subj), content(cont)
    {
        sendTime = std::chrono::system_clock::now();
        expireTime = sendTime + std::chrono::hours(168); // 7天过期
    }
    
    // 添加附件
    void AddAttachment(const MailAttachment& attachment)
    {
        attachments.push_back(attachment);
        hasAttachment = true;
    }
    
    // 标记为已读
    void MarkAsRead()
    {
        if (status == MailStatus::Unread)
        {
            status = MailStatus::Read;
            readTime = std::chrono::system_clock::now();
        }
    }
    
    // 检查是否过期
    bool IsExpired() const
    {
        return std::chrono::system_clock::now() > expireTime;
    }
    
    // 获取类型描述
    std::string GetTypeDescription() const
    {
        switch (type)
        {
        case MailType::Personal: return "个人邮件";
        case MailType::System: return "系统邮件";
        case MailType::Guild: return "帮会邮件";
        case MailType::Auction: return "拍卖邮件";
        case MailType::Trade: return "交易邮件";
        case MailType::Achievement: return "成就邮件";
        case MailType::Event: return "活动邮件";
        case MailType::Notification: return "通知邮件";
        default: return "未知邮件";
        }
    }
    
    // 获取状态描述
    std::string GetStatusDescription() const
    {
        switch (status)
        {
        case MailStatus::Unread: return "未读";
        case MailStatus::Read: return "已读";
        case MailStatus::Replied: return "已回复";
        case MailStatus::Forwarded: return "已转发";
        case MailStatus::Deleted: return "已删除";
        case MailStatus::Archived: return "已归档";
        default: return "未知";
        }
    }
};

// 聊天频道
struct ChatChannel
{
    uint32_t channelId = 0;         // 频道ID
    std::string channelName;        // 频道名称
    std::string description;        // 频道描述
    ChatChannelType type = ChatChannelType::Custom;
    
    uint32_t ownerId = 0;           // 频道所有者
    std::string ownerName;          // 所有者名称
    uint32_t maxMembers = 100;      // 最大成员数
    
    std::vector<uint32_t> members;  // 成员列表
    std::vector<uint32_t> moderators; // 管理员列表
    std::vector<uint32_t> bannedUsers; // 禁言用户
    
    bool isPublic = true;           // 是否公开
    bool requirePassword = false;   // 是否需要密码
    std::string password;           // 频道密码
    bool allowGuests = true;        // 是否允许访客
    
    std::chrono::system_clock::time_point createTime;
    std::chrono::system_clock::time_point lastActivity;
    
    ChatChannel() = default;
    ChatChannel(const std::string& name, uint32_t owner, const std::string& ownerName)
        : channelName(name), ownerId(owner), ownerName(ownerName)
    {
        createTime = std::chrono::system_clock::now();
        lastActivity = createTime;
        members.push_back(owner);
    }
    
    // 添加成员
    bool AddMember(uint32_t playerId)
    {
        if (members.size() >= maxMembers) return false;
        
        auto it = std::find(members.begin(), members.end(), playerId);
        if (it == members.end())
        {
            members.push_back(playerId);
            lastActivity = std::chrono::system_clock::now();
            return true;
        }
        return false;
    }
    
    // 移除成员
    bool RemoveMember(uint32_t playerId)
    {
        auto it = std::find(members.begin(), members.end(), playerId);
        if (it != members.end())
        {
            members.erase(it);
            
            // 同时从管理员列表移除
            auto modIt = std::find(moderators.begin(), moderators.end(), playerId);
            if (modIt != moderators.end())
            {
                moderators.erase(modIt);
            }
            
            lastActivity = std::chrono::system_clock::now();
            return true;
        }
        return false;
    }
    
    // 检查是否为成员
    bool IsMember(uint32_t playerId) const
    {
        return std::find(members.begin(), members.end(), playerId) != members.end();
    }
    
    // 检查是否为管理员
    bool IsModerator(uint32_t playerId) const
    {
        return playerId == ownerId || 
               std::find(moderators.begin(), moderators.end(), playerId) != moderators.end();
    }
    
    // 检查是否被禁言
    bool IsBanned(uint32_t playerId) const
    {
        return std::find(bannedUsers.begin(), bannedUsers.end(), playerId) != bannedUsers.end();
    }
};

// 社交操作结果
enum class SocialResult : uint8_t
{
    Success = 0,        // 成功
    Failed,             // 失败
    NotFound,           // 未找到
    AlreadyExists,      // 已存在
    PermissionDenied,   // 权限不足
    Blocked,            // 被屏蔽
    ChannelFull,        // 频道已满
    InvalidPassword,    // 密码错误
    SelfOperation,      // 自己操作自己
    Offline,            // 离线
    Busy,               // 忙碌
    MessageTooLong,     // 消息过长
    SpamDetected,       // 垃圾信息
    Banned,             // 被禁言
    MailboxFull,        // 邮箱已满
    AttachmentError     // 附件错误
};

} // namespace sword2

#endif // SOCIAL_SYSTEM_H
