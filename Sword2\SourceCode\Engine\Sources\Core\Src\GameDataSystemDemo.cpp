//---------------------------------------------------------------------------
// Sword2 Game Data System Demo (c) 2024
//
// File:	GameDataSystemDemo.cpp
// Date:	2024.07.02
// Code:	AI Assistant
// Desc:	Demonstration and testing of the game data system
//---------------------------------------------------------------------------

#include "KCore.h"
#include "GameDataSystem.h"
#include "WeaponDataParser.h"
#include "MapDataParser.h"
#include "UnifiedLoggingSystem.h"

namespace sword2 {

// 游戏数据系统演示类
class GameDataSystemDemo
{
public:
    GameDataSystemDemo()
    {
        m_initialized = false;
    }
    
    bool Initialize()
    {
        if (m_initialized) return true;
        
        printf("[GAME_DATA] Initializing game data system demo...\n");
        
        // 初始化日志系统
        LOGGER().Initialize();
        LOGGER().AddConsoleAppender(LogLevel::Debug);
        
        // 注册专用解析器
        RegisterCustomParsers();
        
        m_initialized = true;
        LOG_INFO("GAME_DATA", "Game data system demo initialized");
        
        return true;
    }
    
    void RunDemo()
    {
        if (!m_initialized)
        {
            printf("[GAME_DATA] System not initialized\n");
            return;
        }
        
        LOG_INFO("GAME_DATA", "Starting game data system demonstration...");
        
        // 演示各个数据加载功能
        DemoWeaponDataLoading();
        DemoMapDataLoading();
        DemoDataQuerying();
        DemoDataStatistics();
        
        LOG_INFO("GAME_DATA", "Game data system demonstration completed");
    }

private:
    bool m_initialized;
    
    void RegisterCustomParsers()
    {
        LOG_INFO("GAME_DATA", "Registering custom data parsers...");
        
        // 注册武器数据解析器
        GAME_DATA_MANAGER().RegisterParser(std::make_unique<WeaponDataParser>());
        
        // 注册地图数据解析器
        GAME_DATA_MANAGER().RegisterParser(std::make_unique<MapDataParser>());
        
        LOG_INFO("GAME_DATA", "Custom parsers registered successfully");
    }
    
    void DemoWeaponDataLoading()
    {
        LOG_INFO("GAME_DATA", "=== Weapon Data Loading Demo ===");
        
        // 加载武器数据
        std::string weaponFile = "c:\\Sword2Code/Sword2\\SourceCode\\Server/settings/item/weapon.txt";
        
        if (LOAD_GAME_DATA(weaponFile, GameDataType::Weapon))
        {
            const auto& weapons = GET_GAME_DATA(GameDataType::Weapon);
            LOG_INFO("GAME_DATA", "Successfully loaded " + std::to_string(weapons.size()) + " weapons");
            
            // 显示前几个武器的信息
            size_t displayCount = std::min(weapons.size(), size_t(5));
            for (size_t i = 0; i < displayCount; ++i)
            {
                const auto& weapon = weapons[i];
                LOG_INFO("WEAPON", "Weapon: " + weapon.name + 
                        " (ID: " + std::to_string(weapon.id) + 
                        ", Type: " + weapon.GetProperty<std::string>("weapon_type", "Unknown") +
                        ", Damage: " + weapon.GetProperty<std::string>("min_damage", "0") + 
                        "-" + weapon.GetProperty<std::string>("max_damage", "0") + ")");
            }
        }
        else
        {
            LOG_ERROR("GAME_DATA", "Failed to load weapon data from: " + weaponFile);
        }
    }
    
    void DemoMapDataLoading()
    {
        LOG_INFO("GAME_DATA", "=== Map Data Loading Demo ===");
        
        // 加载地图数据
        std::string mapFile = "c:\\Sword2Code/Sword2\\SourceCode\\Server/maps/worldset_1.ini";
        
        if (LOAD_GAME_DATA(mapFile, GameDataType::Map))
        {
            const auto& maps = GET_GAME_DATA(GameDataType::Map);
            LOG_INFO("GAME_DATA", "Successfully loaded " + std::to_string(maps.size()) + " maps");
            
            // 显示地图统计信息
            std::unordered_map<std::string, int> mapTypeCount;
            for (const auto& map : maps)
            {
                std::string mapType = map.GetProperty<std::string>("map_type", "0");
                mapTypeCount[mapType]++;
            }
            
            LOG_INFO("MAP", "Map distribution by type:");
            for (const auto& [type, count] : mapTypeCount)
            {
                LOG_INFO("MAP", "  Type " + type + ": " + std::to_string(count) + " maps");
            }
            
            // 显示前几个地图的信息
            size_t displayCount = std::min(maps.size(), size_t(10));
            for (size_t i = 0; i < displayCount; ++i)
            {
                const auto& map = maps[i];
                LOG_INFO("MAP", "Map: " + map.name + 
                        " (ID: " + std::to_string(map.id) + 
                        ", Type: " + map.GetProperty<std::string>("map_type", "Unknown") +
                        ", Level: " + map.GetProperty<std::string>("recommend_level", "1") + ")");
            }
        }
        else
        {
            LOG_ERROR("GAME_DATA", "Failed to load map data from: " + mapFile);
        }
    }
    
    void DemoDataQuerying()
    {
        LOG_INFO("GAME_DATA", "=== Data Querying Demo ===");
        
        // 查询特定武器
        const auto* weapon = GET_DATA_BY_NAME(GameDataType::Weapon, "青钢剑");
        if (weapon)
        {
            LOG_INFO("QUERY", "Found weapon: " + weapon->name + " (ID: " + std::to_string(weapon->id) + ")");
            
            // 显示武器详细属性
            LOG_INFO("QUERY", "Weapon properties:");
            for (const auto& [key, value] : weapon->properties)
            {
                if (!value.empty())
                {
                    LOG_INFO("QUERY", "  " + key + ": " + value);
                }
            }
        }
        else
        {
            LOG_WARNING("QUERY", "Weapon '青钢剑' not found");
        }
        
        // 查询特定地图
        const auto* map = GET_DATA_BY_ID(GameDataType::Map, 100);
        if (map)
        {
            LOG_INFO("QUERY", "Found map: " + map->name + " (ID: " + std::to_string(map->id) + ")");
            
            // 显示地图属性
            LOG_INFO("QUERY", "Map properties:");
            LOG_INFO("QUERY", "  Type: " + map->GetProperty<std::string>("map_type", "Unknown"));
            LOG_INFO("QUERY", "  Recommend Level: " + map->GetProperty<std::string>("recommend_level", "1"));
            LOG_INFO("QUERY", "  Max Players: " + map->GetProperty<std::string>("max_players", "100"));
        }
        else
        {
            LOG_WARNING("QUERY", "Map with ID 100 not found");
        }
        
        // 演示高级查询
        DemoAdvancedQuerying();
    }
    
    void DemoAdvancedQuerying()
    {
        LOG_INFO("GAME_DATA", "=== Advanced Querying Demo ===");
        
        const auto& weapons = GET_GAME_DATA(GameDataType::Weapon);
        
        // 查找高级武器
        std::vector<const GameDataItem*> highLevelWeapons;
        for (const auto& weapon : weapons)
        {
            int requiredLevel = weapon.GetProperty<int>("required_level", 1);
            if (requiredLevel >= 50)
            {
                highLevelWeapons.push_back(&weapon);
            }
        }
        
        LOG_INFO("QUERY", "Found " + std::to_string(highLevelWeapons.size()) + " high-level weapons (level 50+)");
        
        // 查找剑类武器
        std::vector<const GameDataItem*> swords;
        for (const auto& weapon : weapons)
        {
            int weaponType = weapon.GetProperty<int>("weapon_type", 0);
            if (weaponType == 2) // 剑类
            {
                swords.push_back(&weapon);
            }
        }
        
        LOG_INFO("QUERY", "Found " + std::to_string(swords.size()) + " sword weapons");
        
        // 查找PVP地图
        const auto& maps = GET_GAME_DATA(GameDataType::Map);
        std::vector<const GameDataItem*> pvpMaps;
        for (const auto& map : maps)
        {
            bool pvpEnabled = map.GetProperty<bool>("pvp_enabled", false);
            if (pvpEnabled)
            {
                pvpMaps.push_back(&map);
            }
        }
        
        LOG_INFO("QUERY", "Found " + std::to_string(pvpMaps.size()) + " PVP-enabled maps");
    }
    
    void DemoDataStatistics()
    {
        LOG_INFO("GAME_DATA", "=== Data Statistics Demo ===");
        
        auto stats = GAME_DATA_MANAGER().GetStatistics();
        
        LOG_INFO("STATS", "Total game data items: " + std::to_string(stats.totalItems));
        LOG_INFO("STATS", "Data breakdown by type:");
        
        for (const auto& [dataType, count] : stats.itemsByType)
        {
            std::string typeName = GetDataTypeName(dataType);
            LOG_INFO("STATS", "  " + typeName + ": " + std::to_string(count) + " items");
        }
        
        // 武器统计
        DemoWeaponStatistics();
        
        // 地图统计
        DemoMapStatistics();
    }
    
    void DemoWeaponStatistics()
    {
        const auto& weapons = GET_GAME_DATA(GameDataType::Weapon);
        if (weapons.empty()) return;
        
        LOG_INFO("STATS", "=== Weapon Statistics ===");
        
        // 按类型统计
        std::unordered_map<int, int> weaponTypeCount;
        std::unordered_map<int, int> levelDistribution;
        int totalDamage = 0;
        int weaponCount = 0;
        
        for (const auto& weapon : weapons)
        {
            int weaponType = weapon.GetProperty<int>("weapon_type", 0);
            int requiredLevel = weapon.GetProperty<int>("required_level", 1);
            int minDamage = weapon.GetProperty<int>("min_damage", 0);
            int maxDamage = weapon.GetProperty<int>("max_damage", 0);
            
            weaponTypeCount[weaponType]++;
            levelDistribution[requiredLevel / 10 * 10]++; // 按10级分组
            
            if (minDamage > 0 && maxDamage > 0)
            {
                totalDamage += (minDamage + maxDamage) / 2;
                weaponCount++;
            }
        }
        
        LOG_INFO("STATS", "Weapon type distribution:");
        for (const auto& [type, count] : weaponTypeCount)
        {
            LOG_INFO("STATS", "  Type " + std::to_string(type) + ": " + std::to_string(count) + " weapons");
        }
        
        LOG_INFO("STATS", "Level distribution:");
        for (const auto& [levelRange, count] : levelDistribution)
        {
            LOG_INFO("STATS", "  Level " + std::to_string(levelRange) + "-" + 
                    std::to_string(levelRange + 9) + ": " + std::to_string(count) + " weapons");
        }
        
        if (weaponCount > 0)
        {
            int averageDamage = totalDamage / weaponCount;
            LOG_INFO("STATS", "Average weapon damage: " + std::to_string(averageDamage));
        }
    }
    
    void DemoMapStatistics()
    {
        const auto& maps = GET_GAME_DATA(GameDataType::Map);
        if (maps.empty()) return;
        
        LOG_INFO("STATS", "=== Map Statistics ===");
        
        // 按类型和等级统计
        std::unordered_map<int, int> mapTypeCount;
        std::unordered_map<int, int> levelRangeCount;
        int totalMaxPlayers = 0;
        
        for (const auto& map : maps)
        {
            int mapType = map.GetProperty<int>("map_type", 0);
            int recommendLevel = map.GetProperty<int>("recommend_level", 1);
            int maxPlayers = map.GetProperty<int>("max_players", 100);
            
            mapTypeCount[mapType]++;
            levelRangeCount[recommendLevel / 20 * 20]++; // 按20级分组
            totalMaxPlayers += maxPlayers;
        }
        
        LOG_INFO("STATS", "Map type distribution:");
        for (const auto& [type, count] : mapTypeCount)
        {
            LOG_INFO("STATS", "  Type " + std::to_string(type) + ": " + std::to_string(count) + " maps");
        }
        
        LOG_INFO("STATS", "Level range distribution:");
        for (const auto& [levelRange, count] : levelRangeCount)
        {
            LOG_INFO("STATS", "  Level " + std::to_string(levelRange) + "-" + 
                    std::to_string(levelRange + 19) + ": " + std::to_string(count) + " maps");
        }
        
        if (!maps.empty())
        {
            int averageMaxPlayers = totalMaxPlayers / static_cast<int>(maps.size());
            LOG_INFO("STATS", "Average max players per map: " + std::to_string(averageMaxPlayers));
        }
    }
    
    std::string GetDataTypeName(GameDataType dataType)
    {
        switch (dataType)
        {
        case GameDataType::Weapon: return "Weapons";
        case GameDataType::Armor: return "Armor";
        case GameDataType::Item: return "Items";
        case GameDataType::Skill: return "Skills";
        case GameDataType::NPC: return "NPCs";
        case GameDataType::Map: return "Maps";
        case GameDataType::Quest: return "Quests";
        case GameDataType::DropRate: return "Drop Rates";
        case GameDataType::Setting: return "Settings";
        default: return "Unknown";
        }
    }
};

} // namespace sword2

// 全局游戏数据系统演示实例
sword2::GameDataSystemDemo g_GameDataDemo;

// 初始化游戏数据系统
bool InitializeGameDataSystem()
{
    return g_GameDataDemo.Initialize();
}

// 运行游戏数据系统演示
void RunGameDataSystemDemo()
{
    g_GameDataDemo.RunDemo();
}

// 快速加载游戏数据
bool QuickLoadGameData()
{
    printf("[GAME_DATA] Quick loading essential game data...\n");
    
    // 加载武器数据
    std::string weaponFile = "c:\\Sword2Code/Sword2\\SourceCode\\Server/settings/item/weapon.txt";
    if (!LOAD_GAME_DATA(weaponFile, sword2::GameDataType::Weapon))
    {
        printf("[GAME_DATA] Failed to load weapon data\n");
        return false;
    }
    
    // 加载地图数据
    std::string mapFile = "c:\\Sword2Code/Sword2\\SourceCode\\Server/maps/worldset_1.ini";
    if (!LOAD_GAME_DATA(mapFile, sword2::GameDataType::Map))
    {
        printf("[GAME_DATA] Failed to load map data\n");
        return false;
    }
    
    // 显示加载统计
    auto stats = GAME_DATA_MANAGER().GetStatistics();
    printf("[GAME_DATA] Successfully loaded %zu total items\n", stats.totalItems);
    
    return true;
}
