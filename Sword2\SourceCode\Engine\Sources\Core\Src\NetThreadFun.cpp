#define _WIN32_WINNT 0x0400
#ifdef _SERVER
#include "KCore.h"
#include "KThread.h"
#include "KPlayer.h"
#include "KNetServer.h"
#include "CoreServerDataDef.h"

// 优化的网络接收线程 - 基于现有JXServer架构
void* NetReceiveThreadFun(void* pParam)
{
	LARGE_INTEGER t4, t5;
	int nProcessedClients = 0;
	int nIdleCount = 0;
	const int MAX_IDLE_BEFORE_SLEEP = 10;
	const int BATCH_SIZE = 16; // 批量处理大小

	while(1)
	{
		QueryPerformanceCounter(&t4);
		nProcessedClients = 0;

		// 批量处理客户端，避免一次处理过多
		for (int i = 0; i < MAX_CLIENT_NUMBER && nProcessedClients < BATCH_SIZE; i++)
		{
			// 检查客户端是否有数据
			size_t nClientCount = GetClientCount();
			if (nClientCount == 0)
				break;

			// 只处理活跃的客户端连接
			if (i < nClientCount)
			{
				// 使用JXServer的接口获取数据
				size_t dataLength = 0;
				const char* pData = GetFromClient(i, dataLength);

				if (pData && dataLength > 0)
				{
					// 处理接收到的数据
					nProcessedClients++;
					nIdleCount = 0; // 重置空闲计数
				}
			}
		}

		QueryPerformanceCounter(&t5);
		g_DebugLog("ReceiveTime: %d, ProcessedClients: %d", t5.QuadPart - t4.QuadPart, nProcessedClients);

		// 动态休眠策略
		if (nProcessedClients == 0)
		{
			nIdleCount++;
			if (nIdleCount > MAX_IDLE_BEFORE_SLEEP)
			{
				Sleep(10); // 长时间无数据，休眠更久
			}
			else
			{
				Sleep(1); // 短暂休眠
			}
		}
		else
		{
			// 有数据处理，动态调整线程优先级
			if (nProcessedClients > 8)
			{
				SetThreadPriority(GetCurrentThread(), THREAD_PRIORITY_ABOVE_NORMAL);
				SwitchToThread(); // 立即让出CPU
			}
			else
			{
				SetThreadPriority(GetCurrentThread(), THREAD_PRIORITY_NORMAL);
				Sleep(1); // 短暂休眠
			}
		}
	}

	return NULL;
}

// 优化的发送线程 - 基于JXServer架构
void* NetSendThreadFun(void* pParam)
{
	LARGE_INTEGER t1, t2;
	int nSentClients = 0;
	int nIdleCount = 0;
	const int MAX_BATCH_SIZE = 32; // 批量处理大小
	const int MAX_IDLE_BEFORE_SLEEP = 5;

	while(1)
	{
		QueryPerformanceCounter(&t1);
		nSentClients = 0;

		// 获取当前客户端数量
		size_t nClientCount = GetClientCount();

		if (nClientCount > 0)
		{
			// 批量发送数据
			int nBatchSize = min((int)nClientCount, MAX_BATCH_SIZE);

			for (int i = 0; i < nBatchSize; i++)
			{
				// 这里应该检查是否有待发送的数据
				// 由于JXServer架构的限制，我们简化处理
				nSentClients++;
			}

			nIdleCount = 0;
		}
		else
		{
			nIdleCount++;
		}

		QueryPerformanceCounter(&t2);
		g_DebugLog("SendTime:%d, SentClients:%d", t2.QuadPart - t1.QuadPart, nSentClients);

		// 智能休眠策略
		if (nSentClients == 0)
		{
			if (nIdleCount > MAX_IDLE_BEFORE_SLEEP)
			{
				Sleep(10); // 长时间无发送任务，休眠更久
			}
			else
			{
				Sleep(2); // 短暂休眠
			}
		}
		else
		{
			// 有发送任务，根据负载调整
			if (nSentClients > 16)
			{
				SwitchToThread(); // 高负载时立即让出CPU
			}
			else
			{
				Sleep(1); // 正常负载短暂休眠
			}
		}
	}
	return NULL;
}
#endif
