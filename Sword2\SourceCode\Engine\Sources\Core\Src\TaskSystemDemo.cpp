//---------------------------------------------------------------------------
// Sword2 Task System Demo (c) 2024
//
// File:	TaskSystemDemo.cpp
// Date:	2024.07.02
// Code:	AI Assistant
// Desc:	Comprehensive demonstration of the task system
//---------------------------------------------------------------------------

#include "KCore.h"
#include "TaskSystem.h"
#include "TaskManager.h"
#include "TaskScriptIntegration.h"
#include "PlayerSystem.h"
#include "PlayerManager.h"
#include "ItemManager.h"
#include "InventoryManager.h"
#include "LuaScriptEngine.h"
#include "UnifiedLoggingSystem.h"

namespace sword2 {

// 任务系统演示类
class TaskSystemDemo
{
public:
    TaskSystemDemo()
    {
        m_initialized = false;
    }
    
    bool Initialize()
    {
        if (m_initialized) return true;
        
        printf("[TASK_DEMO] Initializing task system demo...\n");
        
        // 初始化日志系统
        LOGGER().Initialize();
        LOGGER().AddConsoleAppender(LogLevel::Debug);
        
        // 启动玩家管理器
        if (!START_PLAYER_SYSTEM())
        {
            printf("[TASK_DEMO] Failed to start player system\n");
            return false;
        }
        
        // 启动物品管理器
        if (!START_ITEM_SYSTEM())
        {
            printf("[TASK_DEMO] Failed to start item system\n");
            return false;
        }
        
        // 初始化脚本引擎
        if (!INIT_SCRIPT_ENGINE("../../../Server/script"))
        {
            printf("[TASK_DEMO] Failed to initialize script engine\n");
            return false;
        }
        
        // 启动任务管理器
        if (!START_TASK_SYSTEM())
        {
            printf("[TASK_DEMO] Failed to start task system\n");
            return false;
        }
        
        // 初始化任务脚本集成
        if (!INIT_TASK_SCRIPT_INTEGRATION())
        {
            printf("[TASK_DEMO] Failed to initialize task script integration\n");
            return false;
        }
        
        m_initialized = true;
        LOG_INFO("TASK_DEMO", "Task system demo initialized successfully");
        
        return true;
    }
    
    void RunDemo()
    {
        if (!m_initialized)
        {
            printf("[TASK_DEMO] System not initialized\n");
            return;
        }
        
        LOG_INFO("TASK_DEMO", "Starting task system demonstration...");
        
        // 演示各个功能
        DemoTaskTemplateCreation();
        DemoTaskAcceptance();
        DemoTaskProgress();
        DemoTaskCompletion();
        DemoTaskChain();
        DemoTaskScripting();
        DemoTaskEvents();
        DemoTaskStatistics();
        
        LOG_INFO("TASK_DEMO", "Task system demonstration completed");
    }
    
    void Cleanup()
    {
        if (!m_initialized) return;
        
        LOG_INFO("TASK_DEMO", "Cleaning up task system demo...");
        
        // 清理创建的玩家
        for (uint32_t playerId : m_createdPlayers)
        {
            REMOVE_PLAYER_INVENTORY(playerId);
            PLAYER_LOGOUT(playerId);
        }
        m_createdPlayers.clear();
        
        // 停止系统
        STOP_TASK_SYSTEM();
        STOP_ITEM_SYSTEM();
        SHUTDOWN_SCRIPT_ENGINE();
        STOP_PLAYER_SYSTEM();
        
        m_initialized = false;
        LOG_INFO("TASK_DEMO", "Task system demo cleanup completed");
    }

private:
    bool m_initialized;
    std::vector<uint32_t> m_createdPlayers;
    
    void DemoTaskTemplateCreation()
    {
        LOG_INFO("TASK_DEMO", "=== Task Template Creation Demo ===");
        
        // 任务模板已经在TaskManager中创建
        // 显示已创建的任务模板信息
        std::vector<uint32_t> taskIds = {1001, 1002, 2001, 3001, 4001};
        
        for (uint32_t taskId : taskIds)
        {
            auto taskTemplate = GET_TASK_TEMPLATE(taskId);
            if (taskTemplate)
            {
                LOG_INFO("TASK_DEMO", "Task Template: " + taskTemplate->taskName);
                LOG_INFO("TASK_DEMO", "  ID: " + std::to_string(taskTemplate->taskId));
                LOG_INFO("TASK_DEMO", "  Type: " + taskTemplate->GetTypeDescription());
                LOG_INFO("TASK_DEMO", "  Level: " + std::to_string(taskTemplate->level));
                LOG_INFO("TASK_DEMO", "  Description: " + taskTemplate->description);
                LOG_INFO("TASK_DEMO", "  From NPC: " + taskTemplate->fromNpc);
                LOG_INFO("TASK_DEMO", "  To NPC: " + taskTemplate->toNpc);
                LOG_INFO("TASK_DEMO", "  Time Limit: " + std::to_string(taskTemplate->timeLimit) + " seconds");
                LOG_INFO("TASK_DEMO", "  Entities: " + std::to_string(taskTemplate->entities.size()));
                LOG_INFO("TASK_DEMO", "  Awards: " + std::to_string(taskTemplate->awards.size()));
                
                // 显示任务实体
                for (size_t i = 0; i < taskTemplate->entities.size(); ++i)
                {
                    const auto& entity = taskTemplate->entities[i];
                    LOG_INFO("TASK_DEMO", "    Entity " + std::to_string(i + 1) + ": " + entity.description);
                }
                
                // 显示任务奖励
                for (size_t i = 0; i < taskTemplate->awards.size(); ++i)
                {
                    const auto& award = taskTemplate->awards[i];
                    LOG_INFO("TASK_DEMO", "    Award " + std::to_string(i + 1) + ": " + award.GetDescription());
                }
                
                LOG_INFO("TASK_DEMO", "");
            }
        }
    }
    
    void DemoTaskAcceptance()
    {
        LOG_INFO("TASK_DEMO", "=== Task Acceptance Demo ===");
        
        // 创建测试玩家
        PlayerCreationParams params1("任务测试玩家1", "task_test1");
        params1.series = PlayerSeries::Shaolin;
        uint32_t playerId1 = CREATE_PLAYER(params1);
        
        PlayerCreationParams params2("任务测试玩家2", "task_test2");
        params2.series = PlayerSeries::Wudang;
        uint32_t playerId2 = CREATE_PLAYER(params2);
        
        if (playerId1 != 0 && playerId2 != 0)
        {
            m_createdPlayers.push_back(playerId1);
            m_createdPlayers.push_back(playerId2);
            
            // 玩家登录
            uint32_t sessionId1, sessionId2;
            PLAYER_LOGIN(playerId1, "127.0.0.1", 5001, sessionId1);
            PLAYER_LOGIN(playerId2, "127.0.0.1", 5002, sessionId2);
            
            // 设置玩家等级
            auto player1 = GET_ONLINE_PLAYER(playerId1);
            auto player2 = GET_ONLINE_PLAYER(playerId2);
            if (player1 && player2)
            {
                player1->level = 15;
                player1->money = 1000;
                player2->level = 10;
                player2->money = 500;
            }
            
            // 获取可接受的任务
            auto availableTasks1 = GET_AVAILABLE_TASKS(playerId1);
            auto availableTasks2 = GET_AVAILABLE_TASKS(playerId2);
            
            LOG_INFO("TASK_DEMO", "Player 1 available tasks: " + std::to_string(availableTasks1.size()));
            for (uint32_t taskId : availableTasks1)
            {
                auto taskTemplate = GET_TASK_TEMPLATE(taskId);
                if (taskTemplate)
                {
                    LOG_INFO("TASK_DEMO", "  - " + taskTemplate->taskName + " (ID: " + std::to_string(taskId) + ")");
                }
            }
            
            LOG_INFO("TASK_DEMO", "Player 2 available tasks: " + std::to_string(availableTasks2.size()));
            for (uint32_t taskId : availableTasks2)
            {
                auto taskTemplate = GET_TASK_TEMPLATE(taskId);
                if (taskTemplate)
                {
                    LOG_INFO("TASK_DEMO", "  - " + taskTemplate->taskName + " (ID: " + std::to_string(taskId) + ")");
                }
            }
            
            // 接受任务
            TaskOperationResult result1 = ACCEPT_TASK(playerId1, 1001);
            TaskOperationResult result2 = ACCEPT_TASK(playerId1, 2001);
            TaskOperationResult result3 = ACCEPT_TASK(playerId1, 4001); // 少林任务
            TaskOperationResult result4 = ACCEPT_TASK(playerId2, 1001);
            TaskOperationResult result5 = ACCEPT_TASK(playerId2, 3001);
            
            LOG_INFO("TASK_DEMO", "Task acceptance results:");
            LOG_INFO("TASK_DEMO", "  Player 1 - Task 1001: " + std::to_string(static_cast<int>(result1)));
            LOG_INFO("TASK_DEMO", "  Player 1 - Task 2001: " + std::to_string(static_cast<int>(result2)));
            LOG_INFO("TASK_DEMO", "  Player 1 - Task 4001: " + std::to_string(static_cast<int>(result3)));
            LOG_INFO("TASK_DEMO", "  Player 2 - Task 1001: " + std::to_string(static_cast<int>(result4)));
            LOG_INFO("TASK_DEMO", "  Player 2 - Task 3001: " + std::to_string(static_cast<int>(result5)));
        }
    }
    
    void DemoTaskProgress()
    {
        LOG_INFO("TASK_DEMO", "=== Task Progress Demo ===");
        
        if (m_createdPlayers.empty()) return;
        
        uint32_t playerId = m_createdPlayers[0];
        
        // 获取玩家当前任务
        auto currentTasks = GET_PLAYER_TASKS(playerId, TaskState::InProgress);
        
        LOG_INFO("TASK_DEMO", "Player " + std::to_string(playerId) + " current tasks: " + std::to_string(currentTasks.size()));
        
        for (const auto& task : currentTasks)
        {
            if (!task) continue;
            
            auto taskTemplate = GET_TASK_TEMPLATE(task->templateId);
            if (!taskTemplate) continue;
            
            LOG_INFO("TASK_DEMO", "Task: " + taskTemplate->taskName);
            LOG_INFO("TASK_DEMO", "  State: " + task->GetStateDescription());
            LOG_INFO("TASK_DEMO", "  Progress: " + std::to_string(task->GetProgressPercentage() * 100.0f) + "%");
            LOG_INFO("TASK_DEMO", "  Remaining time: " + std::to_string(task->GetRemainingTime()) + " seconds");
            
            // 显示实体进度
            for (size_t i = 0; i < task->entities.size(); ++i)
            {
                const auto& entity = task->entities[i];
                LOG_INFO("TASK_DEMO", "    Entity " + std::to_string(i + 1) + ": " + entity.description + 
                        " (" + entity.GetProgressDescription() + ")");
            }
            
            // 更新任务进度
            for (size_t i = 0; i < task->entities.size(); ++i)
            {
                if (!task->entities[i].IsCompleted())
                {
                    bool updated = UPDATE_TASK_PROGRESS(playerId, task->templateId, i, 1);
                    if (updated)
                    {
                        LOG_INFO("TASK_DEMO", "Updated progress for entity " + std::to_string(i + 1));
                    }
                    break; // 只更新第一个未完成的实体
                }
            }
        }
    }
    
    void DemoTaskCompletion()
    {
        LOG_INFO("TASK_DEMO", "=== Task Completion Demo ===");
        
        if (m_createdPlayers.empty()) return;
        
        uint32_t playerId = m_createdPlayers[0];
        
        // 获取进行中的任务
        auto currentTasks = GET_PLAYER_TASKS(playerId, TaskState::InProgress);
        
        for (const auto& task : currentTasks)
        {
            if (!task) continue;
            
            auto taskTemplate = GET_TASK_TEMPLATE(task->templateId);
            if (!taskTemplate) continue;
            
            // 完成所有实体
            for (size_t i = 0; i < task->entities.size(); ++i)
            {
                auto& entity = task->entities[i];
                entity.currentProgress = entity.targetProgress;
            }
            
            LOG_INFO("TASK_DEMO", "Completing task: " + taskTemplate->taskName);
            
            // 完成任务
            TaskOperationResult completeResult = COMPLETE_TASK(playerId, task->templateId);
            if (completeResult == TaskOperationResult::Success)
            {
                LOG_INFO("TASK_DEMO", "Task completed successfully");
                
                // 提交任务
                TaskOperationResult submitResult = SUBMIT_TASK(playerId, task->templateId);
                if (submitResult == TaskOperationResult::Success)
                {
                    LOG_INFO("TASK_DEMO", "Task submitted successfully");
                    
                    // 显示奖励
                    LOG_INFO("TASK_DEMO", "Rewards received:");
                    for (const auto& award : taskTemplate->awards)
                    {
                        LOG_INFO("TASK_DEMO", "  - " + award.GetDescription());
                    }
                }
                else
                {
                    LOG_WARNING("TASK_DEMO", "Failed to submit task: " + std::to_string(static_cast<int>(submitResult)));
                }
            }
            else
            {
                LOG_WARNING("TASK_DEMO", "Failed to complete task: " + std::to_string(static_cast<int>(completeResult)));
            }
            
            break; // 只完成第一个任务
        }
    }
    
    void DemoTaskChain()
    {
        LOG_INFO("TASK_DEMO", "=== Task Chain Demo ===");
        
        if (m_createdPlayers.empty()) return;
        
        uint32_t playerId = m_createdPlayers[0];
        
        // 检查任务链
        uint32_t chainStartTask = 1001;
        auto taskTemplate = GET_TASK_TEMPLATE(chainStartTask);
        
        if (taskTemplate)
        {
            LOG_INFO("TASK_DEMO", "Task Chain starting from: " + taskTemplate->taskName);
            
            uint32_t currentTaskId = chainStartTask;
            int chainLength = 0;
            
            while (currentTaskId != 0 && chainLength < 10) // 防止无限循环
            {
                auto currentTemplate = GET_TASK_TEMPLATE(currentTaskId);
                if (!currentTemplate) break;
                
                LOG_INFO("TASK_DEMO", "  Chain " + std::to_string(chainLength + 1) + ": " + 
                        currentTemplate->taskName + " (ID: " + std::to_string(currentTaskId) + ")");
                
                // 显示前置和后续任务
                if (currentTemplate->prevTaskId != 0)
                {
                    LOG_INFO("TASK_DEMO", "    Previous: " + std::to_string(currentTemplate->prevTaskId));
                }
                
                if (currentTemplate->nextTaskId != 0)
                {
                    LOG_INFO("TASK_DEMO", "    Next: " + std::to_string(currentTemplate->nextTaskId));
                }
                
                // 显示分支任务
                if (!currentTemplate->branchTasks.empty())
                {
                    LOG_INFO("TASK_DEMO", "    Branch tasks:");
                    for (uint32_t branchId : currentTemplate->branchTasks)
                    {
                        auto branchTemplate = GET_TASK_TEMPLATE(branchId);
                        if (branchTemplate)
                        {
                            LOG_INFO("TASK_DEMO", "      - " + branchTemplate->taskName + " (ID: " + std::to_string(branchId) + ")");
                        }
                    }
                }
                
                currentTaskId = currentTemplate->nextTaskId;
                chainLength++;
            }
            
            LOG_INFO("TASK_DEMO", "Total chain length: " + std::to_string(chainLength));
        }
    }
    
    void DemoTaskScripting()
    {
        LOG_INFO("TASK_DEMO", "=== Task Scripting Demo ===");
        
        // 创建测试脚本
        std::string testScript = R"(
            function task_test_function(taskId, playerId)
                WriteLog("Task script function called!")
                WriteLog("Task ID: " .. taskId)
                WriteLog("Player ID: " .. playerId)
                
                local taskName = GetTaskName(taskId)
                local taskType = GetTaskType(taskId)
                local taskLevel = GetTaskLevel(taskId)
                local taskState = GetTaskState(taskId, playerId)
                
                WriteLog("Task Name: " .. taskName)
                WriteLog("Task Type: " .. taskType)
                WriteLog("Task Level: " .. taskLevel)
                WriteLog("Task State: " .. taskState)
                
                return true
            end
            
            function on_task_accept(taskId, playerId)
                WriteLog("Task accepted: " .. taskId .. " by player " .. playerId)
                
                local taskName = GetTaskName(taskId)
                WriteLog("Welcome to task: " .. taskName)
                
                -- 设置任务变量
                SetTaskVariable(playerId, taskId, "start_time", os.time())
                
                return true
            end
            
            function on_task_complete(taskId, playerId)
                WriteLog("Task completed: " .. taskId .. " by player " .. playerId)
                
                local startTime = GetTaskVariable(playerId, taskId, "start_time")
                local currentTime = os.time()
                local duration = currentTime - startTime
                
                WriteLog("Task duration: " .. duration .. " seconds")
                
                -- 根据完成时间给予额外奖励
                if duration < 300 then -- 5分钟内完成
                    WriteLog("Fast completion bonus!")
                    -- 这里可以给予额外奖励
                end
                
                return true
            end
            
            function check_custom_condition(playerId, conditionType, value)
                WriteLog("Checking custom condition: " .. conditionType .. " = " .. value)
                
                if conditionType == "weather" then
                    -- 检查天气条件
                    return true
                elseif conditionType == "time" then
                    -- 检查时间条件
                    local hour = tonumber(os.date("%H"))
                    return hour >= value
                end
                
                return false
            end
            
            function calculate_dynamic_reward(taskId, playerId, baseReward)
                WriteLog("Calculating dynamic reward for task " .. taskId)
                
                local playerLevel = GetPlayerLevel(playerId)
                local taskLevel = GetTaskLevel(taskId)
                
                -- 根据玩家等级调整奖励
                local levelDiff = playerLevel - taskLevel
                local multiplier = 1.0
                
                if levelDiff > 5 then
                    multiplier = 0.5  -- 等级过高，奖励减半
                elseif levelDiff < -5 then
                    multiplier = 1.5  -- 等级过低，奖励增加
                end
                
                local finalReward = math.floor(baseReward * multiplier)
                WriteLog("Base reward: " .. baseReward .. ", Final reward: " .. finalReward)
                
                return finalReward
            end
        )";
        
        // 执行测试脚本
        auto context = std::make_unique<LuaScriptContext>();
        context->Initialize();
        
        ScriptResult result = context->ExecuteString(testScript);
        if (result == ScriptResult::Success)
        {
            LOG_INFO("TASK_DEMO", "Successfully loaded task test script");
            
            if (!m_createdPlayers.empty())
            {
                uint32_t playerId = m_createdPlayers[0];
                uint32_t taskId = 1001;
                
                // 测试脚本函数调用
                std::vector<LuaValue> args = {
                    LuaValue(static_cast<double>(taskId)),
                    LuaValue(static_cast<double>(playerId))
                };
                
                result = context->CallFunction("task_test_function", args);
                if (result == ScriptResult::Success)
                {
                    LOG_INFO("TASK_DEMO", "Successfully executed task script function");
                }
                
                // 测试事件处理函数
                context->CallFunction("on_task_accept", args);
                context->CallFunction("on_task_complete", args);
                
                // 测试条件检查函数
                std::vector<LuaValue> conditionArgs = {
                    LuaValue(static_cast<double>(playerId)),
                    LuaValue("weather"),
                    LuaValue(static_cast<double>(1))
                };
                context->CallFunction("check_custom_condition", conditionArgs);
                
                // 测试动态奖励计算
                std::vector<LuaValue> rewardArgs = {
                    LuaValue(static_cast<double>(taskId)),
                    LuaValue(static_cast<double>(playerId)),
                    LuaValue(static_cast<double>(100))
                };
                context->CallFunction("calculate_dynamic_reward", rewardArgs);
            }
        }
        else
        {
            LOG_ERROR("TASK_DEMO", "Failed to load task test script");
        }
    }
    
    void DemoTaskEvents()
    {
        LOG_INFO("TASK_DEMO", "=== Task Events Demo ===");
        
        // 注册自定义事件处理器
        auto& eventHandler = TASK_EVENT_HANDLER();
        
        eventHandler.RegisterEventHandler(TaskEvent::Accept, "demo_accept_handler",
            [](const TaskEventHandler::TaskEventData& data) {
                LOG_INFO("TASK_EVENT", "Custom handler - Task accepted: " + data.taskName + 
                        " by player " + std::to_string(data.playerId));
            });
        
        eventHandler.RegisterEventHandler(TaskEvent::Progress, "demo_progress_handler",
            [](const TaskEventHandler::TaskEventData& data) {
                LOG_INFO("TASK_EVENT", "Custom handler - Task progress updated: " + data.taskName);
            });
        
        eventHandler.RegisterEventHandler(TaskEvent::Complete, "demo_complete_handler",
            [](const TaskEventHandler::TaskEventData& data) {
                LOG_INFO("TASK_EVENT", "Custom handler - Task completed: " + data.taskName + 
                        " by player " + std::to_string(data.playerId));
            });
        
        // 获取事件历史
        if (!m_createdPlayers.empty())
        {
            uint32_t playerId = m_createdPlayers[0];
            auto eventHistory = eventHandler.GetEventHistory(playerId);
            
            LOG_INFO("TASK_DEMO", "Event history for player " + std::to_string(playerId) + ": " + 
                    std::to_string(eventHistory.size()) + " events");
            
            for (const auto& event : eventHistory)
            {
                std::string eventName;
                switch (event.event)
                {
                case TaskEvent::Accept: eventName = "Accept"; break;
                case TaskEvent::Complete: eventName = "Complete"; break;
                case TaskEvent::Submit: eventName = "Submit"; break;
                case TaskEvent::Progress: eventName = "Progress"; break;
                default: eventName = "Other"; break;
                }
                
                LOG_INFO("TASK_DEMO", "  Event: " + eventName + " - Task: " + event.taskName + 
                        " - Time: " + std::to_string(std::chrono::duration_cast<std::chrono::seconds>(
                            event.timestamp.time_since_epoch()).count()));
            }
        }
    }
    
    void DemoTaskStatistics()
    {
        LOG_INFO("TASK_DEMO", "=== Task Statistics Demo ===");
        
        // 显示任务系统统计信息
        LOG_INFO("TASK_DEMO", "Task system statistics:");
        
        // 统计任务模板
        std::vector<uint32_t> allTaskIds = {1001, 1002, 2001, 3001, 4001};
        uint32_t totalTemplates = 0;
        std::unordered_map<TaskType, uint32_t> tasksByType;
        
        for (uint32_t taskId : allTaskIds)
        {
            auto taskTemplate = GET_TASK_TEMPLATE(taskId);
            if (taskTemplate)
            {
                totalTemplates++;
                tasksByType[taskTemplate->type]++;
            }
        }
        
        LOG_INFO("TASK_DEMO", "  Total task templates: " + std::to_string(totalTemplates));
        
        LOG_INFO("TASK_DEMO", "Tasks by type:");
        for (const auto& [type, count] : tasksByType)
        {
            std::string typeName;
            switch (type)
            {
            case TaskType::Main: typeName = "Main"; break;
            case TaskType::Side: typeName = "Side"; break;
            case TaskType::Daily: typeName = "Daily"; break;
            case TaskType::Chain: typeName = "Chain"; break;
            default: typeName = "Other"; break;
            }
            
            LOG_INFO("TASK_DEMO", "  " + typeName + ": " + std::to_string(count));
        }
        
        // 统计玩家任务
        for (uint32_t playerId : m_createdPlayers)
        {
            auto allTasks = GET_PLAYER_TASKS(playerId, TaskState::NotStarted); // 获取所有任务
            auto inProgressTasks = GET_PLAYER_TASKS(playerId, TaskState::InProgress);
            auto completedTasks = GET_PLAYER_TASKS(playerId, TaskState::Submitted);
            
            LOG_INFO("TASK_DEMO", "Player " + std::to_string(playerId) + " task statistics:");
            LOG_INFO("TASK_DEMO", "  In progress: " + std::to_string(inProgressTasks.size()));
            LOG_INFO("TASK_DEMO", "  Completed: " + std::to_string(completedTasks.size()));
            
            // 计算完成率
            uint32_t totalAccepted = inProgressTasks.size() + completedTasks.size();
            if (totalAccepted > 0)
            {
                float completionRate = static_cast<float>(completedTasks.size()) / totalAccepted * 100.0f;
                LOG_INFO("TASK_DEMO", "  Completion rate: " + std::to_string(completionRate) + "%");
            }
        }
    }
};

} // namespace sword2

// 全局任务系统演示实例
sword2::TaskSystemDemo g_TaskDemo;

// 初始化任务系统
bool InitializeTaskSystem()
{
    return g_TaskDemo.Initialize();
}

// 运行任务系统演示
void RunTaskSystemDemo()
{
    g_TaskDemo.RunDemo();
}

// 清理任务系统
void CleanupTaskSystem()
{
    g_TaskDemo.Cleanup();
}
