﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="4.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <ItemGroup>
    <Filter Include="Êư¾Ư¿âÏà¹Ø">
      <UniqueIdentifier>{2ba1e8bf-80dd-42c0-b743-2ffeccf82c3e}</UniqueIdentifier>
    </Filter>
    <Filter Include="Lib">
      <UniqueIdentifier>{774d5a28-031a-47bd-95ff-e2d4d3778384}</UniqueIdentifier>
    </Filter>
    <Filter Include="Lib\Release">
      <UniqueIdentifier>{b8f23ef0-9988-4a15-a082-b0c587b65bf2}</UniqueIdentifier>
    </Filter>
    <Filter Include="Lib\Debug">
      <UniqueIdentifier>{452ec915-cab8-4c93-8792-29a25d454f7b}</UniqueIdentifier>
    </Filter>
    <Filter Include="ÅÅĂû">
      <UniqueIdentifier>{8926ecd9-b74d-46a9-bcee-b6fbf1a689f2}</UniqueIdentifier>
    </Filter>
    <Filter Include="USBKey">
      <UniqueIdentifier>{d595b745-550a-4876-a29a-881a0350933b}</UniqueIdentifier>
    </Filter>
    <Filter Include="region">
      <UniqueIdentifier>{813c3db3-3be1-4e09-b09a-d9e02749d638}</UniqueIdentifier>
    </Filter>
    <Filter Include="skill">
      <UniqueIdentifier>{75465c3e-648f-4eb2-bf4c-af6bc78acc50}</UniqueIdentifier>
    </Filter>
    <Filter Include="skill\Íæ¼̉³èÎï">
      <UniqueIdentifier>{9abba53e-4df8-446d-aaff-b453be57aae2}</UniqueIdentifier>
    </Filter>
    <Filter Include="skill\ÍµÇÔ¼¼ÄÜ">
      <UniqueIdentifier>{54be790c-a69d-48de-a979-3ded70988c6c}</UniqueIdentifier>
    </Filter>
    <Filter Include="script">
      <UniqueIdentifier>{a6430099-e2c2-467f-a561-4a6f3287358d}</UniqueIdentifier>
    </Filter>
    <Filter Include="script\Á¢¼´ĐÔ½Å±¾">
      <UniqueIdentifier>{81ef8651-8ea9-480a-b15a-7cb9488ac2a6}</UniqueIdentifier>
    </Filter>
    <Filter Include="script\µ¥²½ĐÔ½Å±¾">
      <UniqueIdentifier>{6a36f673-1cd6-4ecb-9947-83c2d9c31104}</UniqueIdentifier>
    </Filter>
    <Filter Include="scene">
      <UniqueIdentifier>{d14b3490-f8f0-434f-9c9f-7fab8f614e12}</UniqueIdentifier>
    </Filter>
    <Filter Include="scene\³¡¾°ÖĐ¶ÔÏóÓë¶ÔÏóÊ÷">
      <UniqueIdentifier>{6b1f6fc9-11b1-4f70-b0ce-d7df138ce582}</UniqueIdentifier>
    </Filter>
    <Filter Include="magicAttr">
      <UniqueIdentifier>{10297e90-8343-49a3-ac18-65626a734368}</UniqueIdentifier>
    </Filter>
    <Filter Include="protocol">
      <UniqueIdentifier>{7831c7ce-b1e2-488e-b842-714ec3d9bbe3}</UniqueIdentifier>
    </Filter>
    <Filter Include="coreshell">
      <UniqueIdentifier>{d7d5c14d-ad78-4087-ac70-cf7db1743ef0}</UniqueIdentifier>
    </Filter>
    <Filter Include="core">
      <UniqueIdentifier>{2babb587-7911-4f7c-9e5e-e9ee315b3997}</UniqueIdentifier>
    </Filter>
    <Filter Include="npc">
      <UniqueIdentifier>{90b1f389-df2b-4f4e-a28e-8cb910c608c8}</UniqueIdentifier>
    </Filter>
    <Filter Include="task">
      <UniqueIdentifier>{9845a267-68cd-460a-b0ea-1dcb0d07c494}</UniqueIdentifier>
    </Filter>
    <Filter Include="math">
      <UniqueIdentifier>{c4be5f85-a578-4d3f-91c3-4e31e925b5ee}</UniqueIdentifier>
    </Filter>
    <Filter Include="GM">
      <UniqueIdentifier>{9e2ff637-b316-4dc0-906a-f305aa759d37}</UniqueIdentifier>
    </Filter>
    <Filter Include="gameobj">
      <UniqueIdentifier>{8759448f-2351-4cbd-a0cd-0d311a72701f}</UniqueIdentifier>
    </Filter>
    <Filter Include="item">
      <UniqueIdentifier>{71321dfe-d580-49ec-bc1b-78694097e834}</UniqueIdentifier>
    </Filter>
    <Filter Include="msg">
      <UniqueIdentifier>{0105fff1-deec-4a03-8c1b-5caa66b5120d}</UniqueIdentifier>
    </Filter>
    <Filter Include="option">
      <UniqueIdentifier>{05d1f383-5d11-427a-95d9-d856cf4922dd}</UniqueIdentifier>
    </Filter>
    <Filter Include="mapmusic">
      <UniqueIdentifier>{62dfc37f-01d2-4a8b-aa96-77a2dbeec505}</UniqueIdentifier>
    </Filter>
  </ItemGroup>
  <ItemGroup>
    <ClCompile Include="Src\KPartnerSkill.cpp">
      <Filter>skill\Íæ¼̉³èÎï</Filter>
    </ClCompile>
    <ClCompile Include="Src\KPlayerPartner.cpp">
      <Filter>skill\Íæ¼̉³èÎï</Filter>
    </ClCompile>
    <ClCompile Include="Src\KThiefSkill.cpp">
      <Filter>skill\ÍµÇÔ¼¼ÄÜ</Filter>
    </ClCompile>
    <ClCompile Include="Src\KMissle.cpp">
      <Filter>skill</Filter>
    </ClCompile>
    <ClCompile Include="Src\KMissleRes.cpp">
      <Filter>skill</Filter>
    </ClCompile>
    <ClCompile Include="Src\KMissleSet.cpp">
      <Filter>skill</Filter>
    </ClCompile>
    <ClCompile Include="Src\KSkillList.cpp">
      <Filter>skill</Filter>
    </ClCompile>
    <ClCompile Include="Src\KSkillManager.cpp">
      <Filter>skill</Filter>
    </ClCompile>
    <ClCompile Include="Src\KSkills.cpp">
      <Filter>skill</Filter>
    </ClCompile>
    <ClCompile Include="Src\KSkillSpecial.cpp">
      <Filter>skill</Filter>
    </ClCompile>
    <ClCompile Include="Src\Skill.cpp">
      <Filter>skill</Filter>
    </ClCompile>
    <ClCompile Include="Src\KFaction.cpp">
      <Filter>npc</Filter>
    </ClCompile>
    <ClCompile Include="Src\KGameData.cpp">
      <Filter>npc</Filter>
    </ClCompile>
    <ClCompile Include="Src\KNpc.cpp">
      <Filter>npc</Filter>
    </ClCompile>
    <ClCompile Include="Src\KNpcAI.cpp">
      <Filter>npc</Filter>
    </ClCompile>
    <ClCompile Include="Src\KNpcAttribModify.cpp">
      <Filter>npc</Filter>
    </ClCompile>
    <ClCompile Include="Src\KNpcDeathCalcExp.cpp">
      <Filter>npc</Filter>
    </ClCompile>
    <ClCompile Include="Src\KNpcFindPath.cpp">
      <Filter>npc</Filter>
    </ClCompile>
    <ClCompile Include="Src\KNpcRes.cpp">
      <Filter>npc</Filter>
    </ClCompile>
    <ClCompile Include="Src\KNpcResList.cpp">
      <Filter>npc</Filter>
    </ClCompile>
    <ClCompile Include="Src\KNpcResNode.cpp">
      <Filter>npc</Filter>
    </ClCompile>
    <ClCompile Include="Src\KNpcSet.cpp">
      <Filter>npc</Filter>
    </ClCompile>
    <ClCompile Include="Src\KNpcTemplate.cpp">
      <Filter>npc</Filter>
    </ClCompile>
    <ClCompile Include="Src\KPlayer.cpp">
      <Filter>npc</Filter>
    </ClCompile>
    <ClCompile Include="Src\KPlayerAI.cpp">
      <Filter>npc</Filter>
    </ClCompile>
    <ClCompile Include="Src\KPlayerChat.cpp">
      <Filter>npc</Filter>
    </ClCompile>
    <ClCompile Include="Src\KPlayerChatRoom.cpp">
      <Filter>npc</Filter>
    </ClCompile>
    <ClCompile Include="Src\KPlayerDBFuns.cpp">
      <Filter>npc</Filter>
    </ClCompile>
    <ClCompile Include="Src\KPlayerFaction.cpp">
      <Filter>npc</Filter>
    </ClCompile>
    <ClCompile Include="Src\KPlayerMenuState.cpp">
      <Filter>npc</Filter>
    </ClCompile>
    <ClCompile Include="Src\KPlayerPK.cpp">
      <Filter>npc</Filter>
    </ClCompile>
    <ClCompile Include="Src\KPlayerSet.cpp">
      <Filter>npc</Filter>
    </ClCompile>
    <ClCompile Include="Src\KPlayerTask.cpp">
      <Filter>npc</Filter>
    </ClCompile>
    <ClCompile Include="Src\KPlayerTeam.cpp">
      <Filter>npc</Filter>
    </ClCompile>
    <ClCompile Include="Src\KPlayerTong.cpp">
      <Filter>npc</Filter>
    </ClCompile>
    <ClCompile Include="Src\KPlayerTrade.cpp">
      <Filter>npc</Filter>
    </ClCompile>
    <ClCompile Include="Src\KSprControl.cpp">
      <Filter>npc</Filter>
    </ClCompile>
    <ClCompile Include="Src\KTongData.cpp">
      <Filter>npc</Filter>
    </ClCompile>
    <ClCompile Include="Src\KBasPropTbl.CPP">
      <Filter>item</Filter>
    </ClCompile>
    <ClCompile Include="Src\KBuySell.cpp">
      <Filter>item</Filter>
    </ClCompile>
    <ClCompile Include="Src\KInventory.cpp">
      <Filter>item</Filter>
    </ClCompile>
    <ClCompile Include="Src\KItem.cpp">
      <Filter>item</Filter>
    </ClCompile>
    <ClCompile Include="Src\KItemChangeRes.cpp">
      <Filter>item</Filter>
    </ClCompile>
    <ClCompile Include="Src\KItemGenerator.CPP">
      <Filter>item</Filter>
    </ClCompile>
    <ClCompile Include="Src\KItemList.cpp">
      <Filter>item</Filter>
    </ClCompile>
    <ClCompile Include="Src\KItemSet.cpp">
      <Filter>item</Filter>
    </ClCompile>
    <ClCompile Include="Src\KObj.cpp">
      <Filter>item</Filter>
    </ClCompile>
    <ClCompile Include="Src\KObjSet.cpp">
      <Filter>item</Filter>
    </ClCompile>
    <ClCompile Include="Src\KSellItem.cpp">
      <Filter>item</Filter>
    </ClCompile>
    <ClCompile Include="Src\KViewItem.cpp">
      <Filter>item</Filter>
    </ClCompile>
    <ClCompile Include="Src\KCore.cpp">
      <Filter>core</Filter>
    </ClCompile>
    <ClCompile Include="Src\KSubWorld.cpp">
      <Filter>core</Filter>
    </ClCompile>
    <ClCompile Include="Src\KSubWorldSet.cpp">
      <Filter>core</Filter>
    </ClCompile>
    <ClCompile Include="Src\KWeatherMgr.cpp">
      <Filter>core</Filter>
    </ClCompile>
    <ClCompile Include="Src\KRegion.cpp">
      <Filter>region</Filter>
    </ClCompile>
    <ClCompile Include="Src\KWorldMsg.cpp">
      <Filter>msg</Filter>
    </ClCompile>
    <ClCompile Include="Src\KMath.cpp">
      <Filter>math</Filter>
    </ClCompile>
    <ClCompile Include="Src\KGMProcess.cpp">
      <Filter>protocol</Filter>
    </ClCompile>
    <ClCompile Include="Src\KNewProtocolProcess.cpp">
      <Filter>protocol</Filter>
    </ClCompile>
    <ClCompile Include="Src\KProtocol.cpp">
      <Filter>protocol</Filter>
    </ClCompile>
    <ClCompile Include="Src\KProtocolProcess.cpp">
      <Filter>protocol</Filter>
    </ClCompile>
    <ClCompile Include="Src\ScriptFuns.cpp">
      <Filter>script\Á¢¼´ĐÔ½Å±¾</Filter>
    </ClCompile>
    <ClCompile Include="Src\LuaFuns.cpp">
      <Filter>script\µ¥²½ĐÔ½Å±¾</Filter>
    </ClCompile>
    <ClCompile Include="Src\KScriptValueSet.cpp">
      <Filter>script</Filter>
    </ClCompile>
    <ClCompile Include="Src\KSortScript.cpp">
      <Filter>script</Filter>
    </ClCompile>
    <ClCompile Include="src\CoreServerShell.cpp">
      <Filter>coreshell</Filter>
    </ClCompile>
    <ClCompile Include="Src\CoreShell.cpp">
      <Filter>coreshell</Filter>
    </ClCompile>
    <ClCompile Include="Src\Scene\KIpotBranch.cpp">
      <Filter>scene\³¡¾°ÖĐ¶ÔÏóÓë¶ÔÏóÊ÷</Filter>
    </ClCompile>
    <ClCompile Include="Src\Scene\KIpotLeaf.cpp">
      <Filter>scene\³¡¾°ÖĐ¶ÔÏóÓë¶ÔÏóÊ÷</Filter>
    </ClCompile>
    <ClCompile Include="Src\Scene\KIpoTree.cpp">
      <Filter>scene\³¡¾°ÖĐ¶ÔÏóÓë¶ÔÏóÊ÷</Filter>
    </ClCompile>
    <ClCompile Include="Src\Scene\KScenePlaceC.cpp">
      <Filter>scene</Filter>
    </ClCompile>
    <ClCompile Include="Src\Scene\KScenePlaceRegionC.cpp">
      <Filter>scene</Filter>
    </ClCompile>
    <ClCompile Include="Src\Scene\KWeather.cpp">
      <Filter>scene</Filter>
    </ClCompile>
    <ClCompile Include="Src\Scene\SceneMath.cpp">
      <Filter>scene</Filter>
    </ClCompile>
    <ClCompile Include="Src\Scene\ScenePlaceMapC.cpp">
      <Filter>scene</Filter>
    </ClCompile>
    <ClCompile Include="Src\CoreDrawGameObj.cpp">
      <Filter>gameobj</Filter>
    </ClCompile>
    <ClCompile Include="Src\ImgRef.cpp">
      <Filter>gameobj</Filter>
    </ClCompile>
    <ClCompile Include="Src\KMagicDesc.cpp">
      <Filter>magicAttr</Filter>
    </ClCompile>
    <ClCompile Include="Src\KGMCommand.cpp">
      <Filter>GM</Filter>
    </ClCompile>
    <ClCompile Include="Src\KMission.cpp">
      <Filter>task</Filter>
    </ClCompile>
    <ClCompile Include="Src\KTaskFuns.cpp">
      <Filter>task</Filter>
    </ClCompile>
    <ClCompile Include="Src\KOption.cpp">
      <Filter>option</Filter>
    </ClCompile>
    <ClCompile Include="Src\KMapMusic.cpp">
      <Filter>mapmusic</Filter>
    </ClCompile>
    <ClCompile Include="Src\KLadder.cpp">
      <Filter>ÅÅĂû</Filter>
    </ClCompile>
    <ClCompile Include="Src\stdafx.cpp" />
  </ItemGroup>
  <ItemGroup>
    <ClInclude Include="Src\KPartnerSkill.h">
      <Filter>skill\Íæ¼̉³èÎï</Filter>
    </ClInclude>
    <ClInclude Include="Src\KPlayerPartner.h">
      <Filter>skill\Íæ¼̉³èÎï</Filter>
    </ClInclude>
    <ClInclude Include="Src\KThiefSkill.h">
      <Filter>skill\ÍµÇÔ¼¼ÄÜ</Filter>
    </ClInclude>
    <ClInclude Include="Src\KMissle.h">
      <Filter>skill</Filter>
    </ClInclude>
    <ClInclude Include="Src\KMissleMagicAttribsData.h">
      <Filter>skill</Filter>
    </ClInclude>
    <ClInclude Include="Src\KMissleRes.h">
      <Filter>skill</Filter>
    </ClInclude>
    <ClInclude Include="Src\KMissleSet.h">
      <Filter>skill</Filter>
    </ClInclude>
    <ClInclude Include="Src\KSkillList.h">
      <Filter>skill</Filter>
    </ClInclude>
    <ClInclude Include="Src\KSkillManager.h">
      <Filter>skill</Filter>
    </ClInclude>
    <ClInclude Include="Src\KSkills.h">
      <Filter>skill</Filter>
    </ClInclude>
    <ClInclude Include="Src\KSkillSpecial.h">
      <Filter>skill</Filter>
    </ClInclude>
    <ClInclude Include="Src\Skill.h">
      <Filter>skill</Filter>
    </ClInclude>
    <ClInclude Include="Src\SkillDef.h">
      <Filter>skill</Filter>
    </ClInclude>
    <ClInclude Include="Src\KFaction.h">
      <Filter>npc</Filter>
    </ClInclude>
    <ClInclude Include="Src\KGameData.h">
      <Filter>npc</Filter>
    </ClInclude>
    <ClInclude Include="Src\KNpc.h">
      <Filter>npc</Filter>
    </ClInclude>
    <ClInclude Include="Src\KNpcAI.h">
      <Filter>npc</Filter>
    </ClInclude>
    <ClInclude Include="Src\KNpcAttribModify.h">
      <Filter>npc</Filter>
    </ClInclude>
    <ClInclude Include="Src\KNpcDeathCalcExp.h">
      <Filter>npc</Filter>
    </ClInclude>
    <ClInclude Include="Src\KNpcFindPath.h">
      <Filter>npc</Filter>
    </ClInclude>
    <ClInclude Include="Src\KNpcRes.h">
      <Filter>npc</Filter>
    </ClInclude>
    <ClInclude Include="Src\KNpcResList.h">
      <Filter>npc</Filter>
    </ClInclude>
    <ClInclude Include="Src\KNpcResNode.h">
      <Filter>npc</Filter>
    </ClInclude>
    <ClInclude Include="Src\KNpcSet.h">
      <Filter>npc</Filter>
    </ClInclude>
    <ClInclude Include="Src\KNpcTemplate.h">
      <Filter>npc</Filter>
    </ClInclude>
    <ClInclude Include="Src\KPlayer.h">
      <Filter>npc</Filter>
    </ClInclude>
    <ClInclude Include="Src\KPlayerAI.h">
      <Filter>npc</Filter>
    </ClInclude>
    <ClInclude Include="Src\KPlayerChat.h">
      <Filter>npc</Filter>
    </ClInclude>
    <ClInclude Include="Src\KPlayerChatRoom.h">
      <Filter>npc</Filter>
    </ClInclude>
    <ClInclude Include="Src\KPlayerDef.h">
      <Filter>npc</Filter>
    </ClInclude>
    <ClInclude Include="Src\KPlayerFaction.h">
      <Filter>npc</Filter>
    </ClInclude>
    <ClInclude Include="Src\KPlayerMenuState.h">
      <Filter>npc</Filter>
    </ClInclude>
    <ClInclude Include="Src\KPlayerPK.h">
      <Filter>npc</Filter>
    </ClInclude>
    <ClInclude Include="Src\KPlayerSet.h">
      <Filter>npc</Filter>
    </ClInclude>
    <ClInclude Include="Src\KPlayerTask.h">
      <Filter>npc</Filter>
    </ClInclude>
    <ClInclude Include="Src\KPlayerTeam.h">
      <Filter>npc</Filter>
    </ClInclude>
    <ClInclude Include="Src\KPlayerTong.h">
      <Filter>npc</Filter>
    </ClInclude>
    <ClInclude Include="Src\KPlayerTrade.h">
      <Filter>npc</Filter>
    </ClInclude>
    <ClInclude Include="Src\KSprControl.h">
      <Filter>npc</Filter>
    </ClInclude>
    <ClInclude Include="Src\KTongData.h">
      <Filter>npc</Filter>
    </ClInclude>
    <ClInclude Include="Src\KBasPropTbl.h">
      <Filter>item</Filter>
    </ClInclude>
    <ClInclude Include="Src\KBuySell.h">
      <Filter>item</Filter>
    </ClInclude>
    <ClInclude Include="Src\KInventory.h">
      <Filter>item</Filter>
    </ClInclude>
    <ClInclude Include="Src\KItem.h">
      <Filter>item</Filter>
    </ClInclude>
    <ClInclude Include="Src\KItemChangeRes.h">
      <Filter>item</Filter>
    </ClInclude>
    <ClInclude Include="Src\KItemGenerator.h">
      <Filter>item</Filter>
    </ClInclude>
    <ClInclude Include="Src\KItemList.h">
      <Filter>item</Filter>
    </ClInclude>
    <ClInclude Include="Src\KItemSet.h">
      <Filter>item</Filter>
    </ClInclude>
    <ClInclude Include="Src\KObj.h">
      <Filter>item</Filter>
    </ClInclude>
    <ClInclude Include="Src\KObjSet.h">
      <Filter>item</Filter>
    </ClInclude>
    <ClInclude Include="Src\KSellItem.h">
      <Filter>item</Filter>
    </ClInclude>
    <ClInclude Include="Src\KViewItem.h">
      <Filter>item</Filter>
    </ClInclude>
    <ClInclude Include="Src\MyAssert.H">
      <Filter>item</Filter>
    </ClInclude>
    <ClInclude Include="Src\KCore.h">
      <Filter>core</Filter>
    </ClInclude>
    <ClInclude Include="Src\KSubWorld.h">
      <Filter>core</Filter>
    </ClInclude>
    <ClInclude Include="Src\KSubWorldSet.h">
      <Filter>core</Filter>
    </ClInclude>
    <ClInclude Include="Src\KWeatherMgr.h">
      <Filter>core</Filter>
    </ClInclude>
    <ClInclude Include="Src\KRegion.h">
      <Filter>region</Filter>
    </ClInclude>
    <ClInclude Include="Src\KWorldMsg.h">
      <Filter>msg</Filter>
    </ClInclude>
    <ClInclude Include="Src\KMath.h">
      <Filter>math</Filter>
    </ClInclude>
    <ClInclude Include="Src\KNewProtocolProcess.h">
      <Filter>protocol</Filter>
    </ClInclude>
    <ClInclude Include="..\..\Headers\KProtocolDef.h">
      <Filter>protocol</Filter>
    </ClInclude>
    <ClInclude Include="Src\KProtocolProcess.h">
      <Filter>protocol</Filter>
    </ClInclude>
    <ClInclude Include="Src\KScriptValueSet.h">
      <Filter>script</Filter>
    </ClInclude>
    <ClInclude Include="Src\KSortScript.h">
      <Filter>script</Filter>
    </ClInclude>
    <ClInclude Include="Src\LuaFuns.h">
      <Filter>script</Filter>
    </ClInclude>
    <ClInclude Include="Src\CoreObjGenreDef.h">
      <Filter>coreshell</Filter>
    </ClInclude>
    <ClInclude Include="Src\CoreServerDataDef.h">
      <Filter>coreshell</Filter>
    </ClInclude>
    <ClInclude Include="src\CoreServerShell.h">
      <Filter>coreshell</Filter>
    </ClInclude>
    <ClInclude Include="Src\CoreShell.h">
      <Filter>coreshell</Filter>
    </ClInclude>
    <ClInclude Include="Src\CoreUseNameDef.h">
      <Filter>coreshell</Filter>
    </ClInclude>
    <ClInclude Include="Src\GameDataDef.h">
      <Filter>coreshell</Filter>
    </ClInclude>
    <ClInclude Include="Src\KIndexNode.h">
      <Filter>coreshell</Filter>
    </ClInclude>
    <ClInclude Include="Src\MsgGenreDef.h">
      <Filter>coreshell</Filter>
    </ClInclude>
    <ClInclude Include="..\..\Lib\S3DBInterface.h">
      <Filter>Êư¾Ư¿âÏà¹Ø</Filter>
    </ClInclude>
    <ClInclude Include="Src\Scene\KIpotBranch.h">
      <Filter>scene\³¡¾°ÖĐ¶ÔÏóÓë¶ÔÏóÊ÷</Filter>
    </ClInclude>
    <ClInclude Include="Src\Scene\KIpotLeaf.h">
      <Filter>scene\³¡¾°ÖĐ¶ÔÏóÓë¶ÔÏóÊ÷</Filter>
    </ClInclude>
    <ClInclude Include="Src\Scene\KIpoTree.h">
      <Filter>scene\³¡¾°ÖĐ¶ÔÏóÓë¶ÔÏóÊ÷</Filter>
    </ClInclude>
    <ClInclude Include="Src\Scene\KScenePlaceC.h">
      <Filter>scene</Filter>
    </ClInclude>
    <ClInclude Include="Src\Scene\KScenePlaceRegionC.h">
      <Filter>scene</Filter>
    </ClInclude>
    <ClInclude Include="Src\Scene\KWeather.h">
      <Filter>scene</Filter>
    </ClInclude>
    <ClInclude Include="Src\Scene\ObstacleDef.h">
      <Filter>scene</Filter>
    </ClInclude>
    <ClInclude Include="Src\Scene\SceneDataDef.h">
      <Filter>scene</Filter>
    </ClInclude>
    <ClInclude Include="Src\Scene\SceneMath.h">
      <Filter>scene</Filter>
    </ClInclude>
    <ClInclude Include="Src\Scene\ScenePlaceMapC.h">
      <Filter>scene</Filter>
    </ClInclude>
    <ClInclude Include="Src\CoreDrawGameObj.h">
      <Filter>gameobj</Filter>
    </ClInclude>
    <ClInclude Include="Src\ImgRef.h">
      <Filter>gameobj</Filter>
    </ClInclude>
    <ClInclude Include="Src\KMagicAttrib.h">
      <Filter>magicAttr</Filter>
    </ClInclude>
    <ClInclude Include="Src\KMagicDesc.h">
      <Filter>magicAttr</Filter>
    </ClInclude>
    <ClInclude Include="Src\KGMCommand.h">
      <Filter>GM</Filter>
    </ClInclude>
    <ClInclude Include="Src\KLinkArrayTemplate.h">
      <Filter>task</Filter>
    </ClInclude>
    <ClInclude Include="Src\KMission.h">
      <Filter>task</Filter>
    </ClInclude>
    <ClInclude Include="Src\KMissionArray.h">
      <Filter>task</Filter>
    </ClInclude>
    <ClInclude Include="Src\KTaskFuns.h">
      <Filter>task</Filter>
    </ClInclude>
    <ClInclude Include="Src\KOption.h">
      <Filter>option</Filter>
    </ClInclude>
    <ClInclude Include="Src\KMapMusic.h">
      <Filter>mapmusic</Filter>
    </ClInclude>
    <ClInclude Include="Src\KLadder.h">
      <Filter>ÅÅĂû</Filter>
    </ClInclude>
    <ClInclude Include="Src\USBKey\des.h">
      <Filter>USBKey</Filter>
    </ClInclude>
    <ClInclude Include="Src\USBKey\EPASSAPI.H">
      <Filter>USBKey</Filter>
    </ClInclude>
    <ClInclude Include="Src\USBKey\epsJO.h">
      <Filter>USBKey</Filter>
    </ClInclude>
    <ClInclude Include="Src\USBKey\MD5.H">
      <Filter>USBKey</Filter>
    </ClInclude>
    <ClInclude Include="Src\KProtocol.h" />
  </ItemGroup>
  <ItemGroup>
    <Library Include="..\..\Lib\release\engine.lib">
      <Filter>Lib\Release</Filter>
    </Library>
    <Library Include="..\..\Lib\release\common.lib">
      <Filter>Lib\Release</Filter>
    </Library>
    <Library Include="..\..\Lib\debug\engine.lib">
      <Filter>Lib\Debug</Filter>
    </Library>
    <Library Include="..\..\Lib\debug\common.lib">
      <Filter>Lib\Debug</Filter>
    </Library>
    <Library Include="Src\USBKey\EP1KDL20.LIB">
      <Filter>USBKey</Filter>
    </Library>
    <Library Include="Src\USBKey\EpsForJoLib.lib">
      <Filter>USBKey</Filter>
    </Library>
  </ItemGroup>
  <ItemGroup>
    <CustomBuild Include="Src\PlayerPartnerÎÄµµ.txt">
      <Filter>skill\Íæ¼̉³èÎï</Filter>
    </CustomBuild>
    <CustomBuild Include="Src\¼¼ÄÜÏµÍ³.txt">
      <Filter>skill</Filter>
    </CustomBuild>
    <CustomBuild Include="Src\AIÉè¼Æ.txt">
      <Filter>npc</Filter>
    </CustomBuild>
    <CustomBuild Include="Src\NPCÏµÍ³.txt">
      <Filter>npc</Filter>
    </CustomBuild>
    <CustomBuild Include="Protocol.xls">
      <Filter>protocol</Filter>
    </CustomBuild>
    <CustomBuild Include="Src\½Å±¾ËµĂ÷.txt">
      <Filter>script</Filter>
    </CustomBuild>
    <CustomBuild Include="Src\ÈÎÎñÏµÍ³Éè¼ÆÎÄµµ.txt">
      <Filter>task</Filter>
    </CustomBuild>
  </ItemGroup>
</Project>