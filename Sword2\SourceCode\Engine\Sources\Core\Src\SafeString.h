//---------------------------------------------------------------------------
// Sword2 Safe String Operations (c) 2024
//
// File:	SafeString.h
// Date:	2024.07.02
// Code:	AI Assistant
// Desc:	Buffer overflow protection and safe string operations
//---------------------------------------------------------------------------
#ifndef SAFE_STRING_H
#define SAFE_STRING_H

#include <windows.h>
#include <stdio.h>
#include <string.h>

// 安全字符串操作结果
enum SAFE_STRING_RESULT
{
    SSR_SUCCESS = 0,            // 成功
    SSR_BUFFER_TOO_SMALL,       // 缓冲区太小
    SSR_INVALID_PARAMETER,      // 无效参数
    SSR_TRUNCATED,              // 字符串被截断
    SSR_ENCODING_ERROR          // 编码错误
};

// 安全字符串操作类
class CSafeString
{
public:
    // 安全字符串拷贝
    static SAFE_STRING_RESULT SafeStrCopy(char* pDest, size_t nDestSize, const char* pSrc);
    static SAFE_STRING_RESULT SafeStrCopyW(wchar_t* pDest, size_t nDestSize, const wchar_t* pSrc);
    
    // 安全字符串连接
    static SAFE_STRING_RESULT SafeStrCat(char* pDest, size_t nDestSize, const char* pSrc);
    static SAFE_STRING_RESULT SafeStrCatW(wchar_t* pDest, size_t nDestSize, const wchar_t* pSrc);
    
    // 安全格式化字符串
    static SAFE_STRING_RESULT SafeSprintf(char* pDest, size_t nDestSize, const char* pFormat, ...);
    static SAFE_STRING_RESULT SafeSprintfW(wchar_t* pDest, size_t nDestSize, const wchar_t* pFormat, ...);
    
    // 安全字符串比较
    static int SafeStrCmp(const char* pStr1, const char* pStr2, size_t nMaxLen = SIZE_MAX);
    static int SafeStrCmpW(const wchar_t* pStr1, const wchar_t* pStr2, size_t nMaxLen = SIZE_MAX);
    
    // 安全字符串长度
    static size_t SafeStrLen(const char* pStr, size_t nMaxLen = SIZE_MAX);
    static size_t SafeStrLenW(const wchar_t* pStr, size_t nMaxLen = SIZE_MAX);
    
    // 安全内存操作
    static SAFE_STRING_RESULT SafeMemCopy(void* pDest, size_t nDestSize, const void* pSrc, size_t nSrcSize);
    static SAFE_STRING_RESULT SafeMemSet(void* pDest, size_t nDestSize, int nValue, size_t nCount);
    
    // 输入验证
    static BOOL IsValidString(const char* pStr, size_t nMaxLen);
    static BOOL IsValidStringW(const wchar_t* pStr, size_t nMaxLen);
    static BOOL ContainsOnlyPrintable(const char* pStr);
    static BOOL ContainsOnlyAlphaNumeric(const char* pStr);
    
    // 字符串清理
    static void SecureZeroString(char* pStr, size_t nSize);
    static void SecureZeroStringW(wchar_t* pStr, size_t nSize);
    
    // 路径安全检查
    static BOOL IsValidPath(const char* pPath);
    static BOOL IsValidFileName(const char* pFileName);
    static SAFE_STRING_RESULT SanitizePath(char* pPath, size_t nPathSize);

private:
    static BOOL IsValidPointer(const void* pPtr);
    static BOOL IsValidBuffer(const void* pBuffer, size_t nSize);
};

// 缓冲区溢出检测器
class CBufferOverflowDetector
{
public:
    CBufferOverflowDetector();
    ~CBufferOverflowDetector();

    // 缓冲区保护
    static void* AllocateProtectedBuffer(size_t nSize);
    static void FreeProtectedBuffer(void* pBuffer);
    static BOOL CheckBufferIntegrity(void* pBuffer);
    
    // 栈保护
    static void EnableStackProtection();
    static void DisableStackProtection();
    static BOOL CheckStackIntegrity();
    
    // 堆保护
    static void EnableHeapProtection();
    static void DisableHeapProtection();
    
    // 异常处理
    static LONG WINAPI BufferOverflowExceptionHandler(EXCEPTION_POINTERS* pExceptionInfo);

private:
    static const DWORD GUARD_PATTERN_BEGIN = 0xDEADBEEF;
    static const DWORD GUARD_PATTERN_END = 0xBEEFDEAD;
    static const size_t GUARD_SIZE = 16;
    
    struct ProtectedBuffer
    {
        DWORD dwGuardBegin[4];      // 16字节前置保护
        size_t nUserSize;           // 用户数据大小
        // 用户数据区域
        // DWORD dwGuardEnd[4];     // 16字节后置保护（在用户数据后）
    };
    
    static BOOL s_bStackProtectionEnabled;
    static BOOL s_bHeapProtectionEnabled;
    static PVOID s_pOriginalVectoredHandler;
};

// 安全输入验证器
class CInputValidator
{
public:
    // 数值验证
    static BOOL ValidateInteger(const char* pStr, int nMin, int nMax, int* pResult = NULL);
    static BOOL ValidateFloat(const char* pStr, float fMin, float fMax, float* pResult = NULL);
    
    // 字符串验证
    static BOOL ValidateUserName(const char* pUserName);
    static BOOL ValidatePassword(const char* pPassword);
    static BOOL ValidateEmail(const char* pEmail);
    static BOOL ValidateChatMessage(const char* pMessage);
    
    // 网络数据验证
    static BOOL ValidatePacketHeader(const void* pHeader, size_t nHeaderSize);
    static BOOL ValidatePacketData(const void* pData, size_t nDataSize, size_t nMaxSize);
    
    // SQL注入防护
    static BOOL ContainsSQLInjection(const char* pInput);
    static SAFE_STRING_RESULT EscapeSQLString(char* pDest, size_t nDestSize, const char* pSrc);
    
    // XSS防护
    static BOOL ContainsXSS(const char* pInput);
    static SAFE_STRING_RESULT EscapeHTMLString(char* pDest, size_t nDestSize, const char* pSrc);
    
    // 路径遍历防护
    static BOOL ContainsPathTraversal(const char* pPath);
    static SAFE_STRING_RESULT NormalizePath(char* pPath, size_t nPathSize);

private:
    static const char* s_SQLKeywords[];
    static const char* s_XSSPatterns[];
    static const char* s_PathTraversalPatterns[];
    
    static BOOL ContainsPattern(const char* pInput, const char* pPatterns[], size_t nPatternCount);
    static BOOL IsValidCharacter(char c, BOOL bAllowSpecial = FALSE);
};

// 全局安全字符串实例
extern CBufferOverflowDetector g_BufferOverflowDetector;
extern CInputValidator g_InputValidator;

// 安全字符串宏定义
#define SAFE_STRCPY(dest, src) \
    CSafeString::SafeStrCopy(dest, sizeof(dest), src)

#define SAFE_STRCAT(dest, src) \
    CSafeString::SafeStrCat(dest, sizeof(dest), src)

#define SAFE_SPRINTF(dest, format, ...) \
    CSafeString::SafeSprintf(dest, sizeof(dest), format, __VA_ARGS__)

#define SAFE_MEMCPY(dest, src, size) \
    CSafeString::SafeMemCopy(dest, sizeof(dest), src, size)

#define SAFE_MEMSET(dest, value, count) \
    CSafeString::SafeMemSet(dest, sizeof(dest), value, count)

#define VALIDATE_STRING_INPUT(str) \
    (CSafeString::IsValidString(str, 1024) && !g_InputValidator.ContainsSQLInjection(str) && !g_InputValidator.ContainsXSS(str))

#define VALIDATE_PATH_INPUT(path) \
    (CSafeString::IsValidPath(path) && !g_InputValidator.ContainsPathTraversal(path))

// 安全缓冲区类
template<size_t BufferSize>
class CSafeBuffer
{
public:
    CSafeBuffer() 
    {
        SecureZeroMemory(m_Buffer, sizeof(m_Buffer));
        m_nUsed = 0;
    }
    
    ~CSafeBuffer()
    {
        SecureZeroMemory(m_Buffer, sizeof(m_Buffer));
    }
    
    SAFE_STRING_RESULT Append(const char* pData, size_t nDataSize)
    {
        if (!pData || nDataSize == 0)
            return SSR_INVALID_PARAMETER;
            
        if (m_nUsed + nDataSize >= BufferSize)
            return SSR_BUFFER_TOO_SMALL;
            
        memcpy(m_Buffer + m_nUsed, pData, nDataSize);
        m_nUsed += nDataSize;
        m_Buffer[m_nUsed] = '\0';
        
        return SSR_SUCCESS;
    }
    
    SAFE_STRING_RESULT SetString(const char* pStr)
    {
        if (!pStr)
            return SSR_INVALID_PARAMETER;
            
        size_t nLen = strlen(pStr);
        if (nLen >= BufferSize)
            return SSR_BUFFER_TOO_SMALL;
            
        strcpy_s(m_Buffer, BufferSize, pStr);
        m_nUsed = nLen;
        
        return SSR_SUCCESS;
    }
    
    const char* GetString() const { return m_Buffer; }
    size_t GetLength() const { return m_nUsed; }
    size_t GetCapacity() const { return BufferSize - 1; }
    
    void Clear()
    {
        SecureZeroMemory(m_Buffer, sizeof(m_Buffer));
        m_nUsed = 0;
    }

private:
    char m_Buffer[BufferSize];
    size_t m_nUsed;
};

// 常用安全缓冲区类型
typedef CSafeBuffer<256> CSafeString256;
typedef CSafeBuffer<512> CSafeString512;
typedef CSafeBuffer<1024> CSafeString1024;
typedef CSafeBuffer<4096> CSafeString4096;

#endif // SAFE_STRING_H
