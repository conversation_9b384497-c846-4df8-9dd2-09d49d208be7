//---------------------------------------------------------------------------
// Sword2 Task Manager (c) 2024
//
// File:	TaskManager.h
// Date:	2024.07.02
// Code:	AI Assistant
// Desc:	Comprehensive task management system
//---------------------------------------------------------------------------
#ifndef TASK_MANAGER_H
#define TASK_MANAGER_H

#include "TaskSystem.h"
#include "GameDataSystem.h"
#include "LuaScriptEngine.h"
#include "UnifiedLoggingSystem.h"
#include "BusinessMonitor.h"
#include <unordered_map>
#include <memory>
#include <mutex>
#include <shared_mutex>
#include <thread>
#include <atomic>
#include <queue>
#include <functional>

namespace sword2 {

// 任务操作结果
enum class TaskOperationResult : uint8_t
{
    Success = 0,        // 成功
    Failed,             // 失败
    NotFound,           // 未找到
    AlreadyAccepted,    // 已接受
    ConditionNotMet,    // 条件不满足
    NotAccepted,        // 未接受
    NotCompleted,       // 未完成
    AlreadyCompleted,   // 已完成
    Expired,            // 已过期
    CannotCancel,       // 不能取消
    InsufficientMoney,  // 金钱不足
    InventoryFull       // 背包满
};

// 任务事件类型
enum class TaskEvent : uint8_t
{
    Accept = 0,         // 接受任务
    Complete,           // 完成任务
    Submit,             // 提交任务
    Cancel,             // 取消任务
    Expire,             // 任务过期
    Progress,           // 进度更新
    Skip                // 跳过任务
};

// 任务管理器
class TaskManager : public Singleton<TaskManager>
{
public:
    TaskManager()
        : m_running(false), m_nextInstanceId(1), m_updateInterval(std::chrono::seconds(30)) {}
    
    ~TaskManager()
    {
        Stop();
    }
    
    // 启动任务管理器
    bool Start()
    {
        if (m_running) return true;
        
        // 加载任务数据
        if (!LoadTaskData())
        {
            LOG_ERROR("TASK_MGR", "Failed to load task data");
            return false;
        }
        
        m_running = true;
        m_updateThread = std::thread(&TaskManager::UpdateLoop, this);
        
        LOG_INFO("TASK_MGR", "Task manager started with " + std::to_string(m_taskTemplates.size()) + " task templates");
        return true;
    }
    
    // 停止任务管理器
    void Stop()
    {
        if (!m_running) return;
        
        m_running = false;
        if (m_updateThread.joinable())
        {
            m_updateThread.join();
        }
        
        // 清理数据
        {
            std::unique_lock<std::shared_mutex> lock(m_templatesMutex);
            m_taskTemplates.clear();
        }
        
        {
            std::unique_lock<std::shared_mutex> lock(m_instancesMutex);
            m_taskInstances.clear();
            m_playerTasks.clear();
        }
        
        LOG_INFO("TASK_MGR", "Task manager stopped");
    }
    
    // 注册任务模板
    bool RegisterTaskTemplate(const TaskTemplate& taskTemplate)
    {
        std::unique_lock<std::shared_mutex> lock(m_templatesMutex);
        
        if (m_taskTemplates.find(taskTemplate.taskId) != m_taskTemplates.end())
        {
            LOG_WARNING("TASK_MGR", "Task template already exists: " + std::to_string(taskTemplate.taskId));
            return false;
        }
        
        m_taskTemplates[taskTemplate.taskId] = std::make_shared<TaskTemplate>(taskTemplate);
        
        LOG_DEBUG("TASK_MGR", "Registered task template: " + taskTemplate.taskName + " (ID: " + std::to_string(taskTemplate.taskId) + ")");
        return true;
    }
    
    // 获取任务模板
    std::shared_ptr<TaskTemplate> GetTaskTemplate(uint32_t taskId)
    {
        std::shared_lock<std::shared_mutex> lock(m_templatesMutex);
        auto it = m_taskTemplates.find(taskId);
        return (it != m_taskTemplates.end()) ? it->second : nullptr;
    }
    
    // 获取任务模板（按名称）
    std::shared_ptr<TaskTemplate> GetTaskTemplate(const std::string& taskName)
    {
        std::shared_lock<std::shared_mutex> lock(m_templatesMutex);
        for (const auto& [taskId, taskTemplate] : m_taskTemplates)
        {
            if (taskTemplate && taskTemplate->taskName == taskName)
            {
                return taskTemplate;
            }
        }
        return nullptr;
    }
    
    // 接受任务
    TaskOperationResult AcceptTask(uint32_t playerId, uint32_t taskId)
    {
        auto taskTemplate = GetTaskTemplate(taskId);
        if (!taskTemplate)
        {
            LOG_WARNING("TASK_MGR", "Task template not found: " + std::to_string(taskId));
            return TaskOperationResult::NotFound;
        }
        
        // 检查是否已接受
        if (HasTask(playerId, taskId))
        {
            LOG_WARNING("TASK_MGR", "Player " + std::to_string(playerId) + " already has task " + std::to_string(taskId));
            return TaskOperationResult::AlreadyAccepted;
        }
        
        // 检查接受条件
        auto player = GET_ONLINE_PLAYER(playerId);
        if (!player || !taskTemplate->CanAccept(*player))
        {
            LOG_WARNING("TASK_MGR", "Player " + std::to_string(playerId) + " cannot accept task " + std::to_string(taskId));
            return TaskOperationResult::ConditionNotMet;
        }
        
        // 创建任务实例
        uint32_t instanceId = m_nextInstanceId++;
        auto instance = std::make_unique<TaskInstance>(instanceId, taskId, playerId);
        instance->state = TaskState::InProgress;
        instance->entities = taskTemplate->entities; // 复制任务实体
        
        // 设置过期时间
        if (taskTemplate->timeLimit > 0)
        {
            instance->SetExpireTime(taskTemplate->timeLimit);
        }
        
        {
            std::unique_lock<std::shared_mutex> lock(m_instancesMutex);
            m_taskInstances[instanceId] = std::move(instance);
            m_playerTasks[playerId].push_back(instanceId);
        }
        
        // 触发任务事件
        TriggerTaskEvent(TaskEvent::Accept, instanceId, playerId);
        
        LOG_INFO("TASK_MGR", "Player " + std::to_string(playerId) + " accepted task " + std::to_string(taskId) + 
                " (instance: " + std::to_string(instanceId) + ")");
        return TaskOperationResult::Success;
    }
    
    // 完成任务
    TaskOperationResult CompleteTask(uint32_t playerId, uint32_t taskId)
    {
        auto instance = GetPlayerTask(playerId, taskId);
        if (!instance)
        {
            LOG_WARNING("TASK_MGR", "Player " + std::to_string(playerId) + " does not have task " + std::to_string(taskId));
            return TaskOperationResult::NotAccepted;
        }
        
        if (instance->state != TaskState::InProgress)
        {
            LOG_WARNING("TASK_MGR", "Task " + std::to_string(taskId) + " is not in progress for player " + std::to_string(playerId));
            return TaskOperationResult::Failed;
        }
        
        if (instance->IsExpired())
        {
            LOG_WARNING("TASK_MGR", "Task " + std::to_string(taskId) + " has expired for player " + std::to_string(playerId));
            return TaskOperationResult::Expired;
        }
        
        // 检查完成条件
        if (!instance->IsCompleted())
        {
            LOG_WARNING("TASK_MGR", "Task " + std::to_string(taskId) + " is not completed for player " + std::to_string(playerId));
            return TaskOperationResult::NotCompleted;
        }
        
        auto taskTemplate = GetTaskTemplate(taskId);
        if (!taskTemplate)
        {
            return TaskOperationResult::NotFound;
        }
        
        auto player = GET_ONLINE_PLAYER(playerId);
        if (!player || !taskTemplate->CanComplete(*player))
        {
            LOG_WARNING("TASK_MGR", "Player " + std::to_string(playerId) + " cannot complete task " + std::to_string(taskId));
            return TaskOperationResult::ConditionNotMet;
        }
        
        // 标记为完成
        instance->state = TaskState::Completed;
        instance->completeTime = std::chrono::system_clock::now();
        
        // 触发任务事件
        TriggerTaskEvent(TaskEvent::Complete, instance->instanceId, playerId);
        
        LOG_INFO("TASK_MGR", "Player " + std::to_string(playerId) + " completed task " + std::to_string(taskId));
        return TaskOperationResult::Success;
    }
    
    // 提交任务
    TaskOperationResult SubmitTask(uint32_t playerId, uint32_t taskId)
    {
        auto instance = GetPlayerTask(playerId, taskId);
        if (!instance)
        {
            return TaskOperationResult::NotAccepted;
        }
        
        if (instance->state != TaskState::Completed)
        {
            return TaskOperationResult::NotCompleted;
        }
        
        auto taskTemplate = GetTaskTemplate(taskId);
        if (!taskTemplate)
        {
            return TaskOperationResult::NotFound;
        }
        
        // 发放奖励
        TaskOperationResult rewardResult = GiveTaskRewards(playerId, *taskTemplate);
        if (rewardResult != TaskOperationResult::Success)
        {
            return rewardResult;
        }
        
        // 标记为已提交
        instance->state = TaskState::Submitted;
        
        // 触发任务事件
        TriggerTaskEvent(TaskEvent::Submit, instance->instanceId, playerId);
        
        // 自动开始后续任务
        if (taskTemplate->nextTaskId != 0)
        {
            AcceptTask(playerId, taskTemplate->nextTaskId);
        }
        
        // 处理分支任务
        for (uint32_t branchTaskId : taskTemplate->branchTasks)
        {
            AcceptTask(playerId, branchTaskId);
        }
        
        LOG_INFO("TASK_MGR", "Player " + std::to_string(playerId) + " submitted task " + std::to_string(taskId));
        return TaskOperationResult::Success;
    }
    
    // 取消任务
    TaskOperationResult CancelTask(uint32_t playerId, uint32_t taskId)
    {
        auto instance = GetPlayerTask(playerId, taskId);
        if (!instance)
        {
            return TaskOperationResult::NotAccepted;
        }
        
        auto taskTemplate = GetTaskTemplate(taskId);
        if (!taskTemplate)
        {
            return TaskOperationResult::NotFound;
        }
        
        if (!taskTemplate->canCancel)
        {
            LOG_WARNING("TASK_MGR", "Task " + std::to_string(taskId) + " cannot be cancelled");
            return TaskOperationResult::CannotCancel;
        }
        
        // 检查取消费用
        if (taskTemplate->cancelCost > 0)
        {
            auto player = GET_ONLINE_PLAYER(playerId);
            if (!player || player->money < taskTemplate->cancelCost)
            {
                return TaskOperationResult::InsufficientMoney;
            }
            
            // 扣除费用
            player->money -= taskTemplate->cancelCost;
        }
        
        // 标记为已取消
        instance->state = TaskState::Cancelled;
        
        // 触发任务事件
        TriggerTaskEvent(TaskEvent::Cancel, instance->instanceId, playerId);
        
        LOG_INFO("TASK_MGR", "Player " + std::to_string(playerId) + " cancelled task " + std::to_string(taskId));
        return TaskOperationResult::Success;
    }
    
    // 跳过任务
    TaskOperationResult SkipTask(uint32_t playerId, uint32_t taskId)
    {
        auto instance = GetPlayerTask(playerId, taskId);
        if (!instance)
        {
            return TaskOperationResult::NotAccepted;
        }
        
        auto taskTemplate = GetTaskTemplate(taskId);
        if (!taskTemplate)
        {
            return TaskOperationResult::NotFound;
        }
        
        if (!taskTemplate->canSkip)
        {
            LOG_WARNING("TASK_MGR", "Task " + std::to_string(taskId) + " cannot be skipped");
            return TaskOperationResult::Failed;
        }
        
        // 检查跳过费用
        if (taskTemplate->skipCost > 0)
        {
            auto player = GET_ONLINE_PLAYER(playerId);
            if (!player || player->money < taskTemplate->skipCost)
            {
                return TaskOperationResult::InsufficientMoney;
            }
            
            // 扣除费用
            player->money -= taskTemplate->skipCost;
        }
        
        // 直接完成任务
        instance->state = TaskState::Completed;
        instance->completeTime = std::chrono::system_clock::now();
        
        // 完成所有实体
        for (auto& entity : instance->entities)
        {
            entity.currentProgress = entity.targetProgress;
        }
        
        // 触发任务事件
        TriggerTaskEvent(TaskEvent::Skip, instance->instanceId, playerId);
        
        LOG_INFO("TASK_MGR", "Player " + std::to_string(playerId) + " skipped task " + std::to_string(taskId));
        return TaskOperationResult::Success;
    }
    
    // 更新任务进度
    bool UpdateTaskProgress(uint32_t playerId, uint32_t taskId, size_t entityIndex, uint32_t amount = 1)
    {
        auto instance = GetPlayerTask(playerId, taskId);
        if (!instance || instance->state != TaskState::InProgress)
        {
            return false;
        }
        
        if (instance->IsExpired())
        {
            instance->state = TaskState::Expired;
            TriggerTaskEvent(TaskEvent::Expire, instance->instanceId, playerId);
            return false;
        }
        
        bool completed = instance->UpdateEntityProgress(entityIndex, amount);
        
        // 触发进度事件
        TriggerTaskEvent(TaskEvent::Progress, instance->instanceId, playerId);
        
        if (completed)
        {
            LOG_INFO("TASK_MGR", "Task entity " + std::to_string(entityIndex) + " completed for player " + 
                    std::to_string(playerId) + " task " + std::to_string(taskId));
        }
        
        return completed;
    }
    
    // 检查玩家是否有任务
    bool HasTask(uint32_t playerId, uint32_t taskId)
    {
        return GetPlayerTask(playerId, taskId) != nullptr;
    }
    
    // 获取玩家任务
    std::shared_ptr<TaskInstance> GetPlayerTask(uint32_t playerId, uint32_t taskId)
    {
        std::shared_lock<std::shared_mutex> lock(m_instancesMutex);
        
        auto it = m_playerTasks.find(playerId);
        if (it != m_playerTasks.end())
        {
            for (uint32_t instanceId : it->second)
            {
                auto instIt = m_taskInstances.find(instanceId);
                if (instIt != m_taskInstances.end() && instIt->second->templateId == taskId)
                {
                    return instIt->second;
                }
            }
        }
        
        return nullptr;
    }
    
    // 获取玩家所有任务
    std::vector<std::shared_ptr<TaskInstance>> GetPlayerTasks(uint32_t playerId, TaskState state = TaskState::InProgress)
    {
        std::vector<std::shared_ptr<TaskInstance>> tasks;
        
        std::shared_lock<std::shared_mutex> lock(m_instancesMutex);
        
        auto it = m_playerTasks.find(playerId);
        if (it != m_playerTasks.end())
        {
            for (uint32_t instanceId : it->second)
            {
                auto instIt = m_taskInstances.find(instanceId);
                if (instIt != m_taskInstances.end())
                {
                    auto& instance = instIt->second;
                    if (state == TaskState::NotStarted || instance->state == state)
                    {
                        tasks.push_back(instance);
                    }
                }
            }
        }
        
        return tasks;
    }
    
    // 获取可接受的任务
    std::vector<uint32_t> GetAvailableTasks(uint32_t playerId)
    {
        std::vector<uint32_t> availableTasks;
        
        auto player = GET_ONLINE_PLAYER(playerId);
        if (!player) return availableTasks;
        
        std::shared_lock<std::shared_mutex> lock(m_templatesMutex);
        
        for (const auto& [taskId, taskTemplate] : m_taskTemplates)
        {
            if (taskTemplate && !HasTask(playerId, taskId) && taskTemplate->CanAccept(*player))
            {
                availableTasks.push_back(taskId);
            }
        }
        
        return availableTasks;
    }

private:
    std::atomic<bool> m_running;
    std::thread m_updateThread;
    std::chrono::seconds m_updateInterval;
    
    // 任务数据
    mutable std::shared_mutex m_templatesMutex;
    std::unordered_map<uint32_t, std::shared_ptr<TaskTemplate>> m_taskTemplates;
    
    mutable std::shared_mutex m_instancesMutex;
    std::unordered_map<uint32_t, std::shared_ptr<TaskInstance>> m_taskInstances;
    std::unordered_map<uint32_t, std::vector<uint32_t>> m_playerTasks; // playerId -> instanceIds
    
    std::atomic<uint32_t> m_nextInstanceId;
    
    // 更新循环
    void UpdateLoop()
    {
        while (m_running)
        {
            try
            {
                UpdateExpiredTasks();
                std::this_thread::sleep_for(m_updateInterval);
            }
            catch (const std::exception& e)
            {
                LOG_ERROR("TASK_MGR", std::string("Error in update loop: ") + e.what());
            }
        }
    }
    
    void UpdateExpiredTasks()
    {
        std::vector<uint32_t> expiredTasks;
        
        {
            std::shared_lock<std::shared_mutex> lock(m_instancesMutex);
            for (const auto& [instanceId, instance] : m_taskInstances)
            {
                if (instance && instance->state == TaskState::InProgress && instance->IsExpired())
                {
                    expiredTasks.push_back(instanceId);
                }
            }
        }
        
        // 处理过期任务
        for (uint32_t instanceId : expiredTasks)
        {
            std::shared_lock<std::shared_mutex> lock(m_instancesMutex);
            auto it = m_taskInstances.find(instanceId);
            if (it != m_taskInstances.end())
            {
                auto& instance = it->second;
                instance->state = TaskState::Expired;
                
                TriggerTaskEvent(TaskEvent::Expire, instanceId, instance->playerId);
                
                LOG_INFO("TASK_MGR", "Task " + std::to_string(instance->templateId) + 
                        " expired for player " + std::to_string(instance->playerId));
            }
        }
    }
    
    bool LoadTaskData()
    {
        LOG_INFO("TASK_MGR", "Loading task data...");
        
        // 加载任务配置文件
        LoadTaskTemplates();
        LoadTaskEntities();
        LoadTaskAwards();
        LoadTaskTalks();
        
        LOG_INFO("TASK_MGR", "Loaded " + std::to_string(m_taskTemplates.size()) + " task templates");
        return true;
    }
    
    void LoadTaskTemplates()
    {
        // 加载全局任务配置文件 gtask_main.txt
        std::string mainTaskFile = "../../../Server/settings/gtask/gtask_main.txt";

        // 这里应该解析gtask_main.txt文件
        // 暂时创建一些示例任务
        CreateExampleTasks();
    }

    void LoadTaskEntities()
    {
        // 加载任务实体配置文件 task_entity.txt
        std::string entityFile = "../../../Server/settings/task/task_entity.txt";

        // 解析任务实体配置
        LOG_DEBUG("TASK_MGR", "Loading task entities from: " + entityFile);
    }

    void LoadTaskAwards()
    {
        // 加载任务奖励配置文件 task_award.txt
        std::string awardFile = "../../../Server/settings/task/task_award.txt";

        // 解析任务奖励配置
        LOG_DEBUG("TASK_MGR", "Loading task awards from: " + awardFile);
    }

    void LoadTaskTalks()
    {
        // 加载任务对话配置文件 task_talk.txt
        std::string talkFile = "../../../Server/settings/task/task_talk.txt";

        // 解析任务对话配置
        LOG_DEBUG("TASK_MGR", "Loading task talks from: " + talkFile);
    }

    void CreateExampleTasks()
    {
        std::unique_lock<std::shared_mutex> lock(m_templatesMutex);

        // 创建新手任务链
        {
            TaskTemplate task(1001, "新手指导", TaskType::Main);
            task.description = "学习基本的游戏操作";
            task.fromNpc = "新手导师";
            task.toNpc = "新手导师";
            task.level = 1;
            task.timeLimit = 3600; // 1小时

            // 开始条件
            task.startConditions.emplace_back(TaskConditionType::Level, "等级达到1级", 1);

            // 任务实体
            task.entities.emplace_back(TaskEntityType::TalkNpc, "与新手导师对话", 1);
            task.entities.emplace_back(TaskEntityType::ReachLocation, "到达练功场", 1);

            // 任务奖励
            task.awards.emplace_back(TaskAwardType::Experience, 100, "100点经验");
            task.awards.emplace_back(TaskAwardType::Money, 50, "50金钱");
            task.awards.emplace_back(TaskAwardType::TaskStart, 0, "开始下一个任务");
            task.awards.back().taskString = "基础战斗训练";

            // 任务对话
            task.talkData.acceptText = "欢迎来到武侠世界！让我来教你基本操作。";
            task.talkData.completeText = "很好！你已经掌握了基本操作。";
            task.talkData.incompleteText = "请按照指示完成任务。";

            task.nextTaskId = 1002;
            task.scriptPath = "tasks/newbie_guide.lua";

            m_taskTemplates[task.taskId] = std::make_shared<TaskTemplate>(task);
        }

        // 创建基础战斗训练任务
        {
            TaskTemplate task(1002, "基础战斗训练", TaskType::Main);
            task.description = "学习基本的战斗技巧";
            task.fromNpc = "武术教头";
            task.toNpc = "武术教头";
            task.level = 1;
            task.timeLimit = 1800; // 30分钟

            // 开始条件
            task.startConditions.emplace_back(TaskConditionType::TaskComplete, "完成新手指导", 0);
            task.startConditions.back().stringValue = "新手指导";

            // 任务实体
            task.entities.emplace_back(TaskEntityType::KillNpc, "击败练习木人", 3);
            task.entities.emplace_back(TaskEntityType::UseItem, "使用治疗药水", 1);

            // 任务奖励
            task.awards.emplace_back(TaskAwardType::Experience, 200, "200点经验");
            task.awards.emplace_back(TaskAwardType::Item, 1, "新手剑");
            task.awards.back().genre = 1; task.awards.back().detail = 1; task.awards.back().particular = 1;

            task.prevTaskId = 1001;
            task.nextTaskId = 1003;
            task.scriptPath = "tasks/basic_combat.lua";

            m_taskTemplates[task.taskId] = std::make_shared<TaskTemplate>(task);
        }

        // 创建收集任务
        {
            TaskTemplate task(2001, "草药收集", TaskType::Side);
            task.description = "为药师收集草药";
            task.fromNpc = "药师";
            task.toNpc = "药师";
            task.level = 5;
            task.timeLimit = 7200; // 2小时

            // 开始条件
            task.startConditions.emplace_back(TaskConditionType::Level, "等级达到5级", 5);

            // 任务实体
            task.entities.emplace_back(TaskEntityType::CollectItem, "收集草药", 10);

            // 任务奖励
            task.awards.emplace_back(TaskAwardType::Experience, 300, "300点经验");
            task.awards.emplace_back(TaskAwardType::Money, 100, "100金钱");
            task.awards.emplace_back(TaskAwardType::Item, 3, "小还丹");
            task.awards.back().genre = 3; task.awards.back().detail = 1; task.awards.back().particular = 1;

            task.canCancel = true;
            task.scriptPath = "tasks/herb_collection.lua";

            m_taskTemplates[task.taskId] = std::make_shared<TaskTemplate>(task);
        }

        // 创建日常任务
        {
            TaskTemplate task(3001, "每日修炼", TaskType::Daily);
            task.description = "完成每日的修炼任务";
            task.fromNpc = "修炼导师";
            task.toNpc = "修炼导师";
            task.level = 10;
            task.timeLimit = 86400; // 24小时

            // 开始条件
            task.startConditions.emplace_back(TaskConditionType::Level, "等级达到10级", 10);

            // 任务实体
            task.entities.emplace_back(TaskEntityType::KillNpc, "击败野怪", 20);
            task.entities.emplace_back(TaskEntityType::UseItem, "使用修炼丹", 1);

            // 任务奖励
            task.awards.emplace_back(TaskAwardType::Experience, 500, "500点经验");
            task.awards.emplace_back(TaskAwardType::Reputation, 10, "10点声望");

            task.canCancel = true;
            task.scriptPath = "tasks/daily_practice.lua";

            m_taskTemplates[task.taskId] = std::make_shared<TaskTemplate>(task);
        }

        // 创建门派任务
        {
            TaskTemplate task(4001, "少林基础功法", TaskType::Chain);
            task.description = "学习少林基础功法";
            task.fromNpc = "少林长老";
            task.toNpc = "少林长老";
            task.level = 15;

            // 开始条件
            task.startConditions.emplace_back(TaskConditionType::Level, "等级达到15级", 15);
            task.startConditions.emplace_back(TaskConditionType::Series, "少林门派", static_cast<int>(PlayerSeries::Shaolin));

            // 任务实体
            task.entities.emplace_back(TaskEntityType::TalkNpc, "与少林长老对话", 1);
            task.entities.emplace_back(TaskEntityType::ReachLocation, "到达藏经阁", 1);
            task.entities.emplace_back(TaskEntityType::FindGoods, "获得基础心法", 1);

            // 任务奖励
            task.awards.emplace_back(TaskAwardType::Experience, 800, "800点经验");
            task.awards.emplace_back(TaskAwardType::Skill, 1, "基础内功");
            task.awards.emplace_back(TaskAwardType::TaskStart, 0, "开始进阶任务");
            task.awards.back().taskString = "少林进阶功法";

            task.nextTaskId = 4002;
            task.scriptPath = "tasks/shaolin_basic.lua";

            m_taskTemplates[task.taskId] = std::make_shared<TaskTemplate>(task);
        }
    }

    TaskOperationResult GiveTaskRewards(uint32_t playerId, const TaskTemplate& taskTemplate)
    {
        return GIVE_TASK_REWARDS(playerId, taskTemplate.awards);
    }

    void TriggerTaskEvent(TaskEvent event, uint32_t instanceId, uint32_t playerId)
    {
        TRIGGER_TASK_EVENT(event, instanceId, playerId);
    }

    // 获取任务实例（内部使用）
    std::shared_ptr<TaskInstance> GetTaskInstance(uint32_t instanceId)
    {
        std::shared_lock<std::shared_mutex> lock(m_instancesMutex);
        auto it = m_taskInstances.find(instanceId);
        return (it != m_taskInstances.end()) ? it->second : nullptr;
    }
};

} // namespace sword2

// 全局任务管理器访问
#define TASK_MANAGER() sword2::TaskManager::getInstance()

// 便捷宏定义
#define START_TASK_SYSTEM() TASK_MANAGER().Start()
#define STOP_TASK_SYSTEM() TASK_MANAGER().Stop()

#define REGISTER_TASK_TEMPLATE(taskTemplate) TASK_MANAGER().RegisterTaskTemplate(taskTemplate)
#define GET_TASK_TEMPLATE(taskId) TASK_MANAGER().GetTaskTemplate(taskId)

#define ACCEPT_TASK(playerId, taskId) TASK_MANAGER().AcceptTask(playerId, taskId)
#define COMPLETE_TASK(playerId, taskId) TASK_MANAGER().CompleteTask(playerId, taskId)
#define SUBMIT_TASK(playerId, taskId) TASK_MANAGER().SubmitTask(playerId, taskId)
#define CANCEL_TASK(playerId, taskId) TASK_MANAGER().CancelTask(playerId, taskId)
#define SKIP_TASK(playerId, taskId) TASK_MANAGER().SkipTask(playerId, taskId)

#define UPDATE_TASK_PROGRESS(playerId, taskId, entityIndex, amount) TASK_MANAGER().UpdateTaskProgress(playerId, taskId, entityIndex, amount)
#define HAS_TASK(playerId, taskId) TASK_MANAGER().HasTask(playerId, taskId)
#define GET_PLAYER_TASK(playerId, taskId) TASK_MANAGER().GetPlayerTask(playerId, taskId)
#define GET_PLAYER_TASKS(playerId, state) TASK_MANAGER().GetPlayerTasks(playerId, state)
#define GET_AVAILABLE_TASKS(playerId) TASK_MANAGER().GetAvailableTasks(playerId)

#endif // TASK_MANAGER_H
