//---------------------------------------------------------------------------
// Sword2 Design Pattern Integration Example (c) 2024
//
// File:	DesignPatternIntegration.cpp
// Date:	2024.07.02
// Code:	AI Assistant
// Desc:	Demonstration of design patterns working together in practice
//---------------------------------------------------------------------------

#include "KCore.h"
#include "StrategyPattern.h"
#include "CommandPattern.h"
#include "StatePattern.h"
#include "DecoratorPattern.h"
#include "AdapterPattern.h"

// 全局设计模式管理器实例
sword2::StrategyManager<sword2::AIBehavior::AIContext, std::string> g_AIBehaviorManager;
sword2::StrategyManager<sword2::Rendering::RenderContext> g_RenderStrategyManager;
sword2::StrategyManager<sword2::Network::NetworkContext, bool> g_NetworkStrategyManager;

sword2::CommandInvoker g_CommandInvoker;
sword2::AsyncCommandExecutor g_AsyncCommandExecutor;

namespace sword2 {

// 游戏系统集成示例
class GameSystemIntegration
{
public:
    GameSystemIntegration()
    {
        InitializeDesignPatterns();
    }
    
    void InitializeDesignPatterns()
    {
        // 初始化策略模式
        InitializeStrategies();
        
        // 初始化状态机
        InitializeStateMachines();
        
        // 初始化装饰器
        InitializeDecorators();
        
        // 初始化适配器
        InitializeAdapters();
        
        // 启动异步命令执行器
        g_AsyncCommandExecutor.Start();
        
        printf("[DESIGN_PATTERNS] All design patterns initialized successfully\n");
    }
    
    void InitializeStrategies()
    {
        // 注册AI行为策略
        REGISTER_AI_STRATEGY("Attack", AIBehavior::AttackStrategy);
        REGISTER_AI_STRATEGY("Flee", AIBehavior::FleeStrategy);
        REGISTER_AI_STRATEGY("Patrol", AIBehavior::PatrolStrategy);
        
        // 注册渲染策略
        REGISTER_RENDER_STRATEGY("HighQuality", Rendering::HighQualityStrategy);
        REGISTER_RENDER_STRATEGY("Performance", Rendering::PerformanceStrategy);
        REGISTER_RENDER_STRATEGY("Adaptive", Rendering::AdaptiveStrategy);
        
        // 注册网络策略
        REGISTER_NETWORK_STRATEGY("TCP", Network::TCPStrategy);
        REGISTER_NETWORK_STRATEGY("UDP", Network::UDPStrategy);
        
        printf("[STRATEGY] All strategies registered\n");
    }
    
    void InitializeStateMachines()
    {
        // 创建玩家状态机
        m_playerContext = std::make_unique<PlayerContext>();
        m_playerStateMachine = CREATE_STATE_MACHINE(*m_playerContext)
            .AddState(std::make_unique<IdleState>())
            .AddState(std::make_unique<MovingState>())
            .AddState(std::make_unique<JumpingState>())
            .AddTransition(ADD_STATE_TRANSITION("Idle", "Moving", "move"))
            .AddTransition(ADD_STATE_TRANSITION("Moving", "Idle", "stop"))
            .AddTransition(ADD_STATE_TRANSITION("Idle", "Jumping", "jump"))
            .AddTransition(ADD_STATE_TRANSITION("Moving", "Jumping", "jump"))
            .Build();
        
        // 启动状态机
        m_playerStateMachine->Start("Idle");
        
        printf("[STATE_MACHINE] Player state machine initialized\n");
    }
    
    void InitializeDecorators()
    {
        // 创建带装饰器的渲染组件
        auto basicRenderer = std::make_unique<BasicRenderComponent>("player_mesh");
        
        m_decoratedRenderer = MAKE_DECORATOR_BUILDER(IRenderComponent, std::move(basicRenderer))
            .AddDecorator<ShadowDecorator>()
            .AddDecorator<GlowDecorator>(0.8f)
            .AddDecorator<AnimationDecorator>()
            .Build();
        
        // 创建带装饰器的网络组件
        auto basicNetwork = std::make_unique<BasicNetworkComponent>();
        
        m_decoratedNetwork = MAKE_DECORATOR_BUILDER(INetworkComponent, std::move(basicNetwork))
            .AddDecorator<LoggingDecorator>()
            .AddDecorator<EncryptionDecorator>()
            .AddDecorator<CompressionDecorator>()
            .Build();
        
        printf("[DECORATOR] Decorated components created\n");
    }
    
    void InitializeAdapters()
    {
        // 创建音频适配器
        auto legacyAudio = std::make_unique<LegacyAudioSystem>();
        m_audioAdapter = MAKE_AUDIO_ADAPTER(std::move(legacyAudio));
        
        // 创建渲染适配器
        auto legacyRenderer = std::make_unique<LegacyRenderSystem>();
        m_renderAdapter = MAKE_RENDER_ADAPTER(std::move(legacyRenderer));
        
        // 创建网络适配器
        auto legacyNetwork = std::make_unique<LegacyNetworkAPI>();
        m_networkAdapter = MAKE_NETWORK_ADAPTER(std::move(legacyNetwork));
        
        printf("[ADAPTER] Legacy system adapters created\n");
    }
    
    void DemonstratePatterns()
    {
        printf("\n[DEMO] Starting design pattern demonstration...\n");
        
        // 演示策略模式
        DemonstrateStrategyPattern();
        
        // 演示命令模式
        DemonstrateCommandPattern();
        
        // 演示状态模式
        DemonstrateStatePattern();
        
        // 演示装饰器模式
        DemonstrateDecoratorPattern();
        
        // 演示适配器模式
        DemonstrateAdapterPattern();
        
        printf("[DEMO] Design pattern demonstration completed\n");
    }
    
private:
    // 状态机相关
    std::unique_ptr<PlayerContext> m_playerContext;
    std::unique_ptr<StateMachine<PlayerContext>> m_playerStateMachine;
    
    // 装饰器相关
    std::unique_ptr<IRenderComponent> m_decoratedRenderer;
    std::unique_ptr<INetworkComponent> m_decoratedNetwork;
    
    // 适配器相关
    std::unique_ptr<IModernAudioPlayer> m_audioAdapter;
    std::unique_ptr<IModernRenderer> m_renderAdapter;
    std::unique_ptr<IModernNetworkClient> m_networkAdapter;
    
    void DemonstrateStrategyPattern()
    {
        printf("\n--- Strategy Pattern Demo ---\n");
        
        // AI行为策略演示
        AIBehavior::AIContext aiContext;
        aiContext.health = 20.0f; // 低血量
        aiContext.playerVisible = true;
        
        std::string behavior = EXECUTE_AI_STRATEGY("Flee", aiContext);
        printf("AI Behavior: %s\n", behavior.c_str());
        
        // 渲染策略演示
        Rendering::RenderContext renderContext;
        renderContext.frameTime = 20.0f; // 低帧率
        
        EXECUTE_RENDER_STRATEGY("Adaptive", renderContext);
        printf("Render Quality: %s\n", renderContext.qualityLevel.c_str());
    }
    
    void DemonstrateCommandPattern()
    {
        printf("\n--- Command Pattern Demo ---\n");
        
        // 创建游戏对象
        auto gameObject = GAME_OBJECTS().CreateGameObject("TestObject");
        gameObject->AddComponent<TransformComponent>();
        
        // 创建移动命令
        auto moveCommand = MAKE_MOVE_COMMAND(gameObject, TransformComponent::Vector3(10.0f, 0.0f, 0.0f));
        
        // 执行命令
        EXECUTE_COMMAND(std::move(moveCommand));
        printf("Object moved to: (%.2f, %.2f, %.2f)\n", 
               gameObject->Transform()->position.x,
               gameObject->Transform()->position.y,
               gameObject->Transform()->position.z);
        
        // 撤销命令
        if (CAN_UNDO())
        {
            UNDO_COMMAND();
            printf("Move undone. Object position: (%.2f, %.2f, %.2f)\n",
                   gameObject->Transform()->position.x,
                   gameObject->Transform()->position.y,
                   gameObject->Transform()->position.z);
        }
        
        // 重做命令
        if (CAN_REDO())
        {
            REDO_COMMAND();
            printf("Move redone. Object position: (%.2f, %.2f, %.2f)\n",
                   gameObject->Transform()->position.x,
                   gameObject->Transform()->position.y,
                   gameObject->Transform()->position.z);
        }
    }
    
    void DemonstrateStatePattern()
    {
        printf("\n--- State Pattern Demo ---\n");
        
        printf("Current state: %s\n", m_playerStateMachine->GetCurrentStateName().c_str());
        
        // 发送移动事件
        m_playerStateMachine->SendEvent("move");
        printf("After move event: %s\n", m_playerStateMachine->GetCurrentStateName().c_str());
        
        // 发送跳跃事件
        m_playerStateMachine->SendEvent("jump");
        printf("After jump event: %s\n", m_playerStateMachine->GetCurrentStateName().c_str());
        
        // 更新状态机
        m_playerStateMachine->Update(0.016f);
    }
    
    void DemonstrateDecoratorPattern()
    {
        printf("\n--- Decorator Pattern Demo ---\n");
        
        // 渲染装饰器演示
        printf("Rendering with decorators:\n");
        m_decoratedRenderer->Render();
        
        // 网络装饰器演示
        printf("\nNetwork with decorators:\n");
        m_decoratedNetwork->SendMessage("Hello, World!");
        std::string received = m_decoratedNetwork->ReceiveMessage();
        printf("Received: %s\n", received.c_str());
    }
    
    void DemonstrateAdapterPattern()
    {
        printf("\n--- Adapter Pattern Demo ---\n");
        
        // 音频适配器演示
        printf("Audio adapter demo:\n");
        m_audioAdapter->Play("background_music.mp3");
        m_audioAdapter->SetVolume(0.7f);
        printf("Volume: %.2f\n", m_audioAdapter->GetVolume());
        m_audioAdapter->Stop();
        
        // 渲染适配器演示
        printf("\nRender adapter demo:\n");
        m_renderAdapter->BeginFrame();
        m_renderAdapter->SetShader("basic_shader");
        m_renderAdapter->SetTexture("player_texture");
        
        float transform[16] = {1,0,0,0, 0,1,0,0, 0,0,1,0, 5,10,15,1}; // 简单变换矩阵
        m_renderAdapter->DrawMesh("player_mesh", transform);
        m_renderAdapter->EndFrame();
        
        // 网络适配器演示
        printf("\nNetwork adapter demo:\n");
        if (m_networkAdapter->Connect("127.0.0.1", 8080))
        {
            std::vector<uint8_t> data = {'H', 'e', 'l', 'l', 'o'};
            m_networkAdapter->SendData(data);
            
            auto received = m_networkAdapter->ReceiveData();
            printf("Received %zu bytes\n", received.size());
            
            m_networkAdapter->Disconnect();
        }
    }
};

} // namespace sword2

// 全局设计模式集成实例
sword2::GameSystemIntegration g_DesignPatternDemo;

// 初始化所有设计模式
void InitializeDesignPatterns()
{
    g_DesignPatternDemo.InitializeDesignPatterns();
}

// 运行设计模式演示
void RunDesignPatternDemo()
{
    g_DesignPatternDemo.DemonstratePatterns();
}

// 清理设计模式资源
void CleanupDesignPatterns()
{
    g_AsyncCommandExecutor.Stop();
    g_CommandInvoker.ClearHistory();
    
    printf("[DESIGN_PATTERNS] All design patterns cleaned up\n");
}
