//---------------------------------------------------------------------------
// Sword2 Performance Monitor (c) 2024
//
// File:	PerformanceMonitor.cpp
// Date:	2024.07.02
// Code:	AI Assistant
// Desc:	Performance monitoring implementation
//---------------------------------------------------------------------------

#include "KCore.h"
#include "PerformanceMonitor.h"
#include <psapi.h>

// 全局实例
CPerformanceMonitor g_PerformanceMonitor;

// 静态成员初始化
LARGE_INTEGER CPerformanceTimer::s_liFrequency = {0};
BOOL CPerformanceTimer::s_bFrequencyInitialized = FALSE;

CPerformanceMonitor::CPerformanceMonitor()
{
    m_dwFrameStartTime = 0;
    m_dwLastFrameTime = 0;
    m_nFrameCount = 0;
    m_nCurrentFPS = 0;
    m_nAverageFPS = 0;
    m_dwFPSUpdateTime = 0;
    
    m_nNetReceiveBytes = 0;
    m_nNetSendBytes = 0;
    m_nNetReceiveRate = 0;
    m_nNetSendRate = 0;
    m_dwNetUpdateTime = 0;
    
    m_dwMemoryUsage = 0;
    m_dwPeakMemoryUsage = 0;
    m_dwMemoryUpdateTime = 0;
    
    m_nCPUUsage = 0;
    m_dwCPUUpdateTime = 0;
    m_liLastCPUTime.QuadPart = 0;
    m_liLastSystemTime.QuadPart = 0;
    
    m_liFrequency.QuadPart = 0;
    m_bInitialized = FALSE;
}

CPerformanceMonitor::~CPerformanceMonitor()
{
    Cleanup();
}

BOOL CPerformanceMonitor::Initialize()
{
    if (m_bInitialized)
        return TRUE;
        
    // 初始化高精度计时器
    if (!QueryPerformanceFrequency(&m_liFrequency))
        return FALSE;
        
    m_dwFPSUpdateTime = GetTickCount();
    m_dwNetUpdateTime = GetTickCount();
    m_dwMemoryUpdateTime = GetTickCount();
    m_dwCPUUpdateTime = GetTickCount();
    
    m_bInitialized = TRUE;
    return TRUE;
}

void CPerformanceMonitor::Cleanup()
{
    m_bInitialized = FALSE;
}

void CPerformanceMonitor::BeginFrame()
{
    if (!m_bInitialized)
        return;
        
    m_dwFrameStartTime = GetTickCount();
}

void CPerformanceMonitor::EndFrame()
{
    if (!m_bInitialized)
        return;
        
    DWORD dwCurrentTime = GetTickCount();
    m_nFrameCount++;
    
    // 每秒更新一次FPS
    if (dwCurrentTime - m_dwFPSUpdateTime >= 1000)
    {
        m_nCurrentFPS = m_nFrameCount;
        
        // 计算平均FPS
        static int nTotalFrames = 0;
        static DWORD dwStartTime = dwCurrentTime;
        nTotalFrames += m_nFrameCount;
        DWORD dwElapsed = dwCurrentTime - dwStartTime;
        if (dwElapsed > 0)
            m_nAverageFPS = nTotalFrames * 1000 / dwElapsed;
            
        m_nFrameCount = 0;
        m_dwFPSUpdateTime = dwCurrentTime;
    }
}

void CPerformanceMonitor::RecordNetworkReceive(int nBytes)
{
    m_nNetReceiveBytes += nBytes;
    
    DWORD dwCurrentTime = GetTickCount();
    if (dwCurrentTime - m_dwNetUpdateTime >= 1000)
    {
        m_nNetReceiveRate = m_nNetReceiveBytes;
        m_nNetReceiveBytes = 0;
        m_dwNetUpdateTime = dwCurrentTime;
    }
}

void CPerformanceMonitor::RecordNetworkSend(int nBytes)
{
    m_nNetSendBytes += nBytes;
    
    DWORD dwCurrentTime = GetTickCount();
    if (dwCurrentTime - m_dwNetUpdateTime >= 1000)
    {
        m_nNetSendRate = m_nNetSendBytes;
        m_nNetSendBytes = 0;
        m_dwNetUpdateTime = dwCurrentTime;
    }
}

void CPerformanceMonitor::UpdateMemoryUsage()
{
    DWORD dwCurrentTime = GetTickCount();
    if (dwCurrentTime - m_dwMemoryUpdateTime < 1000)
        return; // 每秒更新一次
        
    PROCESS_MEMORY_COUNTERS pmc;
    if (GetProcessMemoryInfo(GetCurrentProcess(), &pmc, sizeof(pmc)))
    {
        m_dwMemoryUsage = (DWORD)(pmc.WorkingSetSize / 1024); // KB
        if (m_dwMemoryUsage > m_dwPeakMemoryUsage)
            m_dwPeakMemoryUsage = m_dwMemoryUsage;
    }
    
    m_dwMemoryUpdateTime = dwCurrentTime;
}

void CPerformanceMonitor::UpdateCPUUsage()
{
    DWORD dwCurrentTime = GetTickCount();
    if (dwCurrentTime - m_dwCPUUpdateTime < 1000)
        return; // 每秒更新一次
        
    // 简化的CPU使用率计算
    // 实际项目中可以使用更精确的方法
    static DWORD dwLastIdleTime = 0;
    DWORD dwIdleTime = GetTickCount(); // 简化实现
    
    m_nCPUUsage = 50; // 占位值，实际应该计算真实CPU使用率
    m_dwCPUUpdateTime = dwCurrentTime;
}

void CPerformanceMonitor::GenerateReport(char* pBuffer, int nBufferSize)
{
    if (!pBuffer || nBufferSize <= 0)
        return;
        
    sprintf_s(pBuffer, nBufferSize,
        "=== Performance Report ===\n"
        "FPS: Current=%d, Average=%d\n"
        "Network: Recv=%d KB/s, Send=%d KB/s\n"
        "Memory: Current=%d KB, Peak=%d KB\n"
        "CPU Usage: %d%%\n",
        m_nCurrentFPS, m_nAverageFPS,
        m_nNetReceiveRate / 1024, m_nNetSendRate / 1024,
        m_dwMemoryUsage, m_dwPeakMemoryUsage,
        m_nCPUUsage);
}

void CPerformanceMonitor::LogPerformanceData()
{
    char szReport[512];
    GenerateReport(szReport, sizeof(szReport));
    
    // 输出到调试日志
    OutputDebugStringA(szReport);
    
    // 也可以写入文件
    // WriteToLogFile(szReport);
}

// CPerformanceTimer 实现
CPerformanceTimer::CPerformanceTimer(const char* pName)
    : m_pName(pName)
{
    if (!s_bFrequencyInitialized)
    {
        QueryPerformanceFrequency(&s_liFrequency);
        s_bFrequencyInitialized = TRUE;
    }
    
    QueryPerformanceCounter(&m_liStartTime);
}

CPerformanceTimer::~CPerformanceTimer()
{
    LARGE_INTEGER liEndTime;
    QueryPerformanceCounter(&liEndTime);
    
    double dElapsedMs = (double)(liEndTime.QuadPart - m_liStartTime.QuadPart) * 1000.0 / s_liFrequency.QuadPart;
    
    char szMsg[256];
    sprintf_s(szMsg, sizeof(szMsg), "[PERF] %s: %.3f ms\n", m_pName, dElapsedMs);
    OutputDebugStringA(szMsg);
}
