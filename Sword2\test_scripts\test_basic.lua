-- Sword2 Basic Test Script
-- 这个脚本用于测试基本的Lua脚本引擎功能

-- 全局变量定义
TEST_SERVER_NAME = "Sword2 Test Server"
TEST_VERSION = "1.0.0"
TEST_MAX_PLAYERS = 1000

-- 基础函数测试
function test_basic_functions()
    WriteLog("=== Basic Functions Test ===")
    WriteLog("Server: " .. TEST_SERVER_NAME)
    WriteLog("Version: " .. TEST_VERSION)
    WriteLog("Max Players: " .. TEST_MAX_PLAYERS)
    
    -- 测试随机数生成
    local rand1 = Random()
    local rand2 = Random(10)
    local rand3 = Random(1, 100)
    
    WriteLog("Random numbers: " .. rand1 .. ", " .. rand2 .. ", " .. rand3)
    
    -- 测试时间函数
    local currentTime = GetCurrentTime()
    WriteLog("Current time: " .. currentTime)
    
    return true
end

-- 玩家功能测试
function test_player_functions(playerId)
    WriteLog("=== Player Functions Test ===")
    
    if not playerId or playerId == 0 then
        WriteLog("Invalid player ID")
        return false
    end
    
    -- 获取玩家基本信息
    local playerName = GetPlayerName(playerId)
    local playerLevel = GetPlayerLevel(playerId)
    local playerMoney = GetPlayerMoney(playerId)
    
    WriteLog("Player Info:")
    WriteLog("  Name: " .. playerName)
    WriteLog("  Level: " .. playerLevel)
    WriteLog("  Money: " .. playerMoney)
    
    -- 获取玩家位置
    local mapId, x, y = GetPlayerPosition(playerId)
    WriteLog("  Position: Map " .. mapId .. " (" .. x .. ", " .. y .. ")")
    
    -- 获取玩家生命值
    local currentHP, maxHP = GetPlayerHP(playerId)
    WriteLog("  HP: " .. currentHP .. "/" .. maxHP)
    
    -- 获取玩家内力值
    local currentMP, maxMP = GetPlayerMP(playerId)
    WriteLog("  MP: " .. currentMP .. "/" .. maxMP)
    
    return true
end

-- NPC对话测试
function test_npc_dialog(playerId, npcId)
    WriteLog("=== NPC Dialog Test ===")
    
    local playerName = GetPlayerName(playerId)
    local playerLevel = GetPlayerLevel(playerId)
    
    Talk("你好，" .. playerName .. "！")
    Talk("我是测试NPC，编号 " .. npcId)
    
    if playerLevel < 5 then
        Talk("你还是个新手，我来帮助你！")
        
        -- 给新手一些金钱
        local currentMoney = GetPlayerMoney(playerId)
        SetPlayerMoney(playerId, currentMoney + 1000)
        Talk("我给了你1000金币，好好利用吧！")
        
        -- 给新手一些经验
        local leveledUp = AddPlayerExp(playerId, 100)
        if leveledUp then
            Talk("恭喜你升级了！")
        else
            Talk("我给了你一些经验，继续努力！")
        end
    else
        Talk("你已经是个有经验的武者了。")
        Talk("让我检查一下你的装备...")
        
        -- 检查是否有特定物品
        if HasItem(playerId, 1001) then
            Talk("我看到你有青钢剑，不错的武器！")
        else
            Talk("你需要一把好武器，我给你一把青钢剑。")
            GiveItem(playerId, 1001, 1)
        end
    end
    
    Talk("祝你在江湖中一路顺风！")
    return true
end

-- 物品使用测试
function test_item_usage(playerId, itemId)
    WriteLog("=== Item Usage Test ===")
    WriteLog("Player " .. playerId .. " is using item " .. itemId)
    
    local playerName = GetPlayerName(playerId)
    
    if itemId == 2001 then  -- 生命药水
        local currentHP, maxHP = GetPlayerHP(playerId)
        WriteLog("Current HP: " .. currentHP .. "/" .. maxHP)
        
        if currentHP < maxHP then
            HealPlayer(playerId, 100)
            Talk(playerName .. " 使用了生命药水，恢复了100点生命值。")
            WriteLog("Healed player for 100 HP")
        else
            Talk(playerName .. " 的生命值已经满了。")
            WriteLog("Player HP is already full")
            return false
        end
        
    elseif itemId == 2002 then  -- 内力药水
        local currentMP, maxMP = GetPlayerMP(playerId)
        WriteLog("Current MP: " .. currentMP .. "/" .. maxMP)
        
        if currentMP < maxMP then
            SetPlayerMP(playerId, maxMP)
            Talk(playerName .. " 使用了内力药水，内力完全恢复。")
            WriteLog("Restored player MP to full")
        else
            Talk(playerName .. " 的内力已经满了。")
            WriteLog("Player MP is already full")
            return false
        end
        
    elseif itemId == 3001 then  -- 传送卷轴
        Talk(playerName .. " 使用了传送卷轴。")
        
        -- 传送到新手村
        TeleportPlayer(playerId, 100, 1000, 1000)
        Talk("你被传送到了新手村。")
        WriteLog("Teleported player to newbie village")
        
    else
        WriteLog("Unknown item ID: " .. itemId)
        return false
    end
    
    return true
end

-- 战斗测试
function test_combat_functions(playerId, targetId)
    WriteLog("=== Combat Functions Test ===")
    
    local playerName = GetPlayerName(playerId)
    WriteLog("Combat test for player: " .. playerName)
    
    -- 获取玩家当前状态
    local currentHP, maxHP = GetPlayerHP(playerId)
    local currentMP, maxMP = GetPlayerMP(playerId)
    
    WriteLog("Before combat - HP: " .. currentHP .. "/" .. maxHP .. ", MP: " .. currentMP .. "/" .. maxMP)
    
    -- 模拟受到伤害
    local damage = Random(10, 50)
    DamagePlayer(playerId, damage)
    WriteLog("Player took " .. damage .. " damage")
    
    -- 检查伤害后的状态
    currentHP, maxHP = GetPlayerHP(playerId)
    WriteLog("After damage - HP: " .. currentHP .. "/" .. maxHP)
    
    -- 如果生命值过低，进行治疗
    if currentHP < maxHP * 0.5 then
        local healAmount = Random(20, 80)
        HealPlayer(playerId, healAmount)
        WriteLog("Player healed for " .. healAmount .. " HP")
        
        currentHP, maxHP = GetPlayerHP(playerId)
        WriteLog("After healing - HP: " .. currentHP .. "/" .. maxHP)
    end
    
    -- 模拟消耗内力
    if currentMP > 20 then
        SetPlayerMP(playerId, currentMP - 20)
        WriteLog("Player used 20 MP for a skill")
        
        currentMP, maxMP = GetPlayerMP(playerId)
        WriteLog("After skill use - MP: " .. currentMP .. "/" .. maxMP)
    end
    
    return true
end

-- 地图功能测试
function test_map_functions(playerId)
    WriteLog("=== Map Functions Test ===")
    
    local playerName = GetPlayerName(playerId)
    local mapId = GetMapId(playerId)
    local mapName = GetMapName(mapId)
    
    WriteLog("Player " .. playerName .. " is in map " .. mapId .. " (" .. mapName .. ")")
    
    -- 获取当前位置
    local currentMapId, x, y = GetPlayerPosition(playerId)
    WriteLog("Current position: (" .. x .. ", " .. y .. ")")
    
    -- 移动到新位置
    local newX = x + Random(-100, 100)
    local newY = y + Random(-100, 100)
    
    SetPlayerPosition(playerId, currentMapId, newX, newY)
    WriteLog("Moved to new position: (" .. newX .. ", " .. newY .. ")")
    
    -- 创建测试NPC
    local npcId = 9999
    CreateNPC(npcId, currentMapId, newX + 10, newY + 10)
    WriteLog("Created test NPC " .. npcId .. " near player")
    
    return true
end

-- 系统功能测试
function test_system_functions()
    WriteLog("=== System Functions Test ===")
    
    -- 广播消息
    Broadcast("这是来自测试脚本的系统广播！")
    WriteLog("Sent system broadcast")
    
    -- 测试多种随机数生成
    WriteLog("Random number tests:")
    for i = 1, 5 do
        local rand = Random(1, 10)
        WriteLog("  Random " .. i .. ": " .. rand)
    end
    
    -- 获取系统时间
    local time = GetCurrentTime()
    WriteLog("System time: " .. time)
    
    return true
end

-- 主测试函数
function run_all_tests(playerId)
    WriteLog("========================================")
    WriteLog("Starting Sword2 Script Engine Tests")
    WriteLog("========================================")
    
    -- 运行基础功能测试
    test_basic_functions()
    
    -- 如果提供了玩家ID，运行玩家相关测试
    if playerId and playerId > 0 then
        test_player_functions(playerId)
        test_npc_dialog(playerId, 1001)
        test_item_usage(playerId, 2001)
        test_combat_functions(playerId, 0)
        test_map_functions(playerId)
    else
        WriteLog("No player ID provided, skipping player-specific tests")
    end
    
    -- 运行系统功能测试
    test_system_functions()
    
    WriteLog("========================================")
    WriteLog("All tests completed!")
    WriteLog("========================================")
    
    return true
end

-- 简单的欢迎函数
function welcome_message()
    WriteLog("欢迎来到剑网2脚本引擎测试！")
    WriteLog("这个脚本演示了基本的游戏API功能。")
    return "Welcome to Sword2 Script Engine!"
end

-- 脚本初始化
WriteLog("Test script loaded successfully!")
WriteLog("Available functions: test_basic_functions, test_player_functions, run_all_tests, welcome_message")
