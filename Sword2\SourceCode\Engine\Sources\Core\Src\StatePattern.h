//---------------------------------------------------------------------------
// Sword2 State Pattern Implementation (c) 2024
//
// File:	StatePattern.h
// Date:	2024.07.02
// Code:	AI Assistant
// Desc:	Modern C++ state pattern for state machine management
//---------------------------------------------------------------------------
#ifndef STATE_PATTERN_H
#define STATE_PATTERN_H

#include "ModernCpp.h"
#include <unordered_map>
#include <functional>
#include <memory>
#include <string>
#include <vector>
#include <chrono>

namespace sword2 {

// 前向声明
template<typename Context>
class StateMachine;

// 状态接口
template<typename Context>
class IState
{
public:
    virtual ~IState() = default;
    
    // 状态生命周期
    virtual void OnEnter(Context& context) {}
    virtual void OnExit(Context& context) {}
    virtual void OnUpdate(Context& context, float deltaTime) {}
    
    // 事件处理
    virtual bool OnEvent(Context& context, const std::string& eventName, const std::any& eventData = {}) { return false; }
    
    // 状态信息
    virtual std::string GetName() const = 0;
    virtual bool CanTransitionTo(const std::string& stateName) const { return true; }
    
    // 状态机访问
    void SetStateMachine(StateMachine<Context>* stateMachine) { m_stateMachine = stateMachine; }

protected:
    StateMachine<Context>* m_stateMachine = nullptr;
    
    // 便捷的状态转换方法
    bool TransitionTo(const std::string& stateName)
    {
        return m_stateMachine ? m_stateMachine->TransitionTo(stateName) : false;
    }
};

// 函数式状态
template<typename Context>
class FunctionState : public IState<Context>
{
public:
    using EnterFunc = std::function<void(Context&)>;
    using ExitFunc = std::function<void(Context&)>;
    using UpdateFunc = std::function<void(Context&, float)>;
    using EventFunc = std::function<bool(Context&, const std::string&, const std::any&)>;
    
    explicit FunctionState(const std::string& name) : m_name(name) {}
    
    FunctionState& SetOnEnter(EnterFunc func) { m_onEnter = std::move(func); return *this; }
    FunctionState& SetOnExit(ExitFunc func) { m_onExit = std::move(func); return *this; }
    FunctionState& SetOnUpdate(UpdateFunc func) { m_onUpdate = std::move(func); return *this; }
    FunctionState& SetOnEvent(EventFunc func) { m_onEvent = std::move(func); return *this; }
    
    void OnEnter(Context& context) override
    {
        if (m_onEnter) m_onEnter(context);
    }
    
    void OnExit(Context& context) override
    {
        if (m_onExit) m_onExit(context);
    }
    
    void OnUpdate(Context& context, float deltaTime) override
    {
        if (m_onUpdate) m_onUpdate(context, deltaTime);
    }
    
    bool OnEvent(Context& context, const std::string& eventName, const std::any& eventData) override
    {
        return m_onEvent ? m_onEvent(context, eventName, eventData) : false;
    }
    
    std::string GetName() const override { return m_name; }

private:
    std::string m_name;
    EnterFunc m_onEnter;
    ExitFunc m_onExit;
    UpdateFunc m_onUpdate;
    EventFunc m_onEvent;
};

// 状态转换条件
template<typename Context>
using TransitionCondition = std::function<bool(const Context&)>;

// 状态转换
template<typename Context>
struct StateTransition
{
    std::string fromState;
    std::string toState;
    std::string triggerEvent;
    TransitionCondition<Context> condition;
    std::function<void(Context&)> onTransition;
    
    StateTransition(const std::string& from, const std::string& to, const std::string& trigger = "")
        : fromState(from), toState(to), triggerEvent(trigger) {}
    
    StateTransition& SetCondition(TransitionCondition<Context> cond)
    {
        condition = std::move(cond);
        return *this;
    }
    
    StateTransition& SetOnTransition(std::function<void(Context&)> func)
    {
        onTransition = std::move(func);
        return *this;
    }
};

// 状态机
template<typename Context>
class StateMachine
{
public:
    explicit StateMachine(Context& context) : m_context(context) {}
    
    ~StateMachine()
    {
        if (m_currentState)
        {
            m_currentState->OnExit(m_context);
        }
    }
    
    // 状态管理
    void AddState(std::unique_ptr<IState<Context>> state)
    {
        state->SetStateMachine(this);
        m_states[state->GetName()] = std::move(state);
    }
    
    void AddTransition(const StateTransition<Context>& transition)
    {
        m_transitions.push_back(transition);
    }
    
    // 状态机控制
    bool Start(const std::string& initialState)
    {
        auto it = m_states.find(initialState);
        if (it != m_states.end())
        {
            m_currentState = it->second.get();
            m_currentStateName = initialState;
            m_currentState->OnEnter(m_context);
            m_stateStartTime = std::chrono::steady_clock::now();
            return true;
        }
        return false;
    }
    
    void Stop()
    {
        if (m_currentState)
        {
            m_currentState->OnExit(m_context);
            m_currentState = nullptr;
            m_currentStateName.clear();
        }
    }
    
    void Update(float deltaTime)
    {
        if (m_currentState)
        {
            m_currentState->OnUpdate(m_context, deltaTime);
            
            // 检查自动转换
            CheckAutoTransitions();
        }
    }
    
    // 状态转换
    bool TransitionTo(const std::string& stateName)
    {
        if (m_currentStateName == stateName) return true;
        
        auto it = m_states.find(stateName);
        if (it == m_states.end()) return false;
        
        // 检查是否允许转换
        if (m_currentState && !m_currentState->CanTransitionTo(stateName))
            return false;
        
        // 查找转换规则
        auto transition = FindTransition(m_currentStateName, stateName);
        if (transition && transition->condition && !transition->condition(m_context))
            return false;
        
        // 执行转换
        if (m_currentState)
        {
            m_currentState->OnExit(m_context);
        }
        
        std::string oldState = m_currentStateName;
        m_currentState = it->second.get();
        m_currentStateName = stateName;
        m_stateStartTime = std::chrono::steady_clock::now();
        
        m_currentState->OnEnter(m_context);
        
        // 执行转换回调
        if (transition && transition->onTransition)
        {
            transition->onTransition(m_context);
        }
        
        // 记录状态历史
        m_stateHistory.push_back({oldState, stateName, std::chrono::steady_clock::now()});
        if (m_stateHistory.size() > m_maxHistorySize)
        {
            m_stateHistory.erase(m_stateHistory.begin());
        }
        
        return true;
    }
    
    // 事件处理
    bool SendEvent(const std::string& eventName, const std::any& eventData = {})
    {
        if (m_currentState)
        {
            // 先让当前状态处理事件
            if (m_currentState->OnEvent(m_context, eventName, eventData))
                return true;
            
            // 检查事件触发的转换
            for (const auto& transition : m_transitions)
            {
                if (transition.fromState == m_currentStateName && 
                    transition.triggerEvent == eventName)
                {
                    if (!transition.condition || transition.condition(m_context))
                    {
                        return TransitionTo(transition.toState);
                    }
                }
            }
        }
        return false;
    }
    
    // 状态查询
    std::string GetCurrentStateName() const { return m_currentStateName; }
    IState<Context>* GetCurrentState() const { return m_currentState; }
    
    std::chrono::milliseconds GetTimeInCurrentState() const
    {
        auto now = std::chrono::steady_clock::now();
        return std::chrono::duration_cast<std::chrono::milliseconds>(now - m_stateStartTime);
    }
    
    bool IsInState(const std::string& stateName) const
    {
        return m_currentStateName == stateName;
    }
    
    std::vector<std::string> GetAvailableStates() const
    {
        std::vector<std::string> states;
        for (const auto& pair : m_states)
        {
            states.push_back(pair.first);
        }
        return states;
    }
    
    // 状态历史
    struct StateHistoryEntry
    {
        std::string fromState;
        std::string toState;
        std::chrono::steady_clock::time_point timestamp;
    };
    
    const std::vector<StateHistoryEntry>& GetStateHistory() const { return m_stateHistory; }
    void SetMaxHistorySize(size_t size) { m_maxHistorySize = size; }

private:
    Context& m_context;
    std::unordered_map<std::string, std::unique_ptr<IState<Context>>> m_states;
    std::vector<StateTransition<Context>> m_transitions;
    
    IState<Context>* m_currentState = nullptr;
    std::string m_currentStateName;
    std::chrono::steady_clock::time_point m_stateStartTime;
    
    std::vector<StateHistoryEntry> m_stateHistory;
    size_t m_maxHistorySize = 100;
    
    const StateTransition<Context>* FindTransition(const std::string& from, const std::string& to) const
    {
        for (const auto& transition : m_transitions)
        {
            if (transition.fromState == from && transition.toState == to)
            {
                return &transition;
            }
        }
        return nullptr;
    }
    
    void CheckAutoTransitions()
    {
        for (const auto& transition : m_transitions)
        {
            if (transition.fromState == m_currentStateName && 
                transition.triggerEvent.empty() && 
                transition.condition && 
                transition.condition(m_context))
            {
                TransitionTo(transition.toState);
                break; // 只执行第一个满足条件的转换
            }
        }
    }
};

// 游戏特定状态机示例

// 玩家状态上下文
struct PlayerContext
{
    float health = 100.0f;
    float mana = 100.0f;
    float stamina = 100.0f;
    bool isGrounded = true;
    bool isMoving = false;
    bool isAttacking = false;
    TransformComponent::Vector3 velocity{0.0f, 0.0f, 0.0f};
};

// 玩家状态
class IdleState : public IState<PlayerContext>
{
public:
    void OnEnter(PlayerContext& context) override
    {
        context.isMoving = false;
        context.velocity = {0.0f, 0.0f, 0.0f};
    }
    
    bool OnEvent(PlayerContext& context, const std::string& eventName, const std::any& eventData) override
    {
        if (eventName == "move")
        {
            return TransitionTo("Moving");
        }
        else if (eventName == "jump" && context.isGrounded)
        {
            return TransitionTo("Jumping");
        }
        else if (eventName == "attack")
        {
            return TransitionTo("Attacking");
        }
        return false;
    }
    
    std::string GetName() const override { return "Idle"; }
};

class MovingState : public IState<PlayerContext>
{
public:
    void OnEnter(PlayerContext& context) override
    {
        context.isMoving = true;
    }
    
    void OnUpdate(PlayerContext& context, float deltaTime) override
    {
        // 更新移动逻辑
        if (context.velocity.x == 0.0f && context.velocity.z == 0.0f)
        {
            TransitionTo("Idle");
        }
    }
    
    bool OnEvent(PlayerContext& context, const std::string& eventName, const std::any& eventData) override
    {
        if (eventName == "stop")
        {
            return TransitionTo("Idle");
        }
        else if (eventName == "jump" && context.isGrounded)
        {
            return TransitionTo("Jumping");
        }
        return false;
    }
    
    std::string GetName() const override { return "Moving"; }
};

class JumpingState : public IState<PlayerContext>
{
public:
    void OnEnter(PlayerContext& context) override
    {
        context.isGrounded = false;
        context.velocity.y = 10.0f; // 跳跃速度
    }
    
    void OnUpdate(PlayerContext& context, float deltaTime) override
    {
        // 重力处理
        context.velocity.y -= 9.8f * deltaTime;
        
        // 检查是否落地
        if (context.velocity.y <= 0.0f && /* 检查地面碰撞 */ true)
        {
            context.isGrounded = true;
            if (context.isMoving)
            {
                TransitionTo("Moving");
            }
            else
            {
                TransitionTo("Idle");
            }
        }
    }
    
    std::string GetName() const override { return "Jumping"; }
};

// AI状态机示例
struct AIContext
{
    float health = 100.0f;
    float alertLevel = 0.0f;
    TransformComponent::Vector3 playerPosition;
    TransformComponent::Vector3 position;
    float detectionRange = 50.0f;
    bool playerVisible = false;
};

// 状态机构建器
template<typename Context>
class StateMachineBuilder
{
public:
    explicit StateMachineBuilder(Context& context) : m_stateMachine(std::make_unique<StateMachine<Context>>(context)) {}
    
    StateMachineBuilder& AddState(std::unique_ptr<IState<Context>> state)
    {
        m_stateMachine->AddState(std::move(state));
        return *this;
    }
    
    StateMachineBuilder& AddTransition(const StateTransition<Context>& transition)
    {
        m_stateMachine->AddTransition(transition);
        return *this;
    }
    
    std::unique_ptr<StateMachine<Context>> Build()
    {
        return std::move(m_stateMachine);
    }

private:
    std::unique_ptr<StateMachine<Context>> m_stateMachine;
};

} // namespace sword2

// 便捷宏定义
#define MAKE_FUNCTION_STATE(name) \
    std::make_unique<sword2::FunctionState<decltype(context)>>(name)

#define CREATE_STATE_MACHINE(context) \
    sword2::StateMachineBuilder(context)

#define ADD_STATE_TRANSITION(from, to, event) \
    sword2::StateTransition<decltype(context)>(from, to, event)

#endif // STATE_PATTERN_H
