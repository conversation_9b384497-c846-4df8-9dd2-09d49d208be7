#include "KCore.h"
#include "KPlayerFaction.h"
#include "KLadder.h"

KLadder	Ladder;

KLadder::KLadder()
{
	ZeroMemory(&GameStatData, sizeof(TGAME_STAT_DATA));
}

KLadder::~KLadder()
{
	ZeroMemory(&GameStatData, sizeof(TGAME_STAT_DATA));
}

BOOL KLadder::Init(void* pData, size_t uSize)
{
	if (uSize != sizeof(TGAME_STAT_DATA))
		return FALSE;

	memcpy(&GameStatData, pData, uSize);
	return TRUE;
}

const TRoleList* KLadder::TopTenFacMasterHand(int nFac)
{
	if (nFac < - 1 || nFac >= MAX_FACTION)
		return NULL;
	return GameStatData.LevelStatBySect[nFac + 1];
}

const TRoleList* KLadder::TopTenFacRich(int nFac)
{
	if (nFac < - 1 || nFac >= MAX_FACTION)
		return NULL;
	return GameStatData.MoneyStatBySect[nFac + 1];
}

const TRoleList* KLadder::TopTenKiller()
{
	return GameStatData.KillerStat;
}

const TRoleList* KLadder::TopTenRepute()
{
	return GameStatData.ReputeStat;
}

const TRoleList* KLadder::TopTenFuYuan()
{
	return GameStatData.FuYuanStat;
}

const TRoleList* KLadder::TopTenAccumStat1()
{
	return GameStatData.AccumStat1;
}

const TRoleList* KLadder::TopTenAccumStat2()
{
	return GameStatData.AccumStat2;
}

const TRoleList* KLadder::TopTenHonorStat()
{
	return GameStatData.HonorStat;
}

const TRoleList* KLadder::TopTenTimeStat()
{
	return GameStatData.TimeStat;
}

const TRoleList* KLadder::TopTenTongLv()
{
	return GameStatData.TongLvStat;
}

const TRoleList* KLadder::TopTenTongMn()
{
	return GameStatData.TongMnStat;
}

const TRoleList* KLadder::TopTenTongEff()
{
	return GameStatData.TongEffStat;
}

const TRoleList* KLadder::TopTenMasterHand()
{
	return GameStatData.LevelStat;
}

const TRoleList* KLadder::TopTenRich()
{
	return GameStatData.MoneyStat;
}

int KLadder::GetFacMasterHandPercent(int nFac)
{
	if (nFac < - 1 || nFac >= MAX_FACTION)
		return 0;
	return GameStatData.SectLevelMost[nFac + 1];
}

int KLadder::GetFacMoneyPercent(int nFac)
{
	if (nFac < - 1 || nFac >= MAX_FACTION)
		return NULL;
	return GameStatData.SectMoneyMost[nFac + 1];
}

int KLadder::GetFacMemberCount(int nFac)
{
	if (nFac < - 1 || nFac >= MAX_FACTION)
		return NULL;
	return GameStatData.SectPlayerNum[nFac + 1];
}

const TRoleList* KLadder::GetTopTen(DWORD dwLadderID)
{
	if (dwLadderID <= enumLadderBegin || dwLadderID >= enumLadderEnd)
	{
		return NULL;
	}

	if (dwLadderID == enumTopTenMasterHand)
	{
		return TopTenMasterHand();
	}
	else if (dwLadderID == enumTopTenRicher)
	{
		return TopTenRich();
	}
	else if (dwLadderID == enumTopTenKiller)
	{
		return TopTenKiller();
	}
	else if (dwLadderID == enumTopTenRepute)
	{
		return TopTenRepute();
	}
	else if (dwLadderID == enumTopTenFuYuan)
	{
		return TopTenFuYuan();
	}
	else if (dwLadderID == enumTopTenAccumStat1)
	{
		return TopTenAccumStat1();
	}
	else if (dwLadderID == enumTopTenAccumStat2)
	{
		return TopTenAccumStat2();
	}
	else if (dwLadderID == enumTopTenHonorStat)
	{
		return TopTenHonorStat();
	}
	else if (dwLadderID == enumTopTenTimeStat)
	{
		return TopTenTimeStat();
	}
	else if (dwLadderID == enumTopTenTongLv)
	{
		return TopTenTongLv();
	}
	else if (dwLadderID == enumTopTenTongMn)
	{
		return TopTenTongMn();
	}
	else if (dwLadderID == enumTopTenTongEff)
	{
		return TopTenTongEff();
	}
	else if (dwLadderID < enumFacTopTenRicher)
	{
		return TopTenFacMasterHand(dwLadderID - enumFacTopTenMasterHand - 1);
	}
	else
	{
		return TopTenFacRich(dwLadderID - enumFacTopTenRicher - 1);
	}
}