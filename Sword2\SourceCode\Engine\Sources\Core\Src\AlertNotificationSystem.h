//---------------------------------------------------------------------------
// Sword2 Alert and Notification System (c) 2024
//
// File:	AlertNotificationSystem.h
// Date:	2024.07.02
// Code:	AI Assistant
// Desc:	Intelligent alerting and multi-channel notification system
//---------------------------------------------------------------------------
#ifndef ALERT_NOTIFICATION_SYSTEM_H
#define ALERT_NOTIFICATION_SYSTEM_H

#include "ModernCpp.h"
#include "UnifiedLoggingSystem.h"
#include <chrono>
#include <thread>
#include <queue>
#include <atomic>
#include <functional>

namespace sword2 {

// 告警级别
enum class AlertLevel : uint8_t
{
    Info = 0,           // 信息
    Warning,            // 警告
    Error,              // 错误
    Critical,           // 严重
    Emergency           // 紧急
};

// 通知渠道类型
enum class NotificationChannel : uint8_t
{
    Console = 0,        // 控制台
    Email,              // 邮件
    SMS,                // 短信
    Webhook,            // Webhook
    Database,           // 数据库
    File,               // 文件
    Custom              // 自定义
};

// 告警状态
enum class AlertStatus : uint8_t
{
    Active = 0,         // 活跃
    Acknowledged,       // 已确认
    Resolved,           // 已解决
    Suppressed          // 已抑制
};

// 告警规则
struct AlertRule
{
    std::string id;
    std::string name;
    std::string description;
    AlertLevel level;
    std::string condition;              // 告警条件表达式
    std::chrono::seconds evaluationInterval; // 评估间隔
    std::chrono::seconds duration;      // 持续时间
    bool enabled;
    std::vector<NotificationChannel> channels; // 通知渠道
    std::unordered_map<std::string, std::string> labels; // 标签
    
    AlertRule(const std::string& ruleId, const std::string& ruleName, AlertLevel lvl)
        : id(ruleId), name(ruleName), level(lvl), 
          evaluationInterval(std::chrono::seconds(60)),
          duration(std::chrono::seconds(300)), enabled(true) {}
};

// 告警实例
struct Alert
{
    std::string id;
    std::string ruleId;
    std::string title;
    std::string message;
    AlertLevel level;
    AlertStatus status;
    std::chrono::system_clock::time_point startTime;
    std::chrono::system_clock::time_point lastUpdate;
    std::chrono::system_clock::time_point resolvedTime;
    std::unordered_map<std::string, std::string> labels;
    std::unordered_map<std::string, std::string> annotations;
    std::string acknowledgedBy;
    std::string resolvedBy;
    
    Alert(const std::string& alertId, const std::string& ruleId, 
          const std::string& title, const std::string& msg, AlertLevel lvl)
        : id(alertId), ruleId(ruleId), title(title), message(msg), 
          level(lvl), status(AlertStatus::Active)
    {
        startTime = lastUpdate = std::chrono::system_clock::now();
    }
    
    std::chrono::seconds GetDuration() const
    {
        auto endTime = (status == AlertStatus::Resolved) ? resolvedTime : std::chrono::system_clock::now();
        return std::chrono::duration_cast<std::chrono::seconds>(endTime - startTime);
    }
};

// 通知消息
struct NotificationMessage
{
    std::string id;
    NotificationChannel channel;
    std::string recipient;
    std::string subject;
    std::string content;
    AlertLevel priority;
    std::chrono::system_clock::time_point timestamp;
    bool sent;
    std::string errorMessage;
    
    NotificationMessage(NotificationChannel ch, const std::string& to, 
                       const std::string& subj, const std::string& body, AlertLevel prio)
        : channel(ch), recipient(to), subject(subj), content(body), 
          priority(prio), timestamp(std::chrono::system_clock::now()), sent(false)
    {
        id = GenerateMessageId();
    }

private:
    std::string GenerateMessageId()
    {
        static std::atomic<uint64_t> counter{0};
        auto now = std::chrono::system_clock::now().time_since_epoch().count();
        return "msg_" + std::to_string(now) + "_" + std::to_string(counter++);
    }
};

// 通知发送器接口
class INotificationSender
{
public:
    virtual ~INotificationSender() = default;
    virtual bool SendNotification(const NotificationMessage& message) = 0;
    virtual NotificationChannel GetChannelType() const = 0;
    virtual bool IsAvailable() const { return true; }
};

// 控制台通知发送器
class ConsoleNotificationSender : public INotificationSender
{
public:
    bool SendNotification(const NotificationMessage& message) override
    {
        std::string levelStr = AlertLevelToString(message.priority);
        printf("[ALERT-%s] %s: %s\n", levelStr.c_str(), 
               message.subject.c_str(), message.content.c_str());
        return true;
    }
    
    NotificationChannel GetChannelType() const override 
    { 
        return NotificationChannel::Console; 
    }

private:
    std::string AlertLevelToString(AlertLevel level)
    {
        switch (level)
        {
        case AlertLevel::Info: return "INFO";
        case AlertLevel::Warning: return "WARN";
        case AlertLevel::Error: return "ERROR";
        case AlertLevel::Critical: return "CRITICAL";
        case AlertLevel::Emergency: return "EMERGENCY";
        default: return "UNKNOWN";
        }
    }
};

// 邮件通知发送器
class EmailNotificationSender : public INotificationSender
{
public:
    EmailNotificationSender(const std::string& smtpServer, int port, 
                           const std::string& username, const std::string& password)
        : m_smtpServer(smtpServer), m_port(port), m_username(username), m_password(password) {}
    
    bool SendNotification(const NotificationMessage& message) override
    {
        // 这里应该实现真实的SMTP邮件发送
        // 为了演示，我们只是记录日志
        LOG_INFO("EMAIL", "Sending email to: " + message.recipient + 
                         ", Subject: " + message.subject);
        
        // 模拟发送延迟
        std::this_thread::sleep_for(std::chrono::milliseconds(100));
        
        return true; // 假设发送成功
    }
    
    NotificationChannel GetChannelType() const override 
    { 
        return NotificationChannel::Email; 
    }

private:
    std::string m_smtpServer;
    int m_port;
    std::string m_username;
    std::string m_password;
};

// Webhook通知发送器
class WebhookNotificationSender : public INotificationSender
{
public:
    explicit WebhookNotificationSender(const std::string& webhookUrl)
        : m_webhookUrl(webhookUrl) {}
    
    bool SendNotification(const NotificationMessage& message) override
    {
        // 这里应该实现HTTP POST请求到webhook URL
        // 为了演示，我们只是记录日志
        LOG_INFO("WEBHOOK", "Sending webhook to: " + m_webhookUrl + 
                           ", Alert: " + message.subject);
        
        // 模拟网络请求延迟
        std::this_thread::sleep_for(std::chrono::milliseconds(200));
        
        return true; // 假设发送成功
    }
    
    NotificationChannel GetChannelType() const override 
    { 
        return NotificationChannel::Webhook; 
    }

private:
    std::string m_webhookUrl;
};

// 文件通知发送器
class FileNotificationSender : public INotificationSender
{
public:
    explicit FileNotificationSender(const std::string& filename)
        : m_filename(filename) {}
    
    bool SendNotification(const NotificationMessage& message) override
    {
        std::ofstream file(m_filename, std::ios::app);
        if (file.is_open())
        {
            auto time_t = std::chrono::system_clock::to_time_t(message.timestamp);
            auto tm = *std::localtime(&time_t);
            
            file << std::put_time(&tm, "%Y-%m-%d %H:%M:%S")
                 << " [" << AlertLevelToString(message.priority) << "]"
                 << " " << message.subject << ": " << message.content << std::endl;
            
            return true;
        }
        return false;
    }
    
    NotificationChannel GetChannelType() const override 
    { 
        return NotificationChannel::File; 
    }

private:
    std::string m_filename;
    
    std::string AlertLevelToString(AlertLevel level)
    {
        switch (level)
        {
        case AlertLevel::Info: return "INFO";
        case AlertLevel::Warning: return "WARN";
        case AlertLevel::Error: return "ERROR";
        case AlertLevel::Critical: return "CRITICAL";
        case AlertLevel::Emergency: return "EMERGENCY";
        default: return "UNKNOWN";
        }
    }
};

// 告警管理器
class AlertManager : public Singleton<AlertManager>
{
public:
    AlertManager() : m_running(false) {}
    
    ~AlertManager()
    {
        Stop();
    }
    
    bool Start()
    {
        if (m_running) return true;
        
        m_running = true;
        m_evaluationThread = std::thread(&AlertManager::EvaluationLoop, this);
        m_notificationThread = std::thread(&AlertManager::NotificationLoop, this);
        
        LOG_INFO("ALERT", "Alert manager started");
        return true;
    }
    
    void Stop()
    {
        if (!m_running) return;
        
        m_running = false;
        m_notificationCondition.notify_all();
        
        if (m_evaluationThread.joinable())
        {
            m_evaluationThread.join();
        }
        
        if (m_notificationThread.joinable())
        {
            m_notificationThread.join();
        }
        
        LOG_INFO("ALERT", "Alert manager stopped");
    }
    
    // 添加告警规则
    void AddAlertRule(const AlertRule& rule)
    {
        std::lock_guard<std::mutex> lock(m_rulesMutex);
        m_alertRules[rule.id] = rule;
        LOG_INFO("ALERT", "Alert rule added: " + rule.name);
    }
    
    // 移除告警规则
    void RemoveAlertRule(const std::string& ruleId)
    {
        std::lock_guard<std::mutex> lock(m_rulesMutex);
        m_alertRules.erase(ruleId);
        LOG_INFO("ALERT", "Alert rule removed: " + ruleId);
    }
    
    // 触发告警
    void TriggerAlert(const std::string& ruleId, const std::string& title, 
                     const std::string& message, const std::unordered_map<std::string, std::string>& labels = {})
    {
        std::lock_guard<std::mutex> lock(m_rulesMutex);
        auto ruleIt = m_alertRules.find(ruleId);
        if (ruleIt == m_alertRules.end() || !ruleIt->second.enabled)
        {
            return;
        }
        
        const auto& rule = ruleIt->second;
        std::string alertId = GenerateAlertId();
        
        Alert alert(alertId, ruleId, title, message, rule.level);
        alert.labels = labels;
        
        {
            std::lock_guard<std::mutex> alertLock(m_alertsMutex);
            m_activeAlerts[alertId] = alert;
        }
        
        // 发送通知
        SendNotifications(alert, rule);
        
        LOG_INFO("ALERT", "Alert triggered: " + title);
    }
    
    // 确认告警
    void AcknowledgeAlert(const std::string& alertId, const std::string& acknowledgedBy)
    {
        std::lock_guard<std::mutex> lock(m_alertsMutex);
        auto it = m_activeAlerts.find(alertId);
        if (it != m_activeAlerts.end())
        {
            it->second.status = AlertStatus::Acknowledged;
            it->second.acknowledgedBy = acknowledgedBy;
            it->second.lastUpdate = std::chrono::system_clock::now();
            
            LOG_INFO("ALERT", "Alert acknowledged: " + alertId + " by " + acknowledgedBy);
        }
    }
    
    // 解决告警
    void ResolveAlert(const std::string& alertId, const std::string& resolvedBy)
    {
        std::lock_guard<std::mutex> lock(m_alertsMutex);
        auto it = m_activeAlerts.find(alertId);
        if (it != m_activeAlerts.end())
        {
            it->second.status = AlertStatus::Resolved;
            it->second.resolvedBy = resolvedBy;
            it->second.resolvedTime = std::chrono::system_clock::now();
            it->second.lastUpdate = it->second.resolvedTime;
            
            // 移动到历史告警
            m_historicalAlerts.push_back(it->second);
            m_activeAlerts.erase(it);
            
            // 限制历史告警数量
            if (m_historicalAlerts.size() > MAX_HISTORICAL_ALERTS)
            {
                m_historicalAlerts.erase(m_historicalAlerts.begin());
            }
            
            LOG_INFO("ALERT", "Alert resolved: " + alertId + " by " + resolvedBy);
        }
    }
    
    // 添加通知发送器
    void AddNotificationSender(std::unique_ptr<INotificationSender> sender)
    {
        std::lock_guard<std::mutex> lock(m_sendersMutex);
        m_notificationSenders[sender->GetChannelType()] = std::move(sender);
    }
    
    // 获取活跃告警
    std::vector<Alert> GetActiveAlerts() const
    {
        std::lock_guard<std::mutex> lock(m_alertsMutex);
        std::vector<Alert> alerts;
        for (const auto& pair : m_activeAlerts)
        {
            alerts.push_back(pair.second);
        }
        return alerts;
    }
    
    // 获取告警统计
    struct AlertStatistics
    {
        size_t totalActive = 0;
        size_t totalAcknowledged = 0;
        size_t totalResolved = 0;
        size_t criticalAlerts = 0;
        size_t emergencyAlerts = 0;
    };
    
    AlertStatistics GetAlertStatistics() const
    {
        std::lock_guard<std::mutex> lock(m_alertsMutex);
        AlertStatistics stats;
        
        for (const auto& pair : m_activeAlerts)
        {
            const auto& alert = pair.second;
            stats.totalActive++;
            
            if (alert.status == AlertStatus::Acknowledged)
                stats.totalAcknowledged++;
            
            if (alert.level == AlertLevel::Critical)
                stats.criticalAlerts++;
            else if (alert.level == AlertLevel::Emergency)
                stats.emergencyAlerts++;
        }
        
        stats.totalResolved = m_historicalAlerts.size();
        return stats;
    }

private:
    std::atomic<bool> m_running;
    std::thread m_evaluationThread;
    std::thread m_notificationThread;
    
    mutable std::mutex m_rulesMutex;
    std::unordered_map<std::string, AlertRule> m_alertRules;
    
    mutable std::mutex m_alertsMutex;
    std::unordered_map<std::string, Alert> m_activeAlerts;
    std::vector<Alert> m_historicalAlerts;
    
    mutable std::mutex m_sendersMutex;
    std::unordered_map<NotificationChannel, std::unique_ptr<INotificationSender>> m_notificationSenders;
    
    std::queue<NotificationMessage> m_notificationQueue;
    std::mutex m_notificationMutex;
    std::condition_variable m_notificationCondition;
    
    static constexpr size_t MAX_HISTORICAL_ALERTS = 10000;
    
    std::string GenerateAlertId()
    {
        static std::atomic<uint64_t> counter{0};
        auto now = std::chrono::system_clock::now().time_since_epoch().count();
        return "alert_" + std::to_string(now) + "_" + std::to_string(counter++);
    }
    
    void EvaluationLoop()
    {
        while (m_running)
        {
            try
            {
                // 这里可以添加定期评估告警规则的逻辑
                // 例如检查性能指标是否超过阈值等
                
                std::this_thread::sleep_for(std::chrono::seconds(10));
            }
            catch (const std::exception& e)
            {
                LOG_ERROR("ALERT", std::string("Error in evaluation loop: ") + e.what());
            }
        }
    }
    
    void NotificationLoop()
    {
        while (m_running)
        {
            std::unique_lock<std::mutex> lock(m_notificationMutex);
            m_notificationCondition.wait(lock, [this] { 
                return !m_notificationQueue.empty() || !m_running; 
            });
            
            while (!m_notificationQueue.empty())
            {
                NotificationMessage message = m_notificationQueue.front();
                m_notificationQueue.pop();
                lock.unlock();
                
                // 发送通知
                SendNotificationMessage(message);
                
                lock.lock();
            }
        }
    }
    
    void SendNotifications(const Alert& alert, const AlertRule& rule)
    {
        for (NotificationChannel channel : rule.channels)
        {
            NotificationMessage message(channel, GetDefaultRecipient(channel),
                                      alert.title, alert.message, alert.level);
            
            std::lock_guard<std::mutex> lock(m_notificationMutex);
            m_notificationQueue.push(message);
        }
        m_notificationCondition.notify_one();
    }
    
    void SendNotificationMessage(const NotificationMessage& message)
    {
        std::lock_guard<std::mutex> lock(m_sendersMutex);
        auto it = m_notificationSenders.find(message.channel);
        if (it != m_notificationSenders.end() && it->second->IsAvailable())
        {
            try
            {
                bool success = it->second->SendNotification(message);
                if (success)
                {
                    LOG_DEBUG("ALERT", "Notification sent successfully: " + message.id);
                }
                else
                {
                    LOG_ERROR("ALERT", "Failed to send notification: " + message.id);
                }
            }
            catch (const std::exception& e)
            {
                LOG_ERROR("ALERT", std::string("Exception sending notification: ") + e.what());
            }
        }
    }
    
    std::string GetDefaultRecipient(NotificationChannel channel)
    {
        switch (channel)
        {
        case NotificationChannel::Email: return "<EMAIL>";
        case NotificationChannel::SMS: return "+1234567890";
        case NotificationChannel::Webhook: return "http://webhook.example.com";
        case NotificationChannel::File: return "alerts.log";
        default: return "";
        }
    }
};

} // namespace sword2

// 全局告警管理器访问
#define ALERT_MANAGER() sword2::AlertManager::getInstance()

// 便捷宏定义
#define START_ALERT_SYSTEM() ALERT_MANAGER().Start()
#define STOP_ALERT_SYSTEM() ALERT_MANAGER().Stop()

#define TRIGGER_ALERT(ruleId, title, message) \
    ALERT_MANAGER().TriggerAlert(ruleId, title, message)

#define TRIGGER_ALERT_WITH_LABELS(ruleId, title, message, labels) \
    ALERT_MANAGER().TriggerAlert(ruleId, title, message, labels)

#define ACKNOWLEDGE_ALERT(alertId, user) \
    ALERT_MANAGER().AcknowledgeAlert(alertId, user)

#define RESOLVE_ALERT(alertId, user) \
    ALERT_MANAGER().ResolveAlert(alertId, user)

#endif // ALERT_NOTIFICATION_SYSTEM_H
