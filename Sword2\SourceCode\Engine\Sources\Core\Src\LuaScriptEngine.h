//---------------------------------------------------------------------------
// Sword2 Lua Script Engine (c) 2024
//
// File:	LuaScriptEngine.h
// Date:	2024.07.02
// Code:	AI Assistant
// Desc:	Comprehensive Lua script engine for Sword2 game logic
//---------------------------------------------------------------------------
#ifndef LUA_SCRIPT_ENGINE_H
#define LUA_SCRIPT_ENGINE_H

#include "ModernCpp.h"
#include "UnifiedLoggingSystem.h"
#include "PlayerSystem.h"
#include "GameDataSystem.h"
#include <string>
#include <unordered_map>
#include <vector>
#include <memory>
#include <functional>
#include <mutex>
#include <filesystem>

// 前向声明Lua相关类型
extern "C" {
    #include "lua.h"
    #include "lauxlib.h"
    #include "lualib.h"
}

namespace sword2 {

// 脚本执行结果
enum class ScriptResult : uint8_t
{
    Success = 0,            // 成功
    FileNotFound,           // 文件未找到
    SyntaxError,            // 语法错误
    RuntimeError,           // 运行时错误
    MemoryError,            // 内存错误
    TypeError,              // 类型错误
    Unknown                 // 未知错误
};

// 脚本类型
enum class ScriptType : uint8_t
{
    Global = 0,             // 全局脚本
    NPC,                    // NPC脚本
    Item,                   // 物品脚本
    Mission,                // 任务脚本
    Skill,                  // 技能脚本
    Map,                    // 地图脚本
    Event                   // 事件脚本
};

// Lua值包装器
class LuaValue
{
public:
    enum Type { Nil, Boolean, Number, String, Table, Function };
    
    LuaValue() : m_type(Nil) {}
    LuaValue(bool value) : m_type(Boolean), m_boolValue(value) {}
    LuaValue(double value) : m_type(Number), m_numberValue(value) {}
    LuaValue(const std::string& value) : m_type(String), m_stringValue(value) {}
    
    Type GetType() const { return m_type; }
    bool AsBool() const { return m_boolValue; }
    double AsNumber() const { return m_numberValue; }
    const std::string& AsString() const { return m_stringValue; }
    
    bool IsNil() const { return m_type == Nil; }
    bool IsBool() const { return m_type == Boolean; }
    bool IsNumber() const { return m_type == Number; }
    bool IsString() const { return m_type == String; }
    bool IsTable() const { return m_type == Table; }
    bool IsFunction() const { return m_type == Function; }

private:
    Type m_type = Nil;
    bool m_boolValue = false;
    double m_numberValue = 0.0;
    std::string m_stringValue;
};

// Lua函数绑定器
class LuaFunctionBinder
{
public:
    using CFunction = std::function<int(lua_State*)>;
    
    static void RegisterFunction(const std::string& name, CFunction func);
    static void RegisterGlobalFunctions(lua_State* L);
    
private:
    static std::unordered_map<std::string, CFunction> s_functions;
};

// Lua脚本上下文
class LuaScriptContext
{
public:
    LuaScriptContext();
    ~LuaScriptContext();
    
    // 禁用拷贝
    LuaScriptContext(const LuaScriptContext&) = delete;
    LuaScriptContext& operator=(const LuaScriptContext&) = delete;
    
    // 初始化Lua状态
    bool Initialize();
    
    // 清理Lua状态
    void Cleanup();
    
    // 执行Lua脚本文件
    ScriptResult ExecuteFile(const std::string& filePath);
    
    // 执行Lua代码字符串
    ScriptResult ExecuteString(const std::string& code);
    
    // 调用Lua函数
    ScriptResult CallFunction(const std::string& functionName, const std::vector<LuaValue>& args = {}, std::vector<LuaValue>* results = nullptr);
    
    // 设置全局变量
    void SetGlobal(const std::string& name, const LuaValue& value);
    
    // 获取全局变量
    LuaValue GetGlobal(const std::string& name);
    
    // 检查函数是否存在
    bool HasFunction(const std::string& functionName);
    
    // 获取Lua状态指针
    lua_State* GetLuaState() { return m_luaState; }
    
    // 获取最后的错误信息
    const std::string& GetLastError() const { return m_lastError; }

private:
    lua_State* m_luaState;
    std::string m_lastError;
    bool m_initialized;
    
    // 错误处理
    void HandleError(int result);
    
    // 将C++值推入Lua栈
    void PushValue(const LuaValue& value);
    
    // 从Lua栈获取值
    LuaValue GetValue(int index);
    
    // 注册游戏API函数
    void RegisterGameAPI();
};

// 脚本管理器
class LuaScriptManager : public Singleton<LuaScriptManager>
{
public:
    LuaScriptManager();
    ~LuaScriptManager();
    
    // 初始化脚本管理器
    bool Initialize(const std::string& scriptRootPath = "script");
    
    // 关闭脚本管理器
    void Shutdown();
    
    // 加载脚本文件
    ScriptResult LoadScript(const std::string& scriptPath, ScriptType type = ScriptType::Global);
    
    // 重新加载脚本
    ScriptResult ReloadScript(const std::string& scriptPath);
    
    // 执行脚本函数
    ScriptResult ExecuteFunction(const std::string& functionName, const std::vector<LuaValue>& args = {}, std::vector<LuaValue>* results = nullptr);
    
    // 执行NPC脚本
    ScriptResult ExecuteNPCScript(uint32_t npcId, const std::string& event, uint32_t playerId = 0);
    
    // 执行物品脚本
    ScriptResult ExecuteItemScript(uint32_t itemId, const std::string& event, uint32_t playerId = 0);
    
    // 执行任务脚本
    ScriptResult ExecuteMissionScript(uint32_t missionId, const std::string& event, uint32_t playerId = 0);
    
    // 执行技能脚本
    ScriptResult ExecuteSkillScript(uint32_t skillId, const std::string& event, uint32_t playerId = 0);
    
    // 设置脚本变量
    void SetScriptVariable(const std::string& name, const LuaValue& value);
    
    // 获取脚本变量
    LuaValue GetScriptVariable(const std::string& name);
    
    // 注册C++函数到Lua
    void RegisterFunction(const std::string& name, LuaFunctionBinder::CFunction func);
    
    // 获取脚本统计信息
    struct ScriptStatistics
    {
        size_t totalScripts = 0;
        size_t loadedScripts = 0;
        size_t failedScripts = 0;
        std::unordered_map<ScriptType, size_t> scriptsByType;
        std::string rootPath;
    };
    
    ScriptStatistics GetStatistics() const;
    
    // 热重载所有脚本
    bool ReloadAllScripts();
    
    // 检查脚本文件是否存在
    bool ScriptExists(const std::string& scriptPath);
    
    // 获取脚本完整路径
    std::string GetFullScriptPath(const std::string& scriptPath);

private:
    std::unique_ptr<LuaScriptContext> m_context;
    std::string m_scriptRootPath;
    std::unordered_map<std::string, ScriptType> m_loadedScripts;
    std::unordered_map<std::string, std::string> m_scriptErrors;
    mutable std::mutex m_scriptMutex;
    bool m_initialized;
    
    // 扫描脚本目录
    void ScanScriptDirectory();
    
    // 确定脚本类型
    ScriptType DetermineScriptType(const std::string& scriptPath);
    
    // 加载全局脚本
    void LoadGlobalScripts();
    
    // 加载autoexec.lua
    void LoadAutoExecScript();
    
    // 处理Include指令
    void ProcessIncludeDirectives(const std::string& scriptContent, const std::string& currentPath);
    
    // 读取脚本文件内容
    std::string ReadScriptFile(const std::string& filePath);
    
    // 标准化脚本路径
    std::string NormalizePath(const std::string& path);
};

// 游戏API函数声明
namespace GameAPI {
    // 玩家相关API
    int GetPlayerName(lua_State* L);
    int GetPlayerLevel(lua_State* L);
    int GetPlayerMoney(lua_State* L);
    int SetPlayerMoney(lua_State* L);
    int AddPlayerExp(lua_State* L);
    int GetPlayerPosition(lua_State* L);
    int SetPlayerPosition(lua_State* L);
    int TeleportPlayer(lua_State* L);
    
    // 物品相关API
    int GiveItem(lua_State* L);
    int RemoveItem(lua_State* L);
    int GetItemCount(lua_State* L);
    int HasItem(lua_State* L);
    
    // 任务相关API
    int StartMission(lua_State* L);
    int CompleteMission(lua_State* L);
    int GetMissionStatus(lua_State* L);
    int SetTaskValue(lua_State* L);
    int GetTaskValue(lua_State* L);
    
    // 对话相关API
    int Talk(lua_State* L);
    int Say(lua_State* L);
    int Ask(lua_State* L);
    int SelectOption(lua_State* L);
    
    // 系统相关API
    int WriteLog(lua_State* L);
    int GetCurrentTime(lua_State* L);
    int Random(lua_State* L);
    int Broadcast(lua_State* L);
    
    // 地图相关API
    int GetMapId(lua_State* L);
    int GetMapName(lua_State* L);
    int CreateNPC(lua_State* L);
    int RemoveNPC(lua_State* L);
    
    // 战斗相关API
    int GetPlayerHP(lua_State* L);
    int SetPlayerHP(lua_State* L);
    int GetPlayerMP(lua_State* L);
    int SetPlayerMP(lua_State* L);
    int DamagePlayer(lua_State* L);
    int HealPlayer(lua_State* L);
}

} // namespace sword2

// 全局脚本管理器访问
#define SCRIPT_MANAGER() sword2::LuaScriptManager::getInstance()

// 便捷宏定义
#define INIT_SCRIPT_ENGINE(rootPath) SCRIPT_MANAGER().Initialize(rootPath)
#define SHUTDOWN_SCRIPT_ENGINE() SCRIPT_MANAGER().Shutdown()

#define LOAD_SCRIPT(path, type) SCRIPT_MANAGER().LoadScript(path, type)
#define RELOAD_SCRIPT(path) SCRIPT_MANAGER().ReloadScript(path)
#define EXECUTE_FUNCTION(name, args, results) SCRIPT_MANAGER().ExecuteFunction(name, args, results)

#define EXECUTE_NPC_SCRIPT(npcId, event, playerId) SCRIPT_MANAGER().ExecuteNPCScript(npcId, event, playerId)
#define EXECUTE_ITEM_SCRIPT(itemId, event, playerId) SCRIPT_MANAGER().ExecuteItemScript(itemId, event, playerId)
#define EXECUTE_MISSION_SCRIPT(missionId, event, playerId) SCRIPT_MANAGER().ExecuteMissionScript(missionId, event, playerId)

#define SET_SCRIPT_VAR(name, value) SCRIPT_MANAGER().SetScriptVariable(name, value)
#define GET_SCRIPT_VAR(name) SCRIPT_MANAGER().GetScriptVariable(name)

#define REGISTER_LUA_FUNCTION(name, func) SCRIPT_MANAGER().RegisterFunction(name, func)

#endif // LUA_SCRIPT_ENGINE_H
