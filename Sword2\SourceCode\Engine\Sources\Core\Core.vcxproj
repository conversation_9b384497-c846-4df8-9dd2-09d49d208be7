﻿<?xml version="1.0" encoding="utf-8"?>
<Project DefaultTargets="Build" ToolsVersion="4.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <ItemGroup Label="ProjectConfigurations">
    <ProjectConfiguration Include="Client Debug|Win32">
      <Configuration>Client Debug</Configuration>
      <Platform>Win32</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Client Release|Win32">
      <Configuration>Client Release</Configuration>
      <Platform>Win32</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Server Debug|Win32">
      <Configuration>Server Debug</Configuration>
      <Platform>Win32</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Server Release|Win32">
      <Configuration>Server Release</Configuration>
      <Platform>Win32</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Template|Win32">
      <Configuration>Template</Configuration>
      <Platform>Win32</Platform>
    </ProjectConfiguration>
  </ItemGroup>
  <PropertyGroup Label="Globals">
    <SccProjectName>"$/SwordOnline/Sources/Core", FPAAAAAA</SccProjectName>
    <SccLocalPath>.</SccLocalPath>
    <ProjectGuid>{5FE1CA1B-F4FB-AA57-F5E6-891ED8922F91}</ProjectGuid>
    <WindowsTargetPlatformVersion>10.0</WindowsTargetPlatformVersion>
  </PropertyGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.Default.props" />
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Template|Win32'" Label="Configuration">
    <ConfigurationType>Application</ConfigurationType>
    <PlatformToolset>v143</PlatformToolset>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Server Release|Win32'" Label="Configuration">
    <ConfigurationType>DynamicLibrary</ConfigurationType>
    <UseOfMfc>false</UseOfMfc>
    <CharacterSet>MultiByte</CharacterSet>
    <PlatformToolset>v143</PlatformToolset>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Server Debug|Win32'" Label="Configuration">
    <ConfigurationType>DynamicLibrary</ConfigurationType>
    <UseOfMfc>false</UseOfMfc>
    <CharacterSet>MultiByte</CharacterSet>
    <PlatformToolset>v143</PlatformToolset>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Client Release|Win32'" Label="Configuration">
    <ConfigurationType>DynamicLibrary</ConfigurationType>
    <UseOfMfc>false</UseOfMfc>
    <CharacterSet>MultiByte</CharacterSet>
    <PlatformToolset>v143</PlatformToolset>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Client Debug|Win32'" Label="Configuration">
    <ConfigurationType>DynamicLibrary</ConfigurationType>
    <UseOfMfc>false</UseOfMfc>
    <CharacterSet>MultiByte</CharacterSet>
    <PlatformToolset>v143</PlatformToolset>
  </PropertyGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.props" />
  <ImportGroup Label="ExtensionSettings">
  </ImportGroup>
  <ImportGroup Label="PropertySheets" Condition="'$(Configuration)|$(Platform)'=='Template|Win32'">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  <ImportGroup Condition="'$(Configuration)|$(Platform)'=='Server Release|Win32'" Label="PropertySheets">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
    <Import Project="$(VCTargetsPath)Microsoft.Cpp.UpgradeFromVC60.props" />
  </ImportGroup>
  <ImportGroup Condition="'$(Configuration)|$(Platform)'=='Server Debug|Win32'" Label="PropertySheets">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
    <Import Project="$(VCTargetsPath)Microsoft.Cpp.UpgradeFromVC60.props" />
  </ImportGroup>
  <ImportGroup Condition="'$(Configuration)|$(Platform)'=='Client Release|Win32'" Label="PropertySheets">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
    <Import Project="$(VCTargetsPath)Microsoft.Cpp.UpgradeFromVC60.props" />
  </ImportGroup>
  <ImportGroup Condition="'$(Configuration)|$(Platform)'=='Client Debug|Win32'" Label="PropertySheets">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
    <Import Project="$(VCTargetsPath)Microsoft.Cpp.UpgradeFromVC60.props" />
  </ImportGroup>
  <PropertyGroup Label="UserMacros" />
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Client Debug|Win32'">
    <OutDir>.\ClientDebug\</OutDir>
    <IntDir>.\ClientDebug\</IntDir>
    <LinkIncremental>false</LinkIncremental>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Client Release|Win32'">
    <OutDir>.\ClientRelease\</OutDir>
    <IntDir>.\ClientRelease\</IntDir>
    <LinkIncremental>false</LinkIncremental>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Server Debug|Win32'">
    <OutDir>.\ServerDebug\</OutDir>
    <IntDir>.\ServerDebug\</IntDir>
    <LinkIncremental>false</LinkIncremental>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Server Release|Win32'">
    <OutDir>.\ServerRelease\</OutDir>
    <IntDir>.\ServerRelease\</IntDir>
    <LinkIncremental>false</LinkIncremental>
  </PropertyGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Client Debug|Win32'">
    <ClCompile>
      <RuntimeLibrary>MultiThreadedDebugDLL</RuntimeLibrary>
      <InlineFunctionExpansion>Default</InlineFunctionExpansion>
      <FunctionLevelLinking>false</FunctionLevelLinking>
      <Optimization>Disabled</Optimization>
      <SuppressStartupBanner>true</SuppressStartupBanner>
      <WarningLevel>Level3</WarningLevel>
      <MinimalRebuild>true</MinimalRebuild>
      <IgnoreStandardIncludePath>false</IgnoreStandardIncludePath>
      <AdditionalIncludeDirectories>..\engine\src;..\..\Headers;..\engine\include;..\Network;src;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <PreprocessorDefinitions>_DEBUG;WIN32;_WINDOWS;_USRDLL;CORE_EXPORTS;SWORDONLINE_SHOW_DBUG_INFO;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <AssemblerListingLocation>.\ClientDebug\</AssemblerListingLocation>
      <BrowseInformation>true</BrowseInformation>
      <PrecompiledHeaderOutputFile>.\ClientDebug\Core.pch</PrecompiledHeaderOutputFile>
      <PrecompiledHeader>Use</PrecompiledHeader>
      <PrecompiledHeaderFile>KCore.h</PrecompiledHeaderFile>
      <ObjectFileName>.\ClientDebug\</ObjectFileName>
      <ProgramDataBaseFileName>.\ClientDebug\</ProgramDataBaseFileName>
      <BasicRuntimeChecks>EnableFastChecks</BasicRuntimeChecks>
    </ClCompile>
    <PostBuildEvent>
      <Command>md ..\..\..\bin\client\debug
copy ClientDebug\CoreClient.dll ..\..\..\bin\client\CoreClient.dll
copy ClientDebug\CoreClient.dll ..\..\..\bin\client\debug\CoreClient.dll

md ..\..\lib\debug
copy ClientDebug\CoreClient.lib ..\..\lib\debug\CoreClient.lib</Command>
    </PostBuildEvent>
    <Midl>
      <SuppressStartupBanner>true</SuppressStartupBanner>
      <PreprocessorDefinitions>_DEBUG;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <TypeLibraryName>.\ClientDebug\Core.tlb</TypeLibraryName>
      <MkTypLibCompatible>true</MkTypLibCompatible>
      <TargetEnvironment>Win32</TargetEnvironment>
    </Midl>
    <ResourceCompile>
      <Culture>0x0409</Culture>
      <PreprocessorDefinitions>_DEBUG;%(PreprocessorDefinitions)</PreprocessorDefinitions>
    </ResourceCompile>
    <Bscmake>
      <SuppressStartupBanner>true</SuppressStartupBanner>
      <OutputFile>.\ClientDebug\Core.bsc</OutputFile>
    </Bscmake>
    <Link>
      <SuppressStartupBanner>true</SuppressStartupBanner>
      <LinkDLL>true</LinkDLL>
      <GenerateDebugInformation>true</GenerateDebugInformation>
      <SubSystem>Console</SubSystem>
      <OutputFile>ClientDebug\CoreClient.dll</OutputFile>
      <ImportLibrary>.\ClientDebug\CoreClient.lib</ImportLibrary>
      <AdditionalLibraryDirectories>../../lib/;%(AdditionalLibraryDirectories)</AdditionalLibraryDirectories>
      <AdditionalDependencies>odbc32.lib;odbccp32.lib;lualibdll.lib;Shlwapi.lib;Winmm.lib;%(AdditionalDependencies)</AdditionalDependencies>
    </Link>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Client Release|Win32'">
    <ClCompile>
      <RuntimeLibrary>MultiThreadedDLL</RuntimeLibrary>
      <InlineFunctionExpansion>OnlyExplicitInline</InlineFunctionExpansion>
      <StringPooling>true</StringPooling>
      <FunctionLevelLinking>true</FunctionLevelLinking>
      <Optimization>MaxSpeed</Optimization>
      <SuppressStartupBanner>true</SuppressStartupBanner>
      <WarningLevel>Level2</WarningLevel>
      <AdditionalIncludeDirectories>..\engine\src;..\engine\include;..\Network;..\..\Headers;src;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <PreprocessorDefinitions>NDEBUG;WIN32;_WINDOWS;_USRDLL;CORE_EXPORTS;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <AssemblerListingLocation>.\ClientRelease\</AssemblerListingLocation>
      <BrowseInformation>true</BrowseInformation>
      <PrecompiledHeaderOutputFile>.\ClientRelease\Core.pch</PrecompiledHeaderOutputFile>
      <PrecompiledHeader>Use</PrecompiledHeader>
      <PrecompiledHeaderFile>KCore.h</PrecompiledHeaderFile>
      <ObjectFileName>.\ClientRelease\</ObjectFileName>
      <ProgramDataBaseFileName>.\ClientRelease\</ProgramDataBaseFileName>
    </ClCompile>
    <PostBuildEvent>
      <Command>md ..\..\..\bin\client\release
md ..\..\lib\release
copy ClientRelease\CoreClient.pdb ..\..\..\bin\client\CoreClient.pdb
copy ClientRelease\CoreClient.map ..\..\..\bin\client\CoreClient.map
copy ClientRelease\CoreClient.dll ..\..\..\bin\client\CoreClient.dll
copy ClientRelease\CoreClient.pdb ..\..\..\bin\client\release\CoreClient.pdb
copy ClientRelease\CoreClient.map ..\..\..\bin\client\release\CoreClient.map
copy ClientRelease\CoreClient.dll ..\..\..\bin\client\release\CoreClient.dll
copy ClientRelease\CoreClient.lib ..\..\lib\release\CoreClient.lib</Command>
    </PostBuildEvent>
    <Midl>
      <SuppressStartupBanner>true</SuppressStartupBanner>
      <PreprocessorDefinitions>NDEBUG;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <TypeLibraryName>.\ClientRelease\Core.tlb</TypeLibraryName>
      <MkTypLibCompatible>true</MkTypLibCompatible>
      <TargetEnvironment>Win32</TargetEnvironment>
    </Midl>
    <ResourceCompile>
      <Culture>0x0804</Culture>
      <PreprocessorDefinitions>NDEBUG;%(PreprocessorDefinitions)</PreprocessorDefinitions>
    </ResourceCompile>
    <Bscmake>
      <SuppressStartupBanner>true</SuppressStartupBanner>
      <OutputFile>.\ClientRelease\Core.bsc</OutputFile>
    </Bscmake>
    <Link>
      <SuppressStartupBanner>true</SuppressStartupBanner>
      <LinkDLL>true</LinkDLL>
      <GenerateDebugInformation>true</GenerateDebugInformation>
      <SubSystem>Console</SubSystem>
      <OutputFile>ClientRelease\CoreClient.dll</OutputFile>
      <ImportLibrary>.\ClientRelease\CoreClient.lib</ImportLibrary>
      <AdditionalLibraryDirectories>../../lib/;%(AdditionalLibraryDirectories)</AdditionalLibraryDirectories>
      <AdditionalDependencies>odbc32.lib;odbccp32.lib;lualibdll.lib;Shlwapi.lib;Winmm.lib;%(AdditionalDependencies)</AdditionalDependencies>
    </Link>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Server Debug|Win32'">
    <ClCompile>
      <RuntimeLibrary>MultiThreadedDebug</RuntimeLibrary>
      <InlineFunctionExpansion>Default</InlineFunctionExpansion>
      <FunctionLevelLinking>false</FunctionLevelLinking>
      <Optimization>Disabled</Optimization>
      <SuppressStartupBanner>true</SuppressStartupBanner>
      <WarningLevel>Level2</WarningLevel>
      <MinimalRebuild>true</MinimalRebuild>
      <AdditionalIncludeDirectories>..\engine\src;..\engine\include;..\Network;..\..\Headers;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <PreprocessorDefinitions>_DEBUG;_SERVER;WIN32;_WINDOWS;_USRDLL;CORE_EXPORTS;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <AssemblerListingLocation>.\ServerDebug\</AssemblerListingLocation>
      <BrowseInformation>true</BrowseInformation>
      <PrecompiledHeaderOutputFile>.\ServerDebug\Core.pch</PrecompiledHeaderOutputFile>
      <PrecompiledHeader>Use</PrecompiledHeader>
      <PrecompiledHeaderFile>KCore.h</PrecompiledHeaderFile>
      <ObjectFileName>.\ServerDebug\</ObjectFileName>
      <ProgramDataBaseFileName>.\ServerDebug\</ProgramDataBaseFileName>
      <AdditionalOptions> /LD </AdditionalOptions>
      <BasicRuntimeChecks>EnableFastChecks</BasicRuntimeChecks>
      <MultiProcessorCompilation>true</MultiProcessorCompilation>
    </ClCompile>
    <PostBuildEvent>
      <Command>md ..\..\..\bin\server\debug
copy ServerDebug\CoreServer.dll ..\..\..\bin\server\Coreserver.dll
copy ServerDebug\CoreServer.dll ..\..\..\bin\server\debug\Coreserver.dll

md ..\..\lib\debug
copy ServerDebug\CoreServer.lib ..\..\lib\debug\CoreServer.lib</Command>
    </PostBuildEvent>
    <Midl>
      <SuppressStartupBanner>true</SuppressStartupBanner>
      <PreprocessorDefinitions>_DEBUG;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <TypeLibraryName>.\ServerDebug\Core.tlb</TypeLibraryName>
      <MkTypLibCompatible>true</MkTypLibCompatible>
      <TargetEnvironment>Win32</TargetEnvironment>
    </Midl>
    <ResourceCompile>
      <Culture>0x0409</Culture>
      <PreprocessorDefinitions>_DEBUG;%(PreprocessorDefinitions)</PreprocessorDefinitions>
    </ResourceCompile>
    <Bscmake>
      <SuppressStartupBanner>true</SuppressStartupBanner>
      <OutputFile>.\ServerDebug\Core.bsc</OutputFile>
    </Bscmake>
    <Link>
      <SuppressStartupBanner>true</SuppressStartupBanner>
      <LinkDLL>true</LinkDLL>
      <GenerateDebugInformation>true</GenerateDebugInformation>
      <SubSystem>Console</SubSystem>
      <IgnoreSpecificDefaultLibraries>libc.lib;libcmt.lib;msvcrt.lib;libcd.lib;msvcrtd.lib;%(IgnoreSpecificDefaultLibraries)</IgnoreSpecificDefaultLibraries>
      <OutputFile>ServerDebug\CoreServer.dll</OutputFile>
      <ImportLibrary>.\ServerDebug\CoreServer.lib</ImportLibrary>
      <AdditionalLibraryDirectories>../../lib/;%(AdditionalLibraryDirectories)</AdditionalLibraryDirectories>
      <AdditionalDependencies>odbc32.lib;odbccp32.lib;lualibdll.lib;Ws2_32.lib;Winmm.lib;Shlwapi.lib;%(AdditionalDependencies)</AdditionalDependencies>
    </Link>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Server Release|Win32'">
    <ClCompile>
      <RuntimeLibrary>MultiThreaded</RuntimeLibrary>
      <InlineFunctionExpansion>OnlyExplicitInline</InlineFunctionExpansion>
      <StringPooling>true</StringPooling>
      <FunctionLevelLinking>true</FunctionLevelLinking>
      <Optimization>MaxSpeed</Optimization>
      <SuppressStartupBanner>true</SuppressStartupBanner>
      <WarningLevel>Level2</WarningLevel>
      <AdditionalIncludeDirectories>..\engine\src;..\engine\include;..\Network;..\..\Headers;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <PreprocessorDefinitions>NDEBUG;_SERVER;WIN32;_WINDOWS;_USRDLL;CORE_EXPORTS;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <AssemblerListingLocation>.\ServerRelease\</AssemblerListingLocation>
      <BrowseInformation>true</BrowseInformation>
      <PrecompiledHeaderOutputFile>.\ServerRelease\Core.pch</PrecompiledHeaderOutputFile>
      <PrecompiledHeader>Use</PrecompiledHeader>
      <PrecompiledHeaderFile>KCore.h</PrecompiledHeaderFile>
      <ObjectFileName>.\ServerRelease\</ObjectFileName>
      <ProgramDataBaseFileName>.\ServerRelease\</ProgramDataBaseFileName>
    </ClCompile>
    <PostBuildEvent>
      <Command>md ..\..\..\bin\server\release
md ..\..\lib\release
copy ServerRelease\CoreServer.map ..\..\..\bin\server\Coreserver.map
copy ServerRelease\CoreServer.pdb ..\..\..\bin\server\Coreserver.pdb
copy ServerRelease\CoreServer.dll ..\..\..\bin\server\Coreserver.dll
copy ServerRelease\CoreServer.map ..\..\..\bin\server\release\Coreserver.map
copy ServerRelease\CoreServer.pdb ..\..\..\bin\server\release\Coreserver.pdb
copy ServerRelease\CoreServer.dll ..\..\..\bin\server\release\Coreserver.dll
copy ServerRelease\CoreServer.lib ..\..\lib\release\CoreServer.lib</Command>
    </PostBuildEvent>
    <Midl>
      <SuppressStartupBanner>true</SuppressStartupBanner>
      <PreprocessorDefinitions>NDEBUG;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <TypeLibraryName>.\ServerRelease\Core.tlb</TypeLibraryName>
      <MkTypLibCompatible>true</MkTypLibCompatible>
      <TargetEnvironment>Win32</TargetEnvironment>
    </Midl>
    <ResourceCompile>
      <Culture>0x0804</Culture>
      <PreprocessorDefinitions>NDEBUG;%(PreprocessorDefinitions)</PreprocessorDefinitions>
    </ResourceCompile>
    <Bscmake>
      <SuppressStartupBanner>true</SuppressStartupBanner>
      <OutputFile>.\ServerRelease\Core.bsc</OutputFile>
    </Bscmake>
    <Link>
      <SuppressStartupBanner>true</SuppressStartupBanner>
      <LinkDLL>true</LinkDLL>
      <GenerateDebugInformation>true</GenerateDebugInformation>
      <SubSystem>Console</SubSystem>
      <OutputFile>ServerRelease\CoreServer.dll</OutputFile>
      <ImportLibrary>.\ServerRelease\CoreServer.lib</ImportLibrary>
      <AdditionalDependencies>odbc32.lib;odbccp32.lib;lualibdll.lib;Ws2_32.lib;Winmm.lib;Shlwapi.lib;%(AdditionalDependencies)</AdditionalDependencies>
    </Link>
  </ItemDefinitionGroup>
  <ItemGroup>
    <ClCompile Include="Src\KPartnerSkill.cpp">
      <AdditionalOptions Condition="'$(Configuration)|$(Platform)'=='Server Debug|Win32'"> /LD   /LD </AdditionalOptions>
    </ClCompile>
    <ClCompile Include="Src\KPlayerPartner.cpp">
      <AdditionalOptions Condition="'$(Configuration)|$(Platform)'=='Server Debug|Win32'"> /LD   /LD </AdditionalOptions>
    </ClCompile>
    <ClCompile Include="Src\KThiefSkill.cpp">
      <AdditionalOptions Condition="'$(Configuration)|$(Platform)'=='Server Debug|Win32'"> /LD   /LD </AdditionalOptions>
    </ClCompile>
    <ClCompile Include="Src\KMissle.cpp">
      <AssemblerOutput Condition="'$(Configuration)|$(Platform)'=='Client Release|Win32'">AssemblyAndSourceCode</AssemblerOutput>
      <AdditionalOptions Condition="'$(Configuration)|$(Platform)'=='Server Debug|Win32'"> /LD   /LD </AdditionalOptions>
    </ClCompile>
    <ClCompile Include="Src\KMissleRes.cpp">
      <AdditionalOptions Condition="'$(Configuration)|$(Platform)'=='Server Debug|Win32'"> /LD   /LD </AdditionalOptions>
    </ClCompile>
    <ClCompile Include="Src\KMissleSet.cpp">
      <AdditionalOptions Condition="'$(Configuration)|$(Platform)'=='Server Debug|Win32'"> /LD   /LD </AdditionalOptions>
    </ClCompile>
    <ClCompile Include="Src\KSkillList.cpp">
      <AdditionalOptions Condition="'$(Configuration)|$(Platform)'=='Server Debug|Win32'"> /LD   /LD </AdditionalOptions>
    </ClCompile>
    <ClCompile Include="Src\KSkillManager.cpp">
      <AdditionalOptions Condition="'$(Configuration)|$(Platform)'=='Server Debug|Win32'"> /LD   /LD </AdditionalOptions>
    </ClCompile>
    <ClCompile Include="Src\KSkills.cpp">
      <AdditionalOptions Condition="'$(Configuration)|$(Platform)'=='Server Debug|Win32'"> /LD   /LD </AdditionalOptions>
    </ClCompile>
    <ClCompile Include="Src\KSkillSpecial.cpp">
      <AdditionalOptions Condition="'$(Configuration)|$(Platform)'=='Server Debug|Win32'"> /LD   /LD </AdditionalOptions>
    </ClCompile>
    <ClCompile Include="Src\Skill.cpp">
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Client Debug|Win32'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Server Debug|Win32'">true</ExcludedFromBuild>
      <AdditionalOptions Condition="'$(Configuration)|$(Platform)'=='Server Debug|Win32'"> /LD   /LD </AdditionalOptions>
    </ClCompile>
    <ClCompile Include="Src\KFaction.cpp">
      <AdditionalOptions Condition="'$(Configuration)|$(Platform)'=='Server Debug|Win32'"> /LD   /LD </AdditionalOptions>
    </ClCompile>
    <ClCompile Include="Src\KGameData.cpp">
      <AdditionalOptions Condition="'$(Configuration)|$(Platform)'=='Server Debug|Win32'"> /LD   /LD </AdditionalOptions>
    </ClCompile>
    <ClCompile Include="Src\KNpc.cpp">
      <AdditionalOptions Condition="'$(Configuration)|$(Platform)'=='Server Debug|Win32'"> /LD   /LD </AdditionalOptions>
    </ClCompile>
    <ClCompile Include="Src\KNpcAI.cpp">
      <AdditionalOptions Condition="'$(Configuration)|$(Platform)'=='Server Debug|Win32'"> /LD   /LD </AdditionalOptions>
    </ClCompile>
    <ClCompile Include="Src\KNpcAttribModify.cpp">
      <AdditionalOptions Condition="'$(Configuration)|$(Platform)'=='Server Debug|Win32'"> /LD   /LD </AdditionalOptions>
    </ClCompile>
    <ClCompile Include="Src\KNpcDeathCalcExp.cpp">
      <AdditionalOptions Condition="'$(Configuration)|$(Platform)'=='Server Debug|Win32'"> /LD   /LD </AdditionalOptions>
    </ClCompile>
    <ClCompile Include="Src\KNpcFindPath.cpp">
      <AdditionalOptions Condition="'$(Configuration)|$(Platform)'=='Server Debug|Win32'"> /LD   /LD </AdditionalOptions>
    </ClCompile>
    <ClCompile Include="Src\KNpcRes.cpp">
      <AdditionalOptions Condition="'$(Configuration)|$(Platform)'=='Server Debug|Win32'"> /LD   /LD </AdditionalOptions>
    </ClCompile>
    <ClCompile Include="Src\KNpcResList.cpp">
      <AdditionalOptions Condition="'$(Configuration)|$(Platform)'=='Server Debug|Win32'"> /LD   /LD </AdditionalOptions>
    </ClCompile>
    <ClCompile Include="Src\KNpcResNode.cpp">
      <AdditionalOptions Condition="'$(Configuration)|$(Platform)'=='Server Debug|Win32'"> /LD   /LD </AdditionalOptions>
    </ClCompile>
    <ClCompile Include="Src\KNpcSet.cpp">
      <AdditionalOptions Condition="'$(Configuration)|$(Platform)'=='Server Debug|Win32'"> /LD   /LD </AdditionalOptions>
    </ClCompile>
    <ClCompile Include="Src\KNpcTemplate.cpp">
      <AdditionalOptions Condition="'$(Configuration)|$(Platform)'=='Server Debug|Win32'"> /LD   /LD </AdditionalOptions>
    </ClCompile>
    <ClCompile Include="Src\KPlayer.cpp">
      <AdditionalOptions Condition="'$(Configuration)|$(Platform)'=='Server Debug|Win32'"> /LD   /LD </AdditionalOptions>
    </ClCompile>
    <ClCompile Include="Src\KPlayerAI.cpp">
      <AdditionalOptions Condition="'$(Configuration)|$(Platform)'=='Server Debug|Win32'"> /LD   /LD </AdditionalOptions>
    </ClCompile>
    <ClCompile Include="Src\KPlayerChat.cpp">
      <AdditionalOptions Condition="'$(Configuration)|$(Platform)'=='Server Debug|Win32'"> /LD   /LD </AdditionalOptions>
    </ClCompile>
    <ClCompile Include="Src\KPlayerChatRoom.cpp">
      <AdditionalOptions Condition="'$(Configuration)|$(Platform)'=='Server Debug|Win32'"> /LD   /LD </AdditionalOptions>
    </ClCompile>
    <ClCompile Include="Src\KPlayerDBFuns.cpp">
      <AdditionalOptions Condition="'$(Configuration)|$(Platform)'=='Server Debug|Win32'"> /LD   /LD </AdditionalOptions>
    </ClCompile>
    <ClCompile Include="Src\KPlayerFaction.cpp">
      <AdditionalOptions Condition="'$(Configuration)|$(Platform)'=='Server Debug|Win32'"> /LD   /LD </AdditionalOptions>
    </ClCompile>
    <ClCompile Include="Src\KPlayerMenuState.cpp">
      <AdditionalOptions Condition="'$(Configuration)|$(Platform)'=='Server Debug|Win32'"> /LD   /LD </AdditionalOptions>
    </ClCompile>
    <ClCompile Include="Src\KPlayerPK.cpp">
      <AdditionalOptions Condition="'$(Configuration)|$(Platform)'=='Server Debug|Win32'"> /LD   /LD </AdditionalOptions>
    </ClCompile>
    <ClCompile Include="Src\KPlayerSet.cpp">
      <AdditionalOptions Condition="'$(Configuration)|$(Platform)'=='Server Debug|Win32'"> /LD   /LD </AdditionalOptions>
    </ClCompile>
    <ClCompile Include="Src\KPlayerTask.cpp">
      <AdditionalOptions Condition="'$(Configuration)|$(Platform)'=='Server Debug|Win32'"> /LD   /LD </AdditionalOptions>
    </ClCompile>
    <ClCompile Include="Src\KPlayerTeam.cpp">
      <AdditionalOptions Condition="'$(Configuration)|$(Platform)'=='Server Debug|Win32'"> /LD   /LD </AdditionalOptions>
    </ClCompile>
    <ClCompile Include="Src\KPlayerTong.cpp">
      <AdditionalOptions Condition="'$(Configuration)|$(Platform)'=='Server Debug|Win32'"> /LD   /LD </AdditionalOptions>
    </ClCompile>
    <ClCompile Include="Src\KPlayerTrade.cpp">
      <AdditionalOptions Condition="'$(Configuration)|$(Platform)'=='Server Debug|Win32'"> /LD   /LD </AdditionalOptions>
    </ClCompile>
    <ClCompile Include="Src\KSprControl.cpp">
      <AdditionalOptions Condition="'$(Configuration)|$(Platform)'=='Server Debug|Win32'"> /LD   /LD </AdditionalOptions>
    </ClCompile>
    <ClCompile Include="Src\KTongData.cpp">
      <AdditionalOptions Condition="'$(Configuration)|$(Platform)'=='Server Debug|Win32'"> /LD   /LD </AdditionalOptions>
    </ClCompile>
    <ClCompile Include="Src\KBasPropTbl.CPP">
      <AdditionalOptions Condition="'$(Configuration)|$(Platform)'=='Server Debug|Win32'"> /LD   /LD </AdditionalOptions>
    </ClCompile>
    <ClCompile Include="Src\KBuySell.cpp">
      <AdditionalOptions Condition="'$(Configuration)|$(Platform)'=='Server Debug|Win32'"> /LD   /LD </AdditionalOptions>
    </ClCompile>
    <ClCompile Include="Src\KInventory.cpp">
      <AdditionalOptions Condition="'$(Configuration)|$(Platform)'=='Server Debug|Win32'"> /LD   /LD </AdditionalOptions>
    </ClCompile>
    <ClCompile Include="Src\KItem.cpp">
      <AdditionalOptions Condition="'$(Configuration)|$(Platform)'=='Server Debug|Win32'"> /LD   /LD </AdditionalOptions>
    </ClCompile>
    <ClCompile Include="Src\KItemChangeRes.cpp">
      <AdditionalOptions Condition="'$(Configuration)|$(Platform)'=='Server Debug|Win32'"> /LD   /LD </AdditionalOptions>
    </ClCompile>
    <ClCompile Include="Src\KItemGenerator.CPP">
      <AdditionalOptions Condition="'$(Configuration)|$(Platform)'=='Server Debug|Win32'"> /LD   /LD </AdditionalOptions>
    </ClCompile>
    <ClCompile Include="Src\KItemList.cpp">
      <AdditionalOptions Condition="'$(Configuration)|$(Platform)'=='Server Debug|Win32'"> /LD   /LD </AdditionalOptions>
    </ClCompile>
    <ClCompile Include="Src\KItemSet.cpp">
      <AdditionalOptions Condition="'$(Configuration)|$(Platform)'=='Server Debug|Win32'"> /LD   /LD </AdditionalOptions>
    </ClCompile>
    <ClCompile Include="Src\KObj.cpp">
      <AdditionalOptions Condition="'$(Configuration)|$(Platform)'=='Server Debug|Win32'"> /LD   /LD </AdditionalOptions>
    </ClCompile>
    <ClCompile Include="Src\KObjSet.cpp">
      <AdditionalOptions Condition="'$(Configuration)|$(Platform)'=='Server Debug|Win32'"> /LD   /LD </AdditionalOptions>
    </ClCompile>
    <ClCompile Include="Src\KSellItem.cpp">
      <AdditionalOptions Condition="'$(Configuration)|$(Platform)'=='Server Debug|Win32'"> /LD   /LD </AdditionalOptions>
    </ClCompile>
    <ClCompile Include="Src\KViewItem.cpp">
      <AdditionalOptions Condition="'$(Configuration)|$(Platform)'=='Server Debug|Win32'"> /LD   /LD </AdditionalOptions>
    </ClCompile>
    <ClCompile Include="Src\KCore.cpp">
      <AdditionalOptions Condition="'$(Configuration)|$(Platform)'=='Server Debug|Win32'"> /LD   /LD </AdditionalOptions>
    </ClCompile>
    <ClCompile Include="Src\KSubWorld.cpp">
      <AdditionalOptions Condition="'$(Configuration)|$(Platform)'=='Server Debug|Win32'"> /LD   /LD </AdditionalOptions>
    </ClCompile>
    <ClCompile Include="Src\KSubWorldSet.cpp">
      <AdditionalOptions Condition="'$(Configuration)|$(Platform)'=='Server Debug|Win32'"> /LD   /LD </AdditionalOptions>
    </ClCompile>
    <ClCompile Include="Src\KWeatherMgr.cpp">
      <AdditionalOptions Condition="'$(Configuration)|$(Platform)'=='Server Debug|Win32'"> /LD   /LD </AdditionalOptions>
    </ClCompile>
    <ClCompile Include="Src\KRegion.cpp">
      <AdditionalOptions Condition="'$(Configuration)|$(Platform)'=='Server Debug|Win32'"> /LD   /LD </AdditionalOptions>
    </ClCompile>
    <ClCompile Include="Src\KWorldMsg.cpp">
      <AdditionalOptions Condition="'$(Configuration)|$(Platform)'=='Server Debug|Win32'"> /LD   /LD </AdditionalOptions>
    </ClCompile>
    <ClCompile Include="Src\KMath.cpp">
      <AdditionalOptions Condition="'$(Configuration)|$(Platform)'=='Server Debug|Win32'"> /LD   /LD </AdditionalOptions>
    </ClCompile>
    <ClCompile Include="Src\KGMProcess.cpp">
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Client Debug|Win32'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Client Release|Win32'">true</ExcludedFromBuild>
      <AdditionalOptions Condition="'$(Configuration)|$(Platform)'=='Server Debug|Win32'"> /LD   /LD </AdditionalOptions>
    </ClCompile>
    <ClCompile Include="Src\KNewProtocolProcess.cpp">
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Client Debug|Win32'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Client Release|Win32'">true</ExcludedFromBuild>
      <AdditionalOptions Condition="'$(Configuration)|$(Platform)'=='Server Debug|Win32'"> /LD   /LD </AdditionalOptions>
    </ClCompile>
    <ClCompile Include="Src\KProtocol.cpp">
      <AdditionalOptions Condition="'$(Configuration)|$(Platform)'=='Server Debug|Win32'"> /LD   /LD </AdditionalOptions>
    </ClCompile>
    <ClCompile Include="Src\KProtocolProcess.cpp">
      <AdditionalOptions Condition="'$(Configuration)|$(Platform)'=='Server Debug|Win32'"> /LD   /LD </AdditionalOptions>
    </ClCompile>
    <ClCompile Include="Src\ScriptFuns.cpp">
      <AdditionalOptions Condition="'$(Configuration)|$(Platform)'=='Server Debug|Win32'"> /LD   /LD </AdditionalOptions>
    </ClCompile>
    <ClCompile Include="Src\LuaFuns.cpp">
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Client Debug|Win32'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Client Release|Win32'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Server Debug|Win32'">true</ExcludedFromBuild>
      <AdditionalOptions Condition="'$(Configuration)|$(Platform)'=='Server Debug|Win32'"> /LD   /LD </AdditionalOptions>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Server Release|Win32'">true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="Src\KScriptValueSet.cpp">
      <AdditionalOptions Condition="'$(Configuration)|$(Platform)'=='Server Debug|Win32'"> /LD   /LD </AdditionalOptions>
    </ClCompile>
    <ClCompile Include="Src\KSortScript.cpp">
      <AdditionalOptions Condition="'$(Configuration)|$(Platform)'=='Server Debug|Win32'"> /LD   /LD </AdditionalOptions>
    </ClCompile>
    <ClCompile Include="src\CoreServerShell.cpp">
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Client Debug|Win32'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Client Release|Win32'">true</ExcludedFromBuild>
      <AdditionalOptions Condition="'$(Configuration)|$(Platform)'=='Server Debug|Win32'"> /LD   /LD </AdditionalOptions>
    </ClCompile>
    <ClCompile Include="Src\CoreShell.cpp">
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Server Debug|Win32'">true</ExcludedFromBuild>
      <AdditionalOptions Condition="'$(Configuration)|$(Platform)'=='Server Debug|Win32'"> /LD   /LD </AdditionalOptions>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Server Release|Win32'">true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="Src\Scene\KIpotBranch.cpp">
      <PrecompiledHeader Condition="'$(Configuration)|$(Platform)'=='Client Debug|Win32'">Use</PrecompiledHeader>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Server Debug|Win32'">true</ExcludedFromBuild>
      <AdditionalOptions Condition="'$(Configuration)|$(Platform)'=='Server Debug|Win32'"> /LD   /LD </AdditionalOptions>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Server Release|Win32'">true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="Src\Scene\KIpotLeaf.cpp">
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Server Debug|Win32'">true</ExcludedFromBuild>
      <AdditionalOptions Condition="'$(Configuration)|$(Platform)'=='Server Debug|Win32'"> /LD   /LD </AdditionalOptions>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Server Release|Win32'">true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="Src\Scene\KIpoTree.cpp">
      <PrecompiledHeader Condition="'$(Configuration)|$(Platform)'=='Client Debug|Win32'">
      </PrecompiledHeader>
      <AssemblerOutput Condition="'$(Configuration)|$(Platform)'=='Client Release|Win32'">AssemblyAndSourceCode</AssemblerOutput>
      <PrecompiledHeader Condition="'$(Configuration)|$(Platform)'=='Client Release|Win32'">
      </PrecompiledHeader>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Server Debug|Win32'">true</ExcludedFromBuild>
      <AdditionalOptions Condition="'$(Configuration)|$(Platform)'=='Server Debug|Win32'"> /LD   /LD </AdditionalOptions>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Server Release|Win32'">true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="Src\Scene\KScenePlaceC.cpp">
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Server Debug|Win32'">true</ExcludedFromBuild>
      <AdditionalOptions Condition="'$(Configuration)|$(Platform)'=='Server Debug|Win32'"> /LD   /LD </AdditionalOptions>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Server Release|Win32'">true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="Src\Scene\KScenePlaceRegionC.cpp">
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Server Debug|Win32'">true</ExcludedFromBuild>
      <AdditionalOptions Condition="'$(Configuration)|$(Platform)'=='Server Debug|Win32'"> /LD   /LD </AdditionalOptions>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Server Release|Win32'">true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="Src\Scene\KWeather.cpp">
      <AdditionalOptions Condition="'$(Configuration)|$(Platform)'=='Server Debug|Win32'"> /LD   /LD </AdditionalOptions>
    </ClCompile>
    <ClCompile Include="Src\Scene\SceneMath.cpp">
      <PrecompiledHeader Condition="'$(Configuration)|$(Platform)'=='Client Debug|Win32'">Use</PrecompiledHeader>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Server Debug|Win32'">true</ExcludedFromBuild>
      <AdditionalOptions Condition="'$(Configuration)|$(Platform)'=='Server Debug|Win32'"> /LD   /LD </AdditionalOptions>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Server Release|Win32'">true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="Src\Scene\ScenePlaceMapC.cpp">
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Server Debug|Win32'">true</ExcludedFromBuild>
      <AdditionalOptions Condition="'$(Configuration)|$(Platform)'=='Server Debug|Win32'"> /LD   /LD </AdditionalOptions>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Server Release|Win32'">true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="Src\CoreDrawGameObj.cpp">
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Server Debug|Win32'">true</ExcludedFromBuild>
      <AdditionalOptions Condition="'$(Configuration)|$(Platform)'=='Server Debug|Win32'"> /LD   /LD </AdditionalOptions>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Server Release|Win32'">true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="Src\ImgRef.cpp">
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Server Debug|Win32'">true</ExcludedFromBuild>
      <AdditionalOptions Condition="'$(Configuration)|$(Platform)'=='Server Debug|Win32'"> /LD   /LD </AdditionalOptions>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Server Release|Win32'">true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="Src\KMagicDesc.cpp">
      <AdditionalOptions Condition="'$(Configuration)|$(Platform)'=='Server Debug|Win32'"> /LD   /LD </AdditionalOptions>
    </ClCompile>
    <ClCompile Include="Src\KGMCommand.cpp">
      <AdditionalOptions Condition="'$(Configuration)|$(Platform)'=='Server Debug|Win32'"> /LD   /LD </AdditionalOptions>
    </ClCompile>
    <ClCompile Include="Src\KMission.cpp">
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Client Debug|Win32'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Client Release|Win32'">true</ExcludedFromBuild>
      <AdditionalOptions Condition="'$(Configuration)|$(Platform)'=='Server Debug|Win32'"> /LD   /LD </AdditionalOptions>
    </ClCompile>
    <ClCompile Include="Src\KTaskFuns.cpp">
      <TreatWarningAsError Condition="'$(Configuration)|$(Platform)'=='Client Debug|Win32'">false</TreatWarningAsError>
      <AdditionalOptions Condition="'$(Configuration)|$(Platform)'=='Server Debug|Win32'"> /LD   /LD </AdditionalOptions>
    </ClCompile>
    <ClCompile Include="Src\KOption.cpp">
      <AdditionalOptions Condition="'$(Configuration)|$(Platform)'=='Server Debug|Win32'"> /LD   /LD </AdditionalOptions>
    </ClCompile>
    <ClCompile Include="Src\KMapMusic.cpp">
      <AdditionalOptions Condition="'$(Configuration)|$(Platform)'=='Server Debug|Win32'"> /LD   /LD </AdditionalOptions>
    </ClCompile>
    <ClCompile Include="Src\KLadder.cpp">
      <AdditionalOptions Condition="'$(Configuration)|$(Platform)'=='Server Debug|Win32'"> /LD   /LD </AdditionalOptions>
    </ClCompile>
    <ClCompile Include="Src\stdafx.cpp">
      <PrecompiledHeader Condition="'$(Configuration)|$(Platform)'=='Client Debug|Win32'">Create</PrecompiledHeader>
      <PrecompiledHeaderFile Condition="'$(Configuration)|$(Platform)'=='Client Debug|Win32'">KCore.h</PrecompiledHeaderFile>
      <PrecompiledHeader Condition="'$(Configuration)|$(Platform)'=='Client Release|Win32'">Create</PrecompiledHeader>
      <PrecompiledHeaderFile Condition="'$(Configuration)|$(Platform)'=='Client Release|Win32'">KCore.h</PrecompiledHeaderFile>
      <PrecompiledHeader Condition="'$(Configuration)|$(Platform)'=='Server Debug|Win32'">Create</PrecompiledHeader>
      <PrecompiledHeaderFile Condition="'$(Configuration)|$(Platform)'=='Server Debug|Win32'">KCore.h</PrecompiledHeaderFile>
      <AdditionalOptions Condition="'$(Configuration)|$(Platform)'=='Server Debug|Win32'"> /LD   /LD </AdditionalOptions>
      <PrecompiledHeader Condition="'$(Configuration)|$(Platform)'=='Server Release|Win32'">Create</PrecompiledHeader>
      <PrecompiledHeaderFile Condition="'$(Configuration)|$(Platform)'=='Server Release|Win32'">KCore.h</PrecompiledHeaderFile>
    </ClCompile>
  </ItemGroup>
  <ItemGroup>
    <ClInclude Include="Src\KPartnerSkill.h" />
    <ClInclude Include="Src\KPlayerPartner.h" />
    <ClInclude Include="Src\KThiefSkill.h" />
    <ClInclude Include="Src\KMissle.h" />
    <ClInclude Include="Src\KMissleMagicAttribsData.h" />
    <ClInclude Include="Src\KMissleRes.h" />
    <ClInclude Include="Src\KMissleSet.h" />
    <ClInclude Include="Src\KSkillList.h" />
    <ClInclude Include="Src\KSkillManager.h" />
    <ClInclude Include="Src\KSkills.h" />
    <ClInclude Include="Src\KSkillSpecial.h" />
    <ClInclude Include="Src\Skill.h" />
    <ClInclude Include="Src\SkillDef.h" />
    <ClInclude Include="Src\KFaction.h" />
    <ClInclude Include="Src\KGameData.h" />
    <ClInclude Include="Src\KNpc.h" />
    <ClInclude Include="Src\KNpcAI.h" />
    <ClInclude Include="Src\KNpcAttribModify.h" />
    <ClInclude Include="Src\KNpcDeathCalcExp.h" />
    <ClInclude Include="Src\KNpcFindPath.h" />
    <ClInclude Include="Src\KNpcRes.h" />
    <ClInclude Include="Src\KNpcResList.h" />
    <ClInclude Include="Src\KNpcResNode.h" />
    <ClInclude Include="Src\KNpcSet.h" />
    <ClInclude Include="Src\KNpcTemplate.h" />
    <ClInclude Include="Src\KPlayer.h" />
    <ClInclude Include="Src\KPlayerAI.h" />
    <ClInclude Include="Src\KPlayerChat.h" />
    <ClInclude Include="Src\KPlayerChatRoom.h" />
    <ClInclude Include="Src\KPlayerDef.h" />
    <ClInclude Include="Src\KPlayerFaction.h" />
    <ClInclude Include="Src\KPlayerMenuState.h" />
    <ClInclude Include="Src\KPlayerPK.h" />
    <ClInclude Include="Src\KPlayerSet.h" />
    <ClInclude Include="Src\KPlayerTask.h" />
    <ClInclude Include="Src\KPlayerTeam.h" />
    <ClInclude Include="Src\KPlayerTong.h" />
    <ClInclude Include="Src\KPlayerTrade.h" />
    <ClInclude Include="Src\KSprControl.h" />
    <ClInclude Include="Src\KTongData.h" />
    <ClInclude Include="Src\KBasPropTbl.h" />
    <ClInclude Include="Src\KBuySell.h" />
    <ClInclude Include="Src\KInventory.h" />
    <ClInclude Include="Src\KItem.h" />
    <ClInclude Include="Src\KItemChangeRes.h" />
    <ClInclude Include="Src\KItemGenerator.h" />
    <ClInclude Include="Src\KItemList.h" />
    <ClInclude Include="Src\KItemSet.h" />
    <ClInclude Include="Src\KObj.h" />
    <ClInclude Include="Src\KObjSet.h" />
    <ClInclude Include="Src\KSellItem.h" />
    <ClInclude Include="Src\KViewItem.h" />
    <ClInclude Include="Src\MyAssert.H" />
    <ClInclude Include="Src\KCore.h" />
    <ClInclude Include="Src\KSubWorld.h" />
    <ClInclude Include="Src\KSubWorldSet.h" />
    <ClInclude Include="Src\KWeatherMgr.h" />
    <ClInclude Include="Src\KRegion.h" />
    <ClInclude Include="Src\KWorldMsg.h" />
    <ClInclude Include="Src\KMath.h" />
    <ClInclude Include="Src\KNewProtocolProcess.h">
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Client Debug|Win32'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Client Release|Win32'">true</ExcludedFromBuild>
    </ClInclude>
    <ClInclude Include="..\..\Headers\KProtocolDef.h" />
    <ClInclude Include="Src\KProtocolProcess.h" />
    <ClInclude Include="Src\KScriptValueSet.h" />
    <ClInclude Include="Src\KSortScript.h" />
    <ClInclude Include="Src\LuaFuns.h" />
    <ClInclude Include="Src\CoreObjGenreDef.h">
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Server Debug|Win32'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Server Release|Win32'">true</ExcludedFromBuild>
    </ClInclude>
    <ClInclude Include="Src\CoreServerDataDef.h">
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Client Debug|Win32'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Client Release|Win32'">true</ExcludedFromBuild>
    </ClInclude>
    <ClInclude Include="src\CoreServerShell.h">
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Client Debug|Win32'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Client Release|Win32'">true</ExcludedFromBuild>
    </ClInclude>
    <ClInclude Include="Src\CoreShell.h">
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Server Debug|Win32'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Server Release|Win32'">true</ExcludedFromBuild>
    </ClInclude>
    <ClInclude Include="Src\CoreUseNameDef.h" />
    <ClInclude Include="Src\GameDataDef.h">
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Server Debug|Win32'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Server Release|Win32'">true</ExcludedFromBuild>
    </ClInclude>
    <ClInclude Include="Src\KIndexNode.h" />
    <ClInclude Include="Src\MsgGenreDef.h" />
    <ClInclude Include="..\..\Lib\S3DBInterface.h" />
    <ClInclude Include="Src\Scene\KIpotBranch.h">
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Server Debug|Win32'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Server Release|Win32'">true</ExcludedFromBuild>
    </ClInclude>
    <ClInclude Include="Src\Scene\KIpotLeaf.h">
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Server Debug|Win32'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Server Release|Win32'">true</ExcludedFromBuild>
    </ClInclude>
    <ClInclude Include="Src\Scene\KIpoTree.h">
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Server Debug|Win32'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Server Release|Win32'">true</ExcludedFromBuild>
    </ClInclude>
    <ClInclude Include="Src\Scene\KScenePlaceC.h">
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Server Debug|Win32'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Server Release|Win32'">true</ExcludedFromBuild>
    </ClInclude>
    <ClInclude Include="Src\Scene\KScenePlaceRegionC.h">
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Server Debug|Win32'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Server Release|Win32'">true</ExcludedFromBuild>
    </ClInclude>
    <ClInclude Include="Src\Scene\KWeather.h" />
    <ClInclude Include="Src\Scene\ObstacleDef.h" />
    <ClInclude Include="Src\Scene\SceneDataDef.h">
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Server Release|Win32'">true</ExcludedFromBuild>
    </ClInclude>
    <ClInclude Include="Src\Scene\SceneMath.h">
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Server Debug|Win32'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Server Release|Win32'">true</ExcludedFromBuild>
    </ClInclude>
    <ClInclude Include="Src\Scene\ScenePlaceMapC.h">
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Server Debug|Win32'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Server Release|Win32'">true</ExcludedFromBuild>
    </ClInclude>
    <ClInclude Include="Src\CoreDrawGameObj.h">
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Server Debug|Win32'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Server Release|Win32'">true</ExcludedFromBuild>
    </ClInclude>
    <ClInclude Include="Src\ImgRef.h">
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Server Debug|Win32'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Server Release|Win32'">true</ExcludedFromBuild>
    </ClInclude>
    <ClInclude Include="Src\KMagicAttrib.h" />
    <ClInclude Include="Src\KMagicDesc.h" />
    <ClInclude Include="Src\KGMCommand.h" />
    <ClInclude Include="Src\KLinkArrayTemplate.h" />
    <ClInclude Include="Src\KMission.h">
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Client Debug|Win32'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Client Release|Win32'">true</ExcludedFromBuild>
    </ClInclude>
    <ClInclude Include="Src\KMissionArray.h">
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Client Debug|Win32'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Client Release|Win32'">true</ExcludedFromBuild>
    </ClInclude>
    <ClInclude Include="Src\KTaskFuns.h" />
    <ClInclude Include="Src\KOption.h" />
    <ClInclude Include="Src\KMapMusic.h" />
    <ClInclude Include="Src\KLadder.h" />
    <ClInclude Include="Src\USBKey\des.h" />
    <ClInclude Include="Src\USBKey\EPASSAPI.H" />
    <ClInclude Include="Src\USBKey\epsJO.h" />
    <ClInclude Include="Src\USBKey\MD5.H" />
    <ClInclude Include="Src\KProtocol.h" />
  </ItemGroup>
  <ItemGroup>
    <CustomBuild Include="Src\PlayerPartnerÎÄµµ.txt" />
    <CustomBuild Include="Src\¼¼ÄÜÏµÍ³.txt" />
    <CustomBuild Include="Src\AIÉè¼Æ.txt" />
    <CustomBuild Include="Src\NPCÏµÍ³.txt" />
    <CustomBuild Include="Protocol.xls">
      <FileType>Document</FileType>
    </CustomBuild>
    <CustomBuild Include="Src\½Å±¾ËµĂ÷.txt" />
    <CustomBuild Include="Src\ÈÎÎñÏµÍ³Éè¼ÆÎÄµµ.txt" />
  </ItemGroup>
  <ItemGroup>
    <Library Include="..\..\Lib\release\engine.lib">
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Client Debug|Win32'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Server Debug|Win32'">true</ExcludedFromBuild>
    </Library>
    <Library Include="..\..\Lib\release\common.lib">
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Client Debug|Win32'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Server Debug|Win32'">true</ExcludedFromBuild>
    </Library>
    <Library Include="..\..\Lib\debug\engine.lib">
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Client Release|Win32'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Server Release|Win32'">true</ExcludedFromBuild>
    </Library>
    <Library Include="..\..\Lib\debug\common.lib">
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Client Release|Win32'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Server Release|Win32'">true</ExcludedFromBuild>
    </Library>
    <Library Include="Src\USBKey\EP1KDL20.LIB" />
    <Library Include="Src\USBKey\EpsForJoLib.lib" />
  </ItemGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.targets" />
  <ImportGroup Label="ExtensionTargets">
  </ImportGroup>
</Project>