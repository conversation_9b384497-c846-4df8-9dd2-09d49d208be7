//---------------------------------------------------------------------------
// Sword2 Combat Script API Implementation (c) 2024
//
// File:	CombatScriptAPI.cpp
// Date:	2024.07.02
// Code:	AI Assistant
// Desc:	Implementation of combat-related Lua script API functions
//---------------------------------------------------------------------------

#include "CombatScriptIntegration.h"
#include "CombatManager.h"
#include "PlayerManager.h"
#include "LuaScriptEngine.h"

namespace sword2 {
namespace CombatScriptAPI {

// 辅助函数：获取实体ID参数
uint32_t GetEntityIdFromLua(lua_State* L, int index = 1)
{
    if (lua_isnumber(L, index))
    {
        return static_cast<uint32_t>(lua_tonumber(L, index));
    }
    return 0;
}

// 战斗基础API实现
int GetCombatState(lua_State* L)
{
    uint32_t entityId = GetEntityIdFromLua(L);
    auto participant = COMBAT_MANAGER().GetParticipant(entityId);
    
    if (participant)
    {
        lua_pushnumber(L, static_cast<int>(participant->combatState));
    }
    else
    {
        lua_pushnumber(L, static_cast<int>(CombatState::Peace));
    }
    return 1;
}

int SetCombatState(lua_State* L)
{
    uint32_t entityId = GetEntityIdFromLua(L);
    int state = lua_isnumber(L, 2) ? static_cast<int>(lua_tonumber(L, 2)) : 0;
    
    auto participant = COMBAT_MANAGER().GetParticipant(entityId);
    if (participant)
    {
        participant->combatState = static_cast<CombatState>(state);
        lua_pushboolean(L, 1);
    }
    else
    {
        lua_pushboolean(L, 0);
    }
    return 1;
}

int IsInCombat(lua_State* L)
{
    uint32_t entityId = GetEntityIdFromLua(L);
    auto participant = COMBAT_MANAGER().GetParticipant(entityId);
    
    bool inCombat = participant ? participant->IsInCombat() : false;
    lua_pushboolean(L, inCombat ? 1 : 0);
    return 1;
}

int GetCombatDuration(lua_State* L)
{
    uint32_t entityId = GetEntityIdFromLua(L);
    auto participant = COMBAT_MANAGER().GetParticipant(entityId);
    
    uint32_t duration = participant ? participant->GetCombatDuration() : 0;
    lua_pushnumber(L, duration);
    return 1;
}

int GetCombatTarget(lua_State* L)
{
    uint32_t entityId = GetEntityIdFromLua(L);
    auto participant = COMBAT_MANAGER().GetParticipant(entityId);
    
    uint32_t target = participant ? participant->currentTarget : 0;
    lua_pushnumber(L, target);
    return 1;
}

int SetCombatTarget(lua_State* L)
{
    uint32_t entityId = GetEntityIdFromLua(L);
    uint32_t targetId = GetEntityIdFromLua(L, 2);
    
    auto participant = COMBAT_MANAGER().GetParticipant(entityId);
    if (participant)
    {
        participant->currentTarget = targetId;
        lua_pushboolean(L, 1);
    }
    else
    {
        lua_pushboolean(L, 0);
    }
    return 1;
}

// 攻击API实现
int StartAttack(lua_State* L)
{
    uint32_t attackerId = GetEntityIdFromLua(L);
    uint32_t targetId = GetEntityIdFromLua(L, 2);
    
    CombatResult result = START_ATTACK(attackerId, targetId);
    lua_pushnumber(L, static_cast<int>(result));
    return 1;
}

int PerformAttack(lua_State* L)
{
    uint32_t attackerId = GetEntityIdFromLua(L);
    uint32_t targetId = GetEntityIdFromLua(L, 2);
    
    CombatResult result = PERFORM_ATTACK(attackerId, targetId);
    lua_pushnumber(L, static_cast<int>(result));
    return 1;
}

int PerformSkillAttack(lua_State* L)
{
    uint32_t casterId = GetEntityIdFromLua(L);
    uint32_t skillId = GetEntityIdFromLua(L, 2);
    uint32_t targetId = lua_isnumber(L, 3) ? GetEntityIdFromLua(L, 3) : 0;
    int32_t x = lua_isnumber(L, 4) ? static_cast<int32_t>(lua_tonumber(L, 4)) : 0;
    int32_t y = lua_isnumber(L, 5) ? static_cast<int32_t>(lua_tonumber(L, 5)) : 0;
    
    CombatResult result = PERFORM_SKILL_ATTACK(casterId, skillId, targetId, x, y);
    lua_pushnumber(L, static_cast<int>(result));
    return 1;
}

int CalculateHit(lua_State* L)
{
    uint32_t attackerId = GetEntityIdFromLua(L);
    uint32_t targetId = GetEntityIdFromLua(L, 2);
    
    auto attacker = GET_ONLINE_PLAYER(attackerId);
    auto target = GET_ONLINE_PLAYER(targetId);
    
    if (attacker && target)
    {
        bool hit = CombatCalculator::CalculateHit(*attacker, *target);
        lua_pushboolean(L, hit ? 1 : 0);
    }
    else
    {
        lua_pushboolean(L, 0);
    }
    return 1;
}

int CalculateCritical(lua_State* L)
{
    uint32_t attackerId = GetEntityIdFromLua(L);
    uint32_t targetId = GetEntityIdFromLua(L, 2);
    
    auto attacker = GET_ONLINE_PLAYER(attackerId);
    auto target = GET_ONLINE_PLAYER(targetId);
    
    if (attacker && target)
    {
        bool critical = CombatCalculator::CalculateCritical(*attacker, *target);
        lua_pushboolean(L, critical ? 1 : 0);
    }
    else
    {
        lua_pushboolean(L, 0);
    }
    return 1;
}

int CalculateDamage(lua_State* L)
{
    uint32_t attackerId = GetEntityIdFromLua(L);
    uint32_t targetId = GetEntityIdFromLua(L, 2);
    int damageType = lua_isnumber(L, 3) ? static_cast<int>(lua_tonumber(L, 3)) : 0;
    bool isCritical = lua_isboolean(L, 4) ? (lua_toboolean(L, 4) != 0) : false;
    
    auto attacker = GET_ONLINE_PLAYER(attackerId);
    auto target = GET_ONLINE_PLAYER(targetId);
    
    if (attacker && target)
    {
        uint32_t damage = 0;
        if (damageType == static_cast<int>(DamageType::Physical))
        {
            damage = CombatCalculator::CalculatePhysicalDamage(*attacker, *target, isCritical);
        }
        else if (damageType == static_cast<int>(DamageType::Magic))
        {
            damage = CombatCalculator::CalculateMagicDamage(*attacker, *target, isCritical);
        }
        
        lua_pushnumber(L, damage);
    }
    else
    {
        lua_pushnumber(L, 0);
    }
    return 1;
}

// 伤害和治疗API实现
int ApplyDamage(lua_State* L)
{
    uint32_t attackerId = GetEntityIdFromLua(L);
    uint32_t targetId = GetEntityIdFromLua(L, 2);
    uint32_t damage = lua_isnumber(L, 3) ? static_cast<uint32_t>(lua_tonumber(L, 3)) : 0;
    int damageType = lua_isnumber(L, 4) ? static_cast<int>(lua_tonumber(L, 4)) : 0;
    
    DamageInfo damageInfo(attackerId, targetId, damage, static_cast<DamageType>(damageType));
    COMBAT_MANAGER().ApplyDamage(damageInfo);
    
    lua_pushboolean(L, 1);
    return 1;
}

int ApplyHeal(lua_State* L)
{
    uint32_t healerId = GetEntityIdFromLua(L);
    uint32_t targetId = GetEntityIdFromLua(L, 2);
    uint32_t heal = lua_isnumber(L, 3) ? static_cast<uint32_t>(lua_tonumber(L, 3)) : 0;
    
    HealInfo healInfo(healerId, targetId, heal);
    COMBAT_MANAGER().ApplyHeal(healInfo);
    
    lua_pushboolean(L, 1);
    return 1;
}

int GetDamageReduction(lua_State* L)
{
    uint32_t targetId = GetEntityIdFromLua(L);
    int damageType = lua_isnumber(L, 2) ? static_cast<int>(lua_tonumber(L, 2)) : 0;
    
    auto target = GET_ONLINE_PLAYER(targetId);
    if (target)
    {
        uint32_t reduction = 0;
        switch (static_cast<DamageType>(damageType))
        {
        case DamageType::Physical:
            reduction = target->attributes.defense;
            break;
        case DamageType::Magic:
            reduction = target->attributes.magicDefense;
            break;
        case DamageType::Fire:
            reduction = target->attributes.fireResist;
            break;
        case DamageType::Ice:
            reduction = target->attributes.iceResist;
            break;
        case DamageType::Lightning:
            reduction = target->attributes.lightningResist;
            break;
        case DamageType::Poison:
            reduction = target->attributes.poisonResist;
            break;
        default:
            break;
        }
        
        lua_pushnumber(L, reduction);
    }
    else
    {
        lua_pushnumber(L, 0);
    }
    return 1;
}

int IsImmune(lua_State* L)
{
    uint32_t targetId = GetEntityIdFromLua(L);
    int damageType = lua_isnumber(L, 2) ? static_cast<int>(lua_tonumber(L, 2)) : 0;
    
    bool immune = HAS_EFFECT_TYPE(targetId, CombatStateType::Immune) ||
                  HAS_EFFECT_TYPE(targetId, CombatStateType::Invincible);
    
    lua_pushboolean(L, immune ? 1 : 0);
    return 1;
}

// 状态效果API实现
int ApplyStateEffect(lua_State* L)
{
    uint32_t casterId = GetEntityIdFromLua(L);
    uint32_t targetId = GetEntityIdFromLua(L, 2);
    int effectType = lua_isnumber(L, 3) ? static_cast<int>(lua_tonumber(L, 3)) : 0;
    int32_t value = lua_isnumber(L, 4) ? static_cast<int32_t>(lua_tonumber(L, 4)) : 0;
    uint32_t duration = lua_isnumber(L, 5) ? static_cast<uint32_t>(lua_tonumber(L, 5)) : 0;
    uint32_t skillId = lua_isnumber(L, 6) ? static_cast<uint32_t>(lua_tonumber(L, 6)) : 0;
    
    uint32_t effectId = APPLY_STATE_EFFECT(casterId, targetId, static_cast<CombatStateType>(effectType), 
                                          value, duration, skillId);
    lua_pushnumber(L, effectId);
    return 1;
}

int RemoveStateEffect(lua_State* L)
{
    uint32_t effectId = GetEntityIdFromLua(L);
    
    bool success = REMOVE_STATE_EFFECT(effectId);
    lua_pushboolean(L, success ? 1 : 0);
    return 1;
}

int HasEffectType(lua_State* L)
{
    uint32_t targetId = GetEntityIdFromLua(L);
    int effectType = lua_isnumber(L, 2) ? static_cast<int>(lua_tonumber(L, 2)) : 0;
    
    bool hasEffect = HAS_EFFECT_TYPE(targetId, static_cast<CombatStateType>(effectType));
    lua_pushboolean(L, hasEffect ? 1 : 0);
    return 1;
}

int GetTargetEffects(lua_State* L)
{
    uint32_t targetId = GetEntityIdFromLua(L);
    
    auto effects = GET_TARGET_EFFECTS(targetId);
    
    lua_newtable(L);
    int index = 1;
    
    for (const auto& effect : effects)
    {
        lua_pushnumber(L, index++);
        lua_newtable(L);
        
        lua_pushstring(L, "effectId");
        lua_pushnumber(L, effect.effectId);
        lua_settable(L, -3);
        
        lua_pushstring(L, "type");
        lua_pushnumber(L, static_cast<int>(effect.type));
        lua_settable(L, -3);
        
        lua_pushstring(L, "casterId");
        lua_pushnumber(L, effect.casterId);
        lua_settable(L, -3);
        
        lua_pushstring(L, "value");
        lua_pushnumber(L, effect.value);
        lua_settable(L, -3);
        
        lua_pushstring(L, "duration");
        lua_pushnumber(L, effect.duration);
        lua_settable(L, -3);
        
        lua_pushstring(L, "remainingTime");
        lua_pushnumber(L, effect.GetRemainingTime());
        lua_settable(L, -3);
        
        lua_pushstring(L, "stackCount");
        lua_pushnumber(L, effect.stackCount);
        lua_settable(L, -3);
        
        lua_pushstring(L, "description");
        lua_pushstring(L, effect.GetDescription().c_str());
        lua_settable(L, -3);
        
        lua_settable(L, -3);
    }
    
    return 1;
}

int ClearAllEffects(lua_State* L)
{
    uint32_t targetId = GetEntityIdFromLua(L);
    
    CLEAR_ALL_EFFECTS(targetId);
    lua_pushboolean(L, 1);
    return 1;
}

int DispelEffects(lua_State* L)
{
    uint32_t targetId = GetEntityIdFromLua(L);
    bool dispelDebuffs = lua_isboolean(L, 2) ? (lua_toboolean(L, 2) != 0) : true;
    uint32_t maxCount = lua_isnumber(L, 3) ? static_cast<uint32_t>(lua_tonumber(L, 3)) : 1;
    
    uint32_t dispelledCount = DISPEL_EFFECTS(targetId, dispelDebuffs, maxCount);
    lua_pushnumber(L, dispelledCount);
    return 1;
}

// 战斗统计API实现
int GetDamageDealt(lua_State* L)
{
    uint32_t entityId = GetEntityIdFromLua(L);
    auto participant = COMBAT_MANAGER().GetParticipant(entityId);
    
    uint32_t damage = participant ? participant->totalDamageDealt : 0;
    lua_pushnumber(L, damage);
    return 1;
}

int GetDamageTaken(lua_State* L)
{
    uint32_t entityId = GetEntityIdFromLua(L);
    auto participant = COMBAT_MANAGER().GetParticipant(entityId);
    
    uint32_t damage = participant ? participant->totalDamageTaken : 0;
    lua_pushnumber(L, damage);
    return 1;
}

int GetHealingDone(lua_State* L)
{
    uint32_t entityId = GetEntityIdFromLua(L);
    auto participant = COMBAT_MANAGER().GetParticipant(entityId);
    
    uint32_t healing = participant ? participant->totalHealingDone : 0;
    lua_pushnumber(L, healing);
    return 1;
}

int GetKillCount(lua_State* L)
{
    uint32_t entityId = GetEntityIdFromLua(L);
    auto participant = COMBAT_MANAGER().GetParticipant(entityId);
    
    uint32_t kills = participant ? participant->killCount : 0;
    lua_pushnumber(L, kills);
    return 1;
}

int GetDeathCount(lua_State* L)
{
    uint32_t entityId = GetEntityIdFromLua(L);
    auto participant = COMBAT_MANAGER().GetParticipant(entityId);
    
    uint32_t deaths = participant ? participant->deathCount : 0;
    lua_pushnumber(L, deaths);
    return 1;
}

int GetDPS(lua_State* L)
{
    uint32_t entityId = GetEntityIdFromLua(L);
    auto participant = COMBAT_MANAGER().GetParticipant(entityId);
    
    float dps = participant ? participant->GetDPS() : 0.0f;
    lua_pushnumber(L, dps);
    return 1;
}

int GetHPS(lua_State* L)
{
    uint32_t entityId = GetEntityIdFromLua(L);
    auto participant = COMBAT_MANAGER().GetParticipant(entityId);
    
    float hps = participant ? participant->GetHPS() : 0.0f;
    lua_pushnumber(L, hps);
    return 1;
}

// 距离和范围API实现
int GetDistance(lua_State* L)
{
    uint32_t entity1Id = GetEntityIdFromLua(L);
    uint32_t entity2Id = GetEntityIdFromLua(L, 2);
    
    auto entity1 = GET_ONLINE_PLAYER(entity1Id);
    auto entity2 = GET_ONLINE_PLAYER(entity2Id);
    
    if (entity1 && entity2)
    {
        float distance = CombatAIHelper::CalculateDistance(
            entity1->position.x, entity1->position.y,
            entity2->position.x, entity2->position.y);
        lua_pushnumber(L, distance);
    }
    else
    {
        lua_pushnumber(L, -1);
    }
    return 1;
}

int IsInRange(lua_State* L)
{
    uint32_t entity1Id = GetEntityIdFromLua(L);
    uint32_t entity2Id = GetEntityIdFromLua(L, 2);
    float range = lua_isnumber(L, 3) ? static_cast<float>(lua_tonumber(L, 3)) : 1.0f;
    
    auto entity1 = GET_ONLINE_PLAYER(entity1Id);
    auto entity2 = GET_ONLINE_PLAYER(entity2Id);
    
    if (entity1 && entity2)
    {
        float distance = CombatAIHelper::CalculateDistance(
            entity1->position.x, entity1->position.y,
            entity2->position.x, entity2->position.y);
        lua_pushboolean(L, (distance <= range) ? 1 : 0);
    }
    else
    {
        lua_pushboolean(L, 0);
    }
    return 1;
}

int IsInAttackRange(lua_State* L)
{
    uint32_t attackerId = GetEntityIdFromLua(L);
    uint32_t targetId = GetEntityIdFromLua(L, 2);
    
    // 这里应该根据武器类型和技能计算攻击范围
    // 暂时使用固定范围
    float attackRange = 2.0f;
    
    auto attacker = GET_ONLINE_PLAYER(attackerId);
    auto target = GET_ONLINE_PLAYER(targetId);
    
    if (attacker && target)
    {
        float distance = CombatAIHelper::CalculateDistance(
            attacker->position.x, attacker->position.y,
            target->position.x, target->position.y);
        lua_pushboolean(L, (distance <= attackRange) ? 1 : 0);
    }
    else
    {
        lua_pushboolean(L, 0);
    }
    return 1;
}

int GetAttackRange(lua_State* L)
{
    uint32_t entityId = GetEntityIdFromLua(L);
    
    // 这里应该根据装备的武器计算攻击范围
    // 暂时返回固定值
    lua_pushnumber(L, 2.0f);
    return 1;
}

} // namespace CombatScriptAPI
} // namespace sword2
