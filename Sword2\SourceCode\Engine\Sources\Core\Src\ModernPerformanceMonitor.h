//---------------------------------------------------------------------------
// Sword2 Modern Performance Monitoring System (c) 2024
//
// File:	ModernPerformanceMonitor.h
// Date:	2024.07.02
// Code:	AI Assistant
// Desc:	Advanced real-time monitoring of system and application performance
//---------------------------------------------------------------------------
#ifndef MODERN_PERFORMANCE_MONITOR_H
#define MODERN_PERFORMANCE_MONITOR_H

#include "ModernCpp.h"
#include "UnifiedLoggingSystem.h"
#include <windows.h>
#include <psapi.h>
#include <pdh.h>
#include <chrono>
#include <thread>
#include <atomic>
#include <vector>
#include <deque>
#include <unordered_map>

#pragma comment(lib, "pdh.lib")
#pragma comment(lib, "psapi.lib")

namespace sword2 {

// 性能指标类型
enum class MetricType : uint32_t
{
    // 系统指标
    CPU_Usage = 0,              // CPU使用率
    Memory_Usage,               // 内存使用量
    Memory_Available,           // 可用内存
    Network_BytesSent,          // 网络发送字节数
    Network_BytesReceived,      // 网络接收字节数
    Disk_ReadBytes,             // 磁盘读取字节数
    Disk_WriteBytes,            // 磁盘写入字节数
    
    // 进程指标
    Process_HandleCount,        // 进程句柄数
    Process_ThreadCount,        // 进程线程数
    Process_MemoryUsage,        // 进程内存使用
    Process_CPUTime,            // 进程CPU时间
    
    // 游戏业务指标
    Game_FPS,                   // 游戏帧率
    Game_OnlineUsers,           // 在线用户数
    Game_ActiveConnections,     // 活跃连接数
    Game_PacketsPerSecond,      // 每秒数据包数
    Game_DatabaseQueries,       // 数据库查询数
    Game_CacheHitRate,          // 缓存命中率
    
    // 自定义指标
    Custom                      // 自定义指标
};

// 性能数据点
struct MetricDataPoint
{
    MetricType type;
    double value;
    std::chrono::steady_clock::time_point timestamp;
    std::string unit;
    std::unordered_map<std::string, std::string> tags;
    
    MetricDataPoint(MetricType t, double v, const std::string& u = "")
        : type(t), value(v), timestamp(std::chrono::steady_clock::now()), unit(u) {}
};

// 性能统计信息
struct MetricStatistics
{
    double min = std::numeric_limits<double>::max();
    double max = std::numeric_limits<double>::lowest();
    double average = 0.0;
    double current = 0.0;
    double sum = 0.0;
    size_t sampleCount = 0;
    std::chrono::steady_clock::time_point lastUpdate;
    
    void Update(double value)
    {
        min = std::min(min, value);
        max = std::max(max, value);
        sum += value;
        average = sum / (sampleCount + 1);
        current = value;
        sampleCount++;
        lastUpdate = std::chrono::steady_clock::now();
    }
    
    void Reset()
    {
        min = std::numeric_limits<double>::max();
        max = std::numeric_limits<double>::lowest();
        average = 0.0;
        current = 0.0;
        sum = 0.0;
        sampleCount = 0;
    }
    
    double GetStandardDeviation() const
    {
        // 简化的标准差计算
        return sampleCount > 1 ? std::sqrt((max - min) * (max - min) / 4.0) : 0.0;
    }
};

// 性能告警规则
struct AlertRule
{
    MetricType metricType;
    double threshold;
    bool isUpperLimit;          // true表示超过阈值告警，false表示低于阈值告警
    std::chrono::seconds duration; // 持续时间
    std::string message;
    bool enabled;
    
    AlertRule(MetricType type, double thresh, bool upper, 
              std::chrono::seconds dur = std::chrono::seconds(60),
              const std::string& msg = "")
        : metricType(type), threshold(thresh), isUpperLimit(upper), 
          duration(dur), message(msg), enabled(true) {}
};

// 高级系统性能收集器
class AdvancedSystemCollector
{
public:
    AdvancedSystemCollector()
    {
        Initialize();
    }
    
    ~AdvancedSystemCollector()
    {
        Cleanup();
    }
    
    bool Initialize()
    {
        // 初始化PDH查询
        if (PdhOpenQuery(NULL, 0, &m_query) != ERROR_SUCCESS)
        {
            LOG_ERROR("PERF", "Failed to open PDH query");
            return false;
        }
        
        // 添加各种性能计数器
        InitializeCounters();
        
        // 第一次收集数据
        PdhCollectQueryData(m_query);
        
        return true;
    }
    
    void Cleanup()
    {
        if (m_query)
        {
            PdhCloseQuery(m_query);
            m_query = nullptr;
        }
    }
    
    std::vector<MetricDataPoint> CollectAllMetrics()
    {
        std::vector<MetricDataPoint> metrics;
        
        // 收集PDH数据
        if (PdhCollectQueryData(m_query) == ERROR_SUCCESS)
        {
            CollectSystemMetrics(metrics);
        }
        
        // 收集进程特定指标
        CollectProcessMetrics(metrics);
        
        // 收集游戏业务指标
        CollectGameMetrics(metrics);
        
        return metrics;
    }

private:
    PDH_HQUERY m_query = nullptr;
    
    // 系统计数器
    PDH_HCOUNTER m_cpuCounter = nullptr;
    PDH_HCOUNTER m_memoryAvailableCounter = nullptr;
    PDH_HCOUNTER m_networkBytesReceivedCounter = nullptr;
    PDH_HCOUNTER m_networkBytesSentCounter = nullptr;
    PDH_HCOUNTER m_diskReadCounter = nullptr;
    PDH_HCOUNTER m_diskWriteCounter = nullptr;
    
    void InitializeCounters()
    {
        // CPU使用率
        PdhAddCounter(m_query, L"\\Processor(_Total)\\% Processor Time", 0, &m_cpuCounter);
        
        // 内存
        PdhAddCounter(m_query, L"\\Memory\\Available Bytes", 0, &m_memoryAvailableCounter);
        
        // 网络（简化处理，实际应该枚举所有网络接口）
        PdhAddCounter(m_query, L"\\Network Interface(*)\\Bytes Received/sec", 0, &m_networkBytesReceivedCounter);
        PdhAddCounter(m_query, L"\\Network Interface(*)\\Bytes Sent/sec", 0, &m_networkBytesSentCounter);
        
        // 磁盘
        PdhAddCounter(m_query, L"\\PhysicalDisk(_Total)\\Disk Read Bytes/sec", 0, &m_diskReadCounter);
        PdhAddCounter(m_query, L"\\PhysicalDisk(_Total)\\Disk Write Bytes/sec", 0, &m_diskWriteCounter);
    }
    
    void CollectSystemMetrics(std::vector<MetricDataPoint>& metrics)
    {
        PDH_FMT_COUNTERVALUE value;
        
        // CPU使用率
        if (PdhGetFormattedCounterValue(m_cpuCounter, PDH_FMT_DOUBLE, NULL, &value) == ERROR_SUCCESS)
        {
            metrics.emplace_back(MetricType::CPU_Usage, value.doubleValue, "%");
        }
        
        // 可用内存
        if (PdhGetFormattedCounterValue(m_memoryAvailableCounter, PDH_FMT_DOUBLE, NULL, &value) == ERROR_SUCCESS)
        {
            metrics.emplace_back(MetricType::Memory_Available, value.doubleValue, "bytes");
        }
        
        // 网络指标
        if (PdhGetFormattedCounterValue(m_networkBytesReceivedCounter, PDH_FMT_DOUBLE, NULL, &value) == ERROR_SUCCESS)
        {
            metrics.emplace_back(MetricType::Network_BytesReceived, value.doubleValue, "bytes/sec");
        }
        
        if (PdhGetFormattedCounterValue(m_networkBytesSentCounter, PDH_FMT_DOUBLE, NULL, &value) == ERROR_SUCCESS)
        {
            metrics.emplace_back(MetricType::Network_BytesSent, value.doubleValue, "bytes/sec");
        }
        
        // 磁盘指标
        if (PdhGetFormattedCounterValue(m_diskReadCounter, PDH_FMT_DOUBLE, NULL, &value) == ERROR_SUCCESS)
        {
            metrics.emplace_back(MetricType::Disk_ReadBytes, value.doubleValue, "bytes/sec");
        }
        
        if (PdhGetFormattedCounterValue(m_diskWriteCounter, PDH_FMT_DOUBLE, NULL, &value) == ERROR_SUCCESS)
        {
            metrics.emplace_back(MetricType::Disk_WriteBytes, value.doubleValue, "bytes/sec");
        }
    }
    
    void CollectProcessMetrics(std::vector<MetricDataPoint>& metrics)
    {
        HANDLE hProcess = GetCurrentProcess();
        
        // 进程内存使用
        PROCESS_MEMORY_COUNTERS_EX memCounters;
        if (GetProcessMemoryInfo(hProcess, (PROCESS_MEMORY_COUNTERS*)&memCounters, sizeof(memCounters)))
        {
            metrics.emplace_back(MetricType::Process_MemoryUsage, 
                static_cast<double>(memCounters.WorkingSetSize), "bytes");
        }
        
        // 句柄数
        DWORD handleCount;
        if (GetProcessHandleCount(hProcess, &handleCount))
        {
            metrics.emplace_back(MetricType::Process_HandleCount, 
                static_cast<double>(handleCount), "count");
        }
        
        // 线程数
        DWORD threadCount = GetCurrentThreadCount();
        metrics.emplace_back(MetricType::Process_ThreadCount, 
            static_cast<double>(threadCount), "count");
        
        // CPU时间
        FILETIME createTime, exitTime, kernelTime, userTime;
        if (GetProcessTimes(hProcess, &createTime, &exitTime, &kernelTime, &userTime))
        {
            ULARGE_INTEGER kernel, user;
            kernel.LowPart = kernelTime.dwLowDateTime;
            kernel.HighPart = kernelTime.dwHighDateTime;
            user.LowPart = userTime.dwLowDateTime;
            user.HighPart = userTime.dwHighDateTime;
            
            double totalCPUTime = static_cast<double>(kernel.QuadPart + user.QuadPart) / 10000.0; // 转换为毫秒
            metrics.emplace_back(MetricType::Process_CPUTime, totalCPUTime, "ms");
        }
    }
    
    void CollectGameMetrics(std::vector<MetricDataPoint>& metrics)
    {
        // 这些指标需要从游戏系统中获取
        // 这里提供接口，具体实现需要与游戏系统集成
        
        // FPS（需要从渲染系统获取）
        // metrics.emplace_back(MetricType::Game_FPS, GetCurrentFPS(), "fps");
        
        // 在线用户数（需要从玩家管理系统获取）
        // metrics.emplace_back(MetricType::Game_OnlineUsers, GetOnlineUserCount(), "count");
        
        // 活跃连接数（需要从网络系统获取）
        // metrics.emplace_back(MetricType::Game_ActiveConnections, GetActiveConnectionCount(), "count");
    }
    
    DWORD GetCurrentThreadCount()
    {
        DWORD threadCount = 0;
        HANDLE hSnapshot = CreateToolhelp32Snapshot(TH32CS_SNAPTHREAD, 0);
        if (hSnapshot != INVALID_HANDLE_VALUE)
        {
            THREADENTRY32 te32;
            te32.dwSize = sizeof(THREADENTRY32);
            
            if (Thread32First(hSnapshot, &te32))
            {
                DWORD processId = GetCurrentProcessId();
                do
                {
                    if (te32.th32OwnerProcessID == processId)
                    {
                        threadCount++;
                    }
                } while (Thread32Next(hSnapshot, &te32));
            }
            CloseHandle(hSnapshot);
        }
        return threadCount;
    }
};

// 现代化性能监控管理器
class ModernPerformanceMonitor : public Singleton<ModernPerformanceMonitor>
{
public:
    ModernPerformanceMonitor() 
        : m_running(false), m_collectInterval(1000), m_maxHistorySize(10000) {}
    
    ~ModernPerformanceMonitor()
    {
        Stop();
    }
    
    bool Start()
    {
        if (m_running) return true;
        
        if (!m_collector.Initialize())
        {
            LOG_ERROR("PERF", "Failed to initialize performance collector");
            return false;
        }
        
        m_running = true;
        m_collectorThread = std::thread(&ModernPerformanceMonitor::CollectorLoop, this);
        
        LOG_INFO("PERF", "Modern performance monitor started");
        return true;
    }
    
    void Stop()
    {
        if (!m_running) return;
        
        m_running = false;
        if (m_collectorThread.joinable())
        {
            m_collectorThread.join();
        }
        
        m_collector.Cleanup();
        LOG_INFO("PERF", "Modern performance monitor stopped");
    }
    
    // 配置方法
    void SetCollectInterval(std::chrono::milliseconds interval) { m_collectInterval = interval; }
    void SetMaxHistorySize(size_t size) { m_maxHistorySize = size; }
    
    // 添加告警规则
    void AddAlertRule(const AlertRule& rule)
    {
        std::lock_guard<std::mutex> lock(m_mutex);
        m_alertRules.push_back(rule);
    }
    
    // 获取性能数据
    std::vector<MetricDataPoint> GetLatestMetrics() const
    {
        std::lock_guard<std::mutex> lock(m_mutex);
        return m_latestMetrics;
    }
    
    // 获取统计信息
    MetricStatistics GetStatistics(MetricType type) const
    {
        std::lock_guard<std::mutex> lock(m_mutex);
        auto it = m_statistics.find(type);
        return it != m_statistics.end() ? it->second : MetricStatistics{};
    }
    
    // 添加自定义指标
    void RecordCustomMetric(const std::string& name, double value, const std::string& unit = "")
    {
        MetricDataPoint point(MetricType::Custom, value, unit);
        point.tags["name"] = name;
        
        std::lock_guard<std::mutex> lock(m_mutex);
        m_latestMetrics.push_back(point);
        AddToHistory(point);
        UpdateStatistics(point);
    }
    
    // 获取性能报告
    std::string GeneratePerformanceReport() const;
    
    // 导出性能数据
    void ExportToCSV(const std::string& filename, MetricType type, 
        std::chrono::steady_clock::time_point since) const;

private:
    AdvancedSystemCollector m_collector;
    std::thread m_collectorThread;
    std::atomic<bool> m_running;
    std::chrono::milliseconds m_collectInterval;
    size_t m_maxHistorySize;
    
    mutable std::mutex m_mutex;
    std::vector<MetricDataPoint> m_latestMetrics;
    std::deque<MetricDataPoint> m_historicalData;
    std::unordered_map<MetricType, MetricStatistics> m_statistics;
    std::vector<AlertRule> m_alertRules;
    
    void CollectorLoop()
    {
        while (m_running)
        {
            try
            {
                auto metrics = m_collector.CollectAllMetrics();
                
                {
                    std::lock_guard<std::mutex> lock(m_mutex);
                    m_latestMetrics = metrics;
                    
                    for (const auto& metric : metrics)
                    {
                        AddToHistory(metric);
                        UpdateStatistics(metric);
                        CheckAlertRules(metric);
                    }
                }
                
                // 记录性能日志
                LogPerformanceMetrics(metrics);
            }
            catch (const std::exception& e)
            {
                LOG_ERROR("PERF", std::string("Error collecting metrics: ") + e.what());
            }
            
            std::this_thread::sleep_for(m_collectInterval);
        }
    }
    
    void AddToHistory(const MetricDataPoint& point)
    {
        m_historicalData.push_back(point);
        
        while (m_historicalData.size() > m_maxHistorySize)
        {
            m_historicalData.pop_front();
        }
    }
    
    void UpdateStatistics(const MetricDataPoint& point)
    {
        m_statistics[point.type].Update(point.value);
    }
    
    void CheckAlertRules(const MetricDataPoint& point)
    {
        for (const auto& rule : m_alertRules)
        {
            if (!rule.enabled || rule.metricType != point.type) continue;
            
            bool violated = rule.isUpperLimit ? 
                (point.value > rule.threshold) : 
                (point.value < rule.threshold);
            
            if (violated)
            {
                TriggerAlert(rule, point);
            }
        }
    }
    
    void TriggerAlert(const AlertRule& rule, const MetricDataPoint& point)
    {
        std::unordered_map<std::string, std::string> metadata;
        metadata["metric_type"] = std::to_string(static_cast<int>(point.type));
        metadata["current_value"] = std::to_string(point.value);
        metadata["threshold_value"] = std::to_string(rule.threshold);
        metadata["unit"] = point.unit;
        metadata["alert_message"] = rule.message;
        
        LOG_WITH_META(LogLevel::Warning, "PERF_ALERT", 
            "Performance alert triggered", metadata);
    }
    
    void LogPerformanceMetrics(const std::vector<MetricDataPoint>& metrics)
    {
        // 只记录关键指标，避免日志过多
        for (const auto& metric : metrics)
        {
            if (ShouldLogMetric(metric.type))
            {
                std::unordered_map<std::string, std::string> metadata;
                metadata["metric_type"] = std::to_string(static_cast<int>(metric.type));
                metadata["value"] = std::to_string(metric.value);
                metadata["unit"] = metric.unit;
                
                LOG_WITH_META(LogLevel::Trace, "PERF_METRIC", 
                    "Performance metric", metadata);
            }
        }
    }
    
    bool ShouldLogMetric(MetricType type) const
    {
        // 只记录重要的指标
        return type == MetricType::CPU_Usage || 
               type == MetricType::Memory_Usage ||
               type == MetricType::Game_FPS ||
               type == MetricType::Game_OnlineUsers;
    }
};

} // namespace sword2

// 全局现代化性能监控器访问
#define MODERN_PERF_MONITOR() sword2::ModernPerformanceMonitor::getInstance()

// 便捷宏定义
#define START_MODERN_PERF_MONITORING() MODERN_PERF_MONITOR().Start()
#define STOP_MODERN_PERF_MONITORING() MODERN_PERF_MONITOR().Stop()
#define RECORD_CUSTOM_METRIC(name, value, unit) MODERN_PERF_MONITOR().RecordCustomMetric(name, value, unit)
#define ADD_PERF_ALERT(type, threshold, isUpper, duration, message) \
    MODERN_PERF_MONITOR().AddAlertRule(sword2::AlertRule(type, threshold, isUpper, duration, message))

#endif // MODERN_PERFORMANCE_MONITOR_H
