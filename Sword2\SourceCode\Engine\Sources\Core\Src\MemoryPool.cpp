//---------------------------------------------------------------------------
// Sword2 High-Performance Memory Pool (c) 2024
//
// File:	MemoryPool.cpp
// Date:	2024.07.02
// Code:	AI Assistant
// Desc:	High-performance memory pool implementation
//---------------------------------------------------------------------------

#include "KCore.h"
#include "MemoryPool.h"

// 魔数用于检测内存损坏
#define MEMORY_MAGIC_NUMBER 0xDEADBEEF

// 全局内存池实例
CMemoryPool g_MemoryPool;

//===========================================================================
// CMemorySubPool 实现
//===========================================================================

CMemorySubPool::CMemorySubPool()
{
    m_dwBlockSize = 0;
    m_dwTotalBlocks = 0;
    m_dwFreeBlocks = 0;
    m_pFreeList = NULL;
    InitializeCriticalSection(&m_CriticalSection);
}

CMemorySubPool::~CMemorySubPool()
{
    Cleanup();
    DeleteCriticalSection(&m_CriticalSection);
}

BOOL CMemorySubPool::Initialize(DWORD dwBlockSize, DWORD dwInitialBlocks)
{
    EnterCriticalSection(&m_CriticalSection);
    
    m_dwBlockSize = ALIGN_SIZE(dwBlockSize + sizeof(MemoryBlockHeader), 16);
    
    // 分配初始内存块
    BOOL bResult = AllocateNewChunk();
    
    LeaveCriticalSection(&m_CriticalSection);
    return bResult;
}

void CMemorySubPool::Cleanup()
{
    EnterCriticalSection(&m_CriticalSection);
    
    // 释放所有内存块
    for (size_t i = 0; i < m_Chunks.size(); i++)
    {
        VirtualFree(m_Chunks[i], 0, MEM_RELEASE);
    }
    m_Chunks.clear();
    
    m_dwTotalBlocks = 0;
    m_dwFreeBlocks = 0;
    m_pFreeList = NULL;
    
    LeaveCriticalSection(&m_CriticalSection);
}

void* CMemorySubPool::Allocate()
{
    EnterCriticalSection(&m_CriticalSection);
    
    // 如果没有空闲块，分配新的内存块
    if (!m_pFreeList && !AllocateNewChunk())
    {
        LeaveCriticalSection(&m_CriticalSection);
        return NULL;
    }
    
    // 从空闲链表中取出一个块
    MemoryBlockHeader* pBlock = m_pFreeList;
    m_pFreeList = pBlock->pNext;
    m_dwFreeBlocks--;
    
    // 设置魔数
    pBlock->dwMagic = MEMORY_MAGIC_NUMBER;
    
    LeaveCriticalSection(&m_CriticalSection);
    
    // 返回用户数据区域
    return (BYTE*)pBlock + sizeof(MemoryBlockHeader);
}

BOOL CMemorySubPool::Free(void* pMemory)
{
    if (!pMemory)
        return FALSE;
        
    EnterCriticalSection(&m_CriticalSection);
    
    // 获取块头
    MemoryBlockHeader* pBlock = (MemoryBlockHeader*)((BYTE*)pMemory - sizeof(MemoryBlockHeader));
    
    // 检查魔数
    if (pBlock->dwMagic != MEMORY_MAGIC_NUMBER)
    {
        LeaveCriticalSection(&m_CriticalSection);
        return FALSE; // 内存损坏或重复释放
    }
    
    // 清除魔数
    pBlock->dwMagic = 0;
    
    // 加入空闲链表
    pBlock->pNext = m_pFreeList;
    m_pFreeList = pBlock;
    m_dwFreeBlocks++;
    
    LeaveCriticalSection(&m_CriticalSection);
    return TRUE;
}

BOOL CMemorySubPool::AllocateNewChunk()
{
    const DWORD BLOCKS_PER_CHUNK = 64;
    DWORD dwChunkSize = m_dwBlockSize * BLOCKS_PER_CHUNK;
    
    // 使用VirtualAlloc分配大块内存
    void* pChunk = VirtualAlloc(NULL, dwChunkSize, MEM_COMMIT | MEM_RESERVE, PAGE_READWRITE);
    if (!pChunk)
        return FALSE;
        
    m_Chunks.push_back(pChunk);
    
    // 初始化空闲链表
    for (DWORD i = 0; i < BLOCKS_PER_CHUNK; i++)
    {
        MemoryBlockHeader* pBlock = (MemoryBlockHeader*)GetBlockFromChunk(pChunk, i);
        pBlock->dwSize = m_dwBlockSize;
        pBlock->dwMagic = 0;
        pBlock->pNext = m_pFreeList;
        m_pFreeList = pBlock;
    }
    
    m_dwTotalBlocks += BLOCKS_PER_CHUNK;
    m_dwFreeBlocks += BLOCKS_PER_CHUNK;
    
    return TRUE;
}

void* CMemorySubPool::GetBlockFromChunk(void* pChunk, DWORD dwIndex)
{
    return (BYTE*)pChunk + (dwIndex * m_dwBlockSize);
}

//===========================================================================
// CMemoryPool 实现
//===========================================================================

CMemoryPool::CMemoryPool()
{
    memset(&m_Stats, 0, sizeof(m_Stats));
    InitializeCriticalSection(&m_CriticalSection);
    m_bInitialized = FALSE;
}

CMemoryPool::~CMemoryPool()
{
    Cleanup();
    DeleteCriticalSection(&m_CriticalSection);
}

BOOL CMemoryPool::Initialize()
{
    if (m_bInitialized)
        return TRUE;
        
    EnterCriticalSection(&m_CriticalSection);
    
    // 初始化各个子池
    DWORD dwSizes[MBS_COUNT] = { MBS_TINY, MBS_SMALL, MBS_MEDIUM, MBS_LARGE, MBS_HUGE };
    
    for (int i = 0; i < MBS_COUNT; i++)
    {
        if (!m_SubPools[i].Initialize(dwSizes[i]))
        {
            LeaveCriticalSection(&m_CriticalSection);
            return FALSE;
        }
    }
    
    m_bInitialized = TRUE;
    LeaveCriticalSection(&m_CriticalSection);
    
    return TRUE;
}

void CMemoryPool::Cleanup()
{
    if (!m_bInitialized)
        return;
        
    EnterCriticalSection(&m_CriticalSection);
    
    for (int i = 0; i < MBS_COUNT; i++)
    {
        m_SubPools[i].Cleanup();
    }
    
    m_bInitialized = FALSE;
    LeaveCriticalSection(&m_CriticalSection);
}

void* CMemoryPool::Allocate(DWORD dwSize)
{
    if (!m_bInitialized)
        return malloc(dwSize);
        
    int nPoolIndex = GetPoolIndex(dwSize);
    void* pMemory = NULL;
    
    if (nPoolIndex >= 0)
    {
        // 使用内存池分配
        pMemory = m_SubPools[nPoolIndex].Allocate();
        if (pMemory)
        {
            UpdateStats(dwSize, TRUE);
            m_Stats.dwPoolHits++;
        }
    }
    
    if (!pMemory)
    {
        // 内存池分配失败，使用系统分配
        pMemory = malloc(dwSize);
        if (pMemory)
        {
            UpdateStats(dwSize, TRUE);
            m_Stats.dwPoolMisses++;
        }
    }
    
    return pMemory;
}

BOOL CMemoryPool::Free(void* pMemory)
{
    if (!pMemory)
        return FALSE;
        
    if (!m_bInitialized)
    {
        free(pMemory);
        return TRUE;
    }
    
    // 尝试从各个子池中释放
    for (int i = 0; i < MBS_COUNT; i++)
    {
        if (m_SubPools[i].Free(pMemory))
        {
            UpdateStats(0, FALSE); // 大小未知，只更新计数
            return TRUE;
        }
    }
    
    // 不是从池中分配的，使用系统释放
    free(pMemory);
    UpdateStats(0, FALSE);
    return TRUE;
}

int CMemoryPool::GetPoolIndex(DWORD dwSize)
{
    if (dwSize <= MBS_TINY) return 0;
    if (dwSize <= MBS_SMALL) return 1;
    if (dwSize <= MBS_MEDIUM) return 2;
    if (dwSize <= MBS_LARGE) return 3;
    if (dwSize <= MBS_HUGE) return 4;
    return -1; // 太大，不使用池
}

void CMemoryPool::UpdateStats(DWORD dwSize, BOOL bAllocate)
{
    EnterCriticalSection(&m_CriticalSection);
    
    if (bAllocate)
    {
        m_Stats.dwTotalAllocated += dwSize;
        m_Stats.dwCurrentUsage += dwSize;
        m_Stats.dwAllocationCount++;
        
        if (m_Stats.dwCurrentUsage > m_Stats.dwPeakUsage)
            m_Stats.dwPeakUsage = m_Stats.dwCurrentUsage;
    }
    else
    {
        m_Stats.dwTotalFreed += dwSize;
        if (m_Stats.dwCurrentUsage >= dwSize)
            m_Stats.dwCurrentUsage -= dwSize;
        m_Stats.dwFreeCount++;
    }
    
    LeaveCriticalSection(&m_CriticalSection);
}

void CMemoryPool::GetStats(MemoryPoolStats* pStats)
{
    if (pStats)
    {
        EnterCriticalSection(&m_CriticalSection);
        *pStats = m_Stats;
        LeaveCriticalSection(&m_CriticalSection);
    }
}

void CMemoryPool::Defragment()
{
    if (!m_bInitialized)
        return;

    EnterCriticalSection(&m_CriticalSection);

    // 对每个子池进行碎片整理
    for (int i = 0; i < MBS_COUNT; i++)
    {
        // 如果空闲块过多，释放一些内存块
        DWORD dwFreeBlocks = m_SubPools[i].GetFreeBlocks();
        DWORD dwTotalBlocks = m_SubPools[i].GetTotalBlocks();

        if (dwTotalBlocks > 64 && dwFreeBlocks > dwTotalBlocks * 3 / 4)
        {
            // 空闲率超过75%，可以释放一些内存
            // 这里简化处理，实际可以实现更复杂的碎片整理
            OutputDebugStringA("[MemoryPool] Defragmentation opportunity detected\n");
        }
    }

    LeaveCriticalSection(&m_CriticalSection);
}

void CMemoryPool::TrimMemory()
{
    if (!m_bInitialized)
        return;

    EnterCriticalSection(&m_CriticalSection);

    // 释放过多的空闲内存
    for (int i = 0; i < MBS_COUNT; i++)
    {
        DWORD dwFreeBlocks = m_SubPools[i].GetFreeBlocks();
        if (dwFreeBlocks > 32) // 如果空闲块超过32个
        {
            // 可以考虑释放一些内存块
            // 实际实现中需要更复杂的逻辑
        }
    }

    LeaveCriticalSection(&m_CriticalSection);
}

void CMemoryPool::LogStats()
{
    MemoryPoolStats stats;
    GetStats(&stats);

    char szLog[512];
    sprintf_s(szLog, sizeof(szLog),
        "[MemoryPool] Allocated: %d KB, Current: %d KB, Peak: %d KB, "
        "Allocs: %d, Frees: %d, Hits: %d, Misses: %d, Efficiency: %.1f%%\n",
        stats.dwTotalAllocated / 1024,
        stats.dwCurrentUsage / 1024,
        stats.dwPeakUsage / 1024,
        stats.dwAllocationCount,
        stats.dwFreeCount,
        stats.dwPoolHits,
        stats.dwPoolMisses,
        stats.dwPoolHits * 100.0f / max(1, stats.dwPoolHits + stats.dwPoolMisses));

    OutputDebugStringA(szLog);
}
