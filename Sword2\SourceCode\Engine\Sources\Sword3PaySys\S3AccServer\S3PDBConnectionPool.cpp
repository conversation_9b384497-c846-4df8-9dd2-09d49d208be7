//-----------------------------------------//
//                                         //
//  File		: S3PDBConnectionPool.cpp  //
//	Author		: <PERSON>            //
//	Modified	: 8/24/2002                //
//                                         //
//-----------------------------------------//
#include "stdafx.h"
#include "GlobalFun.h"

#include "S3PDBConnectionPool.h"

#include "S3PDB_MSSQLServer_Connection.h"
#include "S3PAccount.h"
#include "../../Engine/Src/KGLog.h"

S3PDBConnectionPool* S3PDBConnectionPool::m_pInstance = NULL;

static DWORD g_nGMID = 0;

DWORD GetGMID()
{
	return g_nGMID;
}

S3PDBConnectionPool::S3PDBConnectionPool()
{
	m_dwConLimits = 0;
}

S3PDBConnectionPool::~S3PDBConnectionPool()
{
	for (int i = 0; i < m_ConVBCPool.size(); i++)
	{
		if (m_ConVBCPool[i])
		{
			m_ConVBCPool[i]->CloseConnect();
			delete m_ConVBCPool[i];
		}
	}
}

BOOL S3PDBConnectionPool::Init(const std::string &strINIPath, const std::string &strSection, DWORD dwConLimits)
{
	m_dwConLimits = dwConLimits;

	if (!(strINIPath.empty()))
	{
		TCHAR szServer[def_KEYNAMELEN];
		TCHAR szDataBase[def_KEYNAMELEN];
		TCHAR szUser[def_KEYNAMELEN];
		TCHAR szPassword[def_KEYNAMELEN];
		
		DWORD dwLen =
			KPIGetPrivateProfileString(strSection.c_str(),
			def_SERVERKEYNAME,
			"",
			szServer,
			def_KEYNAMELEN,
			strINIPath.c_str() );
		dwLen =
			KPIGetPrivateProfileString( strSection.c_str(),
			def_DATABASEKEYNAME,
			"",
			szDataBase,
			def_KEYNAMELEN,
			strINIPath.c_str() );
		dwLen =
			KPIGetPrivateProfileString( strSection.c_str(),
			def_USERKEYNAME,
			"",
			szUser,
			def_KEYNAMELEN,
			strINIPath.c_str() );
		dwLen =
			KPIGetPrivateProfileString( strSection.c_str(),
			def_PASSWORDKEYNAME,
			"",
			szPassword,
			def_KEYNAMELEN,
			strINIPath.c_str() );

		_DATABASEINFO DBInfo;
		DBInfo.strServer = szServer;
		DBInfo.strDataBase = szDataBase;
		DBInfo.strUser = szUser;
		DBInfo.strPassword = szPassword;

		S3PDBConVBC* pVBC = NULL;
		for (int i = 0; i < m_dwConLimits; i++)
		{
			if (CreateConnection(&DBInfo, &pVBC))
			{
				m_ConVBCPool.push_back(pVBC);
			}
			else
				break;
		}
	}

	if (m_ConVBCPool.size() > 0 && m_ConVBCPool[0])
	{
		S3PAccount::GetServerID(m_ConVBCPool[0], "gm-kingsoft", g_nGMID);
	}

	return m_ConVBCPool.size() == m_dwConLimits;
}

BOOL S3PDBConnectionPool::RemoveDBCon(S3PDBConVBC** ppInfo)
{
	while (IsLocked())
		Sleep(1);
	Lock();
	BOOL b = FALSE;
	for (int nIndex = 0; nIndex < m_ConVBCPool.size(); nIndex++)
	{
		if (m_ConVBCPool[nIndex] && !m_ConVBCPool[nIndex]->IsLocked())
		{
			*ppInfo = m_ConVBCPool[nIndex];
			m_ConVBCPool[nIndex]->Lock();
			b = TRUE;
			KGLogPrintf(LOG_INFO,"DB Connect %d is busy !\n", nIndex);
			break;
		}
	}
	Unlock();

	return b;
}

BOOL S3PDBConnectionPool::ReturnDBCon(S3PDBConVBC* pInfo)
{
	while (IsLocked())
		Sleep(1);
	Lock();
	BOOL b = FALSE;
	for (int nIndex = 0; nIndex < m_ConVBCPool.size(); nIndex++)
	{
		if (m_ConVBCPool[nIndex] == pInfo && m_ConVBCPool[nIndex]->IsLocked())
		{
			// 在归还连接前检查连接健康状态
			if (!IsConnectionHealthy(m_ConVBCPool[nIndex]))
			{
				KGLogPrintf(LOG_INFO,"DB Connect %d is unhealthy, attempting to reconnect...\n", nIndex);

				// 尝试重新连接
				if (!ReconnectDatabase(nIndex))
				{
					KGLogPrintf(LOG_ERR,"Failed to reconnect DB Connect %d\n", nIndex);
					// 连接失败，但仍然解锁以避免死锁
				}
			}

			m_ConVBCPool[nIndex]->Unlock();
			b = TRUE;
			KGLogPrintf(LOG_INFO,"DB Connect %d is free !\n", nIndex);
			break;
		}
	}
	Unlock();

	return b;
}

S3PDBConnectionPool* S3PDBConnectionPool::Instance()
{
	if ( NULL == m_pInstance )
	{
		m_pInstance = new S3PDBConnectionPool;
	}
	return m_pInstance;
}

void S3PDBConnectionPool::ReleaseInstance()
{
	if ( NULL != m_pInstance )
	{
		delete m_pInstance;
		m_pInstance = NULL;
	}
}

BOOL S3PDBConnectionPool::CreateConnection(_LPDATABASEINFO pDatabase, S3PDBConVBC** ppInfo)
{
	S3PDBConVBC* pCon = new S3PDB_MSSQLServer_Connection();
	if (pCon->OpenConnect(pDatabase))
	{
		*ppInfo = pCon;
		return TRUE;
	}
	delete pCon;
	pCon = NULL;
	return FALSE;
}

BOOL S3PDBConnectionPool::IsConnectionHealthy(S3PDBConVBC* pInfo)
{
	if (!pInfo)
		return FALSE;

	// 尝试执行一个简单的查询来测试连接
	try
	{
		S3P_MSSQLServer_Result result;
		BOOL bRet = pInfo->QueryResult("SELECT 1", &result);
		return bRet;
	}
	catch (...)
	{
		KGLogPrintf(LOG_ERR, "Exception occurred while checking database connection health\n");
		return FALSE;
	}
}

BOOL S3PDBConnectionPool::ReconnectDatabase(int nIndex)
{
	if (nIndex < 0 || nIndex >= m_ConVBCPool.size())
		return FALSE;

	S3PDBConVBC* pOldCon = m_ConVBCPool[nIndex];
	if (pOldCon)
	{
		// 关闭旧连接
		pOldCon->CloseConnect();
		delete pOldCon;
	}

	// 创建新连接
	S3PDBConVBC* pNewCon = NULL;
	if (CreateConnection(&m_DatabaseInfo, &pNewCon))
	{
		m_ConVBCPool[nIndex] = pNewCon;
		KGLogPrintf(LOG_INFO, "Successfully reconnected database connection %d\n", nIndex);
		return TRUE;
	}
	else
	{
		// 重连失败，设置为NULL
		m_ConVBCPool[nIndex] = NULL;
		KGLogPrintf(LOG_ERR, "Failed to reconnect database connection %d\n", nIndex);
		return FALSE;
	}
}