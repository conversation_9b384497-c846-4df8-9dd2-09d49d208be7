//---------------------------------------------------------------------------
// Sword2 Shop Script Integration (c) 2024
//
// File:	ShopScriptIntegration.h
// Date:	2024.07.02
// Code:	AI Assistant
// Desc:	Integration layer between shop system and Lua scripts
//---------------------------------------------------------------------------
#ifndef SHOP_SCRIPT_INTEGRATION_H
#define SHOP_SCRIPT_INTEGRATION_H

#include "ShopManager.h"
#include "AuctionManager.h"
#include "LuaScriptEngine.h"
#include "PlayerManager.h"
#include "ItemManager.h"
#include "UnifiedLoggingSystem.h"
#include <string>
#include <unordered_map>
#include <vector>
#include <functional>

namespace sword2 {

// 商店脚本API扩展
namespace ShopScriptAPI {

    // 商店基础API
    int CreateShop(lua_State* L);
    int DeleteShop(lua_State* L);
    int GetShop(lua_State* L);
    int OpenShop(lua_State* L);
    int CloseShop(lua_State* L);
    int GetShopInfo(lua_State* L);
    int SetShopConfig(lua_State* L);

    // 商店物品API
    int AddShopItem(lua_State* L);
    int RemoveShopItem(lua_State* L);
    int UpdateShopItem(lua_State* L);
    int GetShopItems(lua_State* L);
    int GetAvailableItems(lua_State* L);
    int SetItemPrice(lua_State* L);
    int SetItemStock(lua_State* L);
    int SetItemDiscount(lua_State* L);

    // 购买和销售API
    int BuyItem(lua_State* L);
    int SellItem(lua_State* L);
    int AddToCart(lua_State* L);
    int RemoveFromCart(lua_State* L);
    int GetCart(lua_State* L);
    int BuyCart(lua_State* L);
    int ClearCart(lua_State* L);

    // 价格计算API
    int CalculatePrice(lua_State* L);
    int CalculateSellPrice(lua_State* L);
    int CalculateDiscount(lua_State* L);
    int GetMarketPrice(lua_State* L);
    int SetDynamicPricing(lua_State* L);

    // 货币API
    int GetPlayerMoney(lua_State* L);
    int AddPlayerMoney(lua_State* L);
    int DeductPlayerMoney(lua_State* L);
    int GetCurrencyType(lua_State* L);
    int ConvertCurrency(lua_State* L);

    // 拍卖行API
    int CreateAuction(lua_State* L);
    int PlaceBid(lua_State* L);
    int BuyoutAuction(lua_State* L);
    int CancelAuction(lua_State* L);
    int SearchAuctions(lua_State* L);
    int GetAuctionInfo(lua_State* L);
    int GetPlayerAuctions(lua_State* L);
    int GetPlayerBids(lua_State* L);

    // 统计API
    int GetShopStatistics(lua_State* L);
    int GetAuctionStatistics(lua_State* L);
    int GetSalesHistory(lua_State* L);
    int GetPopularItems(lua_State* L);
    int GetPriceTrends(lua_State* L);

    // 事件API
    int RegisterShopEventHandler(lua_State* L);
    int UnregisterShopEventHandler(lua_State* L);
    int TriggerShopEvent(lua_State* L);
    int OnItemBought(lua_State* L);
    int OnItemSold(lua_State* L);
    int OnAuctionCreated(lua_State* L);
    int OnAuctionCompleted(lua_State* L);

    // 权限API
    int CheckShopAccess(lua_State* L);
    int SetShopPermission(lua_State* L);
    int GetShopOwner(lua_State* L);
    int TransferShopOwnership(lua_State* L);

    // 邮件API
    int SendShopMail(lua_State* L);
    int GetShopMails(lua_State* L);
    int CollectMailAttachment(lua_State* L);
    int DeleteShopMail(lua_State* L);
}

// 商店事件类型
enum class ShopEvent : uint8_t
{
    ShopCreated = 0,    // 商店创建
    ShopDeleted,        // 商店删除
    ShopOpened,         // 商店打开
    ShopClosed,         // 商店关闭
    ItemAdded,          // 物品添加
    ItemRemoved,        // 物品移除
    ItemBought,         // 物品购买
    ItemSold,           // 物品出售
    PriceChanged,       // 价格变动
    StockChanged,       // 库存变动
    AuctionCreated,     // 拍卖创建
    AuctionBid,         // 拍卖竞价
    AuctionCompleted,   // 拍卖完成
    AuctionCancelled    // 拍卖取消
};

// 商店事件处理器
class ShopEventHandler
{
public:
    struct ShopEventData
    {
        ShopEvent event;
        uint32_t shopId;
        uint32_t playerId;
        uint32_t itemId;
        uint32_t quantity;
        uint32_t price;
        uint32_t auctionId;
        std::chrono::system_clock::time_point timestamp;

        ShopEventData() = default;
        ShopEventData(ShopEvent evt, uint32_t shop, uint32_t player = 0)
            : event(evt), shopId(shop), playerId(player), itemId(0), quantity(0), price(0), auctionId(0)
        {
            timestamp = std::chrono::system_clock::now();
        }
    };

    using EventCallback = std::function<void(const ShopEventData&)>;

    // 注册事件处理器
    void RegisterEventHandler(ShopEvent event, const std::string& handlerName, EventCallback callback)
    {
        std::lock_guard<std::mutex> lock(m_handlerMutex);
        m_eventHandlers[event][handlerName] = callback;

        LOG_DEBUG("SHOP_EVENT", "Registered event handler: " + handlerName + " for event " + std::to_string(static_cast<int>(event)));
    }

    // 注销事件处理器
    void UnregisterEventHandler(ShopEvent event, const std::string& handlerName)
    {
        std::lock_guard<std::mutex> lock(m_handlerMutex);
        auto it = m_eventHandlers.find(event);
        if (it != m_eventHandlers.end())
        {
            it->second.erase(handlerName);
            LOG_DEBUG("SHOP_EVENT", "Unregistered event handler: " + handlerName);
        }
    }

    // 触发事件
    void TriggerEvent(const ShopEventData& eventData)
    {
        std::lock_guard<std::mutex> lock(m_handlerMutex);

        auto it = m_eventHandlers.find(eventData.event);
        if (it != m_eventHandlers.end())
        {
            for (const auto& [handlerName, callback] : it->second)
            {
                try
                {
                    callback(eventData);
                }
                catch (const std::exception& e)
                {
                    LOG_ERROR("SHOP_EVENT", "Error in event handler " + handlerName + ": " + e.what());
                }
            }
        }

        // 记录事件历史
        m_eventHistory.push_back(eventData);
        if (m_eventHistory.size() > 1000) // 限制历史记录数量
        {
            m_eventHistory.erase(m_eventHistory.begin());
        }
    }

    // 获取事件历史
    std::vector<ShopEventData> GetEventHistory(uint32_t shopId = 0, ShopEvent event = ShopEvent::ShopCreated) const
    {
        std::lock_guard<std::mutex> lock(m_handlerMutex);
        std::vector<ShopEventData> history;

        for (const auto& eventData : m_eventHistory)
        {
            if ((shopId == 0 || eventData.shopId == shopId) &&
                (event == ShopEvent::ShopCreated || eventData.event == event))
            {
                history.push_back(eventData);
            }
        }

        return history;
    }

private:
    mutable std::mutex m_handlerMutex;
    std::unordered_map<ShopEvent, std::unordered_map<std::string, EventCallback>> m_eventHandlers;
    std::vector<ShopEventData> m_eventHistory;
};

// 动态定价系统
class DynamicPricingSystem
{
public:
    struct PriceData
    {
        uint32_t itemId = 0;
        uint32_t basePrice = 0;
        uint32_t currentPrice = 0;
        float demandFactor = 1.0f;      // 需求因子
        float supplyFactor = 1.0f;      // 供应因子
        float seasonFactor = 1.0f;      // 季节因子
        uint32_t salesCount = 0;        // 销售数量
        uint32_t stockCount = 0;        // 库存数量
        std::chrono::system_clock::time_point lastUpdate;

        PriceData() = default;
        PriceData(uint32_t id, uint32_t price) : itemId(id), basePrice(price), currentPrice(price)
        {
            lastUpdate = std::chrono::system_clock::now();
        }
    };

    // 更新物品价格
    void UpdatePrice(uint32_t itemId, uint32_t basePrice, uint32_t salesCount, uint32_t stockCount)
    {
        std::lock_guard<std::mutex> lock(m_priceMutex);

        auto it = m_priceData.find(itemId);
        if (it == m_priceData.end())
        {
            m_priceData[itemId] = PriceData(itemId, basePrice);
            it = m_priceData.find(itemId);
        }

        auto& data = it->second;
        data.salesCount = salesCount;
        data.stockCount = stockCount;

        // 计算需求因子（销售量越高，需求越大）
        data.demandFactor = 1.0f + (salesCount * 0.01f);

        // 计算供应因子（库存量越高，供应越大，价格越低）
        data.supplyFactor = stockCount > 0 ? 1.0f / (1.0f + stockCount * 0.005f) : 2.0f;

        // 计算季节因子（可以根据游戏时间调整）
        data.seasonFactor = CalculateSeasonFactor(itemId);

        // 计算最终价格
        float priceMultiplier = data.demandFactor * data.supplyFactor * data.seasonFactor;
        data.currentPrice = static_cast<uint32_t>(data.basePrice * priceMultiplier);

        // 限制价格波动范围（50% - 200%）
        uint32_t minPrice = data.basePrice / 2;
        uint32_t maxPrice = data.basePrice * 2;
        data.currentPrice = std::max(minPrice, std::min(maxPrice, data.currentPrice));

        data.lastUpdate = std::chrono::system_clock::now();
    }

    // 获取物品当前价格
    uint32_t GetCurrentPrice(uint32_t itemId) const
    {
        std::lock_guard<std::mutex> lock(m_priceMutex);
        auto it = m_priceData.find(itemId);
        return (it != m_priceData.end()) ? it->second.currentPrice : 0;
    }

    // 获取价格数据
    PriceData GetPriceData(uint32_t itemId) const
    {
        std::lock_guard<std::mutex> lock(m_priceMutex);
        auto it = m_priceData.find(itemId);
        return (it != m_priceData.end()) ? it->second : PriceData();
    }

    // 获取价格趋势
    std::vector<uint32_t> GetPriceTrend(uint32_t itemId) const
    {
        std::lock_guard<std::mutex> lock(m_priceMutex);
        auto it = m_priceTrends.find(itemId);
        return (it != m_priceTrends.end()) ? it->second : std::vector<uint32_t>();
    }

private:
    mutable std::mutex m_priceMutex;
    std::unordered_map<uint32_t, PriceData> m_priceData;
    std::unordered_map<uint32_t, std::vector<uint32_t>> m_priceTrends; // 价格历史

    float CalculateSeasonFactor(uint32_t itemId) const
    {
        // 这里可以根据游戏时间、节日等因素计算季节因子
        // 暂时返回固定值
        return 1.0f;
    }
};

// 商店脚本集成管理器
class ShopScriptIntegration : public Singleton<ShopScriptIntegration>
{
public:
    ShopScriptIntegration() = default;
    ~ShopScriptIntegration() = default;

    // 初始化商店脚本集成
    bool Initialize()
    {
        // 注册商店相关的Lua API函数
        RegisterShopScriptAPI();

        // 注册默认事件处理器
        RegisterDefaultEventHandlers();

        LOG_INFO("SHOP_SCRIPT", "Shop script integration initialized");
        return true;
    }

    // 加载商店脚本
    bool LoadShopScript(const std::string& scriptPath)
    {
        ScriptResult result = LOAD_SCRIPT(scriptPath, ScriptType::Shop);
        if (result == ScriptResult::Success)
        {
            LOG_INFO("SHOP_SCRIPT", "Loaded shop script: " + scriptPath);
            return true;
        }
        else
        {
            LOG_ERROR("SHOP_SCRIPT", "Failed to load shop script: " + scriptPath);
            return false;
        }
    }

    // 执行商店脚本函数
    bool ExecuteShopFunction(const std::string& functionName, uint32_t shopId, uint32_t playerId = 0)
    {
        std::vector<LuaValue> args;
        args.push_back(LuaValue(static_cast<double>(shopId)));
        if (playerId != 0)
        {
            args.push_back(LuaValue(static_cast<double>(playerId)));
        }

        ScriptResult result = EXECUTE_FUNCTION(functionName, args);
        return result == ScriptResult::Success;
    }

    // 触发商店事件
    void TriggerShopEvent(ShopEvent event, uint32_t shopId, uint32_t playerId = 0)
    {
        ShopEventHandler::ShopEventData eventData(event, shopId, playerId);
        m_eventHandler.TriggerEvent(eventData);

        // 执行脚本事件处理
        std::string eventName = GetEventName(event);
        ExecuteShopFunction("on_shop_" + eventName, shopId, playerId);
    }

    // 计算动态价格
    uint32_t CalculateDynamicPrice(uint32_t itemId, uint32_t basePrice, uint32_t shopId)
    {
        // 获取商店统计数据
        auto shop = GET_SHOP(shopId);
        if (!shop) return basePrice;

        auto shopItem = shop->FindItem(itemId);
        if (!shopItem) return basePrice;

        // 更新动态定价
        m_pricingSystem.UpdatePrice(itemId, basePrice, shopItem->totalSold, shopItem->stock);

        return m_pricingSystem.GetCurrentPrice(itemId);
    }

    // 计算折扣价格
    uint32_t CalculateDiscountPrice(uint32_t basePrice, float discountRate, uint32_t playerId)
    {
        // 这里可以调用Lua脚本进行复杂的折扣计算
        std::vector<LuaValue> args = {
            LuaValue(static_cast<double>(basePrice)),
            LuaValue(static_cast<double>(discountRate)),
            LuaValue(static_cast<double>(playerId))
        };

        auto result = EXECUTE_FUNCTION_WITH_RETURN("calculate_discount_price", args);
        if (result.has_value() && result->type == LuaValueType::Number)
        {
            return static_cast<uint32_t>(result->numberValue);
        }

        return static_cast<uint32_t>(basePrice * (1.0f - discountRate));
    }

    // 检查购买条件
    bool CheckPurchaseCondition(uint32_t playerId, uint32_t itemId, uint32_t quantity, uint32_t shopId)
    {
        std::vector<LuaValue> args = {
            LuaValue(static_cast<double>(playerId)),
            LuaValue(static_cast<double>(itemId)),
            LuaValue(static_cast<double>(quantity)),
            LuaValue(static_cast<double>(shopId))
        };

        auto result = EXECUTE_FUNCTION_WITH_RETURN("check_purchase_condition", args);
        if (result.has_value() && result->type == LuaValueType::Boolean)
        {
            return result->booleanValue;
        }

        return true; // 默认允许购买
    }

    // 获取事件处理器
    ShopEventHandler& GetEventHandler() { return m_eventHandler; }

    // 获取动态定价系统
    DynamicPricingSystem& GetPricingSystem() { return m_pricingSystem; }

private:
    ShopEventHandler m_eventHandler;
    DynamicPricingSystem m_pricingSystem;

    void RegisterShopScriptAPI();
    void RegisterDefaultEventHandlers();
    std::string GetEventName(ShopEvent event);
};

} // namespace sword2

// 全局商店脚本集成访问
#define SHOP_SCRIPT_INTEGRATION() sword2::ShopScriptIntegration::getInstance()

// 便捷宏定义
#define INIT_SHOP_SCRIPT_INTEGRATION() SHOP_SCRIPT_INTEGRATION().Initialize()
#define LOAD_SHOP_SCRIPT(scriptPath) SHOP_SCRIPT_INTEGRATION().LoadShopScript(scriptPath)
#define EXECUTE_SHOP_FUNCTION(functionName, shopId, playerId) SHOP_SCRIPT_INTEGRATION().ExecuteShopFunction(functionName, shopId, playerId)

#define TRIGGER_SHOP_EVENT(event, shopId, playerId) SHOP_SCRIPT_INTEGRATION().TriggerShopEvent(event, shopId, playerId)
#define CALCULATE_DYNAMIC_PRICE(itemId, basePrice, shopId) SHOP_SCRIPT_INTEGRATION().CalculateDynamicPrice(itemId, basePrice, shopId)
#define CALCULATE_DISCOUNT_PRICE(basePrice, discountRate, playerId) SHOP_SCRIPT_INTEGRATION().CalculateDiscountPrice(basePrice, discountRate, playerId)
#define CHECK_PURCHASE_CONDITION(playerId, itemId, quantity, shopId) SHOP_SCRIPT_INTEGRATION().CheckPurchaseCondition(playerId, itemId, quantity, shopId)

#define SHOP_EVENT_HANDLER() SHOP_SCRIPT_INTEGRATION().GetEventHandler()
#define DYNAMIC_PRICING_SYSTEM() SHOP_SCRIPT_INTEGRATION().GetPricingSystem()

#endif // SHOP_SCRIPT_INTEGRATION_H