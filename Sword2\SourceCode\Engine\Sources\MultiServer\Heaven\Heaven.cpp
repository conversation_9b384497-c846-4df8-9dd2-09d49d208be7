// Heaven.cpp : Defines the entry point for the DLL application.
//

#define WIN32_LEAN_AND_MEAN		// Exclude rarely-used stuff from Windows headers

#ifndef _WINDOWS_
	#define WIN32_LEAN_AND_MEAN
		#include <winsock2.h>
		#include <windows.h>
	#undef WIN32_LEAN_AND_MEAN
#endif

#pragma message( "* ATTENTION : PLEASE USE IT IN ANSI SETTING *" )

BOOL APIENTRY DllMain( HANDLE hModule, 
                       DWORD  ul_reason_for_call, 
                       LPVOID lpReserved
					 )
{
    return TRUE;
}

