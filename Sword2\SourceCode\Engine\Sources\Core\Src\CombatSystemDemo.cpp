//---------------------------------------------------------------------------
// Sword2 Combat System Demo (c) 2024
//
// File:	CombatSystemDemo.cpp
// Date:	2024.07.02
// Code:	AI Assistant
// Desc:	Comprehensive demonstration of the combat system
//---------------------------------------------------------------------------

#include "KCore.h"
#include "CombatSystem.h"
#include "CombatManager.h"
#include "CombatScriptIntegration.h"
#include "PlayerSystem.h"
#include "PlayerManager.h"
#include "SkillSystem.h"
#include "SkillManager.h"
#include "ItemManager.h"
#include "InventoryManager.h"
#include "LuaScriptEngine.h"
#include "UnifiedLoggingSystem.h"

namespace sword2 {

// 战斗系统演示类
class CombatSystemDemo
{
public:
    CombatSystemDemo()
    {
        m_initialized = false;
    }

    bool Initialize()
    {
        if (m_initialized) return true;

        printf("[COMBAT_DEMO] Initializing combat system demo...\n");

        // 初始化日志系统
        LOGGER().Initialize();
        LOGGER().AddConsoleAppender(LogLevel::Debug);

        // 启动玩家管理器
        if (!START_PLAYER_SYSTEM())
        {
            printf("[COMBAT_DEMO] Failed to start player system\n");
            return false;
        }

        // 启动技能管理器
        if (!START_SKILL_SYSTEM())
        {
            printf("[COMBAT_DEMO] Failed to start skill system\n");
            return false;
        }

        // 启动物品管理器
        if (!START_ITEM_SYSTEM())
        {
            printf("[COMBAT_DEMO] Failed to start item system\n");
            return false;
        }

        // 初始化脚本引擎
        if (!INIT_SCRIPT_ENGINE("../../../Server/script"))
        {
            printf("[COMBAT_DEMO] Failed to initialize script engine\n");
            return false;
        }

        // 启动战斗管理器
        if (!START_COMBAT_SYSTEM())
        {
            printf("[COMBAT_DEMO] Failed to start combat system\n");
            return false;
        }

        // 初始化战斗脚本集成
        if (!INIT_COMBAT_SCRIPT_INTEGRATION())
        {
            printf("[COMBAT_DEMO] Failed to initialize combat script integration\n");
            return false;
        }

        m_initialized = true;
        LOG_INFO("COMBAT_DEMO", "Combat system demo initialized successfully");

        return true;
    }

    void RunDemo()
    {
        if (!m_initialized)
        {
            printf("[COMBAT_DEMO] System not initialized\n");
            return;
        }

        LOG_INFO("COMBAT_DEMO", "Starting combat system demonstration...");

        // 演示各个功能
        DemoBasicCombat();
        DemoSkillCombat();
        DemoStateEffects();
        DemoCombatStatistics();
        DemoCombatEvents();
        DemoCombatScripting();
        DemoAdvancedCombat();

        LOG_INFO("COMBAT_DEMO", "Combat system demonstration completed");
    }

    void Cleanup()
    {
        if (!m_initialized) return;

        LOG_INFO("COMBAT_DEMO", "Cleaning up combat system demo...");

        // 清理创建的玩家
        for (uint32_t playerId : m_createdPlayers)
        {
            UNREGISTER_COMBAT_PARTICIPANT(playerId);
            REMOVE_PLAYER_INVENTORY(playerId);
            PLAYER_LOGOUT(playerId);
        }
        m_createdPlayers.clear();

        // 停止系统
        STOP_COMBAT_SYSTEM();
        STOP_ITEM_SYSTEM();
        STOP_SKILL_SYSTEM();
        SHUTDOWN_SCRIPT_ENGINE();
        STOP_PLAYER_SYSTEM();

        m_initialized = false;
        LOG_INFO("COMBAT_DEMO", "Combat system demo cleanup completed");
    }

private:
    bool m_initialized;
    std::vector<uint32_t> m_createdPlayers;

    void DemoBasicCombat()
    {
        LOG_INFO("COMBAT_DEMO", "=== Basic Combat Demo ===");

        // 创建测试玩家
        PlayerCreationParams params1("战士", "warrior");
        params1.series = PlayerSeries::Shaolin;
        uint32_t warrior = CREATE_PLAYER(params1);

        PlayerCreationParams params2("法师", "mage");
        params2.series = PlayerSeries::Emei;
        uint32_t mage = CREATE_PLAYER(params2);

        if (warrior != 0 && mage != 0)
        {
            m_createdPlayers.push_back(warrior);
            m_createdPlayers.push_back(mage);

            // 玩家登录
            uint32_t sessionId1, sessionId2;
            PLAYER_LOGIN(warrior, "127.0.0.1", 6001, sessionId1);
            PLAYER_LOGIN(mage, "127.0.0.1", 6002, sessionId2);

            // 注册战斗参与者
            REGISTER_COMBAT_PARTICIPANT(warrior, true);
            REGISTER_COMBAT_PARTICIPANT(mage, true);

            // 设置玩家属性
            auto warriorPlayer = GET_ONLINE_PLAYER(warrior);
            auto magePlayer = GET_ONLINE_PLAYER(mage);

            if (warriorPlayer && magePlayer)
            {
                // 战士属性
                warriorPlayer->level = 20;
                warriorPlayer->attributes.maxLife = 1000;
                warriorPlayer->attributes.currentLife = 1000;
                warriorPlayer->attributes.maxMana = 300;
                warriorPlayer->attributes.currentMana = 300;
                warriorPlayer->attributes.minAttack = 80;
                warriorPlayer->attributes.maxAttack = 120;
                warriorPlayer->attributes.defense = 50;
                warriorPlayer->attributes.hit = 85;
                warriorPlayer->attributes.dodge = 15;
                warriorPlayer->attributes.criticalRate = 20;
                warriorPlayer->attributes.criticalDamage = 150;

                // 法师属性
                magePlayer->level = 18;
                magePlayer->attributes.maxLife = 600;
                magePlayer->attributes.currentLife = 600;
                magePlayer->attributes.maxMana = 800;
                magePlayer->attributes.currentMana = 800;
                magePlayer->attributes.minMagicAttack = 100;
                magePlayer->attributes.maxMagicAttack = 150;
                magePlayer->attributes.magicDefense = 30;
                magePlayer->attributes.hit = 75;
                magePlayer->attributes.dodge = 25;
                magePlayer->attributes.criticalRate = 15;
                magePlayer->attributes.criticalDamage = 180;

                // 设置位置
                warriorPlayer->position.x = 100;
                warriorPlayer->position.y = 100;
                magePlayer->position.x = 102;
                magePlayer->position.y = 100;

                LOG_INFO("COMBAT_DEMO", "Created combat participants:");
                LOG_INFO("COMBAT_DEMO", "  Warrior - Level: " + std::to_string(warriorPlayer->level) +
                        ", HP: " + std::to_string(warriorPlayer->attributes.currentLife) +
                        ", Attack: " + std::to_string(warriorPlayer->attributes.minAttack) + "-" + std::to_string(warriorPlayer->attributes.maxAttack));
                LOG_INFO("COMBAT_DEMO", "  Mage - Level: " + std::to_string(magePlayer->level) +
                        ", HP: " + std::to_string(magePlayer->attributes.currentLife) +
                        ", Magic Attack: " + std::to_string(magePlayer->attributes.minMagicAttack) + "-" + std::to_string(magePlayer->attributes.maxMagicAttack));

                // 开始战斗
                CombatResult startResult = START_ATTACK(warrior, mage);
                LOG_INFO("COMBAT_DEMO", "Start attack result: " + std::to_string(static_cast<int>(startResult)));

                // 执行几轮攻击
                for (int round = 1; round <= 5; ++round)
                {
                    LOG_INFO("COMBAT_DEMO", "--- Round " + std::to_string(round) + " ---");

                    // 战士攻击法师
                    CombatResult attackResult1 = PERFORM_ATTACK(warrior, mage);
                    LOG_INFO("COMBAT_DEMO", "Warrior attacks Mage - Result: " + std::to_string(static_cast<int>(attackResult1)));
                    LOG_INFO("COMBAT_DEMO", "  Mage HP: " + std::to_string(magePlayer->attributes.currentLife) + "/" + std::to_string(magePlayer->attributes.maxLife));

                    if (magePlayer->attributes.currentLife == 0)
                    {
                        LOG_INFO("COMBAT_DEMO", "Mage defeated!");
                        break;
                    }

                    // 法师反击
                    CombatResult attackResult2 = PERFORM_ATTACK(mage, warrior);
                    LOG_INFO("COMBAT_DEMO", "Mage attacks Warrior - Result: " + std::to_string(static_cast<int>(attackResult2)));
                    LOG_INFO("COMBAT_DEMO", "  Warrior HP: " + std::to_string(warriorPlayer->attributes.currentLife) + "/" + std::to_string(warriorPlayer->attributes.maxLife));

                    if (warriorPlayer->attributes.currentLife == 0)
                    {
                        LOG_INFO("COMBAT_DEMO", "Warrior defeated!");
                        break;
                    }

                    // 短暂延迟
                    std::this_thread::sleep_for(std::chrono::milliseconds(500));
                }

                // 显示战斗统计
                auto warriorParticipant = COMBAT_MANAGER().GetParticipant(warrior);
                auto mageParticipant = COMBAT_MANAGER().GetParticipant(mage);

                if (warriorParticipant)
                {
                    LOG_INFO("COMBAT_DEMO", "Warrior combat stats:");
                    LOG_INFO("COMBAT_DEMO", "  Damage dealt: " + std::to_string(warriorParticipant->totalDamageDealt));
                    LOG_INFO("COMBAT_DEMO", "  Damage taken: " + std::to_string(warriorParticipant->totalDamageTaken));
                    LOG_INFO("COMBAT_DEMO", "  DPS: " + std::to_string(warriorParticipant->GetDPS()));
                }

                if (mageParticipant)
                {
                    LOG_INFO("COMBAT_DEMO", "Mage combat stats:");
                    LOG_INFO("COMBAT_DEMO", "  Damage dealt: " + std::to_string(mageParticipant->totalDamageDealt));
                    LOG_INFO("COMBAT_DEMO", "  Damage taken: " + std::to_string(mageParticipant->totalDamageTaken));
                    LOG_INFO("COMBAT_DEMO", "  DPS: " + std::to_string(mageParticipant->GetDPS()));
                }
            }
        }
    }

    void DemoSkillCombat()
    {
        LOG_INFO("COMBAT_DEMO", "=== Skill Combat Demo ===");

        if (m_createdPlayers.size() < 2) return;

        uint32_t warrior = m_createdPlayers[0];
        uint32_t mage = m_createdPlayers[1];

        auto warriorPlayer = GET_ONLINE_PLAYER(warrior);
        auto magePlayer = GET_ONLINE_PLAYER(mage);

        if (!warriorPlayer || !magePlayer) return;

        // 恢复生命值
        warriorPlayer->attributes.currentLife = warriorPlayer->attributes.maxLife;
        magePlayer->attributes.currentLife = magePlayer->attributes.maxLife;

        LOG_INFO("COMBAT_DEMO", "Testing skill combat...");

        // 战士使用技能攻击
        CombatResult skillResult1 = PERFORM_SKILL_ATTACK(warrior, 1001, mage, 0, 0);
        LOG_INFO("COMBAT_DEMO", "Warrior uses skill 1001 - Result: " + std::to_string(static_cast<int>(skillResult1)));
        LOG_INFO("COMBAT_DEMO", "  Mage HP after skill: " + std::to_string(magePlayer->attributes.currentLife) + "/" + std::to_string(magePlayer->attributes.maxLife));

        // 法师使用技能攻击
        CombatResult skillResult2 = PERFORM_SKILL_ATTACK(mage, 2001, warrior, 0, 0);
        LOG_INFO("COMBAT_DEMO", "Mage uses skill 2001 - Result: " + std::to_string(static_cast<int>(skillResult2)));
        LOG_INFO("COMBAT_DEMO", "  Warrior HP after skill: " + std::to_string(warriorPlayer->attributes.currentLife) + "/" + std::to_string(warriorPlayer->attributes.maxLife));

        // 法师使用治疗技能
        CombatResult healResult = PERFORM_SKILL_ATTACK(mage, 3001, mage, 0, 0);
        LOG_INFO("COMBAT_DEMO", "Mage uses heal skill 3001 - Result: " + std::to_string(static_cast<int>(healResult)));
        LOG_INFO("COMBAT_DEMO", "  Mage HP after heal: " + std::to_string(magePlayer->attributes.currentLife) + "/" + std::to_string(magePlayer->attributes.maxLife));

        // 测试范围技能
        CombatResult areaResult = PERFORM_SKILL_ATTACK(mage, 4001, 0, 100, 100);
        LOG_INFO("COMBAT_DEMO", "Mage uses area skill 4001 - Result: " + std::to_string(static_cast<int>(areaResult)));
    }

    void DemoStateEffects()
    {
        LOG_INFO("COMBAT_DEMO", "=== State Effects Demo ===");

        if (m_createdPlayers.empty()) return;

        uint32_t playerId = m_createdPlayers[0];
        auto player = GET_ONLINE_PLAYER(playerId);
        if (!player) return;

        LOG_INFO("COMBAT_DEMO", "Testing state effects on player " + std::to_string(playerId));

        // 应用各种状态效果
        uint32_t strengthBoost = APPLY_STATE_EFFECT(playerId, playerId, CombatStateType::StrengthBoost, 50, 10000, 0);
        uint32_t attackBoost = APPLY_STATE_EFFECT(playerId, playerId, CombatStateType::AttackBoost, 30, 15000, 0);
        uint32_t lifeRegen = APPLY_STATE_EFFECT(playerId, playerId, CombatStateType::LifeRegen, 10, 20000, 0);
        uint32_t poison = APPLY_STATE_EFFECT(0, playerId, CombatStateType::Poison, 5, 8000, 0);
        uint32_t shield = APPLY_STATE_EFFECT(playerId, playerId, CombatStateType::Shield, 100, 30000, 0);

        LOG_INFO("COMBAT_DEMO", "Applied state effects:");
        LOG_INFO("COMBAT_DEMO", "  Strength Boost (ID: " + std::to_string(strengthBoost) + ")");
        LOG_INFO("COMBAT_DEMO", "  Attack Boost (ID: " + std::to_string(attackBoost) + ")");
        LOG_INFO("COMBAT_DEMO", "  Life Regen (ID: " + std::to_string(lifeRegen) + ")");
        LOG_INFO("COMBAT_DEMO", "  Poison (ID: " + std::to_string(poison) + ")");
        LOG_INFO("COMBAT_DEMO", "  Shield (ID: " + std::to_string(shield) + ")");

        // 检查状态效果
        bool hasStrengthBoost = HAS_EFFECT_TYPE(playerId, CombatStateType::StrengthBoost);
        bool hasPoison = HAS_EFFECT_TYPE(playerId, CombatStateType::Poison);
        bool hasShield = HAS_EFFECT_TYPE(playerId, CombatStateType::Shield);

        LOG_INFO("COMBAT_DEMO", "Effect checks:");
        LOG_INFO("COMBAT_DEMO", "  Has Strength Boost: " + std::string(hasStrengthBoost ? "Yes" : "No"));
        LOG_INFO("COMBAT_DEMO", "  Has Poison: " + std::string(hasPoison ? "Yes" : "No"));
        LOG_INFO("COMBAT_DEMO", "  Has Shield: " + std::string(hasShield ? "Yes" : "No"));

        // 获取所有状态效果
        auto effects = GET_TARGET_EFFECTS(playerId);
        LOG_INFO("COMBAT_DEMO", "Total active effects: " + std::to_string(effects.size()));

        for (const auto& effect : effects)
        {
            LOG_INFO("COMBAT_DEMO", "  Effect: " + effect.GetDescription() +
                    " (Remaining: " + std::to_string(effect.GetRemainingTime() / 1000) + "s)");
        }

        // 驱散负面效果
        uint32_t dispelledCount = DISPEL_EFFECTS(playerId, true, 2);
        LOG_INFO("COMBAT_DEMO", "Dispelled " + std::to_string(dispelledCount) + " debuffs");

        // 等待一段时间让周期性效果触发
        LOG_INFO("COMBAT_DEMO", "Waiting for periodic effects...");
        std::this_thread::sleep_for(std::chrono::seconds(3));

        // 清理所有效果
        CLEAR_ALL_EFFECTS(playerId);
        LOG_INFO("COMBAT_DEMO", "Cleared all effects");

        auto remainingEffects = GET_TARGET_EFFECTS(playerId);
        LOG_INFO("COMBAT_DEMO", "Remaining effects: " + std::to_string(remainingEffects.size()));
    }

    void DemoCombatStatistics()
    {
        LOG_INFO("COMBAT_DEMO", "=== Combat Statistics Demo ===");

        // 显示所有玩家的战斗统计
        for (uint32_t playerId : m_createdPlayers)
        {
            auto participant = COMBAT_MANAGER().GetParticipant(playerId);
            auto player = GET_ONLINE_PLAYER(playerId);

            if (participant && player)
            {
                LOG_INFO("COMBAT_DEMO", "Player " + player->name + " (" + std::to_string(playerId) + ") statistics:");
                LOG_INFO("COMBAT_DEMO", "  Combat State: " + std::to_string(static_cast<int>(participant->combatState)));
                LOG_INFO("COMBAT_DEMO", "  Combat Duration: " + std::to_string(participant->GetCombatDuration()) + " seconds");
                LOG_INFO("COMBAT_DEMO", "  Total Damage Dealt: " + std::to_string(participant->totalDamageDealt));
                LOG_INFO("COMBAT_DEMO", "  Total Damage Taken: " + std::to_string(participant->totalDamageTaken));
                LOG_INFO("COMBAT_DEMO", "  Total Healing Done: " + std::to_string(participant->totalHealingDone));
                LOG_INFO("COMBAT_DEMO", "  Kill Count: " + std::to_string(participant->killCount));
                LOG_INFO("COMBAT_DEMO", "  Death Count: " + std::to_string(participant->deathCount));
                LOG_INFO("COMBAT_DEMO", "  DPS: " + std::to_string(participant->GetDPS()));
                LOG_INFO("COMBAT_DEMO", "  HPS: " + std::to_string(participant->GetHPS()));
                LOG_INFO("COMBAT_DEMO", "  Current Target: " + std::to_string(participant->currentTarget));
                LOG_INFO("COMBAT_DEMO", "  Active Effects: " + std::to_string(participant->activeEffects.size()));
                LOG_INFO("COMBAT_DEMO", "");
            }
        }

        // 计算总体统计
        uint32_t totalDamage = 0;
        uint32_t totalHealing = 0;
        uint32_t totalKills = 0;
        uint32_t totalDeaths = 0;

        for (uint32_t playerId : m_createdPlayers)
        {
            auto participant = COMBAT_MANAGER().GetParticipant(playerId);
            if (participant)
            {
                totalDamage += participant->totalDamageDealt;
                totalHealing += participant->totalHealingDone;
                totalKills += participant->killCount;
                totalDeaths += participant->deathCount;
            }
        }

        LOG_INFO("COMBAT_DEMO", "Overall statistics:");
        LOG_INFO("COMBAT_DEMO", "  Total Damage: " + std::to_string(totalDamage));
        LOG_INFO("COMBAT_DEMO", "  Total Healing: " + std::to_string(totalHealing));
        LOG_INFO("COMBAT_DEMO", "  Total Kills: " + std::to_string(totalKills));
        LOG_INFO("COMBAT_DEMO", "  Total Deaths: " + std::to_string(totalDeaths));
        LOG_INFO("COMBAT_DEMO", "  Average DPS: " + std::to_string(m_createdPlayers.empty() ? 0 : totalDamage / m_createdPlayers.size()));
    }

    void DemoCombatEvents()
    {
        LOG_INFO("COMBAT_DEMO", "=== Combat Events Demo ===");

        // 注册自定义事件处理器
        auto& eventHandler = COMBAT_EVENT_HANDLER();

        eventHandler.RegisterEventHandler(CombatEvent::AttackHit, "demo_attack_hit",
            [](const CombatEventHandler::CombatEventData& data) {
                LOG_INFO("COMBAT_EVENT", "Custom handler - Attack hit: " + std::to_string(data.entityId) +
                        " -> " + std::to_string(data.otherId) + " (Damage: " + std::to_string(data.damage) + ")");
            });

        eventHandler.RegisterEventHandler(CombatEvent::TakeDamage, "demo_take_damage",
            [](const CombatEventHandler::CombatEventData& data) {
                LOG_INFO("COMBAT_EVENT", "Custom handler - Take damage: " + std::to_string(data.entityId) +
                        " (Damage: " + std::to_string(data.damage) + ")");
            });

        eventHandler.RegisterEventHandler(CombatEvent::StateApply, "demo_state_apply",
            [](const CombatEventHandler::CombatEventData& data) {
                LOG_INFO("COMBAT_EVENT", "Custom handler - State applied to: " + std::to_string(data.entityId) +
                        " by: " + std::to_string(data.otherId));
            });

        // 获取事件历史
        if (!m_createdPlayers.empty())
        {
            uint32_t playerId = m_createdPlayers[0];
            auto eventHistory = eventHandler.GetEventHistory(playerId);

            LOG_INFO("COMBAT_DEMO", "Event history for player " + std::to_string(playerId) + ": " +
                    std::to_string(eventHistory.size()) + " events");

            // 显示最近的几个事件
            size_t maxEvents = std::min(eventHistory.size(), size_t(5));
            for (size_t i = 0; i < maxEvents; ++i)
            {
                const auto& event = eventHistory[i];
                std::string eventName;

                switch (event.event)
                {
                case CombatEvent::AttackStart: eventName = "Attack Start"; break;
                case CombatEvent::AttackHit: eventName = "Attack Hit"; break;
                case CombatEvent::AttackMiss: eventName = "Attack Miss"; break;
                case CombatEvent::TakeDamage: eventName = "Take Damage"; break;
                case CombatEvent::Heal: eventName = "Heal"; break;
                case CombatEvent::Death: eventName = "Death"; break;
                case CombatEvent::StateApply: eventName = "State Apply"; break;
                case CombatEvent::StateRemove: eventName = "State Remove"; break;
                case CombatEvent::CombatStart: eventName = "Combat Start"; break;
                case CombatEvent::CombatEnd: eventName = "Combat End"; break;
                default: eventName = "Unknown"; break;
                }

                LOG_INFO("COMBAT_DEMO", "  Event " + std::to_string(i + 1) + ": " + eventName +
                        " - Entity: " + std::to_string(event.entityId) +
                        " - Other: " + std::to_string(event.otherId) +
                        " - Damage: " + std::to_string(event.damage) +
                        " - Heal: " + std::to_string(event.heal));
            }
        }
    }

    void DemoCombatScripting()
    {
        LOG_INFO("COMBAT_DEMO", "=== Combat Scripting Demo ===");

        // 创建测试脚本
        std::string testScript = R"(
            function combat_test_function(attackerId, targetId)
                WriteLog("Combat script function called!")
                WriteLog("Attacker ID: " .. attackerId)
                WriteLog("Target ID: " .. targetId)

                local attackerState = GetCombatState(attackerId)
                local targetState = GetCombatState(targetId)
                local distance = GetDistance(attackerId, targetId)

                WriteLog("Attacker combat state: " .. attackerState)
                WriteLog("Target combat state: " .. targetState)
                WriteLog("Distance: " .. distance)

                return true
            end

            function on_combat_attack_hit(attackerId, targetId)
                WriteLog("Attack hit event: " .. attackerId .. " -> " .. targetId)

                local damage = GetDamageDealt(attackerId)
                local dps = GetDPS(attackerId)

                WriteLog("Total damage dealt: " .. damage)
                WriteLog("Current DPS: " .. dps)

                return true
            end

            function on_combat_take_damage(targetId, attackerId)
                WriteLog("Take damage event: " .. targetId .. " from " .. attackerId)

                local damageTaken = GetDamageTaken(targetId)
                WriteLog("Total damage taken: " .. damageTaken)

                -- 检查是否需要自动治疗
                local currentHP = GetPlayerAttribute(targetId, "currentLife")
                local maxHP = GetPlayerAttribute(targetId, "maxLife")
                local hpRatio = currentHP / maxHP

                if hpRatio < 0.3 then
                    WriteLog("Low health detected, applying emergency heal")
                    ApplyHeal(targetId, targetId, 100)
                end

                return true
            end

            function calculate_damage_modifier(baseDamage, attackerId, targetId, skillId)
                WriteLog("Calculating damage modifier")
                WriteLog("Base damage: " .. baseDamage)

                local attackerLevel = GetPlayerLevel(attackerId)
                local targetLevel = GetPlayerLevel(targetId)
                local levelDiff = attackerLevel - targetLevel

                -- 等级差异修正
                local modifier = 1.0
                if levelDiff > 5 then
                    modifier = 1.2  -- 等级高很多，伤害增加
                elseif levelDiff < -5 then
                    modifier = 0.8  -- 等级低很多，伤害减少
                end

                -- 检查是否有特殊状态
                if HasEffectType(attackerId, 1) then -- 假设1是攻击增强
                    modifier = modifier * 1.3
                end

                if HasEffectType(targetId, 3) then -- 假设3是防御增强
                    modifier = modifier * 0.7
                end

                local finalDamage = math.floor(baseDamage * modifier)
                WriteLog("Final damage: " .. finalDamage .. " (modifier: " .. modifier .. ")")

                return finalDamage
            end

            function calculate_heal_modifier(baseHeal, healerId, targetId, skillId)
                WriteLog("Calculating heal modifier")

                local healerLevel = GetPlayerLevel(healerId)
                local modifier = 1.0 + (healerLevel * 0.02) -- 每级增加2%治疗效果

                -- 检查治疗增强状态
                if HasEffectType(healerId, 7) then -- 假设7是治疗增强
                    modifier = modifier * 1.5
                end

                local finalHeal = math.floor(baseHeal * modifier)
                WriteLog("Final heal: " .. finalHeal .. " (modifier: " .. modifier .. ")")

                return finalHeal
            end

            function check_combat_condition(condition, entityId, otherId)
                WriteLog("Checking combat condition: " .. condition)

                if condition == "can_attack" then
                    -- 检查是否可以攻击
                    local inCombat = IsInCombat(entityId)
                    local hasTarget = GetCombatTarget(entityId) ~= 0
                    local inRange = IsInAttackRange(entityId, otherId)

                    return inCombat and hasTarget and inRange
                elseif condition == "need_heal" then
                    -- 检查是否需要治疗
                    local currentHP = GetPlayerAttribute(entityId, "currentLife")
                    local maxHP = GetPlayerAttribute(entityId, "maxLife")

                    return (currentHP / maxHP) < 0.5
                elseif condition == "can_use_skill" then
                    -- 检查是否可以使用技能
                    local currentMana = GetPlayerAttribute(entityId, "currentMana")
                    local stunned = HasEffectType(entityId, 10) -- 假设10是眩晕
                    local silenced = HasEffectType(entityId, 11) -- 假设11是沉默

                    return currentMana > 50 and not stunned and not silenced
                end

                return false
            end
        )";

        // 执行测试脚本
        auto context = std::make_unique<LuaScriptContext>();
        context->Initialize();

        ScriptResult result = context->ExecuteString(testScript);
        if (result == ScriptResult::Success)
        {
            LOG_INFO("COMBAT_DEMO", "Successfully loaded combat test script");

            if (m_createdPlayers.size() >= 2)
            {
                uint32_t attackerId = m_createdPlayers[0];
                uint32_t targetId = m_createdPlayers[1];

                // 测试脚本函数调用
                std::vector<LuaValue> args = {
                    LuaValue(static_cast<double>(attackerId)),
                    LuaValue(static_cast<double>(targetId))
                };

                result = context->CallFunction("combat_test_function", args);
                if (result == ScriptResult::Success)
                {
                    LOG_INFO("COMBAT_DEMO", "Successfully executed combat script function");
                }

                // 测试事件处理函数
                context->CallFunction("on_combat_attack_hit", args);
                context->CallFunction("on_combat_take_damage", args);

                // 测试伤害修正函数
                std::vector<LuaValue> damageArgs = {
                    LuaValue(static_cast<double>(100)),
                    LuaValue(static_cast<double>(attackerId)),
                    LuaValue(static_cast<double>(targetId)),
                    LuaValue(static_cast<double>(1001))
                };
                context->CallFunction("calculate_damage_modifier", damageArgs);

                // 测试治疗修正函数
                std::vector<LuaValue> healArgs = {
                    LuaValue(static_cast<double>(50)),
                    LuaValue(static_cast<double>(attackerId)),
                    LuaValue(static_cast<double>(targetId)),
                    LuaValue(static_cast<double>(3001))
                };
                context->CallFunction("calculate_heal_modifier", healArgs);

                // 测试条件检查函数
                std::vector<LuaValue> conditionArgs = {
                    LuaValue("can_attack"),
                    LuaValue(static_cast<double>(attackerId)),
                    LuaValue(static_cast<double>(targetId))
                };
                context->CallFunction("check_combat_condition", conditionArgs);
            }
        }
        else
        {
            LOG_ERROR("COMBAT_DEMO", "Failed to load combat test script");
        }
    }

    void DemoAdvancedCombat()
    {
        LOG_INFO("COMBAT_DEMO", "=== Advanced Combat Demo ===");

        if (m_createdPlayers.size() < 2) return;

        uint32_t player1 = m_createdPlayers[0];
        uint32_t player2 = m_createdPlayers[1];

        auto p1 = GET_ONLINE_PLAYER(player1);
        auto p2 = GET_ONLINE_PLAYER(player2);

        if (!p1 || !p2) return;

        // 恢复状态
        p1->attributes.currentLife = p1->attributes.maxLife;
        p1->attributes.currentMana = p1->attributes.maxMana;
        p2->attributes.currentLife = p2->attributes.maxLife;
        p2->attributes.currentMana = p2->attributes.maxMana;

        LOG_INFO("COMBAT_DEMO", "Testing advanced combat scenarios...");

        // 场景1：连击系统
        LOG_INFO("COMBAT_DEMO", "--- Combo System Test ---");
        for (int i = 0; i < 3; ++i)
        {
            CombatResult result = PERFORM_ATTACK(player1, player2);
            LOG_INFO("COMBAT_DEMO", "Combo attack " + std::to_string(i + 1) + " - Result: " + std::to_string(static_cast<int>(result)));
            std::this_thread::sleep_for(std::chrono::milliseconds(200));
        }

        // 场景2：状态效果组合
        LOG_INFO("COMBAT_DEMO", "--- State Effect Combination Test ---");
        APPLY_STATE_EFFECT(player1, player2, CombatStateType::Stun, 0, 3000, 0);
        APPLY_STATE_EFFECT(player1, player2, CombatStateType::Poison, 8, 5000, 0);
        APPLY_STATE_EFFECT(player1, player1, CombatStateType::AttackBoost, 50, 10000, 0);

        // 尝试在眩晕状态下攻击
        CombatResult stunResult = PERFORM_ATTACK(player2, player1);
        LOG_INFO("COMBAT_DEMO", "Attack while stunned - Result: " + std::to_string(static_cast<int>(stunResult)));

        // 场景3：治疗和护盾测试
        LOG_INFO("COMBAT_DEMO", "--- Heal and Shield Test ---");
        APPLY_STATE_EFFECT(player2, player2, CombatStateType::Shield, 200, 15000, 0);
        APPLY_STATE_EFFECT(player2, player2, CombatStateType::LifeRegen, 15, 10000, 0);

        // 攻击有护盾的目标
        CombatResult shieldResult = PERFORM_ATTACK(player1, player2);
        LOG_INFO("COMBAT_DEMO", "Attack shielded target - Result: " + std::to_string(static_cast<int>(shieldResult)));

        // 场景4：AI辅助测试
        LOG_INFO("COMBAT_DEMO", "--- AI Helper Test ---");
        std::vector<uint32_t> candidates = {player2};
        uint32_t bestTarget = CombatAIHelper::SelectBestTarget(player1, candidates);
        LOG_INFO("COMBAT_DEMO", "AI selected best target: " + std::to_string(bestTarget));

        uint32_t recommendedSkill = CombatAIHelper::RecommendSkill(player1, player2);
        LOG_INFO("COMBAT_DEMO", "AI recommended skill: " + std::to_string(recommendedSkill));

        // 场景5：距离和范围测试
        LOG_INFO("COMBAT_DEMO", "--- Distance and Range Test ---");
        float distance = CombatAIHelper::CalculateDistance(p1->position.x, p1->position.y, p2->position.x, p2->position.y);
        LOG_INFO("COMBAT_DEMO", "Distance between players: " + std::to_string(distance));

        // 移动玩家位置
        p2->position.x = 110;
        p2->position.y = 110;

        float newDistance = CombatAIHelper::CalculateDistance(p1->position.x, p1->position.y, p2->position.x, p2->position.y);
        LOG_INFO("COMBAT_DEMO", "New distance after movement: " + std::to_string(newDistance));

        // 测试攻击范围
        bool inRange = (newDistance <= 2.0f);
        LOG_INFO("COMBAT_DEMO", "In attack range: " + std::string(inRange ? "Yes" : "No"));

        if (!inRange)
        {
            CombatResult rangeResult = PERFORM_ATTACK(player1, player2);
            LOG_INFO("COMBAT_DEMO", "Attack out of range - Result: " + std::to_string(static_cast<int>(rangeResult)));
        }

        // 等待状态效果触发
        LOG_INFO("COMBAT_DEMO", "Waiting for periodic effects...");
        std::this_thread::sleep_for(std::chrono::seconds(2));

        // 最终统计
        LOG_INFO("COMBAT_DEMO", "--- Final Statistics ---");
        auto p1Participant = COMBAT_MANAGER().GetParticipant(player1);
        auto p2Participant = COMBAT_MANAGER().GetParticipant(player2);

        if (p1Participant && p2Participant)
        {
            LOG_INFO("COMBAT_DEMO", "Player 1 final stats - HP: " + std::to_string(p1->attributes.currentLife) +
                    ", Damage: " + std::to_string(p1Participant->totalDamageDealt) +
                    ", DPS: " + std::to_string(p1Participant->GetDPS()));
            LOG_INFO("COMBAT_DEMO", "Player 2 final stats - HP: " + std::to_string(p2->attributes.currentLife) +
                    ", Damage: " + std::to_string(p2Participant->totalDamageDealt) +
                    ", DPS: " + std::to_string(p2Participant->GetDPS()));
        }
    }
};

} // namespace sword2

// 全局战斗系统演示实例
sword2::CombatSystemDemo g_CombatDemo;

// 初始化战斗系统
bool InitializeCombatSystem()
{
    return g_CombatDemo.Initialize();
}

// 运行战斗系统演示
void RunCombatSystemDemo()
{
    g_CombatDemo.RunDemo();
}

// 清理战斗系统
void CleanupCombatSystem()
{
    g_CombatDemo.Cleanup();
}