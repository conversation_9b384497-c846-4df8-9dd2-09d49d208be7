[MemoryPool]
; 内存池配置
EnableMemoryPool=1
InitialPoolSize=1024
MaxPoolSize=16384
GrowthSize=256
AutoShrink=1
ShrinkThreshold=75

; 各级内存池大小配置 (字节)
TinyBlockSize=16
SmallBlockSize=64
MediumBlockSize=256
LargeBlockSize=1024
HugeBlockSize=4096

; 初始块数量
TinyInitialBlocks=128
SmallInitialBlocks=64
MediumInitialBlocks=32
LargeInitialBlocks=16
HugeInitialBlocks=8

[ObjectPool]
; 对象池配置
EnableObjectPool=1
DefaultInitialSize=32
DefaultMaxSize=1024
DefaultGrowthSize=16
AutoShrinkEnabled=1
ShrinkThreshold=75

; 游戏对象池配置
PlayerObjectPoolSize=100
NPCObjectPoolSize=500
ItemObjectPoolSize=1000
EffectObjectPoolSize=200
ProjectileObjectPoolSize=300

[MemoryDefragmentation]
; 内存碎片整理配置
EnableAutoDefrag=1
FragmentationThreshold=60.0
DefragInterval=300000
MaxDefragTime=100
DefragStrategy=1

; 0=Compact, 1=Coalesce, 2=Relocate, 3=Aggressive, 4=Conservative
PreferredStrategy=1

[CacheOptimization]
; 缓存优化配置
EnableCacheOptimization=1
EnableHardwarePrefetch=1
EnableSoftwarePrefetch=1
PrefetchDistance=64
CacheLineSize=64

; 数据布局优化
OptimizeDataLayout=1
SeparateHotColdData=1
AlignToCache=1

[MemoryDebugging]
; 内存调试配置 (仅Debug模式)
EnableLeakDetection=1
EnableBoundsChecking=1
CallStackDepth=8
BreakOnLeak=0
BreakOnBoundsViolation=1

; 统计和报告
EnableDetailedStats=1
LogInterval=5000
GenerateReports=1
ReportPath=./MemoryReports/

[Performance]
; 性能配置
TargetFPS=60.0
MaxFrameTime=33
EnableAdaptiveQuality=1
EnableFrameSkipping=1

; 各阶段时间预算 (毫秒)
InputBudget=1
NetworkBudget=2
LogicBudget=4
PhysicsBudget=2
AnimationBudget=2
RenderBudget=10
AudioBudget=1

[RenderOptimization]
; 渲染优化配置
EnableBatchRendering=1
MaxBatchSize=1000
EnableFrustumCulling=1
EnableOcclusionCulling=0
EnableLOD=1

; LOD距离配置
LODDistance1=50.0
LODDistance2=100.0
LODDistance3=200.0
LODDistance4=500.0

; 渲染质量配置
DefaultRenderQuality=80
DefaultEffectQuality=70
DefaultShadowQuality=60
DefaultTextureQuality=80
DefaultParticleQuality=70

[AutoMemoryManagement]
; 自动内存管理配置
EnableAutoManagement=1
MaintenanceInterval=60000
AggressiveMode=0

; 自动清理阈值
MemoryUsageThreshold=80
FragmentationThreshold=70
AllocationFailureThreshold=5

[Monitoring]
; 监控配置
EnableRealTimeMonitoring=1
MonitoringInterval=1000
EnablePerformanceProfiling=1
MaxProfileSamples=1000

; 警告阈值
MemoryLeakWarningThreshold=100
PerformanceWarningThreshold=50
FragmentationWarningThreshold=80

[Advanced]
; 高级配置
UseVirtualAlloc=1
EnableMemoryCompression=0
CompressionThreshold=1048576
EnableHeatAnalysis=1
HeatAnalysisInterval=10000

; 实验性功能
EnableExperimentalFeatures=0
UseCustomAllocator=1
EnableMemoryProfiling=1
