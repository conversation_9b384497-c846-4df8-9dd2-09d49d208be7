//---------------------------------------------------------------------------
// Sword2 Modern Network Manager (c) 2024
//
// File:	ModernNetworkManager.h
// Date:	2024.07.02
// Code:	AI Assistant
// Desc:	Modern C++ network connection management with RAII and smart pointers
//---------------------------------------------------------------------------
#ifndef MODERN_NETWORK_MANAGER_H
#define MODERN_NETWORK_MANAGER_H

#include "ModernCpp.h"
#include <chrono>
#include <string>
#include <vector>
#include <functional>
#include <atomic>
#include <mutex>
#include <future>

namespace sword2 {

// 网络连接状态
enum class ConnectionState
{
    Disconnected,
    Connecting,
    Connected,
    Disconnecting,
    Error
};

// 网络消息类型
enum class MessageType : uint32_t
{
    Unknown = 0,
    Login,
    Logout,
    GameData,
    Chat,
    System
};

// 网络消息结构
struct NetworkMessage
{
    MessageType type;
    std::vector<uint8_t> data;
    std::chrono::steady_clock::time_point timestamp;
    
    NetworkMessage(MessageType t, const void* pData, size_t size)
        : type(t), timestamp(std::chrono::steady_clock::now())
    {
        if (pData && size > 0)
        {
            data.resize(size);
            std::memcpy(data.data(), pData, size);
        }
    }
};

// 网络事件回调
using MessageCallback = std::function<void(const NetworkMessage&)>;
using ConnectionCallback = std::function<void(ConnectionState, const std::string&)>;

// 现代化的网络连接管理器
class ModernNetworkManager
{
public:
    ModernNetworkManager();
    ~ModernNetworkManager();

    // 禁止拷贝，允许移动
    ModernNetworkManager(const ModernNetworkManager&) = delete;
    ModernNetworkManager& operator=(const ModernNetworkManager&) = delete;
    ModernNetworkManager(ModernNetworkManager&&) = default;
    ModernNetworkManager& operator=(ModernNetworkManager&&) = default;

    // 初始化和清理
    Result<bool, std::string> Initialize();
    void Shutdown() noexcept;

    // 连接管理
    std::future<Result<bool, std::string>> ConnectAsync(
        const std::string& address, 
        uint16_t port,
        std::chrono::milliseconds timeout = std::chrono::seconds(10));
    
    void Disconnect() noexcept;
    
    // 消息发送
    Result<bool, std::string> SendMessage(const NetworkMessage& message);
    Result<bool, std::string> SendRawData(const void* data, size_t size);
    
    // 批量发送
    Result<size_t, std::string> SendMessages(const std::vector<NetworkMessage>& messages);

    // 状态查询
    ConnectionState GetConnectionState() const noexcept;
    bool IsConnected() const noexcept;
    std::chrono::steady_clock::time_point GetLastActivityTime() const noexcept;

    // 回调管理
    void SetMessageCallback(MessageCallback callback);
    void SetConnectionCallback(ConnectionCallback callback);
    void ClearCallbacks() noexcept;

    // 网络处理
    void ProcessNetworkEvents();
    
    // 统计信息
    struct NetworkStats
    {
        std::atomic<uint64_t> bytesSent{0};
        std::atomic<uint64_t> bytesReceived{0};
        std::atomic<uint64_t> messagesSent{0};
        std::atomic<uint64_t> messagesReceived{0};
        std::atomic<uint64_t> connectionAttempts{0};
        std::atomic<uint64_t> connectionFailures{0};
        std::chrono::steady_clock::time_point startTime;
    };
    
    const NetworkStats& GetStats() const noexcept { return m_stats; }
    void ResetStats() noexcept;

    // 配置
    void SetKeepAliveInterval(std::chrono::milliseconds interval);
    void SetReconnectAttempts(uint32_t attempts);
    void SetMessageQueueSize(size_t maxSize);

private:
    // 内部状态
    std::atomic<ConnectionState> m_connectionState{ConnectionState::Disconnected};
    std::string m_serverAddress;
    uint16_t m_serverPort{0};
    
    // 回调函数
    MessageCallback m_messageCallback;
    ConnectionCallback m_connectionCallback;
    
    // 网络统计
    mutable NetworkStats m_stats;
    
    // 配置参数
    std::chrono::milliseconds m_keepAliveInterval{std::chrono::seconds(30)};
    uint32_t m_maxReconnectAttempts{3};
    size_t m_maxMessageQueueSize{1000};
    
    // 线程安全
    mutable std::mutex m_mutex;
    std::atomic<bool> m_shouldStop{false};
    
    // 内部实现
    class NetworkImpl;
    std::unique_ptr<NetworkImpl> m_impl;
    
    // 内部方法
    void OnMessageReceived(const NetworkMessage& message);
    void OnConnectionStateChanged(ConnectionState newState, const std::string& reason);
    void UpdateStats(bool sent, size_t bytes);
};

// 网络连接的RAII包装器
class NetworkConnection
{
public:
    explicit NetworkConnection(std::shared_ptr<ModernNetworkManager> manager)
        : m_manager(std::move(manager)) {}
    
    ~NetworkConnection()
    {
        if (m_manager && m_manager->IsConnected())
        {
            m_manager->Disconnect();
        }
    }
    
    // 禁止拷贝，允许移动
    NetworkConnection(const NetworkConnection&) = delete;
    NetworkConnection& operator=(const NetworkConnection&) = delete;
    NetworkConnection(NetworkConnection&&) = default;
    NetworkConnection& operator=(NetworkConnection&&) = default;
    
    ModernNetworkManager* operator->() const { return m_manager.get(); }
    ModernNetworkManager& operator*() const { return *m_manager; }
    
    bool IsValid() const { return m_manager != nullptr; }

private:
    std::shared_ptr<ModernNetworkManager> m_manager;
};

// 网络管理器工厂
class NetworkManagerFactory
{
public:
    static std::unique_ptr<ModernNetworkManager> CreateTCPManager();
    static std::unique_ptr<ModernNetworkManager> CreateUDPManager();
    static std::unique_ptr<ModernNetworkManager> CreateSecureManager();
    
    // 创建带自动重连的管理器
    static std::unique_ptr<ModernNetworkManager> CreateReliableManager(
        uint32_t maxRetries = 3,
        std::chrono::milliseconds retryInterval = std::chrono::seconds(5));
};

// 网络消息构建器
class MessageBuilder
{
public:
    MessageBuilder& SetType(MessageType type);
    MessageBuilder& AddData(const void* data, size_t size);
    MessageBuilder& AddString(const std::string& str);
    MessageBuilder& AddInteger(uint32_t value);
    MessageBuilder& AddFloat(float value);
    
    NetworkMessage Build();
    
private:
    MessageType m_type{MessageType::Unknown};
    std::vector<uint8_t> m_data;
};

// 便捷的消息发送函数
template<typename T>
Result<bool, std::string> SendTypedMessage(
    ModernNetworkManager& manager,
    MessageType type,
    const T& data)
{
    static_assert(std::is_trivially_copyable_v<T>, "Type must be trivially copyable");
    
    NetworkMessage message(type, &data, sizeof(T));
    return manager.SendMessage(message);
}

// 异步消息发送
template<typename T>
std::future<Result<bool, std::string>> SendTypedMessageAsync(
    std::shared_ptr<ModernNetworkManager> manager,
    MessageType type,
    T data)
{
    return std::async(std::launch::async, [manager, type, data]() {
        return SendTypedMessage(*manager, type, data);
    });
}

} // namespace sword2

// 全局网络管理器实例
extern std::unique_ptr<sword2::ModernNetworkManager> g_ModernNetworkManager;

// 便捷宏定义
#define NETWORK_SEND_MESSAGE(type, data) \
    g_ModernNetworkManager->SendMessage(sword2::NetworkMessage(type, &data, sizeof(data)))

#define NETWORK_CONNECT_ASYNC(address, port) \
    g_ModernNetworkManager->ConnectAsync(address, port)

#define NETWORK_IS_CONNECTED() \
    g_ModernNetworkManager->IsConnected()

#define NETWORK_SET_CALLBACK(callback) \
    g_ModernNetworkManager->SetMessageCallback(callback)

#endif // MODERN_NETWORK_MANAGER_H
