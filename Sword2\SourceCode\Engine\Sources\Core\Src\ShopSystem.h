//---------------------------------------------------------------------------
// Sword2 Shop System (c) 2024
//
// File:	ShopSystem.h
// Date:	2024.07.02
// Code:	AI Assistant
// Desc:	Comprehensive shop system compatible with existing game mechanics
//---------------------------------------------------------------------------
#ifndef SHOP_SYSTEM_H
#define SHOP_SYSTEM_H

#include "ModernCpp.h"
#include "UnifiedLoggingSystem.h"
#include "PlayerSystem.h"
#include "NPCSystem.h"
#include "ItemSystem.h"
#include <string>
#include <unordered_map>
#include <vector>
#include <memory>
#include <chrono>
#include <atomic>
#include <mutex>
#include <functional>

namespace sword2 {

// 商店类型
enum class ShopType : uint8_t
{
    NPC = 0,            // NPC商店
    Player,             // 玩家商店
    Auction,            // 拍卖行
    Guild,              // 帮会商店
    System,             // 系统商店
    Event,              // 活动商店
    Exchange,           // 兑换商店
    Consignment         // 寄售商店
};

// 商店操作结果
enum class ShopResult : uint8_t
{
    Success = 0,        // 成功
    Failed,             // 失败
    NotFound,           // 未找到
    InsufficientMoney,  // 金钱不足
    InsufficientStock,  // 库存不足
    InsufficientSpace,  // 背包空间不足
    CannotTrade,        // 无法交易
    PriceChanged,       // 价格变动
    ShopClosed,         // 商店关闭
    PermissionDenied,   // 权限不足
    ItemBound,          // 物品绑定
    LevelTooLow,        // 等级不足
    InvalidQuantity,    // 数量无效
    Timeout             // 超时
};

// 货币类型
enum class CurrencyType : uint8_t
{
    Gold = 0,           // 金币
    Silver,             // 银币
    Copper,             // 铜币
    Reputation,         // 声望
    Honor,              // 荣誉
    Contribution,       // 贡献度
    Token,              // 代币
    Points              // 积分
};

// 商店物品
struct ShopItem
{
    uint32_t itemId = 0;            // 物品ID
    uint32_t templateId = 0;        // 物品模板ID
    uint32_t price = 0;             // 价格
    CurrencyType currency = CurrencyType::Gold;
    
    uint32_t stock = 0;             // 库存数量
    uint32_t maxStock = 0;          // 最大库存
    bool isLimited = false;         // 是否限量
    bool isUnique = false;          // 是否唯一
    
    // 购买条件
    uint32_t minLevel = 1;          // 最低等级
    uint32_t maxLevel = 999;        // 最高等级
    PlayerSeries requiredSeries = PlayerSeries::None; // 门派要求
    uint32_t requiredReputation = 0; // 声望要求
    
    // 时间相关
    std::chrono::system_clock::time_point addTime;     // 添加时间
    std::chrono::system_clock::time_point expireTime;  // 过期时间
    std::chrono::system_clock::time_point restockTime; // 补货时间
    uint32_t restockInterval = 0;   // 补货间隔（秒）
    uint32_t restockAmount = 0;     // 补货数量
    
    // 折扣相关
    float discount = 1.0f;          // 折扣（1.0为原价）
    std::chrono::system_clock::time_point discountExpire; // 折扣过期时间
    
    // 统计信息
    uint32_t totalSold = 0;         // 总销售量
    uint32_t dailySold = 0;         // 日销售量
    std::chrono::system_clock::time_point lastSaleTime; // 最后销售时间
    
    ShopItem() = default;
    ShopItem(uint32_t id, uint32_t price, CurrencyType curr = CurrencyType::Gold)
        : itemId(id), price(price), currency(curr)
    {
        addTime = std::chrono::system_clock::now();
        lastSaleTime = addTime;
    }
    
    // 检查是否有库存
    bool HasStock(uint32_t quantity = 1) const
    {
        if (!isLimited) return true;
        return stock >= quantity;
    }
    
    // 检查是否过期
    bool IsExpired() const
    {
        if (expireTime == std::chrono::system_clock::time_point{}) return false;
        return std::chrono::system_clock::now() > expireTime;
    }
    
    // 检查是否需要补货
    bool NeedsRestock() const
    {
        if (!isLimited || restockInterval == 0) return false;
        auto now = std::chrono::system_clock::now();
        return now > restockTime;
    }
    
    // 获取实际价格（考虑折扣）
    uint32_t GetActualPrice() const
    {
        if (std::chrono::system_clock::now() > discountExpire)
        {
            return price;
        }
        return static_cast<uint32_t>(price * discount);
    }
    
    // 购买物品
    bool Purchase(uint32_t quantity = 1)
    {
        if (!HasStock(quantity)) return false;
        
        if (isLimited)
        {
            stock -= quantity;
        }
        
        totalSold += quantity;
        dailySold += quantity;
        lastSaleTime = std::chrono::system_clock::now();
        
        return true;
    }
    
    // 补货
    void Restock()
    {
        if (restockAmount > 0)
        {
            stock = std::min(stock + restockAmount, maxStock);
            restockTime = std::chrono::system_clock::now() + std::chrono::seconds(restockInterval);
        }
    }
    
    // 检查购买条件
    bool CheckConditions(const Player& player) const
    {
        // 等级检查
        if (player.level < minLevel || player.level > maxLevel)
            return false;
        
        // 门派检查
        if (requiredSeries != PlayerSeries::None && player.series != requiredSeries)
            return false;
        
        // 声望检查
        if (requiredReputation > 0 && player.reputation < requiredReputation)
            return false;
        
        return true;
    }
    
    // 获取物品描述
    std::string GetDescription() const
    {
        std::string desc = "Item " + std::to_string(itemId);
        desc += " - Price: " + std::to_string(GetActualPrice());
        
        switch (currency)
        {
        case CurrencyType::Gold: desc += " Gold"; break;
        case CurrencyType::Silver: desc += " Silver"; break;
        case CurrencyType::Copper: desc += " Copper"; break;
        case CurrencyType::Reputation: desc += " Reputation"; break;
        case CurrencyType::Honor: desc += " Honor"; break;
        case CurrencyType::Contribution: desc += " Contribution"; break;
        case CurrencyType::Token: desc += " Token"; break;
        case CurrencyType::Points: desc += " Points"; break;
        }
        
        if (isLimited)
        {
            desc += " (Stock: " + std::to_string(stock) + ")";
        }
        
        if (discount < 1.0f && std::chrono::system_clock::now() <= discountExpire)
        {
            desc += " [" + std::to_string(static_cast<int>((1.0f - discount) * 100)) + "% OFF]";
        }
        
        return desc;
    }
};

// 商店配置
struct ShopConfig
{
    std::string name;               // 商店名称
    std::string description;        // 商店描述
    ShopType type = ShopType::NPC;  // 商店类型
    
    uint32_t ownerId = 0;           // 所有者ID（NPC或玩家）
    uint32_t maxItems = 100;        // 最大物品数量
    uint32_t maxCustomers = 10;     // 最大同时顾客数
    
    // 营业时间
    bool alwaysOpen = true;         // 是否24小时营业
    uint32_t openHour = 0;          // 开门时间
    uint32_t closeHour = 24;        // 关门时间
    
    // 权限设置
    bool publicAccess = true;       // 是否公开访问
    std::vector<uint32_t> allowedPlayers; // 允许的玩家列表
    std::vector<PlayerSeries> allowedSeries; // 允许的门派列表
    uint32_t minLevel = 1;          // 最低等级要求
    
    // 税收设置
    float taxRate = 0.0f;           // 税率
    uint32_t taxCollector = 0;      // 税收收集者ID
    
    // 自动设置
    bool autoRestock = true;        // 自动补货
    bool autoCleanup = true;        // 自动清理过期物品
    bool dynamicPricing = false;    // 动态定价
    
    ShopConfig() = default;
    ShopConfig(const std::string& shopName, ShopType shopType)
        : name(shopName), type(shopType) {}
    
    // 检查是否营业
    bool IsOpen() const
    {
        if (alwaysOpen) return true;
        
        auto now = std::chrono::system_clock::now();
        auto time_t = std::chrono::system_clock::to_time_t(now);
        auto tm = *std::localtime(&time_t);
        
        uint32_t currentHour = tm.tm_hour;
        
        if (openHour <= closeHour)
        {
            return currentHour >= openHour && currentHour < closeHour;
        }
        else
        {
            // 跨夜营业
            return currentHour >= openHour || currentHour < closeHour;
        }
    }
    
    // 检查访问权限
    bool CheckAccess(const Player& player) const
    {
        // 等级检查
        if (player.level < minLevel) return false;
        
        // 公开访问
        if (publicAccess) return true;
        
        // 玩家白名单
        if (!allowedPlayers.empty())
        {
            auto it = std::find(allowedPlayers.begin(), allowedPlayers.end(), player.playerId);
            if (it != allowedPlayers.end()) return true;
        }
        
        // 门派白名单
        if (!allowedSeries.empty())
        {
            auto it = std::find(allowedSeries.begin(), allowedSeries.end(), player.series);
            if (it != allowedSeries.end()) return true;
        }
        
        return false;
    }
};

// 购买记录
struct PurchaseRecord
{
    uint32_t recordId = 0;          // 记录ID
    uint32_t shopId = 0;            // 商店ID
    uint32_t playerId = 0;          // 玩家ID
    uint32_t itemId = 0;            // 物品ID
    uint32_t quantity = 0;          // 数量
    uint32_t unitPrice = 0;         // 单价
    uint32_t totalPrice = 0;        // 总价
    CurrencyType currency = CurrencyType::Gold;
    
    std::chrono::system_clock::time_point purchaseTime; // 购买时间
    std::string playerName;         // 玩家名称
    std::string itemName;           // 物品名称
    
    PurchaseRecord() = default;
    PurchaseRecord(uint32_t shop, uint32_t player, uint32_t item, uint32_t qty, uint32_t price, CurrencyType curr)
        : shopId(shop), playerId(player), itemId(item), quantity(qty), unitPrice(price), currency(curr)
    {
        totalPrice = unitPrice * quantity;
        purchaseTime = std::chrono::system_clock::now();
    }
};

// 商店会话
struct ShopSession
{
    uint32_t sessionId = 0;         // 会话ID
    uint32_t shopId = 0;            // 商店ID
    uint32_t playerId = 0;          // 玩家ID
    
    std::chrono::system_clock::time_point startTime; // 开始时间
    std::chrono::system_clock::time_point lastActivity; // 最后活动时间
    
    // 购物车
    std::vector<std::pair<uint32_t, uint32_t>> cart; // itemId, quantity
    uint32_t totalCost = 0;         // 总费用
    CurrencyType cartCurrency = CurrencyType::Gold;
    
    bool isActive = true;           // 是否活跃
    
    ShopSession() = default;
    ShopSession(uint32_t session, uint32_t shop, uint32_t player)
        : sessionId(session), shopId(shop), playerId(player)
    {
        startTime = std::chrono::system_clock::now();
        lastActivity = startTime;
    }
    
    // 检查是否超时
    bool IsExpired() const
    {
        auto now = std::chrono::system_clock::now();
        auto elapsed = std::chrono::duration_cast<std::chrono::minutes>(now - lastActivity);
        return elapsed.count() >= 10; // 10分钟超时
    }
    
    // 更新活动时间
    void UpdateActivity()
    {
        lastActivity = std::chrono::system_clock::now();
    }
    
    // 添加到购物车
    void AddToCart(uint32_t itemId, uint32_t quantity)
    {
        // 查找是否已存在
        for (auto& item : cart)
        {
            if (item.first == itemId)
            {
                item.second += quantity;
                return;
            }
        }
        
        // 添加新物品
        cart.emplace_back(itemId, quantity);
    }
    
    // 从购物车移除
    bool RemoveFromCart(uint32_t itemId, uint32_t quantity = 0)
    {
        for (auto it = cart.begin(); it != cart.end(); ++it)
        {
            if (it->first == itemId)
            {
                if (quantity == 0 || it->second <= quantity)
                {
                    cart.erase(it);
                }
                else
                {
                    it->second -= quantity;
                }
                return true;
            }
        }
        return false;
    }
    
    // 清空购物车
    void ClearCart()
    {
        cart.clear();
        totalCost = 0;
    }
    
    // 获取购物车物品数量
    uint32_t GetCartItemCount() const
    {
        uint32_t total = 0;
        for (const auto& item : cart)
        {
            total += item.second;
        }
        return total;
    }
};

} // namespace sword2

#endif // SHOP_SYSTEM_H
