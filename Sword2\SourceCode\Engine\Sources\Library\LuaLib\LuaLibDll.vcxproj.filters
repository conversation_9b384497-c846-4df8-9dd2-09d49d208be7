﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="4.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <ItemGroup>
    <Filter Include="Source Files">
      <UniqueIdentifier>{31aa630d-cc06-4f32-afe0-f1b6ab30ada2}</UniqueIdentifier>
      <Extensions>cpp;c;cxx;rc;def;r;odl;idl;hpj;bat</Extensions>
    </Filter>
    <Filter Include="Header Files">
      <UniqueIdentifier>{c3eac335-243e-4fd3-9c46-c3db34ed5ded}</UniqueIdentifier>
      <Extensions>h;hpp;hxx;hm;inl</Extensions>
    </Filter>
    <Filter Include="BaseLib">
      <UniqueIdentifier>{f5352a1b-7e24-424f-90e9-acda66edc99b}</UniqueIdentifier>
    </Filter>
    <Filter Include="Resource Files">
      <UniqueIdentifier>{66107866-b690-4b5e-9a6f-496208f02ba8}</UniqueIdentifier>
      <Extensions>ico;cur;bmp;dlg;rc2;rct;bin;rgs;gif;jpg;jpeg;jpe</Extensions>
    </Filter>
  </ItemGroup>
  <ItemGroup>
    <ClCompile Include="src\lapi.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\lcode.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\ldebug.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\ldo.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\lfunc.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\lgc.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\llex.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\lmem.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\lobject.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\lparser.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\lstate.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\lstring.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\ltable.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\ltests.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\ltm.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\lua.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\LuaExtend.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\lundump.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\lvm.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\lzio.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\baselib\lauxlib.c">
      <Filter>BaseLib</Filter>
    </ClCompile>
    <ClCompile Include="src\baselib\lbaselib.c">
      <Filter>BaseLib</Filter>
    </ClCompile>
    <ClCompile Include="src\baselib\ldblib.c">
      <Filter>BaseLib</Filter>
    </ClCompile>
    <ClCompile Include="src\baselib\liolib.c">
      <Filter>BaseLib</Filter>
    </ClCompile>
    <ClCompile Include="src\baselib\lmathlib.c">
      <Filter>BaseLib</Filter>
    </ClCompile>
    <ClCompile Include="src\baselib\lstrlib.c">
      <Filter>BaseLib</Filter>
    </ClCompile>
  </ItemGroup>
  <ItemGroup>
    <ClInclude Include="src\lapi.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="src\lauxlib.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="src\lcode.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="src\ldebug.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="src\ldo.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="src\lfunc.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="src\lgc.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="src\llex.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="src\llimits.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="src\lmem.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="src\lobject.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="src\lopcodes.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="src\lparser.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="src\lstate.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="src\lstring.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="src\ltable.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="src\ltm.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="src\lua.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="src\luadebug.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="src\LuaDef.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="src\lualib.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="src\lundump.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="src\lvm.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="src\lzio.h">
      <Filter>Header Files</Filter>
    </ClInclude>
  </ItemGroup>
</Project>