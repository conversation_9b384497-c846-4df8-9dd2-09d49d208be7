﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="4.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <ItemGroup>
    <Filter Include="Source Files">
      <UniqueIdentifier>{bd32bbd9-4018-4b8f-8825-5aec8eea8167}</UniqueIdentifier>
      <Extensions>cpp;c;cxx;rc;def;r;odl;idl;hpj;bat</Extensions>
    </Filter>
    <Filter Include="Header Files">
      <UniqueIdentifier>{cda1d88d-5ed7-40b2-b3ca-4e93b1ce2b74}</UniqueIdentifier>
      <Extensions>h;hpp;hxx;hm;inl</Extensions>
    </Filter>
    <Filter Include="Resource Files">
      <UniqueIdentifier>{d4172a82-d73a-41c6-9503-ae8ea3ab0cbc}</UniqueIdentifier>
      <Extensions>ico;cur;bmp;dlg;rc2;rct;bin;rgs;gif;jpg;jpeg;jpe</Extensions>
    </Filter>
    <Filter Include="lib">
      <UniqueIdentifier>{5638e2ee-de9c-435e-a4f5-c445903a1d1a}</UniqueIdentifier>
    </Filter>
    <Filter Include="lib\debug">
      <UniqueIdentifier>{90efa29a-221e-4dfa-bc01-6a730303a5a2}</UniqueIdentifier>
    </Filter>
  </ItemGroup>
  <ItemGroup>
    <ClCompile Include="GlobalFun.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="KThread.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="main.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="S3P_MSSQLServer_Result.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="S3PAccount.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="S3PDB_MSSQLServer_Connection.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="S3PDBConnectionPool.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="S3PDBConVBC.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="S3PDBSocketPool.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="S3PResultVBC.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="StdAfx.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
  </ItemGroup>
  <ItemGroup>
    <ClInclude Include="AccountLoginDef.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="GlobalDTD.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="GlobalFun.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="KStdAfx.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="KThread.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="S3P_MSSQLServer_Result.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="S3PAccount.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="S3PDB_MSSQLServer_Connection.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="S3PDBConnectionPool.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="S3PDBConVBC.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="S3PDBSocketPool.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="S3PResultVBC.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="StdAfx.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="resource.h">
      <Filter>Resource Files</Filter>
    </ClInclude>
  </ItemGroup>
  <ItemGroup>
    <ResourceCompile Include="Sword3PaySys.rc">
      <Filter>Resource Files</Filter>
    </ResourceCompile>
  </ItemGroup>
  <ItemGroup>
    <CustomBuild Include="Sword3PaySys.ico">
      <Filter>Resource Files</Filter>
    </CustomBuild>
    <CustomBuild Include="Sword3PaySys2.ico">
      <Filter>Resource Files</Filter>
    </CustomBuild>
    <CustomBuild Include="msado15.tlh" />
    <CustomBuild Include="msado15.tli" />
    <CustomBuild Include="ReadMe.txt" />
  </ItemGroup>
  <ItemGroup>
    <Library Include="..\..\..\Lib\debug\engine.lib">
      <Filter>lib\debug</Filter>
    </Library>
    <Library Include="..\..\..\Lib\debug\LuaLibDll.lib">
      <Filter>lib\debug</Filter>
    </Library>
  </ItemGroup>
</Project>