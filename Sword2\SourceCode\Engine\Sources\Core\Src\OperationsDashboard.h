//---------------------------------------------------------------------------
// Sword2 Operations Dashboard System (c) 2024
//
// File:	OperationsDashboard.h
// Date:	2024.07.02
// Code:	AI Assistant
// Desc:	Intuitive web dashboard for real-time system status display
//---------------------------------------------------------------------------
#ifndef OPERATIONS_DASHBOARD_H
#define OPERATIONS_DASHBOARD_H

#include "ModernCpp.h"
#include "UnifiedLoggingSystem.h"
#include "ModernPerformanceMonitor.h"
#include "BusinessMonitor.h"
#include "AlertNotificationSystem.h"
#include <string>
#include <sstream>
#include <fstream>
#include <thread>
#include <atomic>

namespace sword2 {

// 仪表板数据类型
enum class DashboardDataType
{
    SystemMetrics,      // 系统指标
    BusinessMetrics,    // 业务指标
    AlertStatus,        // 告警状态
    LogSummary,         // 日志摘要
    HealthCheck         // 健康检查
};

// 仪表板配置
struct DashboardConfig
{
    std::string title = "Sword2 Operations Dashboard";
    std::string serverAddress = "127.0.0.1";
    int serverPort = 8080;
    std::chrono::seconds refreshInterval{5};
    bool enableRealTimeUpdates = true;
    std::string theme = "dark";
    std::vector<DashboardDataType> enabledWidgets;
    
    DashboardConfig()
    {
        // 默认启用所有组件
        enabledWidgets = {
            DashboardDataType::SystemMetrics,
            DashboardDataType::BusinessMetrics,
            DashboardDataType::AlertStatus,
            DashboardDataType::LogSummary,
            DashboardDataType::HealthCheck
        };
    }
};

// 仪表板数据聚合器
class DashboardDataAggregator
{
public:
    struct SystemStatus
    {
        double cpuUsage = 0.0;
        double memoryUsage = 0.0;
        double networkThroughput = 0.0;
        size_t activeConnections = 0;
        std::string status = "Unknown";
        std::chrono::system_clock::time_point lastUpdate;
    };
    
    struct BusinessStatus
    {
        size_t onlineUsers = 0;
        size_t activeSessions = 0;
        double transactionsPerMinute = 0.0;
        double averageResponseTime = 0.0;
        std::string status = "Unknown";
        std::chrono::system_clock::time_point lastUpdate;
    };
    
    struct AlertSummary
    {
        size_t totalActive = 0;
        size_t critical = 0;
        size_t warnings = 0;
        size_t acknowledged = 0;
        std::vector<Alert> recentAlerts;
        std::chrono::system_clock::time_point lastUpdate;
    };
    
    struct LogSummary
    {
        size_t totalLogs = 0;
        size_t errors = 0;
        size_t warnings = 0;
        size_t infos = 0;
        std::vector<std::string> recentErrors;
        std::chrono::system_clock::time_point lastUpdate;
    };
    
    SystemStatus GetSystemStatus()
    {
        SystemStatus status;
        
        // 从性能监控器获取数据
        auto cpuStats = MODERN_PERF_MONITOR().GetStatistics(MetricType::CPU_Usage);
        auto memStats = MODERN_PERF_MONITOR().GetStatistics(MetricType::Memory_Usage);
        auto netStats = MODERN_PERF_MONITOR().GetStatistics(MetricType::Network_BytesSent);
        
        status.cpuUsage = cpuStats.current;
        status.memoryUsage = memStats.current;
        status.networkThroughput = netStats.current;
        status.lastUpdate = std::chrono::system_clock::now();
        
        // 确定系统状态
        if (status.cpuUsage > 90.0 || status.memoryUsage > 90.0)
        {
            status.status = "Critical";
        }
        else if (status.cpuUsage > 70.0 || status.memoryUsage > 70.0)
        {
            status.status = "Warning";
        }
        else
        {
            status.status = "Healthy";
        }
        
        return status;
    }
    
    BusinessStatus GetBusinessStatus()
    {
        BusinessStatus status;
        
        // 从业务监控器获取数据
        status.onlineUsers = BUSINESS_MONITOR().GetOnlineUserCount();
        status.activeSessions = BUSINESS_MONITOR().GetActiveSessionCount();
        
        auto transactionStats = BUSINESS_MONITOR().GetMetricStats(BusinessMetricType::ItemTransactions);
        status.transactionsPerMinute = transactionStats.averageValue;
        
        status.lastUpdate = std::chrono::system_clock::now();
        
        // 确定业务状态
        if (status.onlineUsers > 1000)
        {
            status.status = "High Load";
        }
        else if (status.onlineUsers > 100)
        {
            status.status = "Normal";
        }
        else
        {
            status.status = "Low Activity";
        }
        
        return status;
    }
    
    AlertSummary GetAlertSummary()
    {
        AlertSummary summary;
        
        // 从告警管理器获取数据
        auto alertStats = ALERT_MANAGER().GetAlertStatistics();
        summary.totalActive = alertStats.totalActive;
        summary.critical = alertStats.criticalAlerts + alertStats.emergencyAlerts;
        summary.acknowledged = alertStats.totalAcknowledged;
        
        auto activeAlerts = ALERT_MANAGER().GetActiveAlerts();
        summary.recentAlerts = activeAlerts;
        
        summary.lastUpdate = std::chrono::system_clock::now();
        
        return summary;
    }
    
    LogSummary GetLogSummary()
    {
        LogSummary summary;
        
        // 这里应该从日志系统获取统计数据
        // 为了演示，我们提供模拟数据
        summary.totalLogs = 1000;
        summary.errors = 5;
        summary.warnings = 20;
        summary.infos = 975;
        summary.lastUpdate = std::chrono::system_clock::now();
        
        return summary;
    }
};

// HTML生成器
class DashboardHTMLGenerator
{
public:
    explicit DashboardHTMLGenerator(const DashboardConfig& config)
        : m_config(config) {}
    
    std::string GenerateHTML(const DashboardDataAggregator& aggregator)
    {
        std::ostringstream html;
        
        html << GenerateHTMLHeader();
        html << GenerateHTMLBody(aggregator);
        html << GenerateHTMLFooter();
        
        return html.str();
    }

private:
    DashboardConfig m_config;
    
    std::string GenerateHTMLHeader()
    {
        std::ostringstream html;
        html << R"(<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>)" << m_config.title << R"(</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: )" << (m_config.theme == "dark" ? "#1a1a1a" : "#f5f5f5") << R"(;
            color: )" << (m_config.theme == "dark" ? "#ffffff" : "#333333") << R"(;
        }
        .dashboard-container {
            max-width: 1200px;
            margin: 0 auto;
        }
        .dashboard-header {
            text-align: center;
            margin-bottom: 30px;
        }
        .dashboard-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
        }
        .widget {
            background-color: )" << (m_config.theme == "dark" ? "#2d2d2d" : "#ffffff") << R"(;
            border-radius: 8px;
            padding: 20px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            border: 1px solid )" << (m_config.theme == "dark" ? "#404040" : "#e0e0e0") << R"(;
        }
        .widget-title {
            font-size: 18px;
            font-weight: bold;
            margin-bottom: 15px;
            color: )" << (m_config.theme == "dark" ? "#4CAF50" : "#2196F3") << R"(;
        }
        .metric {
            display: flex;
            justify-content: space-between;
            margin-bottom: 10px;
            padding: 8px 0;
            border-bottom: 1px solid )" << (m_config.theme == "dark" ? "#404040" : "#f0f0f0") << R"(;
        }
        .metric-label {
            font-weight: 500;
        }
        .metric-value {
            font-weight: bold;
        }
        .status-healthy { color: #4CAF50; }
        .status-warning { color: #FF9800; }
        .status-critical { color: #F44336; }
        .alert-item {
            background-color: )" << (m_config.theme == "dark" ? "#3d2d2d" : "#fff3cd") << R"(;
            padding: 10px;
            margin-bottom: 8px;
            border-radius: 4px;
            border-left: 4px solid #FF9800;
        }
        .refresh-info {
            text-align: center;
            margin-top: 20px;
            font-size: 12px;
            color: )" << (m_config.theme == "dark" ? "#888888" : "#666666") << R"(;
        }
    </style>
    <script>
        function refreshDashboard() {
            location.reload();
        }
        )" << (m_config.enableRealTimeUpdates ? 
            "setInterval(refreshDashboard, " + std::to_string(m_config.refreshInterval.count() * 1000) + ");" : "") << R"(
    </script>
</head>)";
        return html.str();
    }
    
    std::string GenerateHTMLBody(const DashboardDataAggregator& aggregator)
    {
        std::ostringstream html;
        html << R"(<body>
    <div class="dashboard-container">
        <div class="dashboard-header">
            <h1>)" << m_config.title << R"(</h1>
            <p>Real-time System Monitoring Dashboard</p>
        </div>
        <div class="dashboard-grid">)";
        
        // 生成各个组件
        for (auto widgetType : m_config.enabledWidgets)
        {
            switch (widgetType)
            {
            case DashboardDataType::SystemMetrics:
                html << GenerateSystemMetricsWidget(aggregator.GetSystemStatus());
                break;
            case DashboardDataType::BusinessMetrics:
                html << GenerateBusinessMetricsWidget(aggregator.GetBusinessStatus());
                break;
            case DashboardDataType::AlertStatus:
                html << GenerateAlertStatusWidget(aggregator.GetAlertSummary());
                break;
            case DashboardDataType::LogSummary:
                html << GenerateLogSummaryWidget(aggregator.GetLogSummary());
                break;
            case DashboardDataType::HealthCheck:
                html << GenerateHealthCheckWidget();
                break;
            }
        }
        
        html << R"(        </div>
        <div class="refresh-info">
            Last updated: )" << GetCurrentTimeString() << R"(
            )" << (m_config.enableRealTimeUpdates ? 
                "| Auto-refresh every " + std::to_string(m_config.refreshInterval.count()) + " seconds" : "") << R"(
        </div>
    </div>)";
        
        return html.str();
    }
    
    std::string GenerateHTMLFooter()
    {
        return "</body>\n</html>";
    }
    
    std::string GenerateSystemMetricsWidget(const DashboardDataAggregator::SystemStatus& status)
    {
        std::ostringstream html;
        html << R"(            <div class="widget">
                <div class="widget-title">System Metrics</div>
                <div class="metric">
                    <span class="metric-label">CPU Usage:</span>
                    <span class="metric-value">)" << std::fixed << std::setprecision(1) << status.cpuUsage << R"(%</span>
                </div>
                <div class="metric">
                    <span class="metric-label">Memory Usage:</span>
                    <span class="metric-value">)" << FormatBytes(status.memoryUsage) << R"(</span>
                </div>
                <div class="metric">
                    <span class="metric-label">Network Throughput:</span>
                    <span class="metric-value">)" << FormatBytes(status.networkThroughput) << R"(/s</span>
                </div>
                <div class="metric">
                    <span class="metric-label">System Status:</span>
                    <span class="metric-value )" << GetStatusClass(status.status) << R"(">)" << status.status << R"(</span>
                </div>
            </div>)";
        return html.str();
    }
    
    std::string GenerateBusinessMetricsWidget(const DashboardDataAggregator::BusinessStatus& status)
    {
        std::ostringstream html;
        html << R"(            <div class="widget">
                <div class="widget-title">Business Metrics</div>
                <div class="metric">
                    <span class="metric-label">Online Users:</span>
                    <span class="metric-value">)" << status.onlineUsers << R"(</span>
                </div>
                <div class="metric">
                    <span class="metric-label">Active Sessions:</span>
                    <span class="metric-value">)" << status.activeSessions << R"(</span>
                </div>
                <div class="metric">
                    <span class="metric-label">Transactions/Min:</span>
                    <span class="metric-value">)" << std::fixed << std::setprecision(1) << status.transactionsPerMinute << R"(</span>
                </div>
                <div class="metric">
                    <span class="metric-label">Business Status:</span>
                    <span class="metric-value )" << GetStatusClass(status.status) << R"(">)" << status.status << R"(</span>
                </div>
            </div>)";
        return html.str();
    }
    
    std::string GenerateAlertStatusWidget(const DashboardDataAggregator::AlertSummary& summary)
    {
        std::ostringstream html;
        html << R"(            <div class="widget">
                <div class="widget-title">Alert Status</div>
                <div class="metric">
                    <span class="metric-label">Active Alerts:</span>
                    <span class="metric-value">)" << summary.totalActive << R"(</span>
                </div>
                <div class="metric">
                    <span class="metric-label">Critical:</span>
                    <span class="metric-value status-critical">)" << summary.critical << R"(</span>
                </div>
                <div class="metric">
                    <span class="metric-label">Acknowledged:</span>
                    <span class="metric-value">)" << summary.acknowledged << R"(</span>
                </div>)";
        
        if (!summary.recentAlerts.empty())
        {
            html << R"(                <div style="margin-top: 15px;">
                    <strong>Recent Alerts:</strong>)";
            
            for (size_t i = 0; i < std::min(summary.recentAlerts.size(), size_t(3)); ++i)
            {
                const auto& alert = summary.recentAlerts[i];
                html << R"(                    <div class="alert-item">
                        <strong>)" << alert.title << R"(</strong><br>
                        )" << alert.message << R"(
                    </div>)";
            }
            html << "                </div>";
        }
        
        html << "            </div>";
        return html.str();
    }
    
    std::string GenerateLogSummaryWidget(const DashboardDataAggregator::LogSummary& summary)
    {
        std::ostringstream html;
        html << R"(            <div class="widget">
                <div class="widget-title">Log Summary</div>
                <div class="metric">
                    <span class="metric-label">Total Logs:</span>
                    <span class="metric-value">)" << summary.totalLogs << R"(</span>
                </div>
                <div class="metric">
                    <span class="metric-label">Errors:</span>
                    <span class="metric-value status-critical">)" << summary.errors << R"(</span>
                </div>
                <div class="metric">
                    <span class="metric-label">Warnings:</span>
                    <span class="metric-value status-warning">)" << summary.warnings << R"(</span>
                </div>
                <div class="metric">
                    <span class="metric-label">Info:</span>
                    <span class="metric-value status-healthy">)" << summary.infos << R"(</span>
                </div>
            </div>)";
        return html.str();
    }
    
    std::string GenerateHealthCheckWidget()
    {
        std::ostringstream html;
        html << R"(            <div class="widget">
                <div class="widget-title">Health Check</div>
                <div class="metric">
                    <span class="metric-label">Database:</span>
                    <span class="metric-value status-healthy">Connected</span>
                </div>
                <div class="metric">
                    <span class="metric-label">Cache:</span>
                    <span class="metric-value status-healthy">Online</span>
                </div>
                <div class="metric">
                    <span class="metric-label">External APIs:</span>
                    <span class="metric-value status-healthy">Available</span>
                </div>
                <div class="metric">
                    <span class="metric-label">Overall Health:</span>
                    <span class="metric-value status-healthy">Healthy</span>
                </div>
            </div>)";
        return html.str();
    }
    
    std::string GetStatusClass(const std::string& status)
    {
        if (status == "Healthy" || status == "Normal" || status == "Connected" || status == "Online" || status == "Available")
            return "status-healthy";
        else if (status == "Warning" || status == "High Load")
            return "status-warning";
        else if (status == "Critical" || status == "Error")
            return "status-critical";
        else
            return "";
    }
    
    std::string FormatBytes(double bytes)
    {
        const char* units[] = {"B", "KB", "MB", "GB", "TB"};
        int unitIndex = 0;
        
        while (bytes >= 1024.0 && unitIndex < 4)
        {
            bytes /= 1024.0;
            unitIndex++;
        }
        
        std::ostringstream oss;
        oss << std::fixed << std::setprecision(1) << bytes << " " << units[unitIndex];
        return oss.str();
    }
    
    std::string GetCurrentTimeString()
    {
        auto now = std::chrono::system_clock::now();
        auto time_t = std::chrono::system_clock::to_time_t(now);
        auto tm = *std::localtime(&time_t);
        
        std::ostringstream oss;
        oss << std::put_time(&tm, "%Y-%m-%d %H:%M:%S");
        return oss.str();
    }
};

// 运维仪表板服务器
class OperationsDashboard : public Singleton<OperationsDashboard>
{
public:
    OperationsDashboard() : m_running(false) {}
    
    ~OperationsDashboard()
    {
        Stop();
    }
    
    bool Start(const DashboardConfig& config = DashboardConfig{})
    {
        if (m_running) return true;
        
        m_config = config;
        m_running = true;
        m_serverThread = std::thread(&OperationsDashboard::ServerLoop, this);
        
        LOG_INFO("DASHBOARD", "Operations dashboard started on " + 
                 m_config.serverAddress + ":" + std::to_string(m_config.serverPort));
        return true;
    }
    
    void Stop()
    {
        if (!m_running) return;
        
        m_running = false;
        if (m_serverThread.joinable())
        {
            m_serverThread.join();
        }
        
        LOG_INFO("DASHBOARD", "Operations dashboard stopped");
    }
    
    void SetConfig(const DashboardConfig& config)
    {
        m_config = config;
    }
    
    std::string GenerateDashboardHTML()
    {
        DashboardDataAggregator aggregator;
        DashboardHTMLGenerator generator(m_config);
        return generator.GenerateHTML(aggregator);
    }
    
    void SaveDashboardToFile(const std::string& filename)
    {
        std::string html = GenerateDashboardHTML();
        std::ofstream file(filename);
        if (file.is_open())
        {
            file << html;
            file.close();
            LOG_INFO("DASHBOARD", "Dashboard saved to file: " + filename);
        }
        else
        {
            LOG_ERROR("DASHBOARD", "Failed to save dashboard to file: " + filename);
        }
    }

private:
    std::atomic<bool> m_running;
    std::thread m_serverThread;
    DashboardConfig m_config;
    
    void ServerLoop()
    {
        // 这里应该实现一个简单的HTTP服务器
        // 为了演示，我们定期生成HTML文件
        while (m_running)
        {
            try
            {
                SaveDashboardToFile("dashboard.html");
                std::this_thread::sleep_for(m_config.refreshInterval);
            }
            catch (const std::exception& e)
            {
                LOG_ERROR("DASHBOARD", std::string("Error in server loop: ") + e.what());
            }
        }
    }
};

} // namespace sword2

// 全局运维仪表板访问
#define OPERATIONS_DASHBOARD() sword2::OperationsDashboard::getInstance()

// 便捷宏定义
#define START_DASHBOARD(config) OPERATIONS_DASHBOARD().Start(config)
#define STOP_DASHBOARD() OPERATIONS_DASHBOARD().Stop()
#define SAVE_DASHBOARD(filename) OPERATIONS_DASHBOARD().SaveDashboardToFile(filename)

#endif // OPERATIONS_DASHBOARD_H
