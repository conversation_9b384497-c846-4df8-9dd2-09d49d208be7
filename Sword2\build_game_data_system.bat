@echo off
REM Sword2 Game Data System Build Script
REM This script builds and tests the game data system

setlocal enabledelayedexpansion

echo ========================================
echo   Sword2 Game Data System Builder
echo ========================================
echo.

REM 设置变量
set "SOURCE_DIR=%~dp0SourceCode\Engine\Sources\Core"
set "BUILD_DIR=%~dp0build"
set "CMAKE_BUILD_TYPE=Release"

REM 检查参数
if "%1"=="debug" (
    set "CMAKE_BUILD_TYPE=Debug"
    echo Build type: Debug
) else (
    echo Build type: Release
)

if "%1"=="clean" (
    echo Cleaning build directory...
    if exist "%BUILD_DIR%" (
        rmdir /s /q "%BUILD_DIR%"
        echo Build directory cleaned.
    )
    goto :end
)

echo.
echo Source directory: %SOURCE_DIR%
echo Build directory:  %BUILD_DIR%
echo.

REM 检查CMake
cmake --version >nul 2>&1
if errorlevel 1 (
    echo Error: CMake not found. Please install CMake and add it to PATH.
    goto :error
)

REM 检查源码目录
if not exist "%SOURCE_DIR%" (
    echo Error: Source directory not found: %SOURCE_DIR%
    goto :error
)

REM 创建构建目录
if not exist "%BUILD_DIR%" (
    echo Creating build directory...
    mkdir "%BUILD_DIR%"
)

REM 进入构建目录
cd /d "%BUILD_DIR%"

echo ========================================
echo   Configuring with CMake
echo ========================================

REM 配置项目
cmake "%SOURCE_DIR%" ^
    -DCMAKE_BUILD_TYPE=%CMAKE_BUILD_TYPE% ^
    -DCMAKE_INSTALL_PREFIX="%~dp0install" ^
    -DENABLE_PROFILING=OFF ^
    -DENABLE_MEMORY_CHECK=OFF

if errorlevel 1 (
    echo Error: CMake configuration failed.
    goto :error
)

echo.
echo ========================================
echo   Building Project
echo ========================================

REM 构建项目
cmake --build . --config %CMAKE_BUILD_TYPE%

if errorlevel 1 (
    echo Error: Build failed.
    goto :error
)

echo.
echo ========================================
echo   Running Tests
echo ========================================

REM 运行测试
cmake --build . --target run_tests

if errorlevel 1 (
    echo Warning: Some tests failed.
    set "TEST_FAILED=1"
) else (
    echo All tests passed successfully!
)

echo.
echo ========================================
echo   Build Summary
echo ========================================

echo Build completed successfully!
echo.
echo Generated files:
if exist "bin\GameDataTest.exe" (
    echo   ✓ bin\GameDataTest.exe - Test executable
) else (
    echo   ✗ bin\GameDataTest.exe - Missing
)

if exist "lib\GameDataSystem.lib" (
    echo   ✓ lib\GameDataSystem.lib - Static library
) else (
    echo   ✗ lib\GameDataSystem.lib - Missing
)

echo.
echo To run the demo:
echo   cd "%BUILD_DIR%"
echo   bin\GameDataTest.exe
echo.

if defined TEST_FAILED (
    echo Warning: Some tests failed. Check the output above.
    goto :end
)

echo ========================================
echo   Running Demo
echo ========================================

REM 运行演示
if exist "bin\GameDataTest.exe" (
    echo Running game data system demo...
    echo.
    bin\GameDataTest.exe
    echo.
    echo Demo completed.
) else (
    echo Demo executable not found.
)

goto :end

:error
echo.
echo ========================================
echo   Build Failed
echo ========================================
echo.
echo Please check the error messages above and fix any issues.
echo.
echo Common solutions:
echo   1. Make sure CMake is installed and in PATH
echo   2. Check that all source files exist
echo   3. Ensure you have a C++ compiler installed
echo   4. Try running 'build_game_data_system.bat clean' first
echo.
exit /b 1

:end
echo.
echo Build script completed.
pause
