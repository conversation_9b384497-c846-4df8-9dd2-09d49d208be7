//---------------------------------------------------------------------------
// Sword2 Skill System (c) 2024
//
// File:	SkillSystem.h
// Date:	2024.07.02
// Code:	AI Assistant
// Desc:	Comprehensive skill system for Sword2, compatible with existing code
//---------------------------------------------------------------------------
#ifndef SKILL_SYSTEM_H
#define SKILL_SYSTEM_H

#include "ModernCpp.h"
#include "UnifiedLoggingSystem.h"
#include "PlayerSystem.h"
#include <string>
#include <unordered_map>
#include <vector>
#include <memory>
#include <chrono>
#include <atomic>
#include <mutex>
#include <functional>

namespace sword2 {

// 技能系列（五行）
enum class SkillSeries : uint8_t
{
    None = 0,           // 无系列
    Metal = 1,          // 金系
    Wood = 2,           // 木系
    Water = 3,          // 水系
    Fire = 4,           // 火系
    Earth = 5,          // 土系
    Special = 6         // 特殊系列
};

// 技能类型
enum class SkillType : uint8_t
{
    Passive = 0,        // 被动技能
    Active,             // 主动技能
    Toggle,             // 开关技能
    Aura,               // 光环技能
    Weapon,             // 武器技能
    Magic,              // 法术技能
    Support,            // 辅助技能
    Ultimate            // 终极技能
};

// 技能风格（对应原有的eSkillStyle）
enum class SkillStyle : uint8_t
{
    Melee = 0,          // 近战技能
    Ranged,             // 远程技能
    Magic,              // 法术技能
    Buff,               // 增益技能
    Debuff,             // 减益技能
    Heal,               // 治疗技能
    Summon,             // 召唤技能
    Teleport,           // 传送技能
    Transform,          // 变身技能
    Special             // 特殊技能
};

// 技能状态
enum class SkillStatus : uint8_t
{
    Available = 0,      // 可用
    Cooldown,           // 冷却中
    Casting,            // 施放中
    Disabled,           // 禁用
    Locked              // 锁定
};

// 技能目标类型
enum class SkillTargetType : uint8_t
{
    None = 0,           // 无目标
    Self,               // 自身
    SingleEnemy,        // 单个敌人
    SingleAlly,         // 单个盟友
    MultipleEnemies,    // 多个敌人
    MultipleAllies,     // 多个盟友
    Area,               // 区域
    Direction           // 方向
};

// 技能消耗类型
enum class SkillCostType : uint8_t
{
    None = 0,           // 无消耗
    Mana,               // 内力
    Health,             // 生命值
    Stamina,            // 体力
    Item,               // 物品
    Money,              // 金钱
    Experience          // 经验
};

// 技能效果类型
enum class SkillEffectType : uint8_t
{
    Damage = 0,         // 伤害
    Heal,               // 治疗
    Buff,               // 增益
    Debuff,             // 减益
    Summon,             // 召唤
    Teleport,           // 传送
    Transform,          // 变身
    Special             // 特殊效果
};

// 技能学习条件
struct SkillRequirement
{
    uint32_t requiredLevel = 1;         // 需求等级
    uint32_t requiredExp = 0;           // 需求经验
    uint32_t requiredMoney = 0;         // 需求金钱
    uint32_t requiredReputation = 0;    // 需求声望
    PlayerSeries requiredSeries = PlayerSeries::None; // 需求门派
    std::vector<uint32_t> prerequisiteSkills; // 前置技能
    std::vector<std::pair<uint32_t, uint32_t>> requiredItems; // 需求物品(ID, 数量)

    SkillRequirement() = default;

    // 检查是否满足条件
    bool CheckRequirement(const Player& player) const
    {
        // 检查等级
        if (player.level < requiredLevel)
            return false;

        // 检查门派
        if (requiredSeries != PlayerSeries::None && player.series != requiredSeries)
            return false;

        // 检查金钱
        if (player.money < requiredMoney)
            return false;

        // 这里应该检查其他条件，如前置技能、物品等
        // 暂时简化处理

        return true;
    }
};

// 技能消耗
struct SkillCost
{
    SkillCostType type = SkillCostType::None;
    uint32_t amount = 0;
    uint32_t itemId = 0;            // 物品ID（当type为Item时）
    
    SkillCost() = default;
    SkillCost(SkillCostType costType, uint32_t costAmount, uint32_t item = 0)
        : type(costType), amount(costAmount), itemId(item) {}
};

// 技能效果
struct SkillEffect
{
    SkillEffectType type = SkillEffectType::Damage;
    uint32_t value = 0;             // 效果值
    uint32_t duration = 0;          // 持续时间(毫秒)
    uint32_t probability = 100;     // 触发概率(百分比)
    std::string description;        // 效果描述
    
    SkillEffect() = default;
    SkillEffect(SkillEffectType effectType, uint32_t effectValue, uint32_t effectDuration = 0, uint32_t prob = 100)
        : type(effectType), value(effectValue), duration(effectDuration), probability(prob) {}
};

// 技能等级数据
struct SkillLevelData
{
    uint32_t level = 1;             // 技能等级
    uint32_t maxLevel = 20;         // 最大等级
    uint32_t experience = 0;        // 当前经验
    uint32_t nextLevelExp = 100;    // 升级所需经验
    
    // 等级相关属性
    uint32_t damage = 0;            // 伤害值
    uint32_t range = 100;           // 作用范围
    uint32_t cooldown = 1000;       // 冷却时间(毫秒)
    uint32_t castTime = 0;          // 施法时间(毫秒)
    
    std::vector<SkillCost> costs;   // 消耗列表
    std::vector<SkillEffect> effects; // 效果列表
    
    SkillLevelData() = default;
    
    // 计算等级加成
    uint32_t GetLevelBonus(uint32_t baseValue, double growthRate) const
    {
        return static_cast<uint32_t>(baseValue * (1.0 + (level - 1) * growthRate));
    }
};

// 技能模板
class SkillTemplate
{
public:
    uint32_t skillId = 0;           // 技能ID
    std::string name;               // 技能名称
    std::string description;        // 技能描述
    std::string iconPath;           // 图标路径
    std::string scriptPath;         // 脚本路径
    
    SkillType type = SkillType::Active;
    SkillStyle style = SkillStyle::Melee;
    SkillSeries series = SkillSeries::None;
    SkillTargetType targetType = SkillTargetType::SingleEnemy;
    
    uint32_t maxLevel = 20;         // 最大等级
    bool isBaseSkill = false;       // 是否为基础技能
    bool canUpgrade = true;         // 是否可升级
    
    SkillRequirement requirement;   // 学习条件
    std::vector<SkillLevelData> levelData; // 各等级数据
    
    SkillTemplate() = default;
    SkillTemplate(uint32_t id, const std::string& skillName, SkillType skillType)
        : skillId(id), name(skillName), type(skillType) {}
    
    // 获取指定等级的数据
    const SkillLevelData* GetLevelData(uint32_t level) const
    {
        if (level == 0 || level > levelData.size()) return nullptr;
        return &levelData[level - 1];
    }
    
    // 检查是否可以学习
    bool CanLearn(const Player& player) const
    {
        return requirement.CheckRequirement(player);
    }
    
    // 获取技能系列描述
    std::string GetSeriesDescription() const
    {
        switch (series)
        {
        case SkillSeries::Metal: return "金系";
        case SkillSeries::Wood: return "木系";
        case SkillSeries::Water: return "水系";
        case SkillSeries::Fire: return "火系";
        case SkillSeries::Earth: return "土系";
        case SkillSeries::Special: return "特殊";
        default: return "无系列";
        }
    }
    
    // 获取技能类型描述
    std::string GetTypeDescription() const
    {
        switch (type)
        {
        case SkillType::Passive: return "被动技能";
        case SkillType::Active: return "主动技能";
        case SkillType::Toggle: return "开关技能";
        case SkillType::Aura: return "光环技能";
        case SkillType::Weapon: return "武器技能";
        case SkillType::Magic: return "法术技能";
        case SkillType::Support: return "辅助技能";
        case SkillType::Ultimate: return "终极技能";
        default: return "未知类型";
        }
    }
};

// 玩家技能实例
class PlayerSkill
{
public:
    uint32_t skillId = 0;           // 技能ID
    uint32_t level = 1;             // 当前等级
    uint32_t experience = 0;        // 当前经验
    uint32_t addLevel = 0;          // 附加等级（装备、状态等加成）
    
    SkillStatus status = SkillStatus::Available;
    std::chrono::system_clock::time_point lastCastTime; // 最后施放时间
    std::chrono::system_clock::time_point cooldownEndTime; // 冷却结束时间
    
    bool isTempSkill = false;       // 是否为临时技能
    uint32_t maxTimes = 0;          // 最大使用次数（0表示无限制）
    uint32_t remainTimes = 0;       // 剩余使用次数
    
    PlayerSkill() = default;
    PlayerSkill(uint32_t id, uint32_t skillLevel = 1)
        : skillId(id), level(skillLevel)
    {
        lastCastTime = cooldownEndTime = std::chrono::system_clock::now();
    }
    
    // 获取有效等级（包含附加等级）
    uint32_t GetEffectiveLevel() const
    {
        return level + addLevel;
    }
    
    // 检查是否在冷却中
    bool IsInCooldown() const
    {
        return std::chrono::system_clock::now() < cooldownEndTime;
    }
    
    // 获取剩余冷却时间（毫秒）
    uint32_t GetRemainingCooldown() const
    {
        if (!IsInCooldown()) return 0;
        
        auto now = std::chrono::system_clock::now();
        auto remaining = std::chrono::duration_cast<std::chrono::milliseconds>(cooldownEndTime - now);
        return static_cast<uint32_t>(remaining.count());
    }
    
    // 设置冷却时间
    void SetCooldown(uint32_t cooldownMs)
    {
        auto now = std::chrono::system_clock::now();
        lastCastTime = now;
        cooldownEndTime = now + std::chrono::milliseconds(cooldownMs);
        status = SkillStatus::Cooldown;
    }
    
    // 更新状态
    void Update()
    {
        if (status == SkillStatus::Cooldown && !IsInCooldown())
        {
            status = SkillStatus::Available;
        }
    }
    
    // 检查是否可以使用
    bool CanUse() const
    {
        if (status != SkillStatus::Available) return false;
        if (IsInCooldown()) return false;
        if (maxTimes > 0 && remainTimes == 0) return false;
        return true;
    }
    
    // 使用技能
    bool Use()
    {
        if (!CanUse()) return false;
        
        if (maxTimes > 0 && remainTimes > 0)
        {
            remainTimes--;
        }
        
        return true;
    }
    
    // 升级技能
    bool LevelUp(uint32_t expGained = 0)
    {
        experience += expGained;
        // 这里应该检查是否达到升级条件
        // 暂时简化处理
        return true;
    }
};

// 技能树节点
struct SkillTreeNode
{
    uint32_t skillId = 0;           // 技能ID
    uint32_t tier = 1;              // 层级
    uint32_t position = 0;          // 在层级中的位置
    std::vector<uint32_t> prerequisites; // 前置技能
    std::vector<uint32_t> unlocks;  // 解锁的技能
    bool isKeySkill = false;        // 是否为关键技能
    
    SkillTreeNode() = default;
    SkillTreeNode(uint32_t id, uint32_t skillTier, uint32_t pos)
        : skillId(id), tier(skillTier), position(pos) {}
};

// 门派技能树
class FactionSkillTree
{
public:
    PlayerSeries faction = PlayerSeries::None;
    std::string factionName;
    std::unordered_map<uint32_t, SkillTreeNode> nodes; // 技能节点
    std::unordered_map<uint32_t, std::vector<uint32_t>> tierSkills; // 每层的技能
    
    FactionSkillTree() = default;
    FactionSkillTree(PlayerSeries fac, const std::string& name)
        : faction(fac), factionName(name) {}
    
    // 添加技能节点
    void AddSkillNode(const SkillTreeNode& node)
    {
        nodes[node.skillId] = node;
        tierSkills[node.tier].push_back(node.skillId);
    }
    
    // 获取技能节点
    const SkillTreeNode* GetSkillNode(uint32_t skillId) const
    {
        auto it = nodes.find(skillId);
        return (it != nodes.end()) ? &it->second : nullptr;
    }
    
    // 检查技能是否可学习（前置条件）
    bool CanLearnSkill(uint32_t skillId, const std::unordered_map<uint32_t, PlayerSkill>& playerSkills) const
    {
        const auto* node = GetSkillNode(skillId);
        if (!node) return false;
        
        // 检查前置技能
        for (uint32_t prereqId : node->prerequisites)
        {
            auto it = playerSkills.find(prereqId);
            if (it == playerSkills.end() || it->second.level == 0)
            {
                return false;
            }
        }
        
        return true;
    }
    
    // 获取指定层级的技能
    std::vector<uint32_t> GetTierSkills(uint32_t tier) const
    {
        auto it = tierSkills.find(tier);
        return (it != tierSkills.end()) ? it->second : std::vector<uint32_t>();
    }
    
    // 获取最大层级
    uint32_t GetMaxTier() const
    {
        uint32_t maxTier = 0;
        for (const auto& [tier, skills] : tierSkills)
        {
            maxTier = std::max(maxTier, tier);
        }
        return maxTier;
    }
};

} // namespace sword2

#endif // SKILL_SYSTEM_H
