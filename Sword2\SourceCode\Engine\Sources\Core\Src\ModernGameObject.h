//---------------------------------------------------------------------------
// Sword2 Modern Game Object System (c) 2024
//
// File:	ModernGameObject.h
// Date:	2024.07.02
// Code:	AI Assistant
// Desc:	Modern C++ game object system with component architecture
//---------------------------------------------------------------------------
#ifndef MODERN_GAME_OBJECT_H
#define MODERN_GAME_OBJECT_H

#include "ModernCpp.h"
#include <unordered_map>
#include <typeindex>
#include <vector>
#include <string>
#include <memory>

namespace sword2 {

// 前向声明
class GameObject;
class Component;
class ComponentSystem;

// 组件基类
class Component
{
public:
    Component() = default;
    virtual ~Component() = default;
    
    // 禁止拷贝，允许移动
    Component(const Component&) = delete;
    Component& operator=(const Component&) = delete;
    Component(Component&&) = default;
    Component& operator=(Component&&) = default;
    
    // 组件生命周期
    virtual void OnAttach(GameObject* owner) { m_owner = owner; }
    virtual void OnDetach() { m_owner = nullptr; }
    virtual void OnUpdate(float deltaTime) {}
    virtual void OnRender() {}
    virtual void OnDestroy() {}
    
    // 获取所属游戏对象
    GameObject* GetOwner() const { return m_owner; }
    
    // 组件类型信息
    virtual std::type_index GetTypeIndex() const = 0;

protected:
    GameObject* m_owner = nullptr;
};

// 组件模板基类
template<typename T>
class ComponentBase : public Component
{
public:
    std::type_index GetTypeIndex() const override
    {
        return std::type_index(typeid(T));
    }
};

// 变换组件
class TransformComponent : public ComponentBase<TransformComponent>
{
public:
    struct Vector3
    {
        float x = 0.0f, y = 0.0f, z = 0.0f;
        
        Vector3() = default;
        Vector3(float x_, float y_, float z_) : x(x_), y(y_), z(z_) {}
        
        Vector3 operator+(const Vector3& other) const
        {
            return Vector3(x + other.x, y + other.y, z + other.z);
        }
        
        Vector3& operator+=(const Vector3& other)
        {
            x += other.x; y += other.y; z += other.z;
            return *this;
        }
    };
    
    Vector3 position;
    Vector3 rotation;
    Vector3 scale{1.0f, 1.0f, 1.0f};
    
    // 变换操作
    void Translate(const Vector3& delta) { position += delta; }
    void SetPosition(const Vector3& pos) { position = pos; }
    void SetRotation(const Vector3& rot) { rotation = rot; }
    void SetScale(const Vector3& scl) { scale = scl; }
};

// 渲染组件
class RenderComponent : public ComponentBase<RenderComponent>
{
public:
    std::string meshName;
    std::string textureName;
    bool visible = true;
    float alpha = 1.0f;
    
    void OnRender() override;
    void SetVisible(bool vis) { visible = vis; }
    void SetAlpha(float a) { alpha = std::clamp(a, 0.0f, 1.0f); }
};

// 物理组件
class PhysicsComponent : public ComponentBase<PhysicsComponent>
{
public:
    TransformComponent::Vector3 velocity;
    TransformComponent::Vector3 acceleration;
    float mass = 1.0f;
    bool isStatic = false;
    
    void OnUpdate(float deltaTime) override;
    void ApplyForce(const TransformComponent::Vector3& force);
    void SetVelocity(const TransformComponent::Vector3& vel) { velocity = vel; }
};

// 游戏对象类
class GameObject
{
public:
    explicit GameObject(const std::string& name = "GameObject");
    ~GameObject();
    
    // 禁止拷贝，允许移动
    GameObject(const GameObject&) = delete;
    GameObject& operator=(const GameObject&) = delete;
    GameObject(GameObject&&) = default;
    GameObject& operator=(GameObject&&) = default;
    
    // 基本属性
    const std::string& GetName() const { return m_name; }
    void SetName(const std::string& name) { m_name = name; }
    
    uint32_t GetId() const { return m_id; }
    bool IsActive() const { return m_active; }
    void SetActive(bool active) { m_active = active; }
    
    // 组件管理
    template<typename T, typename... Args>
    T* AddComponent(Args&&... args)
    {
        static_assert(std::is_base_of_v<Component, T>, "T must derive from Component");
        
        auto component = std::make_unique<T>(std::forward<Args>(args)...);
        T* ptr = component.get();
        
        component->OnAttach(this);
        m_components[std::type_index(typeid(T))] = std::move(component);
        
        return ptr;
    }
    
    template<typename T>
    T* GetComponent()
    {
        static_assert(std::is_base_of_v<Component, T>, "T must derive from Component");
        
        auto it = m_components.find(std::type_index(typeid(T)));
        if (it != m_components.end())
        {
            return static_cast<T*>(it->second.get());
        }
        return nullptr;
    }
    
    template<typename T>
    bool HasComponent() const
    {
        return m_components.find(std::type_index(typeid(T))) != m_components.end();
    }
    
    template<typename T>
    bool RemoveComponent()
    {
        auto it = m_components.find(std::type_index(typeid(T)));
        if (it != m_components.end())
        {
            it->second->OnDetach();
            it->second->OnDestroy();
            m_components.erase(it);
            return true;
        }
        return false;
    }
    
    // 生命周期
    void Update(float deltaTime);
    void Render();
    void Destroy();
    
    // 层次结构
    void SetParent(GameObject* parent);
    GameObject* GetParent() const { return m_parent; }
    const std::vector<std::unique_ptr<GameObject>>& GetChildren() const { return m_children; }
    
    // 便捷的组件访问
    TransformComponent* Transform() { return GetComponent<TransformComponent>(); }
    RenderComponent* Renderer() { return GetComponent<RenderComponent>(); }
    PhysicsComponent* Physics() { return GetComponent<PhysicsComponent>(); }

private:
    std::string m_name;
    uint32_t m_id;
    bool m_active = true;
    
    // 组件存储
    std::unordered_map<std::type_index, std::unique_ptr<Component>> m_components;
    
    // 层次结构
    GameObject* m_parent = nullptr;
    std::vector<std::unique_ptr<GameObject>> m_children;
    
    // 静态ID生成器
    static uint32_t s_nextId;
};

// 游戏对象管理器
class GameObjectManager : public Singleton<GameObjectManager>
{
public:
    // 对象创建和销毁
    template<typename... Args>
    GameObject* CreateGameObject(const std::string& name, Args&&... args)
    {
        auto obj = std::make_unique<GameObject>(name, std::forward<Args>(args)...);
        GameObject* ptr = obj.get();
        
        m_gameObjects[ptr->GetId()] = std::move(obj);
        m_namedObjects[name].push_back(ptr);
        
        return ptr;
    }
    
    void DestroyGameObject(uint32_t id);
    void DestroyGameObject(GameObject* obj);
    
    // 对象查找
    GameObject* FindById(uint32_t id);
    GameObject* FindByName(const std::string& name);
    std::vector<GameObject*> FindAllByName(const std::string& name);
    
    template<typename T>
    std::vector<GameObject*> FindObjectsWithComponent()
    {
        std::vector<GameObject*> result;
        for (const auto& pair : m_gameObjects)
        {
            if (pair.second->HasComponent<T>())
            {
                result.push_back(pair.second.get());
            }
        }
        return result;
    }
    
    // 批量操作
    void UpdateAll(float deltaTime);
    void RenderAll();
    void DestroyAll();
    
    // 统计信息
    size_t GetObjectCount() const { return m_gameObjects.size(); }
    void GetStats(size_t& totalObjects, size_t& activeObjects, size_t& totalComponents);

private:
    std::unordered_map<uint32_t, std::unique_ptr<GameObject>> m_gameObjects;
    std::unordered_map<std::string, std::vector<GameObject*>> m_namedObjects;
};

// 组件系统基类
class ComponentSystem
{
public:
    ComponentSystem() = default;
    virtual ~ComponentSystem() = default;
    
    virtual void Update(float deltaTime) = 0;
    virtual void Render() {}
    virtual void Initialize() {}
    virtual void Shutdown() {}
    
    // 系统优先级
    virtual int GetPriority() const { return 0; }
};

// 系统管理器
class SystemManager : public Singleton<SystemManager>
{
public:
    template<typename T, typename... Args>
    T* AddSystem(Args&&... args)
    {
        static_assert(std::is_base_of_v<ComponentSystem, T>, "T must derive from ComponentSystem");
        
        auto system = std::make_unique<T>(std::forward<Args>(args)...);
        T* ptr = system.get();
        
        system->Initialize();
        m_systems.push_back(std::move(system));
        
        // 按优先级排序
        std::sort(m_systems.begin(), m_systems.end(),
            [](const auto& a, const auto& b) {
                return a->GetPriority() < b->GetPriority();
            });
        
        return ptr;
    }
    
    void UpdateSystems(float deltaTime);
    void RenderSystems();
    void ShutdownSystems();

private:
    std::vector<std::unique_ptr<ComponentSystem>> m_systems;
};

} // namespace sword2

// 全局管理器访问
#define GAME_OBJECTS() sword2::GameObjectManager::getInstance()
#define SYSTEMS() sword2::SystemManager::getInstance()

// 便捷宏
#define CREATE_GAME_OBJECT(name) GAME_OBJECTS().CreateGameObject(name)
#define FIND_GAME_OBJECT(name) GAME_OBJECTS().FindByName(name)
#define ADD_SYSTEM(type, ...) SYSTEMS().AddSystem<type>(__VA_ARGS__)

#endif // MODERN_GAME_OBJECT_H
