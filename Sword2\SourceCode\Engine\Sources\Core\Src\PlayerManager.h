//---------------------------------------------------------------------------
// Sword2 Player Manager (c) 2024
//
// File:	PlayerManager.h
// Date:	2024.07.02
// Code:	AI Assistant
// Desc:	Comprehensive player management and session handling system
//---------------------------------------------------------------------------
#ifndef PLAYER_MANAGER_H
#define PLAYER_MANAGER_H

#include "PlayerSystem.h"
#include "UnifiedLoggingSystem.h"
#include "BusinessMonitor.h"
#include <unordered_map>
#include <unordered_set>
#include <memory>
#include <mutex>
#include <shared_mutex>
#include <thread>
#include <atomic>
#include <queue>
#include <functional>

namespace sword2 {

// 玩家会话信息
struct PlayerSession
{
    uint32_t sessionId = 0;         // 会话ID
    uint32_t playerId = 0;          // 玩家ID
    std::string ipAddress;          // IP地址
    std::chrono::system_clock::time_point loginTime;    // 登录时间
    std::chrono::system_clock::time_point lastActivity; // 最后活动时间
    uint32_t connectionId = 0;      // 连接ID
    bool isActive = true;           // 是否活跃
    
    PlayerSession() = default;
    PlayerSession(uint32_t sessId, uint32_t pId, const std::string& ip, uint32_t connId)
        : sessionId(sessId), playerId(pId), ipAddress(ip), connectionId(connId)
    {
        loginTime = lastActivity = std::chrono::system_clock::now();
    }
    
    // 更新活动时间
    void UpdateActivity()
    {
        lastActivity = std::chrono::system_clock::now();
    }
    
    // 获取会话持续时间
    std::chrono::seconds GetSessionDuration() const
    {
        auto now = std::chrono::system_clock::now();
        return std::chrono::duration_cast<std::chrono::seconds>(now - loginTime);
    }
    
    // 检查是否超时
    bool IsTimeout(std::chrono::seconds timeoutDuration) const
    {
        auto now = std::chrono::system_clock::now();
        auto inactiveTime = std::chrono::duration_cast<std::chrono::seconds>(now - lastActivity);
        return inactiveTime >= timeoutDuration;
    }
};

// 玩家创建参数
struct PlayerCreationParams
{
    std::string playerName;         // 玩家名称
    std::string accountName;        // 账号名称
    PlayerGender gender = PlayerGender::Male; // 性别
    PlayerSeries series = PlayerSeries::None; // 门派
    uint32_t startMapId = 100;      // 起始地图
    int32_t startX = 0;             // 起始X坐标
    int32_t startY = 0;             // 起始Y坐标
    
    PlayerCreationParams() = default;
    PlayerCreationParams(const std::string& name, const std::string& account)
        : playerName(name), accountName(account) {}
};

// 玩家登录结果
enum class LoginResult : uint8_t
{
    Success = 0,                    // 成功
    InvalidCredentials,             // 无效凭据
    PlayerNotFound,                 // 玩家不存在
    AlreadyOnline,                  // 已经在线
    ServerFull,                     // 服务器满员
    Banned,                         // 被封禁
    MaintenanceMode,                // 维护模式
    NetworkError                    // 网络错误
};

// 玩家管理器
class PlayerManager : public Singleton<PlayerManager>
{
public:
    PlayerManager()
        : m_running(false), m_nextPlayerId(1), m_nextSessionId(1),
          m_maxOnlinePlayers(1000), m_sessionTimeout(std::chrono::minutes(30)) {}
    
    ~PlayerManager()
    {
        Stop();
    }
    
    // 启动玩家管理器
    bool Start()
    {
        if (m_running) return true;
        
        m_running = true;
        m_cleanupThread = std::thread(&PlayerManager::CleanupLoop, this);
        
        LOG_INFO("PLAYER_MGR", "Player manager started");
        return true;
    }
    
    // 停止玩家管理器
    void Stop()
    {
        if (!m_running) return;
        
        m_running = false;
        if (m_cleanupThread.joinable())
        {
            m_cleanupThread.join();
        }
        
        // 清理所有在线玩家
        {
            std::unique_lock<std::shared_mutex> lock(m_playersMutex);
            for (auto& [playerId, player] : m_onlinePlayers)
            {
                player->status = PlayerStatus::Offline;
                player->lastLogoutTime = std::chrono::system_clock::now();
            }
            m_onlinePlayers.clear();
        }
        
        {
            std::lock_guard<std::mutex> lock(m_sessionsMutex);
            m_activeSessions.clear();
        }
        
        LOG_INFO("PLAYER_MGR", "Player manager stopped");
    }
    
    // 创建新玩家
    uint32_t CreatePlayer(const PlayerCreationParams& params)
    {
        // 检查玩家名是否已存在
        if (IsPlayerNameExists(params.playerName))
        {
            LOG_WARNING("PLAYER_MGR", "Player name already exists: " + params.playerName);
            return 0;
        }
        
        // 生成新的玩家ID
        uint32_t playerId = m_nextPlayerId++;
        
        // 创建玩家对象
        auto player = std::make_shared<Player>(playerId, params.playerName, params.accountName);
        player->gender = params.gender;
        player->series = params.series;
        player->SetPosition(params.startMapId, params.startX, params.startY);
        
        // 存储玩家数据
        {
            std::lock_guard<std::mutex> lock(m_allPlayersMutex);
            m_allPlayers[playerId] = player;
            m_playerNameToId[params.playerName] = playerId;
        }
        
        LOG_INFO("PLAYER_MGR", "Created new player: " + params.playerName + " (ID: " + std::to_string(playerId) + ")");
        
        // 记录业务指标
        RECORD_USER_REGISTRATION(params.accountName);
        
        return playerId;
    }
    
    // 玩家登录
    LoginResult PlayerLogin(uint32_t playerId, const std::string& ipAddress, uint32_t connectionId, uint32_t& sessionId)
    {
        // 检查服务器是否满员
        if (GetOnlinePlayerCount() >= m_maxOnlinePlayers)
        {
            LOG_WARNING("PLAYER_MGR", "Server full, rejecting login for player " + std::to_string(playerId));
            return LoginResult::ServerFull;
        }
        
        // 获取玩家数据
        std::shared_ptr<Player> player;
        {
            std::lock_guard<std::mutex> lock(m_allPlayersMutex);
            auto it = m_allPlayers.find(playerId);
            if (it == m_allPlayers.end())
            {
                LOG_WARNING("PLAYER_MGR", "Player not found: " + std::to_string(playerId));
                return LoginResult::PlayerNotFound;
            }
            player = it->second;
        }
        
        // 检查玩家是否被封禁
        if (player->status == PlayerStatus::Banned)
        {
            LOG_WARNING("PLAYER_MGR", "Banned player attempted login: " + player->playerName);
            return LoginResult::Banned;
        }
        
        // 检查是否已经在线
        if (IsPlayerOnline(playerId))
        {
            LOG_WARNING("PLAYER_MGR", "Player already online: " + player->playerName);
            return LoginResult::AlreadyOnline;
        }
        
        // 创建会话
        sessionId = m_nextSessionId++;
        PlayerSession session(sessionId, playerId, ipAddress, connectionId);
        
        // 更新玩家状态
        player->status = PlayerStatus::Online;
        player->lastLoginTime = std::chrono::system_clock::now();
        
        // 添加到在线玩家列表
        {
            std::unique_lock<std::shared_mutex> lock(m_playersMutex);
            m_onlinePlayers[playerId] = player;
        }
        
        // 添加会话
        {
            std::lock_guard<std::mutex> lock(m_sessionsMutex);
            m_activeSessions[sessionId] = session;
            m_playerToSession[playerId] = sessionId;
        }
        
        LOG_INFO("PLAYER_MGR", "Player logged in: " + player->playerName + 
                " (Session: " + std::to_string(sessionId) + ", IP: " + ipAddress + ")");
        
        // 记录业务指标
        std::string sessionIdStr = RECORD_USER_LOGIN(player->playerName, ipAddress);
        
        return LoginResult::Success;
    }
    
    // 玩家登出
    bool PlayerLogout(uint32_t playerId)
    {
        std::shared_ptr<Player> player;
        uint32_t sessionId = 0;
        
        // 获取玩家和会话信息
        {
            std::shared_lock<std::shared_mutex> lock(m_playersMutex);
            auto it = m_onlinePlayers.find(playerId);
            if (it == m_onlinePlayers.end())
            {
                LOG_WARNING("PLAYER_MGR", "Attempted to logout offline player: " + std::to_string(playerId));
                return false;
            }
            player = it->second;
        }
        
        {
            std::lock_guard<std::mutex> lock(m_sessionsMutex);
            auto sessionIt = m_playerToSession.find(playerId);
            if (sessionIt != m_playerToSession.end())
            {
                sessionId = sessionIt->second;
            }
        }
        
        // 更新玩家状态
        player->status = PlayerStatus::Offline;
        player->lastLogoutTime = std::chrono::system_clock::now();
        
        // 从在线列表移除
        {
            std::unique_lock<std::shared_mutex> lock(m_playersMutex);
            m_onlinePlayers.erase(playerId);
        }
        
        // 清理会话
        if (sessionId != 0)
        {
            std::lock_guard<std::mutex> lock(m_sessionsMutex);
            m_activeSessions.erase(sessionId);
            m_playerToSession.erase(playerId);
            
            // 记录业务指标
            RECORD_USER_LOGOUT(sessionId);
        }
        
        LOG_INFO("PLAYER_MGR", "Player logged out: " + player->playerName);
        return true;
    }
    
    // 获取在线玩家
    std::shared_ptr<Player> GetOnlinePlayer(uint32_t playerId)
    {
        std::shared_lock<std::shared_mutex> lock(m_playersMutex);
        auto it = m_onlinePlayers.find(playerId);
        return (it != m_onlinePlayers.end()) ? it->second : nullptr;
    }
    
    // 获取玩家（包括离线）
    std::shared_ptr<Player> GetPlayer(uint32_t playerId)
    {
        std::lock_guard<std::mutex> lock(m_allPlayersMutex);
        auto it = m_allPlayers.find(playerId);
        return (it != m_allPlayers.end()) ? it->second : nullptr;
    }
    
    // 根据名称获取玩家ID
    uint32_t GetPlayerIdByName(const std::string& playerName)
    {
        std::lock_guard<std::mutex> lock(m_allPlayersMutex);
        auto it = m_playerNameToId.find(playerName);
        return (it != m_playerNameToId.end()) ? it->second : 0;
    }
    
    // 检查玩家是否在线
    bool IsPlayerOnline(uint32_t playerId)
    {
        std::shared_lock<std::shared_mutex> lock(m_playersMutex);
        return m_onlinePlayers.find(playerId) != m_onlinePlayers.end();
    }
    
    // 检查玩家名是否存在
    bool IsPlayerNameExists(const std::string& playerName)
    {
        std::lock_guard<std::mutex> lock(m_allPlayersMutex);
        return m_playerNameToId.find(playerName) != m_playerNameToId.end();
    }
    
    // 获取在线玩家数量
    size_t GetOnlinePlayerCount()
    {
        std::shared_lock<std::shared_mutex> lock(m_playersMutex);
        return m_onlinePlayers.size();
    }
    
    // 获取总玩家数量
    size_t GetTotalPlayerCount()
    {
        std::lock_guard<std::mutex> lock(m_allPlayersMutex);
        return m_allPlayers.size();
    }
    
    // 获取在线玩家列表
    std::vector<std::shared_ptr<Player>> GetOnlinePlayerList()
    {
        std::shared_lock<std::shared_mutex> lock(m_playersMutex);
        std::vector<std::shared_ptr<Player>> players;
        players.reserve(m_onlinePlayers.size());
        
        for (const auto& [playerId, player] : m_onlinePlayers)
        {
            players.push_back(player);
        }
        
        return players;
    }
    
    // 根据地图获取玩家列表
    std::vector<std::shared_ptr<Player>> GetPlayersInMap(uint32_t mapId)
    {
        std::shared_lock<std::shared_mutex> lock(m_playersMutex);
        std::vector<std::shared_ptr<Player>> players;
        
        for (const auto& [playerId, player] : m_onlinePlayers)
        {
            if (player->position.mapId == mapId)
            {
                players.push_back(player);
            }
        }
        
        return players;
    }
    
    // 根据范围获取玩家列表
    std::vector<std::shared_ptr<Player>> GetPlayersInRange(const PlayerPosition& center, double range)
    {
        std::shared_lock<std::shared_mutex> lock(m_playersMutex);
        std::vector<std::shared_ptr<Player>> players;
        
        for (const auto& [playerId, player] : m_onlinePlayers)
        {
            if (player->position.IsInRange(center, range))
            {
                players.push_back(player);
            }
        }
        
        return players;
    }
    
    // 更新玩家活动时间
    void UpdatePlayerActivity(uint32_t playerId)
    {
        std::lock_guard<std::mutex> lock(m_sessionsMutex);
        auto sessionIt = m_playerToSession.find(playerId);
        if (sessionIt != m_playerToSession.end())
        {
            auto it = m_activeSessions.find(sessionIt->second);
            if (it != m_activeSessions.end())
            {
                it->second.UpdateActivity();
            }
        }
    }
    
    // 设置最大在线玩家数
    void SetMaxOnlinePlayers(size_t maxPlayers)
    {
        m_maxOnlinePlayers = maxPlayers;
        LOG_INFO("PLAYER_MGR", "Max online players set to: " + std::to_string(maxPlayers));
    }
    
    // 设置会话超时时间
    void SetSessionTimeout(std::chrono::seconds timeout)
    {
        m_sessionTimeout = timeout;
        LOG_INFO("PLAYER_MGR", "Session timeout set to: " + std::to_string(timeout.count()) + " seconds");
    }
    
    // 广播消息给所有在线玩家
    void BroadcastMessage(const std::string& message)
    {
        std::shared_lock<std::shared_mutex> lock(m_playersMutex);
        LOG_INFO("PLAYER_MGR", "Broadcasting message to " + std::to_string(m_onlinePlayers.size()) + " players: " + message);
        
        // 这里应该实现实际的消息发送逻辑
        // 暂时只记录日志
    }
    
    // 获取玩家统计信息
    struct PlayerStatistics
    {
        size_t totalPlayers = 0;
        size_t onlinePlayers = 0;
        size_t activeSessions = 0;
        std::unordered_map<PlayerSeries, size_t> playersBySeries;
        std::unordered_map<uint32_t, size_t> playersPerLevel;
        std::unordered_map<uint32_t, size_t> playersPerMap;
    };
    
    PlayerStatistics GetPlayerStatistics()
    {
        PlayerStatistics stats;
        
        {
            std::lock_guard<std::mutex> lock(m_allPlayersMutex);
            stats.totalPlayers = m_allPlayers.size();
            
            for (const auto& [playerId, player] : m_allPlayers)
            {
                stats.playersBySeries[player->series]++;
                stats.playersPerLevel[player->level / 10 * 10]++; // 按10级分组
            }
        }
        
        {
            std::shared_lock<std::shared_mutex> lock(m_playersMutex);
            stats.onlinePlayers = m_onlinePlayers.size();
            
            for (const auto& [playerId, player] : m_onlinePlayers)
            {
                stats.playersPerMap[player->position.mapId]++;
            }
        }
        
        {
            std::lock_guard<std::mutex> lock(m_sessionsMutex);
            stats.activeSessions = m_activeSessions.size();
        }
        
        return stats;
    }

private:
    std::atomic<bool> m_running;
    std::thread m_cleanupThread;
    
    // 玩家数据
    mutable std::shared_mutex m_playersMutex;
    std::unordered_map<uint32_t, std::shared_ptr<Player>> m_onlinePlayers;
    
    mutable std::mutex m_allPlayersMutex;
    std::unordered_map<uint32_t, std::shared_ptr<Player>> m_allPlayers;
    std::unordered_map<std::string, uint32_t> m_playerNameToId;
    
    // 会话管理
    mutable std::mutex m_sessionsMutex;
    std::unordered_map<uint32_t, PlayerSession> m_activeSessions;
    std::unordered_map<uint32_t, uint32_t> m_playerToSession; // playerId -> sessionId
    
    // 配置参数
    std::atomic<uint32_t> m_nextPlayerId;
    std::atomic<uint32_t> m_nextSessionId;
    size_t m_maxOnlinePlayers;
    std::chrono::seconds m_sessionTimeout;
    
    // 清理超时会话
    void CleanupLoop()
    {
        while (m_running)
        {
            try
            {
                CleanupTimeoutSessions();
                std::this_thread::sleep_for(std::chrono::minutes(1)); // 每分钟清理一次
            }
            catch (const std::exception& e)
            {
                LOG_ERROR("PLAYER_MGR", std::string("Error in cleanup loop: ") + e.what());
            }
        }
    }
    
    void CleanupTimeoutSessions()
    {
        std::vector<uint32_t> timeoutPlayers;
        
        {
            std::lock_guard<std::mutex> lock(m_sessionsMutex);
            for (const auto& [sessionId, session] : m_activeSessions)
            {
                if (session.IsTimeout(m_sessionTimeout))
                {
                    timeoutPlayers.push_back(session.playerId);
                }
            }
        }
        
        // 登出超时玩家
        for (uint32_t playerId : timeoutPlayers)
        {
            LOG_INFO("PLAYER_MGR", "Logging out timeout player: " + std::to_string(playerId));
            PlayerLogout(playerId);
        }
    }
};

} // namespace sword2

// 全局玩家管理器访问
#define PLAYER_MANAGER() sword2::PlayerManager::getInstance()

// 便捷宏定义
#define START_PLAYER_SYSTEM() PLAYER_MANAGER().Start()
#define STOP_PLAYER_SYSTEM() PLAYER_MANAGER().Stop()

#define CREATE_PLAYER(params) PLAYER_MANAGER().CreatePlayer(params)
#define PLAYER_LOGIN(playerId, ip, connId, sessionId) PLAYER_MANAGER().PlayerLogin(playerId, ip, connId, sessionId)
#define PLAYER_LOGOUT(playerId) PLAYER_MANAGER().PlayerLogout(playerId)

#define GET_ONLINE_PLAYER(playerId) PLAYER_MANAGER().GetOnlinePlayer(playerId)
#define GET_PLAYER(playerId) PLAYER_MANAGER().GetPlayer(playerId)
#define IS_PLAYER_ONLINE(playerId) PLAYER_MANAGER().IsPlayerOnline(playerId)

#define GET_ONLINE_PLAYER_COUNT() PLAYER_MANAGER().GetOnlinePlayerCount()
#define BROADCAST_MESSAGE(message) PLAYER_MANAGER().BroadcastMessage(message)

#endif // PLAYER_MANAGER_H
