//---------------------------------------------------------------------------
// Sword2 Player System (c) 2024
//
// File:	PlayerSystem.h
// Date:	2024.07.02
// Code:	AI Assistant
// Desc:	Comprehensive player management system for Sword2
//---------------------------------------------------------------------------
#ifndef PLAYER_SYSTEM_H
#define PLAYER_SYSTEM_H

#include "ModernCpp.h"
#include "UnifiedLoggingSystem.h"
#include "GameDataSystem.h"
#include <string>
#include <unordered_map>
#include <vector>
#include <memory>
#include <chrono>
#include <atomic>
#include <mutex>

namespace sword2 {

// 玩家状态枚举
enum class PlayerStatus : uint8_t
{
    Offline = 0,        // 离线
    Online,             // 在线
    InGame,             // 游戏中
    InBattle,           // 战斗中
    Trading,            // 交易中
    AFK,                // 暂离
    Banned              // 被封禁
};

// 门派系列
enum class PlayerSeries : uint8_t
{
    None = 0,           // 无门派
    Shaolin = 1,        // 少林
    Wudang = 2,         // 武当
    Emei = 3,           // 峨眉
    Beggar = 4,         // 丐帮
    Tangmen = 5,        // 唐门
    Wudu = 6,           // 五毒
    Kunlun = 7,         // 昆仑
    Mingjiao = 8,       // 明教
    Cuiyan = 9,         // 翠烟
    Cangjian = 10       // 藏剑
};

// 玩家性别
enum class PlayerGender : uint8_t
{
    Male = 0,           // 男性
    Female = 1          // 女性
};

// 玩家基础属性
struct PlayerAttributes
{
    // 基础属性
    uint32_t strength = 10;         // 力量
    uint32_t agility = 10;          // 敏捷
    uint32_t vitality = 10;         // 体力
    uint32_t energy = 10;           // 精力
    
    // 战斗属性
    uint32_t maxLife = 100;         // 最大生命值
    uint32_t currentLife = 100;     // 当前生命值
    uint32_t maxMana = 100;         // 最大内力值
    uint32_t currentMana = 100;     // 当前内力值
    uint32_t maxStamina = 100;      // 最大体力值
    uint32_t currentStamina = 100;  // 当前体力值
    
    // 攻击属性
    uint32_t minAttack = 1;         // 最小攻击力
    uint32_t maxAttack = 1;         // 最大攻击力
    uint32_t minMagicAttack = 0;    // 最小法术攻击
    uint32_t maxMagicAttack = 0;    // 最大法术攻击
    
    // 防御属性
    uint32_t defense = 0;           // 物理防御
    uint32_t magicDefense = 0;      // 法术防御
    uint32_t dodge = 0;             // 闪避
    uint32_t hit = 100;             // 命中
    
    // 特殊属性
    uint32_t criticalRate = 0;      // 暴击率
    uint32_t criticalDamage = 150;  // 暴击伤害
    uint32_t attackSpeed = 100;     // 攻击速度
    uint32_t moveSpeed = 100;       // 移动速度
    
    // 抗性属性
    uint32_t fireResist = 0;        // 火抗
    uint32_t iceResist = 0;         // 冰抗
    uint32_t lightningResist = 0;   // 雷抗
    uint32_t poisonResist = 0;      // 毒抗
    
    PlayerAttributes() = default;
    
    // 计算总攻击力
    uint32_t GetTotalAttack() const
    {
        return (minAttack + maxAttack) / 2 + (minMagicAttack + maxMagicAttack) / 2;
    }
    
    // 计算总防御力
    uint32_t GetTotalDefense() const
    {
        return (defense + magicDefense) / 2;
    }
    
    // 检查是否死亡
    bool IsDead() const
    {
        return currentLife == 0;
    }
    
    // 检查是否满血
    bool IsFullLife() const
    {
        return currentLife == maxLife;
    }
    
    // 恢复生命值
    void RestoreLife(uint32_t amount)
    {
        currentLife = std::min(maxLife, currentLife + amount);
    }
    
    // 恢复内力值
    void RestoreMana(uint32_t amount)
    {
        currentMana = std::min(maxMana, currentMana + amount);
    }
    
    // 消耗内力
    bool ConsumeMana(uint32_t amount)
    {
        if (currentMana >= amount)
        {
            currentMana -= amount;
            return true;
        }
        return false;
    }
};

// 玩家位置信息
struct PlayerPosition
{
    uint32_t mapId = 0;             // 地图ID
    int32_t x = 0;                  // X坐标
    int32_t y = 0;                  // Y坐标
    uint32_t direction = 0;         // 朝向
    
    PlayerPosition() = default;
    PlayerPosition(uint32_t map, int32_t posX, int32_t posY, uint32_t dir = 0)
        : mapId(map), x(posX), y(posY), direction(dir) {}
    
    // 计算与另一个位置的距离
    double DistanceTo(const PlayerPosition& other) const
    {
        if (mapId != other.mapId) return -1.0; // 不同地图
        
        int32_t dx = x - other.x;
        int32_t dy = y - other.y;
        return std::sqrt(dx * dx + dy * dy);
    }
    
    // 检查是否在指定范围内
    bool IsInRange(const PlayerPosition& other, double range) const
    {
        double distance = DistanceTo(other);
        return distance >= 0 && distance <= range;
    }
};

// 玩家装备槽位
enum class EquipSlot : uint8_t
{
    Weapon = 0,         // 武器
    Armor,              // 护甲
    Helmet,             // 头盔
    Necklace,           // 项链
    Ring1,              // 戒指1
    Ring2,              // 戒指2
    Boots,              // 靴子
    Belt,               // 腰带
    Gloves,             // 手套
    Cloak,              // 斗篷
    MaxSlots            // 最大槽位数
};

// 装备信息
struct EquipmentItem
{
    uint32_t itemId = 0;            // 物品ID
    std::string itemName;           // 物品名称
    EquipSlot slot = EquipSlot::Weapon; // 装备槽位
    PlayerAttributes bonusAttributes; // 属性加成
    uint32_t durability = 100;      // 耐久度
    uint32_t maxDurability = 100;   // 最大耐久度
    uint32_t enhanceLevel = 0;      // 强化等级
    std::vector<std::string> gems;  // 镶嵌宝石
    
    EquipmentItem() = default;
    EquipmentItem(uint32_t id, const std::string& name, EquipSlot equipSlot)
        : itemId(id), itemName(name), slot(equipSlot) {}
    
    // 检查是否损坏
    bool IsBroken() const
    {
        return durability == 0;
    }
    
    // 修理装备
    void Repair()
    {
        durability = maxDurability;
    }
    
    // 减少耐久度
    void ReduceDurability(uint32_t amount = 1)
    {
        if (durability >= amount)
            durability -= amount;
        else
            durability = 0;
    }
};

// 玩家数据结构
class Player
{
public:
    // 基础信息
    uint32_t playerId = 0;          // 玩家ID
    std::string playerName;         // 玩家名称
    std::string accountName;        // 账号名称
    PlayerGender gender = PlayerGender::Male; // 性别
    PlayerSeries series = PlayerSeries::None; // 门派
    
    // 等级和经验
    uint32_t level = 1;             // 等级
    uint64_t experience = 0;        // 经验值
    uint64_t nextLevelExp = 100;    // 升级所需经验
    
    // 状态信息
    PlayerStatus status = PlayerStatus::Offline; // 状态
    PlayerPosition position;        // 位置
    PlayerAttributes attributes;    // 属性
    
    // 装备信息
    std::array<std::unique_ptr<EquipmentItem>, static_cast<size_t>(EquipSlot::MaxSlots)> equipment;
    
    // 时间信息
    std::chrono::system_clock::time_point createTime;     // 创建时间
    std::chrono::system_clock::time_point lastLoginTime;  // 最后登录时间
    std::chrono::system_clock::time_point lastLogoutTime; // 最后登出时间
    uint64_t totalOnlineTime = 0;   // 总在线时间（秒）
    
    // 游戏数据
    uint64_t money = 0;             // 金钱
    uint32_t pkValue = 0;           // PK值
    uint32_t reputation = 0;        // 声望
    uint32_t killCount = 0;         // 击杀数
    uint32_t deathCount = 0;        // 死亡数
    
    Player() = default;
    Player(uint32_t id, const std::string& name, const std::string& account)
        : playerId(id), playerName(name), accountName(account)
    {
        createTime = std::chrono::system_clock::now();
        InitializeDefaultAttributes();
    }
    
    // 初始化默认属性
    void InitializeDefaultAttributes()
    {
        // 根据门派设置初始属性
        switch (series)
        {
        case PlayerSeries::Shaolin:
            attributes.strength = 15;
            attributes.vitality = 15;
            attributes.agility = 8;
            attributes.energy = 7;
            break;
        case PlayerSeries::Wudang:
            attributes.strength = 10;
            attributes.vitality = 10;
            attributes.agility = 12;
            attributes.energy = 13;
            break;
        case PlayerSeries::Emei:
            attributes.strength = 8;
            attributes.vitality = 12;
            attributes.agility = 10;
            attributes.energy = 15;
            break;
        default:
            attributes.strength = 10;
            attributes.vitality = 10;
            attributes.agility = 10;
            attributes.energy = 10;
            break;
        }
        
        RecalculateAttributes();
    }
    
    // 重新计算属性
    void RecalculateAttributes()
    {
        // 基础属性计算
        attributes.maxLife = 100 + attributes.vitality * 10 + level * 5;
        attributes.maxMana = 100 + attributes.energy * 10 + level * 3;
        attributes.maxStamina = 100 + attributes.agility * 5 + level * 2;
        
        // 攻击力计算
        attributes.minAttack = attributes.strength / 2 + level;
        attributes.maxAttack = attributes.strength + level * 2;
        
        // 防御力计算
        attributes.defense = attributes.vitality / 3 + level;
        attributes.magicDefense = attributes.energy / 3 + level;
        
        // 命中和闪避
        attributes.hit = 100 + attributes.agility / 2;
        attributes.dodge = attributes.agility / 3;
        
        // 添加装备属性加成
        ApplyEquipmentBonuses();
        
        // 确保当前值不超过最大值
        attributes.currentLife = std::min(attributes.currentLife, attributes.maxLife);
        attributes.currentMana = std::min(attributes.currentMana, attributes.maxMana);
        attributes.currentStamina = std::min(attributes.currentStamina, attributes.maxStamina);
    }
    
    // 应用装备属性加成
    void ApplyEquipmentBonuses()
    {
        for (const auto& item : equipment)
        {
            if (item && !item->IsBroken())
            {
                // 添加装备属性加成
                attributes.minAttack += item->bonusAttributes.minAttack;
                attributes.maxAttack += item->bonusAttributes.maxAttack;
                attributes.defense += item->bonusAttributes.defense;
                attributes.magicDefense += item->bonusAttributes.magicDefense;
                // ... 其他属性加成
            }
        }
    }
    
    // 装备物品
    bool EquipItem(std::unique_ptr<EquipmentItem> item)
    {
        if (!item) return false;
        
        EquipSlot slot = item->slot;
        size_t slotIndex = static_cast<size_t>(slot);
        
        if (slotIndex >= equipment.size()) return false;
        
        // 卸下原有装备
        if (equipment[slotIndex])
        {
            UnequipItem(slot);
        }
        
        // 装备新物品
        equipment[slotIndex] = std::move(item);
        RecalculateAttributes();
        
        LOG_INFO("PLAYER", playerName + " equipped item in slot " + std::to_string(slotIndex));
        return true;
    }
    
    // 卸下装备
    std::unique_ptr<EquipmentItem> UnequipItem(EquipSlot slot)
    {
        size_t slotIndex = static_cast<size_t>(slot);
        if (slotIndex >= equipment.size()) return nullptr;
        
        auto item = std::move(equipment[slotIndex]);
        equipment[slotIndex] = nullptr;
        RecalculateAttributes();
        
        if (item)
        {
            LOG_INFO("PLAYER", playerName + " unequipped item from slot " + std::to_string(slotIndex));
        }
        
        return item;
    }
    
    // 获取装备
    const EquipmentItem* GetEquipment(EquipSlot slot) const
    {
        size_t slotIndex = static_cast<size_t>(slot);
        if (slotIndex >= equipment.size()) return nullptr;
        
        return equipment[slotIndex].get();
    }
    
    // 增加经验值
    bool AddExperience(uint64_t exp)
    {
        experience += exp;
        bool leveledUp = false;
        
        while (experience >= nextLevelExp && level < 999) // 最大等级999
        {
            experience -= nextLevelExp;
            level++;
            nextLevelExp = CalculateNextLevelExp();
            leveledUp = true;
            
            LOG_INFO("PLAYER", playerName + " leveled up to " + std::to_string(level));
        }
        
        if (leveledUp)
        {
            RecalculateAttributes();
            // 升级时恢复满血满蓝
            attributes.currentLife = attributes.maxLife;
            attributes.currentMana = attributes.maxMana;
            attributes.currentStamina = attributes.maxStamina;
        }
        
        return leveledUp;
    }
    
    // 计算下一级所需经验
    uint64_t CalculateNextLevelExp() const
    {
        // 经验公式：基础经验 * 等级系数
        return 100 * level * level + 50 * level;
    }
    
    // 检查是否可以升级
    bool CanLevelUp() const
    {
        return experience >= nextLevelExp && level < 999;
    }
    
    // 获取门派名称
    std::string GetSeriesName() const
    {
        switch (series)
        {
        case PlayerSeries::Shaolin: return "少林";
        case PlayerSeries::Wudang: return "武当";
        case PlayerSeries::Emei: return "峨眉";
        case PlayerSeries::Beggar: return "丐帮";
        case PlayerSeries::Tangmen: return "唐门";
        case PlayerSeries::Wudu: return "五毒";
        case PlayerSeries::Kunlun: return "昆仑";
        case PlayerSeries::Mingjiao: return "明教";
        case PlayerSeries::Cuiyan: return "翠烟";
        case PlayerSeries::Cangjian: return "藏剑";
        default: return "无门派";
        }
    }
    
    // 获取状态描述
    std::string GetStatusDescription() const
    {
        switch (status)
        {
        case PlayerStatus::Offline: return "离线";
        case PlayerStatus::Online: return "在线";
        case PlayerStatus::InGame: return "游戏中";
        case PlayerStatus::InBattle: return "战斗中";
        case PlayerStatus::Trading: return "交易中";
        case PlayerStatus::AFK: return "暂离";
        case PlayerStatus::Banned: return "被封禁";
        default: return "未知";
        }
    }
    
    // 检查是否在线
    bool IsOnline() const
    {
        return status != PlayerStatus::Offline && status != PlayerStatus::Banned;
    }
    
    // 检查是否可以战斗
    bool CanFight() const
    {
        return IsOnline() && !attributes.IsDead() && 
               status != PlayerStatus::Trading;
    }
    
    // 设置位置
    void SetPosition(uint32_t mapId, int32_t x, int32_t y, uint32_t direction = 0)
    {
        position.mapId = mapId;
        position.x = x;
        position.y = y;
        position.direction = direction;
    }
    
    // 移动到指定位置
    bool MoveTo(int32_t x, int32_t y)
    {
        if (!CanFight()) return false;
        
        position.x = x;
        position.y = y;
        return true;
    }
    
    // 传送到指定地图
    bool TeleportTo(uint32_t mapId, int32_t x, int32_t y)
    {
        if (!IsOnline()) return false;
        
        position.mapId = mapId;
        position.x = x;
        position.y = y;
        
        LOG_INFO("PLAYER", playerName + " teleported to map " + std::to_string(mapId) + 
                " at (" + std::to_string(x) + ", " + std::to_string(y) + ")");
        return true;
    }
};

} // namespace sword2

#endif // PLAYER_SYSTEM_H
