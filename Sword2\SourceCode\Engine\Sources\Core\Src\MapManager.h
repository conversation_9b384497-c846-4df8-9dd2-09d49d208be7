//---------------------------------------------------------------------------
// Sword2 Map Manager (c) 2024
//
// File:	MapManager.h
// Date:	2024.07.02
// Code:	AI Assistant
// Desc:	Comprehensive map and scene management system
//---------------------------------------------------------------------------
#ifndef MAP_MANAGER_H
#define MAP_MANAGER_H

#include "MapSystem.h"
#include "GameDataSystem.h"
#include "LuaScriptEngine.h"
#include "UnifiedLoggingSystem.h"
#include "BusinessMonitor.h"
#include <unordered_map>
#include <memory>
#include <mutex>
#include <shared_mutex>
#include <thread>
#include <atomic>
#include <queue>
#include <functional>

namespace sword2 {

// 地图事件类型
enum class MapEvent : uint8_t
{
    PlayerEnter = 0,    // 玩家进入
    PlayerLeave,        // 玩家离开
    ObjectInteract,     // 对象交互
    TriggerActivate,    // 触发器激活
    ResourceHarvest,    // 资源采集
    PortalUse,          // 传送门使用
    MapCreate,          // 地图创建
    MapDestroy          // 地图销毁
};

// 地图管理器
class MapManager : public Singleton<MapManager>
{
public:
    MapManager()
        : m_running(false), m_nextInstanceId(1), m_nextObjectId(1), m_updateInterval(std::chrono::seconds(1)) {}
    
    ~MapManager()
    {
        Stop();
    }
    
    // 启动地图管理器
    bool Start()
    {
        if (m_running) return true;
        
        // 加载地图数据
        if (!LoadMapData())
        {
            LOG_ERROR("MAP_MGR", "Failed to load map data");
            return false;
        }
        
        m_running = true;
        m_updateThread = std::thread(&MapManager::UpdateLoop, this);
        
        LOG_INFO("MAP_MGR", "Map manager started with " + std::to_string(m_mapTemplates.size()) + " map templates");
        return true;
    }
    
    // 停止地图管理器
    void Stop()
    {
        if (!m_running) return;
        
        m_running = false;
        if (m_updateThread.joinable())
        {
            m_updateThread.join();
        }
        
        // 清理所有地图实例
        {
            std::unique_lock<std::shared_mutex> lock(m_instancesMutex);
            m_mapInstances.clear();
        }
        
        {
            std::lock_guard<std::mutex> lock(m_templatesMutex);
            m_mapTemplates.clear();
        }
        
        LOG_INFO("MAP_MGR", "Map manager stopped");
    }
    
    // 创建地图实例
    uint32_t CreateMapInstance(uint32_t mapId, bool isPrivate = false)
    {
        // 获取地图模板
        std::shared_ptr<EnhancedMapData> mapTemplate;
        {
            std::lock_guard<std::mutex> lock(m_templatesMutex);
            auto it = m_mapTemplates.find(mapId);
            if (it == m_mapTemplates.end())
            {
                LOG_ERROR("MAP_MGR", "Map template not found: " + std::to_string(mapId));
                return 0;
            }
            mapTemplate = it->second;
        }
        
        // 生成实例ID
        uint32_t instanceId = m_nextInstanceId++;
        
        // 创建地图实例
        auto instance = std::make_shared<MapInstance>(instanceId, mapId, *mapTemplate);
        
        // 初始化场景对象
        InitializeSceneObjects(instance);
        
        // 添加到管理器
        {
            std::unique_lock<std::shared_mutex> lock(m_instancesMutex);
            m_mapInstances[instanceId] = instance;
            m_instancesByMap[mapId].insert(instanceId);
        }
        
        // 触发地图创建事件
        TriggerMapEvent(instanceId, MapEvent::MapCreate, 0);
        
        LOG_INFO("MAP_MGR", "Created map instance " + std::to_string(instanceId) + 
                " for map " + std::to_string(mapId) + " (" + mapTemplate->name + ")");
        
        return instanceId;
    }
    
    // 销毁地图实例
    bool DestroyMapInstance(uint32_t instanceId)
    {
        std::unique_lock<std::shared_mutex> lock(m_instancesMutex);
        
        auto it = m_mapInstances.find(instanceId);
        if (it == m_mapInstances.end())
        {
            LOG_WARNING("MAP_MGR", "Map instance not found for destruction: " + std::to_string(instanceId));
            return false;
        }
        
        auto instance = it->second;
        uint32_t mapId = instance->mapId;
        
        // 触发地图销毁事件
        TriggerMapEvent(instanceId, MapEvent::MapDestroy, 0);
        
        // 从地图索引中移除
        auto mapIt = m_instancesByMap.find(mapId);
        if (mapIt != m_instancesByMap.end())
        {
            mapIt->second.erase(instanceId);
            if (mapIt->second.empty())
            {
                m_instancesByMap.erase(mapIt);
            }
        }
        
        // 移除实例
        m_mapInstances.erase(it);
        
        LOG_INFO("MAP_MGR", "Destroyed map instance " + std::to_string(instanceId));
        return true;
    }
    
    // 获取地图实例
    std::shared_ptr<MapInstance> GetMapInstance(uint32_t instanceId)
    {
        std::shared_lock<std::shared_mutex> lock(m_instancesMutex);
        auto it = m_mapInstances.find(instanceId);
        return (it != m_mapInstances.end()) ? it->second : nullptr;
    }
    
    // 获取地图的所有实例
    std::vector<std::shared_ptr<MapInstance>> GetMapInstances(uint32_t mapId)
    {
        std::shared_lock<std::shared_mutex> lock(m_instancesMutex);
        std::vector<std::shared_ptr<MapInstance>> instances;
        
        auto mapIt = m_instancesByMap.find(mapId);
        if (mapIt != m_instancesByMap.end())
        {
            instances.reserve(mapIt->second.size());
            for (uint32_t instanceId : mapIt->second)
            {
                auto it = m_mapInstances.find(instanceId);
                if (it != m_mapInstances.end())
                {
                    instances.push_back(it->second);
                }
            }
        }
        
        return instances;
    }
    
    // 查找合适的地图实例（用于玩家进入）
    uint32_t FindSuitableInstance(uint32_t mapId, uint32_t playerId = 0)
    {
        auto instances = GetMapInstances(mapId);
        
        // 查找有空位的实例
        for (const auto& instance : instances)
        {
            if (instance && instance->GetPlayerCount() < instance->maxPlayers)
            {
                return instance->instanceId;
            }
        }
        
        // 没有合适的实例，创建新的
        return CreateMapInstance(mapId);
    }
    
    // 玩家进入地图
    bool PlayerEnterMap(uint32_t playerId, uint32_t mapId, int32_t x = -1, int32_t y = -1)
    {
        // 查找合适的实例
        uint32_t instanceId = FindSuitableInstance(mapId, playerId);
        if (instanceId == 0)
        {
            LOG_ERROR("MAP_MGR", "Failed to find suitable map instance for player " + std::to_string(playerId));
            return false;
        }
        
        return PlayerEnterInstance(playerId, instanceId, x, y);
    }
    
    // 玩家进入地图实例
    bool PlayerEnterInstance(uint32_t playerId, uint32_t instanceId, int32_t x = -1, int32_t y = -1)
    {
        auto instance = GetMapInstance(instanceId);
        if (!instance)
        {
            LOG_ERROR("MAP_MGR", "Map instance not found: " + std::to_string(instanceId));
            return false;
        }
        
        // 检查玩家是否可以进入
        // 这里应该从PlayerManager获取玩家信息进行检查
        // 暂时简化处理
        
        if (!instance->AddPlayer(playerId))
        {
            LOG_WARNING("MAP_MGR", "Failed to add player " + std::to_string(playerId) + 
                       " to map instance " + std::to_string(instanceId));
            return false;
        }
        
        // 设置玩家位置
        if (x == -1 || y == -1)
        {
            // 使用默认出生点
            if (!instance->mapData.spawnPoints.empty())
            {
                auto& spawnPoint = instance->mapData.spawnPoints[0];
                x = spawnPoint.first;
                y = spawnPoint.second;
            }
            else
            {
                x = 1000;
                y = 1000;
            }
        }
        
        // 触发玩家进入事件
        TriggerMapEvent(instanceId, MapEvent::PlayerEnter, playerId);
        
        LOG_INFO("MAP_MGR", "Player " + std::to_string(playerId) + " entered map instance " + 
                std::to_string(instanceId) + " at (" + std::to_string(x) + ", " + std::to_string(y) + ")");
        
        return true;
    }
    
    // 玩家离开地图
    bool PlayerLeaveMap(uint32_t playerId, uint32_t instanceId)
    {
        auto instance = GetMapInstance(instanceId);
        if (!instance)
        {
            LOG_WARNING("MAP_MGR", "Map instance not found for player leave: " + std::to_string(instanceId));
            return false;
        }
        
        if (!instance->RemovePlayer(playerId))
        {
            LOG_WARNING("MAP_MGR", "Player " + std::to_string(playerId) + 
                       " was not in map instance " + std::to_string(instanceId));
            return false;
        }
        
        // 触发玩家离开事件
        TriggerMapEvent(instanceId, MapEvent::PlayerLeave, playerId);
        
        LOG_INFO("MAP_MGR", "Player " + std::to_string(playerId) + " left map instance " + std::to_string(instanceId));
        
        return true;
    }
    
    // 创建场景对象
    uint32_t CreateSceneObject(uint32_t instanceId, SceneObjectType type, const std::string& name, 
                              int32_t x, int32_t y, const std::string& scriptPath = "")
    {
        auto instance = GetMapInstance(instanceId);
        if (!instance)
        {
            LOG_ERROR("MAP_MGR", "Map instance not found for object creation: " + std::to_string(instanceId));
            return 0;
        }
        
        uint32_t objectId = m_nextObjectId++;
        std::shared_ptr<SceneObject> object;
        
        // 根据类型创建对象
        switch (type)
        {
        case SceneObjectType::Trigger:
            object = std::make_shared<TriggerObject>(objectId, name, TriggerType::OnEnter);
            break;
        case SceneObjectType::Portal:
            object = std::make_shared<PortalObject>(objectId, name, 0, 0, 0);
            break;
        case SceneObjectType::Resource:
            object = std::make_shared<ResourceObject>(objectId, name, "generic");
            break;
        default:
            object = std::make_shared<SceneObject>(objectId, name, type);
            break;
        }
        
        if (!object)
        {
            LOG_ERROR("MAP_MGR", "Failed to create scene object");
            return 0;
        }
        
        object->SetPosition(instance->mapId, x, y);
        object->scriptPath = scriptPath;
        
        if (!instance->AddSceneObject(object))
        {
            LOG_ERROR("MAP_MGR", "Failed to add scene object to map instance");
            return 0;
        }
        
        LOG_INFO("MAP_MGR", "Created scene object " + name + " (ID: " + std::to_string(objectId) + 
                ") in map instance " + std::to_string(instanceId));
        
        return objectId;
    }
    
    // 移除场景对象
    bool RemoveSceneObject(uint32_t instanceId, uint32_t objectId)
    {
        auto instance = GetMapInstance(instanceId);
        if (!instance)
        {
            LOG_WARNING("MAP_MGR", "Map instance not found for object removal: " + std::to_string(instanceId));
            return false;
        }
        
        if (!instance->RemoveSceneObject(objectId))
        {
            LOG_WARNING("MAP_MGR", "Scene object not found for removal: " + std::to_string(objectId));
            return false;
        }
        
        LOG_INFO("MAP_MGR", "Removed scene object " + std::to_string(objectId) + 
                " from map instance " + std::to_string(instanceId));
        
        return true;
    }
    
    // 玩家与场景对象交互
    bool PlayerInteractObject(uint32_t playerId, uint32_t instanceId, uint32_t objectId)
    {
        auto instance = GetMapInstance(instanceId);
        if (!instance)
        {
            LOG_WARNING("MAP_MGR", "Map instance not found for interaction: " + std::to_string(instanceId));
            return false;
        }
        
        auto object = instance->GetSceneObject(objectId);
        if (!object)
        {
            LOG_WARNING("MAP_MGR", "Scene object not found for interaction: " + std::to_string(objectId));
            return false;
        }
        
        // 检查交互距离
        // 这里应该获取玩家位置进行检查
        // 暂时简化处理
        
        // 触发交互事件
        TriggerMapEvent(instanceId, MapEvent::ObjectInteract, playerId, objectId);
        
        // 根据对象类型执行特定逻辑
        switch (object->type)
        {
        case SceneObjectType::Portal:
            if (auto portal = std::dynamic_pointer_cast<PortalObject>(object))
            {
                // 这里应该获取玩家对象进行传送
                LOG_INFO("MAP_MGR", "Player " + std::to_string(playerId) + " used portal " + object->name);
                TriggerMapEvent(instanceId, MapEvent::PortalUse, playerId, objectId);
            }
            break;
        case SceneObjectType::Resource:
            if (auto resource = std::dynamic_pointer_cast<ResourceObject>(object))
            {
                // 这里应该获取玩家对象进行采集
                LOG_INFO("MAP_MGR", "Player " + std::to_string(playerId) + " harvested resource " + object->name);
                TriggerMapEvent(instanceId, MapEvent::ResourceHarvest, playerId, objectId);
            }
            break;
        default:
            LOG_INFO("MAP_MGR", "Player " + std::to_string(playerId) + " interacted with " + object->name);
            break;
        }
        
        return true;
    }
    
    // 触发器检测
    void CheckTriggers(uint32_t instanceId, uint32_t playerId, int32_t x, int32_t y)
    {
        auto instance = GetMapInstance(instanceId);
        if (!instance) return;
        
        for (const auto& [triggerId, trigger] : instance->triggers)
        {
            if (!trigger || trigger->status != SceneObjectStatus::Active)
                continue;
            
            // 检查触发范围
            if (trigger->IsInRange(x, y, trigger->triggerRange))
            {
                // 这里应该获取玩家对象进行检查
                // 暂时简化处理
                if (trigger->Trigger(playerId))
                {
                    TriggerMapEvent(instanceId, MapEvent::TriggerActivate, playerId, triggerId);
                    LOG_DEBUG("MAP_MGR", "Trigger " + trigger->name + " activated by player " + std::to_string(playerId));
                }
            }
        }
    }
    
    // 获取地图统计信息
    struct MapStatistics
    {
        size_t totalMaps = 0;
        size_t totalInstances = 0;
        size_t totalPlayers = 0;
        size_t totalObjects = 0;
        std::unordered_map<MapType, size_t> mapsByType;
        std::unordered_map<uint32_t, size_t> playersPerMap;
        std::unordered_map<uint32_t, size_t> instancesPerMap;
    };
    
    MapStatistics GetStatistics()
    {
        std::shared_lock<std::shared_mutex> lock(m_instancesMutex);
        
        MapStatistics stats;
        stats.totalMaps = m_mapTemplates.size();
        stats.totalInstances = m_mapInstances.size();
        
        for (const auto& [instanceId, instance] : m_mapInstances)
        {
            stats.totalPlayers += instance->GetPlayerCount();
            stats.totalObjects += instance->sceneObjects.size();
            stats.playersPerMap[instance->mapId] += instance->GetPlayerCount();
            stats.instancesPerMap[instance->mapId]++;
            stats.mapsByType[instance->mapData.mapType]++;
        }
        
        return stats;
    }
    
    // 设置更新间隔
    void SetUpdateInterval(std::chrono::seconds interval)
    {
        m_updateInterval = interval;
        LOG_INFO("MAP_MGR", "Map update interval set to " + std::to_string(interval.count()) + " seconds");
    }

private:
    std::atomic<bool> m_running;
    std::thread m_updateThread;
    std::chrono::seconds m_updateInterval;
    
    // 地图数据
    mutable std::shared_mutex m_instancesMutex;
    std::unordered_map<uint32_t, std::shared_ptr<MapInstance>> m_mapInstances;
    std::unordered_map<uint32_t, std::unordered_set<uint32_t>> m_instancesByMap; // mapId -> instanceIds
    
    // 模板数据
    mutable std::mutex m_templatesMutex;
    std::unordered_map<uint32_t, std::shared_ptr<EnhancedMapData>> m_mapTemplates;
    
    std::atomic<uint32_t> m_nextInstanceId;
    std::atomic<uint32_t> m_nextObjectId;
    
    // 更新循环
    void UpdateLoop()
    {
        while (m_running)
        {
            try
            {
                UpdateAllInstances();
                CleanupInstances();
                std::this_thread::sleep_for(m_updateInterval);
            }
            catch (const std::exception& e)
            {
                LOG_ERROR("MAP_MGR", std::string("Error in update loop: ") + e.what());
            }
        }
    }
    
    void UpdateAllInstances()
    {
        std::shared_lock<std::shared_mutex> lock(m_instancesMutex);
        
        for (const auto& [instanceId, instance] : m_mapInstances)
        {
            if (instance)
            {
                instance->Update();
            }
        }
    }
    
    void CleanupInstances()
    {
        std::vector<uint32_t> instancesToDestroy;
        
        {
            std::shared_lock<std::shared_mutex> lock(m_instancesMutex);
            for (const auto& [instanceId, instance] : m_mapInstances)
            {
                if (instance && instance->ShouldDestroy())
                {
                    instancesToDestroy.push_back(instanceId);
                }
            }
        }
        
        // 销毁空闲实例
        for (uint32_t instanceId : instancesToDestroy)
        {
            DestroyMapInstance(instanceId);
        }
    }
    
    bool LoadMapData()
    {
        LOG_INFO("MAP_MGR", "Loading map data...");
        
        // 从GameDataSystem加载地图数据
        const auto& mapItems = GET_GAME_DATA(GameDataType::Map);
        
        std::lock_guard<std::mutex> lock(m_templatesMutex);
        m_mapTemplates.clear();
        
        for (const auto& item : mapItems)
        {
            auto mapData = std::make_shared<EnhancedMapData>();
            mapData->id = item.id;
            mapData->name = item.name;
            mapData->description = item.description;
            
            // 从属性中恢复地图数据
            mapData->worldId = item.GetProperty<uint32_t>("world_id", 0);
            mapData->mapType = static_cast<MapType>(item.GetProperty<int>("map_type", 0));
            mapData->maxPlayers = item.GetProperty<uint32_t>("max_players", 100);
            mapData->isPvpEnabled = item.GetProperty<bool>("pvp_enabled", false);
            mapData->minLevel = item.GetProperty<uint32_t>("min_level", 1);
            mapData->maxLevel = item.GetProperty<uint32_t>("max_level", 999);
            mapData->recommendLevel = item.GetProperty<uint32_t>("recommend_level", 1);
            mapData->timeLimit = item.GetProperty<uint32_t>("time_limit", 0);
            mapData->entryLimit = item.GetProperty<uint32_t>("entry_limit", 0);
            mapData->requiresParty = item.GetProperty<bool>("requires_party", false);
            
            m_mapTemplates[mapData->id] = mapData;
        }
        
        LOG_INFO("MAP_MGR", "Loaded " + std::to_string(m_mapTemplates.size()) + " map templates");
        return true;
    }
    
    void InitializeSceneObjects(std::shared_ptr<MapInstance> instance)
    {
        if (!instance) return;
        
        const auto& mapData = instance->mapData;
        
        // 创建传送点
        for (const auto& teleport : mapData.teleports)
        {
            auto portal = std::make_shared<PortalObject>(m_nextObjectId++, teleport.name, 
                                                        teleport.targetMapId, teleport.targetX, teleport.targetY);
            portal->SetPosition(instance->mapId, teleport.x, teleport.y);
            portal->requiredLevel = teleport.requiredLevel;
            portal->cost = teleport.cost;
            
            instance->AddSceneObject(portal);
        }
        
        // 创建资源点
        for (const auto& resource : mapData.resources)
        {
            auto resourceObj = std::make_shared<ResourceObject>(m_nextObjectId++, "Resource_" + std::to_string(resource.id), resource.type);
            resourceObj->SetPosition(instance->mapId, resource.x, resource.y);
            resourceObj->respawnTime = resource.respawnTime;
            resourceObj->requiredSkill = resource.requiredSkill;
            
            // 设置掉落物品
            for (uint32_t itemId : resource.dropItems)
            {
                resourceObj->dropItems.emplace_back(itemId, 1);
            }
            
            instance->AddSceneObject(resourceObj);
        }
        
        LOG_DEBUG("MAP_MGR", "Initialized " + std::to_string(instance->sceneObjects.size()) + 
                 " scene objects for map instance " + std::to_string(instance->instanceId));
    }
    
    void TriggerMapEvent(uint32_t instanceId, MapEvent event, uint32_t playerId, uint32_t objectId = 0)
    {
        // 这里可以触发脚本事件或其他处理
        std::string eventName = GetEventName(event);
        LOG_DEBUG("MAP_MGR", "Map event triggered: " + eventName + " in instance " + std::to_string(instanceId));
        
        // 可以在这里执行脚本或其他逻辑
    }
    
    std::string GetEventName(MapEvent event)
    {
        switch (event)
        {
        case MapEvent::PlayerEnter: return "PlayerEnter";
        case MapEvent::PlayerLeave: return "PlayerLeave";
        case MapEvent::ObjectInteract: return "ObjectInteract";
        case MapEvent::TriggerActivate: return "TriggerActivate";
        case MapEvent::ResourceHarvest: return "ResourceHarvest";
        case MapEvent::PortalUse: return "PortalUse";
        case MapEvent::MapCreate: return "MapCreate";
        case MapEvent::MapDestroy: return "MapDestroy";
        default: return "Unknown";
        }
    }
};

} // namespace sword2

// 全局地图管理器访问
#define MAP_MANAGER() sword2::MapManager::getInstance()

// 便捷宏定义
#define START_MAP_SYSTEM() MAP_MANAGER().Start()
#define STOP_MAP_SYSTEM() MAP_MANAGER().Stop()

#define CREATE_MAP_INSTANCE(mapId, isPrivate) MAP_MANAGER().CreateMapInstance(mapId, isPrivate)
#define DESTROY_MAP_INSTANCE(instanceId) MAP_MANAGER().DestroyMapInstance(instanceId)
#define GET_MAP_INSTANCE(instanceId) MAP_MANAGER().GetMapInstance(instanceId)

#define PLAYER_ENTER_MAP(playerId, mapId, x, y) MAP_MANAGER().PlayerEnterMap(playerId, mapId, x, y)
#define PLAYER_LEAVE_MAP(playerId, instanceId) MAP_MANAGER().PlayerLeaveMap(playerId, instanceId)

#define CREATE_SCENE_OBJECT(instanceId, type, name, x, y, script) MAP_MANAGER().CreateSceneObject(instanceId, type, name, x, y, script)
#define REMOVE_SCENE_OBJECT(instanceId, objectId) MAP_MANAGER().RemoveSceneObject(instanceId, objectId)

#define PLAYER_INTERACT_OBJECT(playerId, instanceId, objectId) MAP_MANAGER().PlayerInteractObject(playerId, instanceId, objectId)
#define CHECK_TRIGGERS(instanceId, playerId, x, y) MAP_MANAGER().CheckTriggers(instanceId, playerId, x, y)

#endif // MAP_MANAGER_H
