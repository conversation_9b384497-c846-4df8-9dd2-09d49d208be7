//---------------------------------------------------------------------------
// Sword2 Performance Monitor (c) 2024
//
// File:	PerformanceMonitor.h
// Date:	2024.07.02
// Code:	AI Assistant
// Desc:	Performance monitoring and optimization tracking
//---------------------------------------------------------------------------
#ifndef PERFORMANCE_MONITOR_H
#define PERFORMANCE_MONITOR_H

#include <windows.h>

// 性能监控类
class CPerformanceMonitor
{
public:
    CPerformanceMonitor();
    ~CPerformanceMonitor();

    // 初始化和清理
    BOOL Initialize();
    void Cleanup();

    // 帧率监控
    void BeginFrame();
    void EndFrame();
    int GetCurrentFPS() const { return m_nCurrentFPS; }
    int GetAverageFPS() const { return m_nAverageFPS; }

    // 网络性能监控
    void RecordNetworkReceive(int nBytes);
    void RecordNetworkSend(int nBytes);
    int GetNetworkReceiveRate() const { return m_nNetReceiveRate; }
    int GetNetworkSendRate() const { return m_nNetSendRate; }

    // 内存使用监控
    void UpdateMemoryUsage();
    DWORD GetMemoryUsage() const { return m_dwMemoryUsage; }
    DWORD GetPeakMemoryUsage() const { return m_dwPeakMemoryUsage; }

    // CPU使用率监控
    void UpdateCPUUsage();
    int GetCPUUsage() const { return m_nCPUUsage; }

    // 性能报告
    void GenerateReport(char* pBuffer, int nBufferSize);
    void LogPerformanceData();

private:
    // 帧率相关
    DWORD m_dwFrameStartTime;
    DWORD m_dwLastFrameTime;
    int m_nFrameCount;
    int m_nCurrentFPS;
    int m_nAverageFPS;
    DWORD m_dwFPSUpdateTime;

    // 网络相关
    int m_nNetReceiveBytes;
    int m_nNetSendBytes;
    int m_nNetReceiveRate;
    int m_nNetSendRate;
    DWORD m_dwNetUpdateTime;

    // 内存相关
    DWORD m_dwMemoryUsage;
    DWORD m_dwPeakMemoryUsage;
    DWORD m_dwMemoryUpdateTime;

    // CPU相关
    int m_nCPUUsage;
    DWORD m_dwCPUUpdateTime;
    LARGE_INTEGER m_liLastCPUTime;
    LARGE_INTEGER m_liLastSystemTime;

    // 性能计数器
    LARGE_INTEGER m_liFrequency;
    BOOL m_bInitialized;
};

// 全局性能监控实例
extern CPerformanceMonitor g_PerformanceMonitor;

// 便捷宏定义
#define PERF_BEGIN_FRAME()          g_PerformanceMonitor.BeginFrame()
#define PERF_END_FRAME()            g_PerformanceMonitor.EndFrame()
#define PERF_RECORD_NET_RECV(bytes) g_PerformanceMonitor.RecordNetworkReceive(bytes)
#define PERF_RECORD_NET_SEND(bytes) g_PerformanceMonitor.RecordNetworkSend(bytes)
#define PERF_UPDATE_MEMORY()        g_PerformanceMonitor.UpdateMemoryUsage()
#define PERF_UPDATE_CPU()           g_PerformanceMonitor.UpdateCPUUsage()

// 性能计时器类 - 用于测量代码块执行时间
class CPerformanceTimer
{
public:
    CPerformanceTimer(const char* pName);
    ~CPerformanceTimer();

private:
    const char* m_pName;
    LARGE_INTEGER m_liStartTime;
    static LARGE_INTEGER s_liFrequency;
    static BOOL s_bFrequencyInitialized;
};

// 性能计时宏
#define PERF_TIMER(name) CPerformanceTimer _timer(name)

#endif // PERFORMANCE_MONITOR_H
