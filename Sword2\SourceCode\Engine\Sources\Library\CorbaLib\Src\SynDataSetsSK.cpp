// This file is generated by omniidl (C++ backend)- omniORB_3_0. Do not edit.

#include "SynDataSets.h"
#include <omniORB3/callDescriptor.h>

static const char* _0RL_library_version = omniORB_3_0;

SynDataSets_ptr SynDataSets_Helper::_nil() {
  return SynDataSets::_nil();
}

CORBA::Boolean SynDataSets_Helper::is_nil(SynDataSets_ptr p) {
  return CORBA::is_nil(p);

}

void SynDataSets_Helper::release(SynDataSets_ptr p) {
  CORBA::release(p);
}

void SynDataSets_Helper::duplicate(SynDataSets_ptr p) {
  if( p && !p->_NP_is_nil() )  omni::duplicateObjRef(p);
}

size_t SynDataSets_Helper::NP_alignedSize(SynDataSets_ptr obj, size_t offset) {
  return SynDataSets::_alignedSize(obj, offset);
}

void SynDataSets_Helper::marshalObjRef(SynDataSets_ptr obj, NetBufferedStream& s) {
  SynDataSets::_marshalObjRef(obj, s);
}

SynDataSets_ptr SynDataSets_Helper::unmarshalObjRef(NetBufferedStream& s) {
  return SynDataSets::_unmarshalObjRef(s);
}

void SynDataSets_Helper::marshalObjRef(SynDataSets_ptr obj, MemBufferedStream& s) {
  SynDataSets::_marshalObjRef(obj, s);
}

SynDataSets_ptr SynDataSets_Helper::unmarshalObjRef(MemBufferedStream& s) {
  return SynDataSets::_unmarshalObjRef(s);
}

SynDataSets_ptr
SynDataSets::_duplicate(SynDataSets_ptr obj)
{
  if( obj && !obj->_NP_is_nil() )  omni::duplicateObjRef(obj);

  return obj;
}

SynDataSets_ptr
SynDataSets::_narrow(CORBA::Object_ptr obj)
{
  if( !obj || obj->_NP_is_nil() || obj->_NP_is_pseudo() ) return _nil();
  _ptr_type e = (_ptr_type) obj->_PR_getobj()->_realNarrow(_PD_repoId);
  return e ? e : _nil();
}

SynDataSets_ptr
SynDataSets::_nil()
{
  static _objref_SynDataSets* _the_nil_ptr = 0;
  if( !_the_nil_ptr ) {
    omni::nilRefLock().lock();
  if( !_the_nil_ptr )  _the_nil_ptr = new _objref_SynDataSets;
    omni::nilRefLock().unlock();
  }
  return _the_nil_ptr;
}

const char* SynDataSets::_PD_repoId = "IDL:SynDataSets:1.0";

_objref_SynDataSets::~_objref_SynDataSets() {}

_objref_SynDataSets::_objref_SynDataSets(const char* mdri,
   IOP::TaggedProfileList* p, omniIdentity* id, omniLocalIdentity* lid) :
   
   omniObjRef(SynDataSets::_PD_repoId, mdri, p, id, lid)
{
  _PR_setobj(this);
}

void*
_objref_SynDataSets::_ptrToObjRef(const char* id)
{
  if( !strcmp(id, CORBA::Object::_PD_repoId) )
    return (CORBA::Object_ptr) this;
  if( !strcmp(id, SynDataSets::_PD_repoId) )
    return (SynDataSets_ptr) this;
  
  return 0;
}

// Proxy call descriptor class. Mangled signature:
//  _clong_i_clong_i_clong_i_clong_i_cstring
class _0RL_cd_44d3ab5e289658ff_00000000
  : public omniCallDescriptor
{
public:
  inline _0RL_cd_44d3ab5e289658ff_00000000(LocalCallFn lcfn, const char* op, size_t oplen, _CORBA_Boolean oneway, CORBA::Long a_0, CORBA::Long a_1, CORBA::Long a_2, const char* a_3):
     omniCallDescriptor(lcfn, op, oplen, oneway),
     arg_0(a_0),
     arg_1(a_1),
     arg_2(a_2),
     arg_3(a_3) {}

  virtual CORBA::ULong alignedSize(CORBA::ULong size_in);
  virtual void marshalArguments(GIOP_C&);
  
  virtual void unmarshalReturnedValues(GIOP_C&);
    
  inline CORBA::Long result() { return pd_result; }
  CORBA::Long arg_0;
  CORBA::Long arg_1;
  CORBA::Long arg_2;
  const char* arg_3;
  CORBA::Long pd_result;
};

CORBA::ULong _0RL_cd_44d3ab5e289658ff_00000000::alignedSize(CORBA::ULong msgsize)
{
  msgsize = omni::align_to(msgsize, omni::ALIGN_4) + 4;
  
  msgsize = omni::align_to(msgsize, omni::ALIGN_4) + 4;
  
  msgsize = omni::align_to(msgsize, omni::ALIGN_4) + 4;
  
  msgsize = omni::align_to(msgsize, omni::ALIGN_4) + 4;
  msgsize += ((const char*) arg_3) ? strlen((const char*) arg_3) + 1 : 1;
  
  return msgsize;
}

void _0RL_cd_44d3ab5e289658ff_00000000::marshalArguments(GIOP_C& giop_client)
{
  arg_0 >>= giop_client;
  arg_1 >>= giop_client;
  arg_2 >>= giop_client;
  {
    CORBA::ULong _len = (((const char*) arg_3)? strlen((const char*) arg_3) + 1 : 1);
    
    _len >>= giop_client;
    if (_len > 1)
      giop_client.put_char_array((const CORBA::Char *)((const char*)arg_3),_len);
    else {
      if ((const char*) arg_3 == 0 && omniORB::traceLevel > 1)
        _CORBA_null_string_ptr(0);
      CORBA::Char('\0') >>= giop_client;
    }
  }
  
}

void _0RL_cd_44d3ab5e289658ff_00000000::unmarshalReturnedValues(GIOP_C& giop_client)
{
  
  pd_result <<= giop_client;
  
}

// Local call call-back function.
static void
_0RL_lcfn_44d3ab5e289658ff_10000000(omniCallDescriptor* cd, omniServant* svnt)
{
  _0RL_cd_44d3ab5e289658ff_00000000* tcd = (_0RL_cd_44d3ab5e289658ff_00000000*) cd;
  _impl_SynDataSets* impl = (_impl_SynDataSets*) svnt->_ptrToInterface(SynDataSets::_PD_repoId);
  tcd->pd_result = impl->Lock(tcd->arg_0, tcd->arg_1, tcd->arg_2, tcd->arg_3);
}

CORBA::Long _objref_SynDataSets::Lock(CORBA::Long MapId, CORBA::Long StyleId, CORBA::Long Id, const char* ObjName)
{
  _0RL_cd_44d3ab5e289658ff_00000000 _call_desc(_0RL_lcfn_44d3ab5e289658ff_10000000, "Lock", 5, 0, MapId, StyleId, Id, ObjName);
  
  _invoke(_call_desc);
  return _call_desc.result();
}

// Proxy call descriptor class. Mangled signature:
//  _clong_i_clong_i_clong_i_clong_i_clong
class _0RL_cd_44d3ab5e289658ff_20000000
  : public omniCallDescriptor
{
public:
  inline _0RL_cd_44d3ab5e289658ff_20000000(LocalCallFn lcfn, const char* op, size_t oplen, _CORBA_Boolean oneway, CORBA::Long a_0, CORBA::Long a_1, CORBA::Long a_2, CORBA::Long a_3):
     omniCallDescriptor(lcfn, op, oplen, oneway),
     arg_0(a_0),
     arg_1(a_1),
     arg_2(a_2),
     arg_3(a_3) {}

  virtual CORBA::ULong alignedSize(CORBA::ULong size_in);
  virtual void marshalArguments(GIOP_C&);
  
  virtual void unmarshalReturnedValues(GIOP_C&);
    
  inline CORBA::Long result() { return pd_result; }
  CORBA::Long arg_0;
  CORBA::Long arg_1;
  CORBA::Long arg_2;
  CORBA::Long arg_3;
  CORBA::Long pd_result;
};

CORBA::ULong _0RL_cd_44d3ab5e289658ff_20000000::alignedSize(CORBA::ULong msgsize)
{
  msgsize = omni::align_to(msgsize, omni::ALIGN_4) + 4;
  
  msgsize = omni::align_to(msgsize, omni::ALIGN_4) + 4;
  
  msgsize = omni::align_to(msgsize, omni::ALIGN_4) + 4;
  
  msgsize = omni::align_to(msgsize, omni::ALIGN_4) + 4;
  
  return msgsize;
}

void _0RL_cd_44d3ab5e289658ff_20000000::marshalArguments(GIOP_C& giop_client)
{
  arg_0 >>= giop_client;
  arg_1 >>= giop_client;
  arg_2 >>= giop_client;
  arg_3 >>= giop_client;
  
}

void _0RL_cd_44d3ab5e289658ff_20000000::unmarshalReturnedValues(GIOP_C& giop_client)
{
  
  pd_result <<= giop_client;
  
}

// Local call call-back function.
static void
_0RL_lcfn_44d3ab5e289658ff_30000000(omniCallDescriptor* cd, omniServant* svnt)
{
  _0RL_cd_44d3ab5e289658ff_20000000* tcd = (_0RL_cd_44d3ab5e289658ff_20000000*) cd;
  _impl_SynDataSets* impl = (_impl_SynDataSets*) svnt->_ptrToInterface(SynDataSets::_PD_repoId);
  tcd->pd_result = impl->UnLock(tcd->arg_0, tcd->arg_1, tcd->arg_2, tcd->arg_3);
}

CORBA::Long _objref_SynDataSets::UnLock(CORBA::Long MapId, CORBA::Long StyleId, CORBA::Long Id, CORBA::Long index)
{
  _0RL_cd_44d3ab5e289658ff_20000000 _call_desc(_0RL_lcfn_44d3ab5e289658ff_30000000, "UnLock", 7, 0, MapId, StyleId, Id, index);
  
  _invoke(_call_desc);
  return _call_desc.result();
}

// Proxy call descriptor class. Mangled signature:
//  _cany_i_clong_i_clong_i_clong
class _0RL_cd_44d3ab5e289658ff_40000000
  : public omniCallDescriptor
{
public:
  inline _0RL_cd_44d3ab5e289658ff_40000000(LocalCallFn lcfn, const char* op, size_t oplen, _CORBA_Boolean oneway, CORBA::Long a_0, CORBA::Long a_1, CORBA::Long a_2):
     omniCallDescriptor(lcfn, op, oplen, oneway),
     arg_0(a_0),
     arg_1(a_1),
     arg_2(a_2) {}

  virtual CORBA::ULong alignedSize(CORBA::ULong size_in);
  virtual void marshalArguments(GIOP_C&);
  
  virtual void unmarshalReturnedValues(GIOP_C&);
    
  inline CORBA::Any* result() { return pd_result; }
  CORBA::Long arg_0;
  CORBA::Long arg_1;
  CORBA::Long arg_2;
  CORBA::Any* pd_result;
};

CORBA::ULong _0RL_cd_44d3ab5e289658ff_40000000::alignedSize(CORBA::ULong msgsize)
{
  msgsize = omni::align_to(msgsize, omni::ALIGN_4) + 4;
  
  msgsize = omni::align_to(msgsize, omni::ALIGN_4) + 4;
  
  msgsize = omni::align_to(msgsize, omni::ALIGN_4) + 4;
  
  return msgsize;
}

void _0RL_cd_44d3ab5e289658ff_40000000::marshalArguments(GIOP_C& giop_client)
{
  arg_0 >>= giop_client;
  arg_1 >>= giop_client;
  arg_2 >>= giop_client;
  
}

void _0RL_cd_44d3ab5e289658ff_40000000::unmarshalReturnedValues(GIOP_C& giop_client)
{
  
  pd_result = new CORBA::Any;
  
  *pd_result <<= giop_client;
  
}

// Local call call-back function.
static void
_0RL_lcfn_44d3ab5e289658ff_50000000(omniCallDescriptor* cd, omniServant* svnt)
{
  _0RL_cd_44d3ab5e289658ff_40000000* tcd = (_0RL_cd_44d3ab5e289658ff_40000000*) cd;
  _impl_SynDataSets* impl = (_impl_SynDataSets*) svnt->_ptrToInterface(SynDataSets::_PD_repoId);
  tcd->pd_result = impl->GetData(tcd->arg_0, tcd->arg_1, tcd->arg_2);
}

CORBA::Any* _objref_SynDataSets::GetData(CORBA::Long MapId, CORBA::Long StyleId, CORBA::Long Id)
{
  _0RL_cd_44d3ab5e289658ff_40000000 _call_desc(_0RL_lcfn_44d3ab5e289658ff_50000000, "GetData", 8, 0, MapId, StyleId, Id);
  
  _invoke(_call_desc);
  return _call_desc.result();
}

// Proxy call descriptor class. Mangled signature:
//  _cany_i_clong_i_clong_i_clong_i_clong
class _0RL_cd_44d3ab5e289658ff_60000000
  : public omniCallDescriptor
{
public:
  inline _0RL_cd_44d3ab5e289658ff_60000000(LocalCallFn lcfn, const char* op, size_t oplen, _CORBA_Boolean oneway, CORBA::Long a_0, CORBA::Long a_1, CORBA::Long a_2, CORBA::Long a_3):
     omniCallDescriptor(lcfn, op, oplen, oneway),
     arg_0(a_0),
     arg_1(a_1),
     arg_2(a_2),
     arg_3(a_3) {}

  virtual CORBA::ULong alignedSize(CORBA::ULong size_in);
  virtual void marshalArguments(GIOP_C&);
  
  virtual void unmarshalReturnedValues(GIOP_C&);
    
  inline CORBA::Any* result() { return pd_result; }
  CORBA::Long arg_0;
  CORBA::Long arg_1;
  CORBA::Long arg_2;
  CORBA::Long arg_3;
  CORBA::Any* pd_result;
};

CORBA::ULong _0RL_cd_44d3ab5e289658ff_60000000::alignedSize(CORBA::ULong msgsize)
{
  msgsize = omni::align_to(msgsize, omni::ALIGN_4) + 4;
  
  msgsize = omni::align_to(msgsize, omni::ALIGN_4) + 4;
  
  msgsize = omni::align_to(msgsize, omni::ALIGN_4) + 4;
  
  msgsize = omni::align_to(msgsize, omni::ALIGN_4) + 4;
  
  return msgsize;
}

void _0RL_cd_44d3ab5e289658ff_60000000::marshalArguments(GIOP_C& giop_client)
{
  arg_0 >>= giop_client;
  arg_1 >>= giop_client;
  arg_2 >>= giop_client;
  arg_3 >>= giop_client;
  
}

void _0RL_cd_44d3ab5e289658ff_60000000::unmarshalReturnedValues(GIOP_C& giop_client)
{
  
  pd_result = new CORBA::Any;
  
  *pd_result <<= giop_client;
  
}

// Local call call-back function.
static void
_0RL_lcfn_44d3ab5e289658ff_70000000(omniCallDescriptor* cd, omniServant* svnt)
{
  _0RL_cd_44d3ab5e289658ff_60000000* tcd = (_0RL_cd_44d3ab5e289658ff_60000000*) cd;
  _impl_SynDataSets* impl = (_impl_SynDataSets*) svnt->_ptrToInterface(SynDataSets::_PD_repoId);
  tcd->pd_result = impl->GetDataMember(tcd->arg_0, tcd->arg_1, tcd->arg_2, tcd->arg_3);
}

CORBA::Any* _objref_SynDataSets::GetDataMember(CORBA::Long MapId, CORBA::Long StyleId, CORBA::Long Id, CORBA::Long MemberId)
{
  _0RL_cd_44d3ab5e289658ff_60000000 _call_desc(_0RL_lcfn_44d3ab5e289658ff_70000000, "GetDataMember", 14, 0, MapId, StyleId, Id, MemberId);
  
  _invoke(_call_desc);
  return _call_desc.result();
}

// Proxy call descriptor class. Mangled signature:
//  _clong_i_clong_i_clong_i_clong_i_cany
class _0RL_cd_44d3ab5e289658ff_80000000
  : public omniCallDescriptor
{
public:
  inline _0RL_cd_44d3ab5e289658ff_80000000(LocalCallFn lcfn, const char* op, size_t oplen, _CORBA_Boolean oneway, CORBA::Long a_0, CORBA::Long a_1, CORBA::Long a_2, const CORBA::Any& a_3):
     omniCallDescriptor(lcfn, op, oplen, oneway),
     arg_0(a_0),
     arg_1(a_1),
     arg_2(a_2),
     arg_3(a_3) {}

  virtual CORBA::ULong alignedSize(CORBA::ULong size_in);
  virtual void marshalArguments(GIOP_C&);
  
  virtual void unmarshalReturnedValues(GIOP_C&);
    
  inline CORBA::Long result() { return pd_result; }
  CORBA::Long arg_0;
  CORBA::Long arg_1;
  CORBA::Long arg_2;
  const CORBA::Any& arg_3;
  CORBA::Long pd_result;
};

CORBA::ULong _0RL_cd_44d3ab5e289658ff_80000000::alignedSize(CORBA::ULong msgsize)
{
  msgsize = omni::align_to(msgsize, omni::ALIGN_4) + 4;
  
  msgsize = omni::align_to(msgsize, omni::ALIGN_4) + 4;
  
  msgsize = omni::align_to(msgsize, omni::ALIGN_4) + 4;
  
  msgsize = arg_3._NP_alignedSize(msgsize);
  
  return msgsize;
}

void _0RL_cd_44d3ab5e289658ff_80000000::marshalArguments(GIOP_C& giop_client)
{
  arg_0 >>= giop_client;
  arg_1 >>= giop_client;
  arg_2 >>= giop_client;
  arg_3 >>= giop_client;
  
}

void _0RL_cd_44d3ab5e289658ff_80000000::unmarshalReturnedValues(GIOP_C& giop_client)
{
  
  pd_result <<= giop_client;
  
}

// Local call call-back function.
static void
_0RL_lcfn_44d3ab5e289658ff_90000000(omniCallDescriptor* cd, omniServant* svnt)
{
  _0RL_cd_44d3ab5e289658ff_80000000* tcd = (_0RL_cd_44d3ab5e289658ff_80000000*) cd;
  _impl_SynDataSets* impl = (_impl_SynDataSets*) svnt->_ptrToInterface(SynDataSets::_PD_repoId);
  tcd->pd_result = impl->SetData(tcd->arg_0, tcd->arg_1, tcd->arg_2, tcd->arg_3);
}

CORBA::Long _objref_SynDataSets::SetData(CORBA::Long MapId, CORBA::Long StyleId, CORBA::Long Id, const CORBA::Any& data)
{
  _0RL_cd_44d3ab5e289658ff_80000000 _call_desc(_0RL_lcfn_44d3ab5e289658ff_90000000, "SetData", 8, 0, MapId, StyleId, Id, data);
  
  _invoke(_call_desc);
  return _call_desc.result();
}

// Proxy call descriptor class. Mangled signature:
//  _clong_i_clong_i_clong_i_clong_i_clong_i_cany
class _0RL_cd_44d3ab5e289658ff_a0000000
  : public omniCallDescriptor
{
public:
  inline _0RL_cd_44d3ab5e289658ff_a0000000(LocalCallFn lcfn, const char* op, size_t oplen, _CORBA_Boolean oneway, CORBA::Long a_0, CORBA::Long a_1, CORBA::Long a_2, CORBA::Long a_3, const CORBA::Any& a_4):
     omniCallDescriptor(lcfn, op, oplen, oneway),
     arg_0(a_0),
     arg_1(a_1),
     arg_2(a_2),
     arg_3(a_3),
     arg_4(a_4) {}

  virtual CORBA::ULong alignedSize(CORBA::ULong size_in);
  virtual void marshalArguments(GIOP_C&);
  
  virtual void unmarshalReturnedValues(GIOP_C&);
    
  inline CORBA::Long result() { return pd_result; }
  CORBA::Long arg_0;
  CORBA::Long arg_1;
  CORBA::Long arg_2;
  CORBA::Long arg_3;
  const CORBA::Any& arg_4;
  CORBA::Long pd_result;
};

CORBA::ULong _0RL_cd_44d3ab5e289658ff_a0000000::alignedSize(CORBA::ULong msgsize)
{
  msgsize = omni::align_to(msgsize, omni::ALIGN_4) + 4;
  
  msgsize = omni::align_to(msgsize, omni::ALIGN_4) + 4;
  
  msgsize = omni::align_to(msgsize, omni::ALIGN_4) + 4;
  
  msgsize = omni::align_to(msgsize, omni::ALIGN_4) + 4;
  
  msgsize = arg_4._NP_alignedSize(msgsize);
  
  return msgsize;
}

void _0RL_cd_44d3ab5e289658ff_a0000000::marshalArguments(GIOP_C& giop_client)
{
  arg_0 >>= giop_client;
  arg_1 >>= giop_client;
  arg_2 >>= giop_client;
  arg_3 >>= giop_client;
  arg_4 >>= giop_client;
  
}

void _0RL_cd_44d3ab5e289658ff_a0000000::unmarshalReturnedValues(GIOP_C& giop_client)
{
  
  pd_result <<= giop_client;
  
}

// Local call call-back function.
static void
_0RL_lcfn_44d3ab5e289658ff_b0000000(omniCallDescriptor* cd, omniServant* svnt)
{
  _0RL_cd_44d3ab5e289658ff_a0000000* tcd = (_0RL_cd_44d3ab5e289658ff_a0000000*) cd;
  _impl_SynDataSets* impl = (_impl_SynDataSets*) svnt->_ptrToInterface(SynDataSets::_PD_repoId);
  tcd->pd_result = impl->SetDataMember(tcd->arg_0, tcd->arg_1, tcd->arg_2, tcd->arg_3, tcd->arg_4);
}

CORBA::Long _objref_SynDataSets::SetDataMember(CORBA::Long MapId, CORBA::Long StyleId, CORBA::Long Id, CORBA::Long MemberId, const CORBA::Any& data)
{
  _0RL_cd_44d3ab5e289658ff_a0000000 _call_desc(_0RL_lcfn_44d3ab5e289658ff_b0000000, "SetDataMember", 14, 0, MapId, StyleId, Id, MemberId, data);
  
  _invoke(_call_desc);
  return _call_desc.result();
}

// Proxy call descriptor class. Mangled signature:
//  _cany_i_clong_i_clong_i_clong_o_clong_i_cstring
class _0RL_cd_44d3ab5e289658ff_c0000000
  : public omniCallDescriptor
{
public:
  inline _0RL_cd_44d3ab5e289658ff_c0000000(LocalCallFn lcfn, const char* op, size_t oplen, _CORBA_Boolean oneway, CORBA::Long a_0, CORBA::Long a_1, CORBA::Long a_2, CORBA::Long& a_3, const char* a_4):
     omniCallDescriptor(lcfn, op, oplen, oneway),
     arg_0(a_0),
     arg_1(a_1),
     arg_2(a_2),
     arg_3(a_3),
     arg_4(a_4) {}

  virtual CORBA::ULong alignedSize(CORBA::ULong size_in);
  virtual void marshalArguments(GIOP_C&);
  
  virtual void unmarshalReturnedValues(GIOP_C&);
    
  inline CORBA::Any* result() { return pd_result; }
  CORBA::Long arg_0;
  CORBA::Long arg_1;
  CORBA::Long arg_2;
  CORBA::Long& arg_3;
  const char* arg_4;
  CORBA::Any* pd_result;
};

CORBA::ULong _0RL_cd_44d3ab5e289658ff_c0000000::alignedSize(CORBA::ULong msgsize)
{
  msgsize = omni::align_to(msgsize, omni::ALIGN_4) + 4;
  
  msgsize = omni::align_to(msgsize, omni::ALIGN_4) + 4;
  
  msgsize = omni::align_to(msgsize, omni::ALIGN_4) + 4;
  
  msgsize = omni::align_to(msgsize, omni::ALIGN_4) + 4;
  msgsize += ((const char*) arg_4) ? strlen((const char*) arg_4) + 1 : 1;
  
  return msgsize;
}

void _0RL_cd_44d3ab5e289658ff_c0000000::marshalArguments(GIOP_C& giop_client)
{
  arg_0 >>= giop_client;
  arg_1 >>= giop_client;
  arg_2 >>= giop_client;
  {
    CORBA::ULong _len = (((const char*) arg_4)? strlen((const char*) arg_4) + 1 : 1);
    
    _len >>= giop_client;
    if (_len > 1)
      giop_client.put_char_array((const CORBA::Char *)((const char*)arg_4),_len);
    else {
      if ((const char*) arg_4 == 0 && omniORB::traceLevel > 1)
        _CORBA_null_string_ptr(0);
      CORBA::Char('\0') >>= giop_client;
    }
  }
  
}

void _0RL_cd_44d3ab5e289658ff_c0000000::unmarshalReturnedValues(GIOP_C& giop_client)
{
  
  pd_result = new CORBA::Any;
  
  *pd_result <<= giop_client;
  arg_3 <<= giop_client;
  
}

// Local call call-back function.
static void
_0RL_lcfn_44d3ab5e289658ff_d0000000(omniCallDescriptor* cd, omniServant* svnt)
{
  _0RL_cd_44d3ab5e289658ff_c0000000* tcd = (_0RL_cd_44d3ab5e289658ff_c0000000*) cd;
  _impl_SynDataSets* impl = (_impl_SynDataSets*) svnt->_ptrToInterface(SynDataSets::_PD_repoId);
  tcd->pd_result = impl->GetDataWithLock(tcd->arg_0, tcd->arg_1, tcd->arg_2, tcd->arg_3, tcd->arg_4);
}

CORBA::Any* _objref_SynDataSets::GetDataWithLock(CORBA::Long MapId, CORBA::Long StyleId, CORBA::Long Id, CORBA::Long& Result, const char* ObjName)
{
  _0RL_cd_44d3ab5e289658ff_c0000000 _call_desc(_0RL_lcfn_44d3ab5e289658ff_d0000000, "GetDataWithLock", 16, 0, MapId, StyleId, Id, Result, ObjName);
  
  _invoke(_call_desc);
  return _call_desc.result();
}

// Proxy call descriptor class. Mangled signature:
//  _cany_i_clong_i_clong_i_clong_i_clong_i_cany_o_clong_i_cstring
class _0RL_cd_44d3ab5e289658ff_e0000000
  : public omniCallDescriptor
{
public:
  inline _0RL_cd_44d3ab5e289658ff_e0000000(LocalCallFn lcfn, const char* op, size_t oplen, _CORBA_Boolean oneway, CORBA::Long a_0, CORBA::Long a_1, CORBA::Long a_2, CORBA::Long a_3, const CORBA::Any& a_4, CORBA::Long& a_5, const char* a_6):
     omniCallDescriptor(lcfn, op, oplen, oneway),
     arg_0(a_0),
     arg_1(a_1),
     arg_2(a_2),
     arg_3(a_3),
     arg_4(a_4),
     arg_5(a_5),
     arg_6(a_6) {}

  virtual CORBA::ULong alignedSize(CORBA::ULong size_in);
  virtual void marshalArguments(GIOP_C&);
  
  virtual void unmarshalReturnedValues(GIOP_C&);
    
  inline CORBA::Any* result() { return pd_result; }
  CORBA::Long arg_0;
  CORBA::Long arg_1;
  CORBA::Long arg_2;
  CORBA::Long arg_3;
  const CORBA::Any& arg_4;
  CORBA::Long& arg_5;
  const char* arg_6;
  CORBA::Any* pd_result;
};

CORBA::ULong _0RL_cd_44d3ab5e289658ff_e0000000::alignedSize(CORBA::ULong msgsize)
{
  msgsize = omni::align_to(msgsize, omni::ALIGN_4) + 4;
  
  msgsize = omni::align_to(msgsize, omni::ALIGN_4) + 4;
  
  msgsize = omni::align_to(msgsize, omni::ALIGN_4) + 4;
  
  msgsize = omni::align_to(msgsize, omni::ALIGN_4) + 4;
  
  msgsize = arg_4._NP_alignedSize(msgsize);
  
  msgsize = omni::align_to(msgsize, omni::ALIGN_4) + 4;
  msgsize += ((const char*) arg_6) ? strlen((const char*) arg_6) + 1 : 1;
  
  return msgsize;
}

void _0RL_cd_44d3ab5e289658ff_e0000000::marshalArguments(GIOP_C& giop_client)
{
  arg_0 >>= giop_client;
  arg_1 >>= giop_client;
  arg_2 >>= giop_client;
  arg_3 >>= giop_client;
  arg_4 >>= giop_client;
  {
    CORBA::ULong _len = (((const char*) arg_6)? strlen((const char*) arg_6) + 1 : 1);
    
    _len >>= giop_client;
    if (_len > 1)
      giop_client.put_char_array((const CORBA::Char *)((const char*)arg_6),_len);
    else {
      if ((const char*) arg_6 == 0 && omniORB::traceLevel > 1)
        _CORBA_null_string_ptr(0);
      CORBA::Char('\0') >>= giop_client;
    }
  }
  
}

void _0RL_cd_44d3ab5e289658ff_e0000000::unmarshalReturnedValues(GIOP_C& giop_client)
{
  
  pd_result = new CORBA::Any;
  
  *pd_result <<= giop_client;
  arg_5 <<= giop_client;
  
}

// Local call call-back function.
static void
_0RL_lcfn_44d3ab5e289658ff_f0000000(omniCallDescriptor* cd, omniServant* svnt)
{
  _0RL_cd_44d3ab5e289658ff_e0000000* tcd = (_0RL_cd_44d3ab5e289658ff_e0000000*) cd;
  _impl_SynDataSets* impl = (_impl_SynDataSets*) svnt->_ptrToInterface(SynDataSets::_PD_repoId);
  tcd->pd_result = impl->GetDataMemberWithLock(tcd->arg_0, tcd->arg_1, tcd->arg_2, tcd->arg_3, tcd->arg_4, tcd->arg_5, tcd->arg_6);
}

CORBA::Any* _objref_SynDataSets::GetDataMemberWithLock(CORBA::Long MapId, CORBA::Long StyleId, CORBA::Long Id, CORBA::Long MemberId, const CORBA::Any& data, CORBA::Long& Result, const char* ObjName)
{
  _0RL_cd_44d3ab5e289658ff_e0000000 _call_desc(_0RL_lcfn_44d3ab5e289658ff_f0000000, "GetDataMemberWithLock", 22, 0, MapId, StyleId, Id, MemberId, data, Result, ObjName);
  
  _invoke(_call_desc);
  return _call_desc.result();
}

// Proxy call descriptor class. Mangled signature:
//  _clong_i_clong_i_clong_i_clong_i_cany_o_clong_i_cstring
class _0RL_cd_44d3ab5e289658ff_01000000
  : public omniCallDescriptor
{
public:
  inline _0RL_cd_44d3ab5e289658ff_01000000(LocalCallFn lcfn, const char* op, size_t oplen, _CORBA_Boolean oneway, CORBA::Long a_0, CORBA::Long a_1, CORBA::Long a_2, const CORBA::Any& a_3, CORBA::Long& a_4, const char* a_5):
     omniCallDescriptor(lcfn, op, oplen, oneway),
     arg_0(a_0),
     arg_1(a_1),
     arg_2(a_2),
     arg_3(a_3),
     arg_4(a_4),
     arg_5(a_5) {}

  virtual CORBA::ULong alignedSize(CORBA::ULong size_in);
  virtual void marshalArguments(GIOP_C&);
  
  virtual void unmarshalReturnedValues(GIOP_C&);
    
  inline CORBA::Long result() { return pd_result; }
  CORBA::Long arg_0;
  CORBA::Long arg_1;
  CORBA::Long arg_2;
  const CORBA::Any& arg_3;
  CORBA::Long& arg_4;
  const char* arg_5;
  CORBA::Long pd_result;
};

CORBA::ULong _0RL_cd_44d3ab5e289658ff_01000000::alignedSize(CORBA::ULong msgsize)
{
  msgsize = omni::align_to(msgsize, omni::ALIGN_4) + 4;
  
  msgsize = omni::align_to(msgsize, omni::ALIGN_4) + 4;
  
  msgsize = omni::align_to(msgsize, omni::ALIGN_4) + 4;
  
  msgsize = arg_3._NP_alignedSize(msgsize);
  
  msgsize = omni::align_to(msgsize, omni::ALIGN_4) + 4;
  msgsize += ((const char*) arg_5) ? strlen((const char*) arg_5) + 1 : 1;
  
  return msgsize;
}

void _0RL_cd_44d3ab5e289658ff_01000000::marshalArguments(GIOP_C& giop_client)
{
  arg_0 >>= giop_client;
  arg_1 >>= giop_client;
  arg_2 >>= giop_client;
  arg_3 >>= giop_client;
  {
    CORBA::ULong _len = (((const char*) arg_5)? strlen((const char*) arg_5) + 1 : 1);
    
    _len >>= giop_client;
    if (_len > 1)
      giop_client.put_char_array((const CORBA::Char *)((const char*)arg_5),_len);
    else {
      if ((const char*) arg_5 == 0 && omniORB::traceLevel > 1)
        _CORBA_null_string_ptr(0);
      CORBA::Char('\0') >>= giop_client;
    }
  }
  
}

void _0RL_cd_44d3ab5e289658ff_01000000::unmarshalReturnedValues(GIOP_C& giop_client)
{
  
  pd_result <<= giop_client;
  arg_4 <<= giop_client;
  
}

// Local call call-back function.
static void
_0RL_lcfn_44d3ab5e289658ff_11000000(omniCallDescriptor* cd, omniServant* svnt)
{
  _0RL_cd_44d3ab5e289658ff_01000000* tcd = (_0RL_cd_44d3ab5e289658ff_01000000*) cd;
  _impl_SynDataSets* impl = (_impl_SynDataSets*) svnt->_ptrToInterface(SynDataSets::_PD_repoId);
  tcd->pd_result = impl->SetDataWithLock(tcd->arg_0, tcd->arg_1, tcd->arg_2, tcd->arg_3, tcd->arg_4, tcd->arg_5);
}

CORBA::Long _objref_SynDataSets::SetDataWithLock(CORBA::Long MapId, CORBA::Long StyleId, CORBA::Long Id, const CORBA::Any& data, CORBA::Long& Result, const char* ObjName)
{
  _0RL_cd_44d3ab5e289658ff_01000000 _call_desc(_0RL_lcfn_44d3ab5e289658ff_11000000, "SetDataWithLock", 16, 0, MapId, StyleId, Id, data, Result, ObjName);
  
  _invoke(_call_desc);
  return _call_desc.result();
}

// Proxy call descriptor class. Mangled signature:
//  _clong_i_clong_i_clong_i_clong_i_clong_i_cany_o_clong_i_cstring
class _0RL_cd_44d3ab5e289658ff_21000000
  : public omniCallDescriptor
{
public:
  inline _0RL_cd_44d3ab5e289658ff_21000000(LocalCallFn lcfn, const char* op, size_t oplen, _CORBA_Boolean oneway, CORBA::Long a_0, CORBA::Long a_1, CORBA::Long a_2, CORBA::Long a_3, const CORBA::Any& a_4, CORBA::Long& a_5, const char* a_6):
     omniCallDescriptor(lcfn, op, oplen, oneway),
     arg_0(a_0),
     arg_1(a_1),
     arg_2(a_2),
     arg_3(a_3),
     arg_4(a_4),
     arg_5(a_5),
     arg_6(a_6) {}

  virtual CORBA::ULong alignedSize(CORBA::ULong size_in);
  virtual void marshalArguments(GIOP_C&);
  
  virtual void unmarshalReturnedValues(GIOP_C&);
    
  inline CORBA::Long result() { return pd_result; }
  CORBA::Long arg_0;
  CORBA::Long arg_1;
  CORBA::Long arg_2;
  CORBA::Long arg_3;
  const CORBA::Any& arg_4;
  CORBA::Long& arg_5;
  const char* arg_6;
  CORBA::Long pd_result;
};

CORBA::ULong _0RL_cd_44d3ab5e289658ff_21000000::alignedSize(CORBA::ULong msgsize)
{
  msgsize = omni::align_to(msgsize, omni::ALIGN_4) + 4;
  
  msgsize = omni::align_to(msgsize, omni::ALIGN_4) + 4;
  
  msgsize = omni::align_to(msgsize, omni::ALIGN_4) + 4;
  
  msgsize = omni::align_to(msgsize, omni::ALIGN_4) + 4;
  
  msgsize = arg_4._NP_alignedSize(msgsize);
  
  msgsize = omni::align_to(msgsize, omni::ALIGN_4) + 4;
  msgsize += ((const char*) arg_6) ? strlen((const char*) arg_6) + 1 : 1;
  
  return msgsize;
}

void _0RL_cd_44d3ab5e289658ff_21000000::marshalArguments(GIOP_C& giop_client)
{
  arg_0 >>= giop_client;
  arg_1 >>= giop_client;
  arg_2 >>= giop_client;
  arg_3 >>= giop_client;
  arg_4 >>= giop_client;
  {
    CORBA::ULong _len = (((const char*) arg_6)? strlen((const char*) arg_6) + 1 : 1);
    
    _len >>= giop_client;
    if (_len > 1)
      giop_client.put_char_array((const CORBA::Char *)((const char*)arg_6),_len);
    else {
      if ((const char*) arg_6 == 0 && omniORB::traceLevel > 1)
        _CORBA_null_string_ptr(0);
      CORBA::Char('\0') >>= giop_client;
    }
  }
  
}

void _0RL_cd_44d3ab5e289658ff_21000000::unmarshalReturnedValues(GIOP_C& giop_client)
{
  
  pd_result <<= giop_client;
  arg_5 <<= giop_client;
  
}

// Local call call-back function.
static void
_0RL_lcfn_44d3ab5e289658ff_31000000(omniCallDescriptor* cd, omniServant* svnt)
{
  _0RL_cd_44d3ab5e289658ff_21000000* tcd = (_0RL_cd_44d3ab5e289658ff_21000000*) cd;
  _impl_SynDataSets* impl = (_impl_SynDataSets*) svnt->_ptrToInterface(SynDataSets::_PD_repoId);
  tcd->pd_result = impl->SetDataMemberWithLock(tcd->arg_0, tcd->arg_1, tcd->arg_2, tcd->arg_3, tcd->arg_4, tcd->arg_5, tcd->arg_6);
}

CORBA::Long _objref_SynDataSets::SetDataMemberWithLock(CORBA::Long MapId, CORBA::Long StyleId, CORBA::Long Id, CORBA::Long MemberId, const CORBA::Any& data, CORBA::Long& Result, const char* ObjName)
{
  _0RL_cd_44d3ab5e289658ff_21000000 _call_desc(_0RL_lcfn_44d3ab5e289658ff_31000000, "SetDataMemberWithLock", 22, 0, MapId, StyleId, Id, MemberId, data, Result, ObjName);
  
  _invoke(_call_desc);
  return _call_desc.result();
}

// Local call call-back function.
static void
_0RL_lcfn_44d3ab5e289658ff_41000000(omniCallDescriptor* cd, omniServant* svnt)
{
  _0RL_cd_44d3ab5e289658ff_c0000000* tcd = (_0RL_cd_44d3ab5e289658ff_c0000000*) cd;
  _impl_SynDataSets* impl = (_impl_SynDataSets*) svnt->_ptrToInterface(SynDataSets::_PD_repoId);
  tcd->pd_result = impl->GetDataWithOnce(tcd->arg_0, tcd->arg_1, tcd->arg_2, tcd->arg_3, tcd->arg_4);
}

CORBA::Any* _objref_SynDataSets::GetDataWithOnce(CORBA::Long MapId, CORBA::Long StyleId, CORBA::Long Id, CORBA::Long& Result, const char* ObjName)
{
  _0RL_cd_44d3ab5e289658ff_c0000000 _call_desc(_0RL_lcfn_44d3ab5e289658ff_41000000, "GetDataWithOnce", 16, 0, MapId, StyleId, Id, Result, ObjName);
  
  _invoke(_call_desc);
  return _call_desc.result();
}

// Proxy call descriptor class. Mangled signature:
//  _cany_i_clong_i_clong_i_clong_i_clong_o_clong_i_cstring
class _0RL_cd_44d3ab5e289658ff_51000000
  : public omniCallDescriptor
{
public:
  inline _0RL_cd_44d3ab5e289658ff_51000000(LocalCallFn lcfn, const char* op, size_t oplen, _CORBA_Boolean oneway, CORBA::Long a_0, CORBA::Long a_1, CORBA::Long a_2, CORBA::Long a_3, CORBA::Long& a_4, const char* a_5):
     omniCallDescriptor(lcfn, op, oplen, oneway),
     arg_0(a_0),
     arg_1(a_1),
     arg_2(a_2),
     arg_3(a_3),
     arg_4(a_4),
     arg_5(a_5) {}

  virtual CORBA::ULong alignedSize(CORBA::ULong size_in);
  virtual void marshalArguments(GIOP_C&);
  
  virtual void unmarshalReturnedValues(GIOP_C&);
    
  inline CORBA::Any* result() { return pd_result; }
  CORBA::Long arg_0;
  CORBA::Long arg_1;
  CORBA::Long arg_2;
  CORBA::Long arg_3;
  CORBA::Long& arg_4;
  const char* arg_5;
  CORBA::Any* pd_result;
};

CORBA::ULong _0RL_cd_44d3ab5e289658ff_51000000::alignedSize(CORBA::ULong msgsize)
{
  msgsize = omni::align_to(msgsize, omni::ALIGN_4) + 4;
  
  msgsize = omni::align_to(msgsize, omni::ALIGN_4) + 4;
  
  msgsize = omni::align_to(msgsize, omni::ALIGN_4) + 4;
  
  msgsize = omni::align_to(msgsize, omni::ALIGN_4) + 4;
  
  msgsize = omni::align_to(msgsize, omni::ALIGN_4) + 4;
  msgsize += ((const char*) arg_5) ? strlen((const char*) arg_5) + 1 : 1;
  
  return msgsize;
}

void _0RL_cd_44d3ab5e289658ff_51000000::marshalArguments(GIOP_C& giop_client)
{
  arg_0 >>= giop_client;
  arg_1 >>= giop_client;
  arg_2 >>= giop_client;
  arg_3 >>= giop_client;
  {
    CORBA::ULong _len = (((const char*) arg_5)? strlen((const char*) arg_5) + 1 : 1);
    
    _len >>= giop_client;
    if (_len > 1)
      giop_client.put_char_array((const CORBA::Char *)((const char*)arg_5),_len);
    else {
      if ((const char*) arg_5 == 0 && omniORB::traceLevel > 1)
        _CORBA_null_string_ptr(0);
      CORBA::Char('\0') >>= giop_client;
    }
  }
  
}

void _0RL_cd_44d3ab5e289658ff_51000000::unmarshalReturnedValues(GIOP_C& giop_client)
{
  
  pd_result = new CORBA::Any;
  
  *pd_result <<= giop_client;
  arg_4 <<= giop_client;
  
}

// Local call call-back function.
static void
_0RL_lcfn_44d3ab5e289658ff_61000000(omniCallDescriptor* cd, omniServant* svnt)
{
  _0RL_cd_44d3ab5e289658ff_51000000* tcd = (_0RL_cd_44d3ab5e289658ff_51000000*) cd;
  _impl_SynDataSets* impl = (_impl_SynDataSets*) svnt->_ptrToInterface(SynDataSets::_PD_repoId);
  tcd->pd_result = impl->GetDataMemberWithOnce(tcd->arg_0, tcd->arg_1, tcd->arg_2, tcd->arg_3, tcd->arg_4, tcd->arg_5);
}

CORBA::Any* _objref_SynDataSets::GetDataMemberWithOnce(CORBA::Long MapId, CORBA::Long StyleId, CORBA::Long Id, CORBA::Long MemberId, CORBA::Long& Result, const char* ObjName)
{
  _0RL_cd_44d3ab5e289658ff_51000000 _call_desc(_0RL_lcfn_44d3ab5e289658ff_61000000, "GetDataMemberWithOnce", 22, 0, MapId, StyleId, Id, MemberId, Result, ObjName);
  
  _invoke(_call_desc);
  return _call_desc.result();
}

// Local call call-back function.
static void
_0RL_lcfn_44d3ab5e289658ff_71000000(omniCallDescriptor* cd, omniServant* svnt)
{
  _0RL_cd_44d3ab5e289658ff_01000000* tcd = (_0RL_cd_44d3ab5e289658ff_01000000*) cd;
  _impl_SynDataSets* impl = (_impl_SynDataSets*) svnt->_ptrToInterface(SynDataSets::_PD_repoId);
  tcd->pd_result = impl->SetDataWithOnce(tcd->arg_0, tcd->arg_1, tcd->arg_2, tcd->arg_3, tcd->arg_4, tcd->arg_5);
}

CORBA::Long _objref_SynDataSets::SetDataWithOnce(CORBA::Long MapId, CORBA::Long StyleId, CORBA::Long Id, const CORBA::Any& data, CORBA::Long& Result, const char* ObjName)
{
  _0RL_cd_44d3ab5e289658ff_01000000 _call_desc(_0RL_lcfn_44d3ab5e289658ff_71000000, "SetDataWithOnce", 16, 0, MapId, StyleId, Id, data, Result, ObjName);
  
  _invoke(_call_desc);
  return _call_desc.result();
}

// Local call call-back function.
static void
_0RL_lcfn_44d3ab5e289658ff_81000000(omniCallDescriptor* cd, omniServant* svnt)
{
  _0RL_cd_44d3ab5e289658ff_21000000* tcd = (_0RL_cd_44d3ab5e289658ff_21000000*) cd;
  _impl_SynDataSets* impl = (_impl_SynDataSets*) svnt->_ptrToInterface(SynDataSets::_PD_repoId);
  tcd->pd_result = impl->SetDataMemberWithOnce(tcd->arg_0, tcd->arg_1, tcd->arg_2, tcd->arg_3, tcd->arg_4, tcd->arg_5, tcd->arg_6);
}

CORBA::Long _objref_SynDataSets::SetDataMemberWithOnce(CORBA::Long MapId, CORBA::Long StyleId, CORBA::Long Id, CORBA::Long MemberId, const CORBA::Any& data, CORBA::Long& Result, const char* ObjName)
{
  _0RL_cd_44d3ab5e289658ff_21000000 _call_desc(_0RL_lcfn_44d3ab5e289658ff_81000000, "SetDataMemberWithOnce", 22, 0, MapId, StyleId, Id, MemberId, data, Result, ObjName);
  
  _invoke(_call_desc);
  return _call_desc.result();
}

// Proxy call descriptor class. Mangled signature:
//  _cDataSeq_i_clong_i_clong
class _0RL_cd_44d3ab5e289658ff_91000000
  : public omniCallDescriptor
{
public:
  inline _0RL_cd_44d3ab5e289658ff_91000000(LocalCallFn lcfn, const char* op, size_t oplen, _CORBA_Boolean oneway, CORBA::Long a_0, CORBA::Long a_1):
     omniCallDescriptor(lcfn, op, oplen, oneway),
     arg_0(a_0),
     arg_1(a_1) {}

  virtual CORBA::ULong alignedSize(CORBA::ULong size_in);
  virtual void marshalArguments(GIOP_C&);
  
  virtual void unmarshalReturnedValues(GIOP_C&);
    
  inline DataSeq* result() { return pd_result; }
  CORBA::Long arg_0;
  CORBA::Long arg_1;
  DataSeq* pd_result;
};

CORBA::ULong _0RL_cd_44d3ab5e289658ff_91000000::alignedSize(CORBA::ULong msgsize)
{
  msgsize = omni::align_to(msgsize, omni::ALIGN_4) + 4;
  
  msgsize = omni::align_to(msgsize, omni::ALIGN_4) + 4;
  
  return msgsize;
}

void _0RL_cd_44d3ab5e289658ff_91000000::marshalArguments(GIOP_C& giop_client)
{
  arg_0 >>= giop_client;
  arg_1 >>= giop_client;
  
}

void _0RL_cd_44d3ab5e289658ff_91000000::unmarshalReturnedValues(GIOP_C& giop_client)
{
  
  pd_result = new DataSeq;
  
  *pd_result <<= giop_client;
  
}

// Local call call-back function.
static void
_0RL_lcfn_44d3ab5e289658ff_a1000000(omniCallDescriptor* cd, omniServant* svnt)
{
  _0RL_cd_44d3ab5e289658ff_91000000* tcd = (_0RL_cd_44d3ab5e289658ff_91000000*) cd;
  _impl_SynDataSets* impl = (_impl_SynDataSets*) svnt->_ptrToInterface(SynDataSets::_PD_repoId);
  tcd->pd_result = impl->SaveDataSets(tcd->arg_0, tcd->arg_1);
}

DataSeq* _objref_SynDataSets::SaveDataSets(CORBA::Long MapId, CORBA::Long StyleId)
{
  _0RL_cd_44d3ab5e289658ff_91000000 _call_desc(_0RL_lcfn_44d3ab5e289658ff_a1000000, "SaveDataSets", 13, 0, MapId, StyleId);
  
  _invoke(_call_desc);
  return _call_desc.result();
}

_pof_SynDataSets::~_pof_SynDataSets() {}

omniObjRef*
_pof_SynDataSets::newObjRef(const char* mdri, IOP::TaggedProfileList* p,
               omniIdentity* id, omniLocalIdentity* lid)
{
  return new _objref_SynDataSets(mdri, p, id, lid);
}

CORBA::Boolean
_pof_SynDataSets::is_a(const char* id) const
{
  if( !strcmp(id, SynDataSets::_PD_repoId) )
    return 1;
  
  return 0;
}

const _pof_SynDataSets _the_pof_SynDataSets;

_impl_SynDataSets::~_impl_SynDataSets() {}

CORBA::Boolean
_impl_SynDataSets::_dispatch(GIOP_S& giop_s)
{
  if( !strcmp(giop_s.operation(), "Lock") ) {
    
    CORBA::Long arg_MapId;
    
    arg_MapId <<= giop_s;
    CORBA::Long arg_StyleId;
    
    arg_StyleId <<= giop_s;
    CORBA::Long arg_Id;
    
    arg_Id <<= giop_s;
    CORBA::String_var arg_ObjName;
    
    {
      CORBA::String_member _0RL_str_tmp;
      _0RL_str_tmp <<=  giop_s;
      arg_ObjName = _0RL_str_tmp._ptr;
      _0RL_str_tmp._ptr = 0;
    }
    
    giop_s.RequestReceived();
    CORBA::Long result;
    
    result = this->Lock(arg_MapId, arg_StyleId, arg_Id, arg_ObjName.in());
    
    if( giop_s.response_expected() ) {
      size_t msgsize = (size_t) GIOP_S::ReplyHeaderSize();
      msgsize = omni::align_to(msgsize, omni::ALIGN_4) + 4;
      
      giop_s.InitialiseReply(GIOP::NO_EXCEPTION, (CORBA::ULong) msgsize);
      result >>= giop_s;
      
    }
    giop_s.ReplyCompleted();
    return 1;
  }

  if( !strcmp(giop_s.operation(), "UnLock") ) {
    
    CORBA::Long arg_MapId;
    
    arg_MapId <<= giop_s;
    CORBA::Long arg_StyleId;
    
    arg_StyleId <<= giop_s;
    CORBA::Long arg_Id;
    
    arg_Id <<= giop_s;
    CORBA::Long arg_index;
    
    arg_index <<= giop_s;
    
    giop_s.RequestReceived();
    CORBA::Long result;
    
    result = this->UnLock(arg_MapId, arg_StyleId, arg_Id, arg_index);
    
    if( giop_s.response_expected() ) {
      size_t msgsize = (size_t) GIOP_S::ReplyHeaderSize();
      msgsize = omni::align_to(msgsize, omni::ALIGN_4) + 4;
      
      giop_s.InitialiseReply(GIOP::NO_EXCEPTION, (CORBA::ULong) msgsize);
      result >>= giop_s;
      
    }
    giop_s.ReplyCompleted();
    return 1;
  }

  if( !strcmp(giop_s.operation(), "GetData") ) {
    
    CORBA::Long arg_MapId;
    
    arg_MapId <<= giop_s;
    CORBA::Long arg_StyleId;
    
    arg_StyleId <<= giop_s;
    CORBA::Long arg_Id;
    
    arg_Id <<= giop_s;
    
    giop_s.RequestReceived();
    CORBA::Any_var result;
    
    result = this->GetData(arg_MapId, arg_StyleId, arg_Id);
    
    if( giop_s.response_expected() ) {
      size_t msgsize = (size_t) GIOP_S::ReplyHeaderSize();
      msgsize = (result.operator->())->_NP_alignedSize(msgsize);
      
      giop_s.InitialiseReply(GIOP::NO_EXCEPTION, (CORBA::ULong) msgsize);
      *(result.operator->()) >>= giop_s;
      
    }
    giop_s.ReplyCompleted();
    return 1;
  }

  if( !strcmp(giop_s.operation(), "GetDataMember") ) {
    
    CORBA::Long arg_MapId;
    
    arg_MapId <<= giop_s;
    CORBA::Long arg_StyleId;
    
    arg_StyleId <<= giop_s;
    CORBA::Long arg_Id;
    
    arg_Id <<= giop_s;
    CORBA::Long arg_MemberId;
    
    arg_MemberId <<= giop_s;
    
    giop_s.RequestReceived();
    CORBA::Any_var result;
    
    result = this->GetDataMember(arg_MapId, arg_StyleId, arg_Id, arg_MemberId);
    
    if( giop_s.response_expected() ) {
      size_t msgsize = (size_t) GIOP_S::ReplyHeaderSize();
      msgsize = (result.operator->())->_NP_alignedSize(msgsize);
      
      giop_s.InitialiseReply(GIOP::NO_EXCEPTION, (CORBA::ULong) msgsize);
      *(result.operator->()) >>= giop_s;
      
    }
    giop_s.ReplyCompleted();
    return 1;
  }

  if( !strcmp(giop_s.operation(), "SetData") ) {
    
    CORBA::Long arg_MapId;
    
    arg_MapId <<= giop_s;
    CORBA::Long arg_StyleId;
    
    arg_StyleId <<= giop_s;
    CORBA::Long arg_Id;
    
    arg_Id <<= giop_s;
    CORBA::Any arg_data;
    
    arg_data <<= giop_s;
    
    giop_s.RequestReceived();
    CORBA::Long result;
    
    result = this->SetData(arg_MapId, arg_StyleId, arg_Id, arg_data);
    
    if( giop_s.response_expected() ) {
      size_t msgsize = (size_t) GIOP_S::ReplyHeaderSize();
      msgsize = omni::align_to(msgsize, omni::ALIGN_4) + 4;
      
      giop_s.InitialiseReply(GIOP::NO_EXCEPTION, (CORBA::ULong) msgsize);
      result >>= giop_s;
      
    }
    giop_s.ReplyCompleted();
    return 1;
  }

  if( !strcmp(giop_s.operation(), "SetDataMember") ) {
    
    CORBA::Long arg_MapId;
    
    arg_MapId <<= giop_s;
    CORBA::Long arg_StyleId;
    
    arg_StyleId <<= giop_s;
    CORBA::Long arg_Id;
    
    arg_Id <<= giop_s;
    CORBA::Long arg_MemberId;
    
    arg_MemberId <<= giop_s;
    CORBA::Any arg_data;
    
    arg_data <<= giop_s;
    
    giop_s.RequestReceived();
    CORBA::Long result;
    
    result = this->SetDataMember(arg_MapId, arg_StyleId, arg_Id, arg_MemberId, arg_data);
    
    if( giop_s.response_expected() ) {
      size_t msgsize = (size_t) GIOP_S::ReplyHeaderSize();
      msgsize = omni::align_to(msgsize, omni::ALIGN_4) + 4;
      
      giop_s.InitialiseReply(GIOP::NO_EXCEPTION, (CORBA::ULong) msgsize);
      result >>= giop_s;
      
    }
    giop_s.ReplyCompleted();
    return 1;
  }

  if( !strcmp(giop_s.operation(), "GetDataWithLock") ) {
    
    CORBA::Long arg_MapId;
    
    arg_MapId <<= giop_s;
    CORBA::Long arg_StyleId;
    
    arg_StyleId <<= giop_s;
    CORBA::Long arg_Id;
    
    arg_Id <<= giop_s;
    CORBA::Long arg_Result;
    
    CORBA::String_var arg_ObjName;
    
    {
      CORBA::String_member _0RL_str_tmp;
      _0RL_str_tmp <<=  giop_s;
      arg_ObjName = _0RL_str_tmp._ptr;
      _0RL_str_tmp._ptr = 0;
    }
    
    giop_s.RequestReceived();
    CORBA::Any_var result;
    
    result = this->GetDataWithLock(arg_MapId, arg_StyleId, arg_Id, arg_Result, arg_ObjName.in());
    
    if( giop_s.response_expected() ) {
      size_t msgsize = (size_t) GIOP_S::ReplyHeaderSize();
      msgsize = (result.operator->())->_NP_alignedSize(msgsize);
      
      msgsize = omni::align_to(msgsize, omni::ALIGN_4) + 4;
      
      giop_s.InitialiseReply(GIOP::NO_EXCEPTION, (CORBA::ULong) msgsize);
      *(result.operator->()) >>= giop_s;
      
      arg_Result >>= giop_s;
      
    }
    giop_s.ReplyCompleted();
    return 1;
  }

  if( !strcmp(giop_s.operation(), "GetDataMemberWithLock") ) {
    
    CORBA::Long arg_MapId;
    
    arg_MapId <<= giop_s;
    CORBA::Long arg_StyleId;
    
    arg_StyleId <<= giop_s;
    CORBA::Long arg_Id;
    
    arg_Id <<= giop_s;
    CORBA::Long arg_MemberId;
    
    arg_MemberId <<= giop_s;
    CORBA::Any arg_data;
    
    arg_data <<= giop_s;
    CORBA::Long arg_Result;
    
    CORBA::String_var arg_ObjName;
    
    {
      CORBA::String_member _0RL_str_tmp;
      _0RL_str_tmp <<=  giop_s;
      arg_ObjName = _0RL_str_tmp._ptr;
      _0RL_str_tmp._ptr = 0;
    }
    
    giop_s.RequestReceived();
    CORBA::Any_var result;
    
    result = this->GetDataMemberWithLock(arg_MapId, arg_StyleId, arg_Id, arg_MemberId, arg_data, arg_Result, arg_ObjName.in());
    
    if( giop_s.response_expected() ) {
      size_t msgsize = (size_t) GIOP_S::ReplyHeaderSize();
      msgsize = (result.operator->())->_NP_alignedSize(msgsize);
      
      msgsize = omni::align_to(msgsize, omni::ALIGN_4) + 4;
      
      giop_s.InitialiseReply(GIOP::NO_EXCEPTION, (CORBA::ULong) msgsize);
      *(result.operator->()) >>= giop_s;
      
      arg_Result >>= giop_s;
      
    }
    giop_s.ReplyCompleted();
    return 1;
  }

  if( !strcmp(giop_s.operation(), "SetDataWithLock") ) {
    
    CORBA::Long arg_MapId;
    
    arg_MapId <<= giop_s;
    CORBA::Long arg_StyleId;
    
    arg_StyleId <<= giop_s;
    CORBA::Long arg_Id;
    
    arg_Id <<= giop_s;
    CORBA::Any arg_data;
    
    arg_data <<= giop_s;
    CORBA::Long arg_Result;
    
    CORBA::String_var arg_ObjName;
    
    {
      CORBA::String_member _0RL_str_tmp;
      _0RL_str_tmp <<=  giop_s;
      arg_ObjName = _0RL_str_tmp._ptr;
      _0RL_str_tmp._ptr = 0;
    }
    
    giop_s.RequestReceived();
    CORBA::Long result;
    
    result = this->SetDataWithLock(arg_MapId, arg_StyleId, arg_Id, arg_data, arg_Result, arg_ObjName.in());
    
    if( giop_s.response_expected() ) {
      size_t msgsize = (size_t) GIOP_S::ReplyHeaderSize();
      msgsize = omni::align_to(msgsize, omni::ALIGN_4) + 4;
      
      msgsize = omni::align_to(msgsize, omni::ALIGN_4) + 4;
      
      giop_s.InitialiseReply(GIOP::NO_EXCEPTION, (CORBA::ULong) msgsize);
      result >>= giop_s;
      
      arg_Result >>= giop_s;
      
    }
    giop_s.ReplyCompleted();
    return 1;
  }

  if( !strcmp(giop_s.operation(), "SetDataMemberWithLock") ) {
    
    CORBA::Long arg_MapId;
    
    arg_MapId <<= giop_s;
    CORBA::Long arg_StyleId;
    
    arg_StyleId <<= giop_s;
    CORBA::Long arg_Id;
    
    arg_Id <<= giop_s;
    CORBA::Long arg_MemberId;
    
    arg_MemberId <<= giop_s;
    CORBA::Any arg_data;
    
    arg_data <<= giop_s;
    CORBA::Long arg_Result;
    
    CORBA::String_var arg_ObjName;
    
    {
      CORBA::String_member _0RL_str_tmp;
      _0RL_str_tmp <<=  giop_s;
      arg_ObjName = _0RL_str_tmp._ptr;
      _0RL_str_tmp._ptr = 0;
    }
    
    giop_s.RequestReceived();
    CORBA::Long result;
    
    result = this->SetDataMemberWithLock(arg_MapId, arg_StyleId, arg_Id, arg_MemberId, arg_data, arg_Result, arg_ObjName.in());
    
    if( giop_s.response_expected() ) {
      size_t msgsize = (size_t) GIOP_S::ReplyHeaderSize();
      msgsize = omni::align_to(msgsize, omni::ALIGN_4) + 4;
      
      msgsize = omni::align_to(msgsize, omni::ALIGN_4) + 4;
      
      giop_s.InitialiseReply(GIOP::NO_EXCEPTION, (CORBA::ULong) msgsize);
      result >>= giop_s;
      
      arg_Result >>= giop_s;
      
    }
    giop_s.ReplyCompleted();
    return 1;
  }

  if( !strcmp(giop_s.operation(), "GetDataWithOnce") ) {
    
    CORBA::Long arg_MapId;
    
    arg_MapId <<= giop_s;
    CORBA::Long arg_StyleId;
    
    arg_StyleId <<= giop_s;
    CORBA::Long arg_Id;
    
    arg_Id <<= giop_s;
    CORBA::Long arg_Result;
    
    CORBA::String_var arg_ObjName;
    
    {
      CORBA::String_member _0RL_str_tmp;
      _0RL_str_tmp <<=  giop_s;
      arg_ObjName = _0RL_str_tmp._ptr;
      _0RL_str_tmp._ptr = 0;
    }
    
    giop_s.RequestReceived();
    CORBA::Any_var result;
    
    result = this->GetDataWithOnce(arg_MapId, arg_StyleId, arg_Id, arg_Result, arg_ObjName.in());
    
    if( giop_s.response_expected() ) {
      size_t msgsize = (size_t) GIOP_S::ReplyHeaderSize();
      msgsize = (result.operator->())->_NP_alignedSize(msgsize);
      
      msgsize = omni::align_to(msgsize, omni::ALIGN_4) + 4;
      
      giop_s.InitialiseReply(GIOP::NO_EXCEPTION, (CORBA::ULong) msgsize);
      *(result.operator->()) >>= giop_s;
      
      arg_Result >>= giop_s;
      
    }
    giop_s.ReplyCompleted();
    return 1;
  }

  if( !strcmp(giop_s.operation(), "GetDataMemberWithOnce") ) {
    
    CORBA::Long arg_MapId;
    
    arg_MapId <<= giop_s;
    CORBA::Long arg_StyleId;
    
    arg_StyleId <<= giop_s;
    CORBA::Long arg_Id;
    
    arg_Id <<= giop_s;
    CORBA::Long arg_MemberId;
    
    arg_MemberId <<= giop_s;
    CORBA::Long arg_Result;
    
    CORBA::String_var arg_ObjName;
    
    {
      CORBA::String_member _0RL_str_tmp;
      _0RL_str_tmp <<=  giop_s;
      arg_ObjName = _0RL_str_tmp._ptr;
      _0RL_str_tmp._ptr = 0;
    }
    
    giop_s.RequestReceived();
    CORBA::Any_var result;
    
    result = this->GetDataMemberWithOnce(arg_MapId, arg_StyleId, arg_Id, arg_MemberId, arg_Result, arg_ObjName.in());
    
    if( giop_s.response_expected() ) {
      size_t msgsize = (size_t) GIOP_S::ReplyHeaderSize();
      msgsize = (result.operator->())->_NP_alignedSize(msgsize);
      
      msgsize = omni::align_to(msgsize, omni::ALIGN_4) + 4;
      
      giop_s.InitialiseReply(GIOP::NO_EXCEPTION, (CORBA::ULong) msgsize);
      *(result.operator->()) >>= giop_s;
      
      arg_Result >>= giop_s;
      
    }
    giop_s.ReplyCompleted();
    return 1;
  }

  if( !strcmp(giop_s.operation(), "SetDataWithOnce") ) {
    
    CORBA::Long arg_MapId;
    
    arg_MapId <<= giop_s;
    CORBA::Long arg_StyleId;
    
    arg_StyleId <<= giop_s;
    CORBA::Long arg_Id;
    
    arg_Id <<= giop_s;
    CORBA::Any arg_data;
    
    arg_data <<= giop_s;
    CORBA::Long arg_Result;
    
    CORBA::String_var arg_ObjName;
    
    {
      CORBA::String_member _0RL_str_tmp;
      _0RL_str_tmp <<=  giop_s;
      arg_ObjName = _0RL_str_tmp._ptr;
      _0RL_str_tmp._ptr = 0;
    }
    
    giop_s.RequestReceived();
    CORBA::Long result;
    
    result = this->SetDataWithOnce(arg_MapId, arg_StyleId, arg_Id, arg_data, arg_Result, arg_ObjName.in());
    
    if( giop_s.response_expected() ) {
      size_t msgsize = (size_t) GIOP_S::ReplyHeaderSize();
      msgsize = omni::align_to(msgsize, omni::ALIGN_4) + 4;
      
      msgsize = omni::align_to(msgsize, omni::ALIGN_4) + 4;
      
      giop_s.InitialiseReply(GIOP::NO_EXCEPTION, (CORBA::ULong) msgsize);
      result >>= giop_s;
      
      arg_Result >>= giop_s;
      
    }
    giop_s.ReplyCompleted();
    return 1;
  }

  if( !strcmp(giop_s.operation(), "SetDataMemberWithOnce") ) {
    
    CORBA::Long arg_MapId;
    
    arg_MapId <<= giop_s;
    CORBA::Long arg_StyleId;
    
    arg_StyleId <<= giop_s;
    CORBA::Long arg_Id;
    
    arg_Id <<= giop_s;
    CORBA::Long arg_MemberId;
    
    arg_MemberId <<= giop_s;
    CORBA::Any arg_data;
    
    arg_data <<= giop_s;
    CORBA::Long arg_Result;
    
    CORBA::String_var arg_ObjName;
    
    {
      CORBA::String_member _0RL_str_tmp;
      _0RL_str_tmp <<=  giop_s;
      arg_ObjName = _0RL_str_tmp._ptr;
      _0RL_str_tmp._ptr = 0;
    }
    
    giop_s.RequestReceived();
    CORBA::Long result;
    
    result = this->SetDataMemberWithOnce(arg_MapId, arg_StyleId, arg_Id, arg_MemberId, arg_data, arg_Result, arg_ObjName.in());
    
    if( giop_s.response_expected() ) {
      size_t msgsize = (size_t) GIOP_S::ReplyHeaderSize();
      msgsize = omni::align_to(msgsize, omni::ALIGN_4) + 4;
      
      msgsize = omni::align_to(msgsize, omni::ALIGN_4) + 4;
      
      giop_s.InitialiseReply(GIOP::NO_EXCEPTION, (CORBA::ULong) msgsize);
      result >>= giop_s;
      
      arg_Result >>= giop_s;
      
    }
    giop_s.ReplyCompleted();
    return 1;
  }

  if( !strcmp(giop_s.operation(), "SaveDataSets") ) {
    
    CORBA::Long arg_MapId;
    
    arg_MapId <<= giop_s;
    CORBA::Long arg_StyleId;
    
    arg_StyleId <<= giop_s;
    
    giop_s.RequestReceived();
    DataSeq_var result;
    
    result = this->SaveDataSets(arg_MapId, arg_StyleId);
    
    if( giop_s.response_expected() ) {
      size_t msgsize = (size_t) GIOP_S::ReplyHeaderSize();
      msgsize = (result.operator->())->_NP_alignedSize(msgsize);
      
      giop_s.InitialiseReply(GIOP::NO_EXCEPTION, (CORBA::ULong) msgsize);
      *(result.operator->()) >>= giop_s;
      
    }
    giop_s.ReplyCompleted();
    return 1;
  }

  return 0;
}

void*
_impl_SynDataSets::_ptrToInterface(const char* id)
{
  if( !strcmp(id, CORBA::Object::_PD_repoId) )
    return (void*) 1;
  if( !strcmp(id, SynDataSets::_PD_repoId) )
    return (_impl_SynDataSets*) this;
  
  return 0;
}

const char*
_impl_SynDataSets::_mostDerivedRepoId()
{
  return SynDataSets::_PD_repoId;
}

POA_SynDataSets::~POA_SynDataSets() {}

