# Sword2 Game Data System CMake Configuration
cmake_minimum_required(VERSION 3.16)

project(Sword2GameDataSystem 
    VERSION 1.0.0
    DESCRIPTION "Sword2 Game Data Management System"
    LANGUAGES CXX
)

# 设置C++标准
set(CMAKE_CXX_STANDARD 17)
set(CMAKE_CXX_STANDARD_REQUIRED ON)
set(CMAKE_CXX_EXTENSIONS OFF)

# 编译选项
if(MSVC)
    add_compile_options(/W4 /WX- /utf-8)
    add_compile_definitions(_CRT_SECURE_NO_WARNINGS)
else()
    add_compile_options(-Wall -Wextra -Wpedantic)
endif()

# 设置输出目录
set(CMAKE_RUNTIME_OUTPUT_DIRECTORY ${CMAKE_BINARY_DIR}/bin)
set(CMAKE_LIBRARY_OUTPUT_DIRECTORY ${CMAKE_BINARY_DIR}/lib)
set(CMAKE_ARCHIVE_OUTPUT_DIRECTORY ${CMAKE_BINARY_DIR}/lib)

# 包含目录
include_directories(
    ${CMAKE_CURRENT_SOURCE_DIR}/Src
    ${CMAKE_CURRENT_SOURCE_DIR}/../../../Include
)

# 源文件
set(GAME_DATA_SOURCES
    Src/GameDataSystem.h
    Src/WeaponDataParser.h
    Src/MapDataParser.h
    Src/GameDataSystemDemo.cpp
    Src/UnifiedLoggingSystem.h
    Src/ModernCpp.h
)

# 测试源文件
set(TEST_SOURCES
    Src/GameDataSystemTest.cpp
)

# 创建游戏数据系统库
add_library(GameDataSystem STATIC ${GAME_DATA_SOURCES})

# 设置库的属性
set_target_properties(GameDataSystem PROPERTIES
    CXX_STANDARD 17
    CXX_STANDARD_REQUIRED ON
)

# 创建测试可执行文件
add_executable(GameDataSystemTest ${TEST_SOURCES})

# 链接库
target_link_libraries(GameDataSystemTest GameDataSystem)

# 设置测试程序属性
set_target_properties(GameDataSystemTest PROPERTIES
    CXX_STANDARD 17
    CXX_STANDARD_REQUIRED ON
    OUTPUT_NAME "GameDataTest"
)

# 添加编译定义
target_compile_definitions(GameDataSystem PRIVATE
    SWORD2_GAME_DATA_SYSTEM=1
    UNICODE
    _UNICODE
)

target_compile_definitions(GameDataSystemTest PRIVATE
    SWORD2_GAME_DATA_SYSTEM=1
    UNICODE
    _UNICODE
)

# 平台特定设置
if(WIN32)
    target_compile_definitions(GameDataSystem PRIVATE WIN32_LEAN_AND_MEAN)
    target_compile_definitions(GameDataSystemTest PRIVATE WIN32_LEAN_AND_MEAN)
endif()

# 安装规则
install(TARGETS GameDataSystem
    ARCHIVE DESTINATION lib
    LIBRARY DESTINATION lib
    RUNTIME DESTINATION bin
)

install(TARGETS GameDataSystemTest
    RUNTIME DESTINATION bin
)

# 安装头文件
install(FILES
    Src/GameDataSystem.h
    Src/WeaponDataParser.h
    Src/MapDataParser.h
    DESTINATION include/Sword2
)

# 创建配置文件
configure_file(
    "${CMAKE_CURRENT_SOURCE_DIR}/GameDataSystemConfig.cmake.in"
    "${CMAKE_CURRENT_BINARY_DIR}/GameDataSystemConfig.cmake"
    @ONLY
)

# 测试配置
enable_testing()

# 添加基本测试
add_test(
    NAME GameDataSystemBasicTest
    COMMAND GameDataSystemTest
    WORKING_DIRECTORY ${CMAKE_RUNTIME_OUTPUT_DIRECTORY}
)

# 设置测试属性
set_tests_properties(GameDataSystemBasicTest PROPERTIES
    TIMEOUT 60
    PASS_REGULAR_EXPRESSION "All tests passed"
)

# 自定义目标
add_custom_target(run_tests
    COMMAND ${CMAKE_CTEST_COMMAND} --verbose
    DEPENDS GameDataSystemTest
    COMMENT "Running game data system tests"
)

add_custom_target(demo
    COMMAND GameDataSystemTest
    DEPENDS GameDataSystemTest
    WORKING_DIRECTORY ${CMAKE_RUNTIME_OUTPUT_DIRECTORY}
    COMMENT "Running game data system demo"
)

# 文档生成（如果有Doxygen）
find_package(Doxygen QUIET)
if(DOXYGEN_FOUND)
    set(DOXYGEN_IN ${CMAKE_CURRENT_SOURCE_DIR}/Doxyfile.in)
    set(DOXYGEN_OUT ${CMAKE_CURRENT_BINARY_DIR}/Doxyfile)
    
    if(EXISTS ${DOXYGEN_IN})
        configure_file(${DOXYGEN_IN} ${DOXYGEN_OUT} @ONLY)
        
        add_custom_target(docs
            COMMAND ${DOXYGEN_EXECUTABLE} ${DOXYGEN_OUT}
            WORKING_DIRECTORY ${CMAKE_CURRENT_BINARY_DIR}
            COMMENT "Generating API documentation with Doxygen"
            VERBATIM
        )
    endif()
endif()

# 打印配置信息
message(STATUS "Sword2 Game Data System Configuration:")
message(STATUS "  Version: ${PROJECT_VERSION}")
message(STATUS "  C++ Standard: ${CMAKE_CXX_STANDARD}")
message(STATUS "  Build Type: ${CMAKE_BUILD_TYPE}")
message(STATUS "  Install Prefix: ${CMAKE_INSTALL_PREFIX}")
message(STATUS "  Output Directory: ${CMAKE_RUNTIME_OUTPUT_DIRECTORY}")

# 构建信息
if(CMAKE_BUILD_TYPE STREQUAL "Debug")
    message(STATUS "  Debug build - enabling additional logging")
    target_compile_definitions(GameDataSystem PRIVATE DEBUG_BUILD=1)
    target_compile_definitions(GameDataSystemTest PRIVATE DEBUG_BUILD=1)
elseif(CMAKE_BUILD_TYPE STREQUAL "Release")
    message(STATUS "  Release build - optimizations enabled")
    target_compile_definitions(GameDataSystem PRIVATE RELEASE_BUILD=1)
    target_compile_definitions(GameDataSystemTest PRIVATE RELEASE_BUILD=1)
endif()

# 性能分析支持
option(ENABLE_PROFILING "Enable profiling support" OFF)
if(ENABLE_PROFILING)
    message(STATUS "  Profiling support enabled")
    target_compile_definitions(GameDataSystem PRIVATE ENABLE_PROFILING=1)
    target_compile_definitions(GameDataSystemTest PRIVATE ENABLE_PROFILING=1)
endif()

# 内存检查支持
option(ENABLE_MEMORY_CHECK "Enable memory checking" OFF)
if(ENABLE_MEMORY_CHECK)
    message(STATUS "  Memory checking enabled")
    target_compile_definitions(GameDataSystem PRIVATE ENABLE_MEMORY_CHECK=1)
    target_compile_definitions(GameDataSystemTest PRIVATE ENABLE_MEMORY_CHECK=1)
endif()

# 代码覆盖率支持
option(ENABLE_COVERAGE "Enable code coverage" OFF)
if(ENABLE_COVERAGE AND CMAKE_CXX_COMPILER_ID MATCHES "GNU|Clang")
    message(STATUS "  Code coverage enabled")
    target_compile_options(GameDataSystem PRIVATE --coverage)
    target_link_options(GameDataSystem PRIVATE --coverage)
    target_compile_options(GameDataSystemTest PRIVATE --coverage)
    target_link_options(GameDataSystemTest PRIVATE --coverage)
endif()

# 静态分析支持
find_program(CLANG_TIDY_EXE NAMES "clang-tidy")
if(CLANG_TIDY_EXE)
    message(STATUS "  clang-tidy found: ${CLANG_TIDY_EXE}")
    set_target_properties(GameDataSystem PROPERTIES
        CXX_CLANG_TIDY "${CLANG_TIDY_EXE};-checks=-*,readability-*,performance-*,modernize-*"
    )
endif()

# 包管理支持
include(CMakePackageConfigHelpers)

write_basic_package_version_file(
    "${CMAKE_CURRENT_BINARY_DIR}/GameDataSystemConfigVersion.cmake"
    VERSION ${PROJECT_VERSION}
    COMPATIBILITY AnyNewerVersion
)

export(TARGETS GameDataSystem
    FILE "${CMAKE_CURRENT_BINARY_DIR}/GameDataSystemTargets.cmake"
)

# 最终状态报告
message(STATUS "Configuration complete. Available targets:")
message(STATUS "  GameDataSystem - Static library")
message(STATUS "  GameDataSystemTest - Test executable")
message(STATUS "  run_tests - Run all tests")
message(STATUS "  demo - Run demonstration")
if(DOXYGEN_FOUND)
    message(STATUS "  docs - Generate documentation")
endif()
message(STATUS "")
message(STATUS "To build: cmake --build .")
message(STATUS "To test:  cmake --build . --target run_tests")
message(STATUS "To demo:  cmake --build . --target demo")
