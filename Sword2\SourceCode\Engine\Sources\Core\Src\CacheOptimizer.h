//---------------------------------------------------------------------------
// Sword2 Cache Optimizer (c) 2024
//
// File:	CacheOptimizer.h
// Date:	2024.07.02
// Code:	AI Assistant
// Desc:	CPU cache optimization and data layout optimization system
//---------------------------------------------------------------------------
#ifndef CACHE_OPTIMIZER_H
#define CACHE_OPTIMIZER_H

#include <windows.h>
#include <vector>

// 缓存行大小常量
#define CACHE_LINE_SIZE         64
#define L1_CACHE_SIZE           (32 * 1024)    // 32KB
#define L2_CACHE_SIZE           (256 * 1024)   // 256KB
#define L3_CACHE_SIZE           (8 * 1024 * 1024) // 8MB

// 数据访问模式
enum DATA_ACCESS_PATTERN
{
    DAP_SEQUENTIAL = 0,         // 顺序访问
    DAP_RANDOM,                 // 随机访问
    DAP_STRIDED,                // 跨步访问
    DAP_TEMPORAL,               // 时间局部性
    DAP_SPATIAL                 // 空间局部性
};

// 缓存友好的数据结构基类
class ICacheFriendly
{
public:
    virtual ~ICacheFriendly() {}
    virtual void OptimizeLayout() = 0;
    virtual DWORD GetCacheFootprint() const = 0;
    virtual DATA_ACCESS_PATTERN GetAccessPattern() const = 0;
};

// 内存对齐分配器
template<size_t Alignment = CACHE_LINE_SIZE>
class CAlignedAllocator
{
public:
    static void* Allocate(size_t size)
    {
        // 使用_aligned_malloc进行对齐分配
        return _aligned_malloc(size, Alignment);
    }

    static void Free(void* ptr)
    {
        _aligned_free(ptr);
    }

    template<typename T>
    static T* AllocateArray(size_t count)
    {
        return static_cast<T*>(Allocate(sizeof(T) * count));
    }

    template<typename T>
    static void FreeArray(T* ptr)
    {
        Free(ptr);
    }
};

// 缓存友好的向量容器
template<typename T, size_t Alignment = CACHE_LINE_SIZE>
class CCacheFriendlyVector : public ICacheFriendly
{
public:
    CCacheFriendlyVector() : m_pData(nullptr), m_nSize(0), m_nCapacity(0) {}
    ~CCacheFriendlyVector() { Clear(); }

    void Reserve(size_t capacity)
    {
        if (capacity > m_nCapacity)
        {
            T* pNewData = CAlignedAllocator<Alignment>::template AllocateArray<T>(capacity);
            if (m_pData)
            {
                memcpy(pNewData, m_pData, m_nSize * sizeof(T));
                CAlignedAllocator<Alignment>::FreeArray(m_pData);
            }
            m_pData = pNewData;
            m_nCapacity = capacity;
        }
    }

    void PushBack(const T& item)
    {
        if (m_nSize >= m_nCapacity)
        {
            Reserve(m_nCapacity == 0 ? 8 : m_nCapacity * 2);
        }
        m_pData[m_nSize++] = item;
    }

    T& operator[](size_t index) { return m_pData[index]; }
    const T& operator[](size_t index) const { return m_pData[index]; }

    size_t Size() const { return m_nSize; }
    T* Data() { return m_pData; }
    const T* Data() const { return m_pData; }

    void Clear()
    {
        if (m_pData)
        {
            CAlignedAllocator<Alignment>::FreeArray(m_pData);
            m_pData = nullptr;
        }
        m_nSize = m_nCapacity = 0;
    }

    // ICacheFriendly 接口实现
    virtual void OptimizeLayout() override
    {
        // 重新排列数据以提高缓存命中率
        // 这里可以实现具体的优化算法
    }

    virtual DWORD GetCacheFootprint() const override
    {
        return (DWORD)(m_nSize * sizeof(T));
    }

    virtual DATA_ACCESS_PATTERN GetAccessPattern() const override
    {
        return DAP_SEQUENTIAL; // 向量通常是顺序访问
    }

private:
    T* m_pData;
    size_t m_nSize;
    size_t m_nCapacity;
};

// 数据结构优化器
class CDataStructureOptimizer
{
public:
    CDataStructureOptimizer();
    ~CDataStructureOptimizer();

    // 结构体优化
    template<typename T>
    void OptimizeStruct();

    // 数组优化
    void OptimizeArrayLayout(void* pArray, size_t elementSize, size_t count, 
                           DATA_ACCESS_PATTERN pattern);

    // 热/冷数据分离
    void SeparateHotColdData(void* pData, size_t dataSize, 
                           const std::vector<size_t>& hotOffsets);

    // 数据预取
    void PrefetchData(const void* pData, size_t size);
    void PrefetchSequential(const void* pStart, const void* pEnd);

private:
    void AnalyzeStructLayout();
    void ReorderFields();
    void AddPadding();
};

// 缓存性能分析器
class CCachePerformanceAnalyzer
{
public:
    CCachePerformanceAnalyzer();
    ~CCachePerformanceAnalyzer();

    BOOL Initialize();
    void Cleanup();

    // 性能计数器
    void StartProfiling();
    void StopProfiling();
    
    // 缓存统计
    struct CacheStats
    {
        DWORD dwL1Hits;
        DWORD dwL1Misses;
        DWORD dwL2Hits;
        DWORD dwL2Misses;
        DWORD dwL3Hits;
        DWORD dwL3Misses;
        float fL1HitRate;
        float fL2HitRate;
        float fL3HitRate;
        DWORD dwTotalAccesses;
    };

    void GetCacheStats(CacheStats* pStats);
    void ResetStats();

    // 热点分析
    void RecordMemoryAccess(void* pAddress, size_t size);
    void AnalyzeHotspots();

private:
    CacheStats m_Stats;
    BOOL m_bProfiling;
    LARGE_INTEGER m_liStartTime;
    
    struct MemoryAccess
    {
        void* pAddress;
        size_t size;
        DWORD dwTimestamp;
    };
    
    std::vector<MemoryAccess> m_AccessHistory;
    CRITICAL_SECTION m_CriticalSection;

    void UpdateCacheStats();
    BOOL IsInCache(void* pAddress, size_t cacheSize);
};

// 缓存优化器主类
class CCacheOptimizer
{
public:
    CCacheOptimizer();
    ~CCacheOptimizer();

    BOOL Initialize();
    void Cleanup();

    // 全局优化
    void OptimizeGlobalDataLayout();
    void OptimizeCodeLayout();
    
    // 运行时优化
    void OptimizeForCurrentWorkload();
    void AdaptToAccessPattern(DATA_ACCESS_PATTERN pattern);

    // 预取策略
    void EnableHardwarePrefetch(BOOL bEnable);
    void EnableSoftwarePrefetch(BOOL bEnable);
    void SetPrefetchDistance(DWORD dwDistance);

    // 统计和报告
    void GenerateOptimizationReport();
    void LogCachePerformance();

private:
    CDataStructureOptimizer m_StructOptimizer;
    CCachePerformanceAnalyzer m_PerfAnalyzer;
    
    BOOL m_bInitialized;
    BOOL m_bHardwarePrefetch;
    BOOL m_bSoftwarePrefetch;
    DWORD m_dwPrefetchDistance;

    void AnalyzeCurrentLayout();
    void ApplyOptimizations();
};

// 缓存友好的游戏对象基类
class CCacheFriendlyGameObject
{
public:
    CCacheFriendlyGameObject();
    virtual ~CCacheFriendlyGameObject();

    // 热数据 - 经常访问的数据放在前面
    struct HotData
    {
        float x, y, z;          // 位置 (12 bytes)
        DWORD dwFlags;          // 状态标志 (4 bytes)
        // 总共16字节，正好1/4缓存行
    } m_HotData;

    // 温数据 - 中等频率访问的数据
    struct WarmData
    {
        float vx, vy, vz;       // 速度 (12 bytes)
        DWORD dwType;           // 对象类型 (4 bytes)
        DWORD dwID;             // 对象ID (4 bytes)
        // 总共20字节
    } m_WarmData;

    // 冷数据 - 很少访问的数据
    struct ColdData
    {
        char szName[32];        // 名称 (32 bytes)
        DWORD dwCreationTime;   // 创建时间 (4 bytes)
        void* pExtendedData;    // 扩展数据指针 (4/8 bytes)
    } m_ColdData;

    // 访问器方法
    const HotData& GetHotData() const { return m_HotData; }
    const WarmData& GetWarmData() const { return m_WarmData; }
    const ColdData& GetColdData() const { return m_ColdData; }

    // 批量操作接口
    static void UpdateHotDataBatch(CCacheFriendlyGameObject** ppObjects, DWORD dwCount);
    static void UpdateWarmDataBatch(CCacheFriendlyGameObject** ppObjects, DWORD dwCount);
};

// 全局缓存优化器实例
extern CCacheOptimizer g_CacheOptimizer;

// 便捷宏定义
#define CACHE_ALIGN __declspec(align(CACHE_LINE_SIZE))
#define PREFETCH(addr) _mm_prefetch((const char*)(addr), _MM_HINT_T0)
#define PREFETCH_NTA(addr) _mm_prefetch((const char*)(addr), _MM_HINT_NTA)

// 缓存友好的循环宏
#define CACHE_FRIENDLY_LOOP(ptr, count, block_size) \
    for (size_t _block = 0; _block < (count); _block += (block_size)) { \
        size_t _end = min(_block + (block_size), (count)); \
        for (size_t _i = _block; _i < _end; ++_i) { \
            auto& _item = (ptr)[_i];

#define END_CACHE_FRIENDLY_LOOP \
        } \
    }

#endif // CACHE_OPTIMIZER_H
