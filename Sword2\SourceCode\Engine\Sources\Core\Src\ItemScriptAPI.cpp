//---------------------------------------------------------------------------
// Sword2 Item Script API Implementation (c) 2024
//
// File:	ItemScriptAPI.cpp
// Date:	2024.07.02
// Code:	AI Assistant
// Desc:	Implementation of item-related Lua script API functions
//---------------------------------------------------------------------------

#include "ItemScriptIntegration.h"
#include "ItemManager.h"
#include "InventoryManager.h"
#include "PlayerManager.h"
#include "LuaScriptEngine.h"

namespace sword2 {
namespace ItemScriptAPI {

// 辅助函数：获取物品ID参数
uint32_t GetItemIdFromLua(lua_State* L, int index = 1)
{
    if (lua_isnumber(L, index))
    {
        return static_cast<uint32_t>(lua_tonumber(L, index));
    }
    return 0;
}

// 辅助函数：获取物品模板
std::shared_ptr<ItemTemplate> GetItemTemplateFromLua(lua_State* L, int index = 1)
{
    uint32_t itemId = GetItemIdFromLua(L, index);
    if (itemId == 0) return nullptr;
    
    return ITEM_MANAGER().GetItemTemplate(itemId);
}

// 物品基础API实现
int GetItemName(lua_State* L)
{
    auto itemTemplate = GetItemTemplateFromLua(L);
    if (itemTemplate)
    {
        lua_pushstring(L, itemTemplate->name.c_str());
    }
    else
    {
        lua_pushstring(L, "");
    }
    return 1;
}

int GetItemDescription(lua_State* L)
{
    auto itemTemplate = GetItemTemplateFromLua(L);
    if (itemTemplate)
    {
        lua_pushstring(L, itemTemplate->description.c_str());
    }
    else
    {
        lua_pushstring(L, "");
    }
    return 1;
}

int GetItemLevel(lua_State* L)
{
    auto itemTemplate = GetItemTemplateFromLua(L);
    if (itemTemplate)
    {
        lua_pushnumber(L, itemTemplate->level);
    }
    else
    {
        lua_pushnumber(L, 0);
    }
    return 1;
}

int GetItemType(lua_State* L)
{
    auto itemTemplate = GetItemTemplateFromLua(L);
    if (itemTemplate)
    {
        lua_pushnumber(L, static_cast<int>(itemTemplate->detailType));
        lua_pushstring(L, itemTemplate->GetDetailTypeDescription().c_str());
        return 2;
    }
    else
    {
        lua_pushnumber(L, 0);
        lua_pushstring(L, "Unknown");
        return 2;
    }
}

int GetItemGenre(lua_State* L)
{
    auto itemTemplate = GetItemTemplateFromLua(L);
    if (itemTemplate)
    {
        lua_pushnumber(L, static_cast<int>(itemTemplate->genre));
        
        std::string genreDesc;
        switch (itemTemplate->genre)
        {
        case ItemGenre::Equipment: genreDesc = "装备"; break;
        case ItemGenre::Medicine: genreDesc = "药品"; break;
        case ItemGenre::Event: genreDesc = "事件物品"; break;
        case ItemGenre::Materials: genreDesc = "材料"; break;
        case ItemGenre::Task: genreDesc = "任务物品"; break;
        default: genreDesc = "未知"; break;
        }
        
        lua_pushstring(L, genreDesc.c_str());
        return 2;
    }
    else
    {
        lua_pushnumber(L, 0);
        lua_pushstring(L, "Unknown");
        return 2;
    }
}

int GetItemPrice(lua_State* L)
{
    auto itemTemplate = GetItemTemplateFromLua(L);
    if (itemTemplate)
    {
        lua_pushnumber(L, itemTemplate->price);
    }
    else
    {
        lua_pushnumber(L, 0);
    }
    return 1;
}

int GetItemWeight(lua_State* L)
{
    auto itemTemplate = GetItemTemplateFromLua(L);
    if (itemTemplate)
    {
        lua_pushnumber(L, itemTemplate->weight);
    }
    else
    {
        lua_pushnumber(L, 0);
    }
    return 1;
}

int GetItemStack(lua_State* L)
{
    auto itemTemplate = GetItemTemplateFromLua(L);
    if (itemTemplate)
    {
        lua_pushnumber(L, itemTemplate->maxStack);
    }
    else
    {
        lua_pushnumber(L, 1);
    }
    return 1;
}

// 物品实例API实现
int CreateItemInstance(lua_State* L)
{
    if (lua_isnumber(L, 1) && lua_isnumber(L, 2))
    {
        uint32_t templateId = static_cast<uint32_t>(lua_tonumber(L, 1));
        uint32_t ownerId = static_cast<uint32_t>(lua_tonumber(L, 2));
        uint32_t stackCount = lua_isnumber(L, 3) ? static_cast<uint32_t>(lua_tonumber(L, 3)) : 1;
        
        uint32_t instanceId = CREATE_ITEM_INSTANCE(templateId, ownerId, stackCount);
        lua_pushnumber(L, instanceId);
        
        if (instanceId != 0)
        {
            LOG_INFO("ITEM_API", "Created item instance " + std::to_string(instanceId) + 
                    " for template " + std::to_string(templateId) + " owner " + std::to_string(ownerId));
        }
    }
    else
    {
        lua_pushnumber(L, 0);
    }
    return 1;
}

int DestroyItemInstance(lua_State* L)
{
    if (lua_isnumber(L, 1))
    {
        uint32_t instanceId = static_cast<uint32_t>(lua_tonumber(L, 1));
        
        bool success = DESTROY_ITEM_INSTANCE(instanceId);
        lua_pushboolean(L, success ? 1 : 0);
        
        if (success)
        {
            LOG_INFO("ITEM_API", "Destroyed item instance " + std::to_string(instanceId));
        }
    }
    else
    {
        lua_pushboolean(L, 0);
    }
    return 1;
}

int GetItemInstance(lua_State* L)
{
    if (lua_isnumber(L, 1))
    {
        uint32_t instanceId = static_cast<uint32_t>(lua_tonumber(L, 1));
        
        auto instance = GET_ITEM_INSTANCE(instanceId);
        if (instance)
        {
            // 创建Lua表返回物品实例信息
            lua_newtable(L);
            
            lua_pushstring(L, "instanceId");
            lua_pushnumber(L, instance->instanceId);
            lua_settable(L, -3);
            
            lua_pushstring(L, "templateId");
            lua_pushnumber(L, instance->templateId);
            lua_settable(L, -3);
            
            lua_pushstring(L, "ownerId");
            lua_pushnumber(L, instance->ownerId);
            lua_settable(L, -3);
            
            lua_pushstring(L, "stackCount");
            lua_pushnumber(L, instance->stackCount);
            lua_settable(L, -3);
            
            lua_pushstring(L, "durability");
            lua_pushnumber(L, instance->durability);
            lua_settable(L, -3);
            
            lua_pushstring(L, "enhanceLevel");
            lua_pushnumber(L, instance->enhanceLevel);
            lua_settable(L, -3);
            
            lua_pushstring(L, "position");
            lua_pushnumber(L, static_cast<int>(instance->position));
            lua_settable(L, -3);
            
            lua_pushstring(L, "isBound");
            lua_pushboolean(L, instance->isBound ? 1 : 0);
            lua_settable(L, -3);
            
            lua_pushstring(L, "canTrade");
            lua_pushboolean(L, instance->canTrade ? 1 : 0);
            lua_settable(L, -3);
            
            lua_pushstring(L, "remainingTime");
            lua_pushnumber(L, instance->GetRemainingTime());
            lua_settable(L, -3);
        }
        else
        {
            lua_pushnil(L);
        }
    }
    else
    {
        lua_pushnil(L);
    }
    return 1;
}

int GetItemOwner(lua_State* L)
{
    if (lua_isnumber(L, 1))
    {
        uint32_t instanceId = static_cast<uint32_t>(lua_tonumber(L, 1));
        
        auto instance = GET_ITEM_INSTANCE(instanceId);
        if (instance)
        {
            lua_pushnumber(L, instance->ownerId);
        }
        else
        {
            lua_pushnumber(L, 0);
        }
    }
    else
    {
        lua_pushnumber(L, 0);
    }
    return 1;
}

int GetItemPosition(lua_State* L)
{
    if (lua_isnumber(L, 1))
    {
        uint32_t instanceId = static_cast<uint32_t>(lua_tonumber(L, 1));
        
        auto instance = GET_ITEM_INSTANCE(instanceId);
        if (instance)
        {
            lua_pushnumber(L, static_cast<int>(instance->position));
        }
        else
        {
            lua_pushnumber(L, 0);
        }
    }
    else
    {
        lua_pushnumber(L, 0);
    }
    return 1;
}

int GetItemDurability(lua_State* L)
{
    if (lua_isnumber(L, 1))
    {
        uint32_t instanceId = static_cast<uint32_t>(lua_tonumber(L, 1));
        
        auto instance = GET_ITEM_INSTANCE(instanceId);
        if (instance)
        {
            lua_pushnumber(L, instance->durability);
        }
        else
        {
            lua_pushnumber(L, 0);
        }
    }
    else
    {
        lua_pushnumber(L, 0);
    }
    return 1;
}

int GetItemEnhanceLevel(lua_State* L)
{
    if (lua_isnumber(L, 1))
    {
        uint32_t instanceId = static_cast<uint32_t>(lua_tonumber(L, 1));
        
        auto instance = GET_ITEM_INSTANCE(instanceId);
        if (instance)
        {
            lua_pushnumber(L, instance->enhanceLevel);
        }
        else
        {
            lua_pushnumber(L, 0);
        }
    }
    else
    {
        lua_pushnumber(L, 0);
    }
    return 1;
}

// 物品操作API实现
int UseItem(lua_State* L)
{
    if (lua_isnumber(L, 1) && lua_isnumber(L, 2))
    {
        uint32_t instanceId = static_cast<uint32_t>(lua_tonumber(L, 1));
        uint32_t playerId = static_cast<uint32_t>(lua_tonumber(L, 2));
        
        ItemOperationResult result = USE_ITEM(instanceId, playerId);
        lua_pushnumber(L, static_cast<int>(result));
        
        if (result == ItemOperationResult::Success)
        {
            LOG_INFO("ITEM_API", "Player " + std::to_string(playerId) + " used item " + std::to_string(instanceId));
        }
    }
    else
    {
        lua_pushnumber(L, static_cast<int>(ItemOperationResult::Failed));
    }
    return 1;
}

int MoveItem(lua_State* L)
{
    if (lua_isnumber(L, 1) && lua_isnumber(L, 2))
    {
        uint32_t instanceId = static_cast<uint32_t>(lua_tonumber(L, 1));
        int position = static_cast<int>(lua_tonumber(L, 2));
        uint32_t x = lua_isnumber(L, 3) ? static_cast<uint32_t>(lua_tonumber(L, 3)) : 0;
        uint32_t y = lua_isnumber(L, 4) ? static_cast<uint32_t>(lua_tonumber(L, 4)) : 0;
        
        ItemOperationResult result = MOVE_ITEM(instanceId, static_cast<ItemPosition>(position), x, y);
        lua_pushnumber(L, static_cast<int>(result));
        
        if (result == ItemOperationResult::Success)
        {
            LOG_INFO("ITEM_API", "Moved item " + std::to_string(instanceId) + " to position " + std::to_string(position));
        }
    }
    else
    {
        lua_pushnumber(L, static_cast<int>(ItemOperationResult::Failed));
    }
    return 1;
}

int EquipItem(lua_State* L)
{
    if (lua_isnumber(L, 1) && lua_isnumber(L, 2))
    {
        uint32_t instanceId = static_cast<uint32_t>(lua_tonumber(L, 1));
        uint32_t playerId = static_cast<uint32_t>(lua_tonumber(L, 2));
        
        ItemOperationResult result = EQUIP_ITEM(instanceId, playerId);
        lua_pushnumber(L, static_cast<int>(result));
        
        if (result == ItemOperationResult::Success)
        {
            LOG_INFO("ITEM_API", "Player " + std::to_string(playerId) + " equipped item " + std::to_string(instanceId));
        }
    }
    else
    {
        lua_pushnumber(L, static_cast<int>(ItemOperationResult::Failed));
    }
    return 1;
}

int UnequipItem(lua_State* L)
{
    if (lua_isnumber(L, 1) && lua_isnumber(L, 2))
    {
        uint32_t instanceId = static_cast<uint32_t>(lua_tonumber(L, 1));
        uint32_t playerId = static_cast<uint32_t>(lua_tonumber(L, 2));
        
        ItemOperationResult result = UNEQUIP_ITEM(instanceId, playerId);
        lua_pushnumber(L, static_cast<int>(result));
        
        if (result == ItemOperationResult::Success)
        {
            LOG_INFO("ITEM_API", "Player " + std::to_string(playerId) + " unequipped item " + std::to_string(instanceId));
        }
    }
    else
    {
        lua_pushnumber(L, static_cast<int>(ItemOperationResult::Failed));
    }
    return 1;
}

int EnhanceItem(lua_State* L)
{
    if (lua_isnumber(L, 1) && lua_isnumber(L, 2))
    {
        uint32_t instanceId = static_cast<uint32_t>(lua_tonumber(L, 1));
        uint32_t playerId = static_cast<uint32_t>(lua_tonumber(L, 2));
        
        ItemOperationResult result = ENHANCE_ITEM(instanceId, playerId);
        lua_pushnumber(L, static_cast<int>(result));
        
        if (result == ItemOperationResult::Success)
        {
            LOG_INFO("ITEM_API", "Player " + std::to_string(playerId) + " enhanced item " + std::to_string(instanceId));
        }
    }
    else
    {
        lua_pushnumber(L, static_cast<int>(ItemOperationResult::Failed));
    }
    return 1;
}

int RepairItem(lua_State* L)
{
    if (lua_isnumber(L, 1))
    {
        uint32_t instanceId = static_cast<uint32_t>(lua_tonumber(L, 1));
        uint32_t amount = lua_isnumber(L, 2) ? static_cast<uint32_t>(lua_tonumber(L, 2)) : UINT32_MAX;
        
        auto instance = GET_ITEM_INSTANCE(instanceId);
        if (instance)
        {
            instance->Repair(amount);
            lua_pushboolean(L, 1);
            
            LOG_INFO("ITEM_API", "Repaired item " + std::to_string(instanceId) + " by " + std::to_string(amount));
        }
        else
        {
            lua_pushboolean(L, 0);
        }
    }
    else
    {
        lua_pushboolean(L, 0);
    }
    return 1;
}

int SocketGem(lua_State* L)
{
    if (lua_isnumber(L, 1) && lua_isnumber(L, 2) && lua_isnumber(L, 3))
    {
        uint32_t instanceId = static_cast<uint32_t>(lua_tonumber(L, 1));
        uint32_t gemId = static_cast<uint32_t>(lua_tonumber(L, 2));
        uint32_t playerId = static_cast<uint32_t>(lua_tonumber(L, 3));
        
        ItemOperationResult result = SOCKET_GEM(instanceId, gemId, playerId);
        lua_pushnumber(L, static_cast<int>(result));
        
        if (result == ItemOperationResult::Success)
        {
            LOG_INFO("ITEM_API", "Player " + std::to_string(playerId) + " socketed gem " + std::to_string(gemId) + 
                    " into item " + std::to_string(instanceId));
        }
    }
    else
    {
        lua_pushnumber(L, static_cast<int>(ItemOperationResult::Failed));
    }
    return 1;
}

int RemoveGem(lua_State* L)
{
    if (lua_isnumber(L, 1) && lua_isnumber(L, 2))
    {
        uint32_t instanceId = static_cast<uint32_t>(lua_tonumber(L, 1));
        size_t index = static_cast<size_t>(lua_tonumber(L, 2));
        
        auto instance = GET_ITEM_INSTANCE(instanceId);
        if (instance)
        {
            bool success = instance->RemoveGem(index);
            lua_pushboolean(L, success ? 1 : 0);
            
            if (success)
            {
                LOG_INFO("ITEM_API", "Removed gem at index " + std::to_string(index) + 
                        " from item " + std::to_string(instanceId));
            }
        }
        else
        {
            lua_pushboolean(L, 0);
        }
    }
    else
    {
        lua_pushboolean(L, 0);
    }
    return 1;
}

// 背包API实现
int GetPlayerInventory(lua_State* L)
{
    if (lua_isnumber(L, 1))
    {
        uint32_t playerId = static_cast<uint32_t>(lua_tonumber(L, 1));
        int containerType = lua_isnumber(L, 2) ? static_cast<int>(lua_tonumber(L, 2)) : 0;

        auto inventory = GET_PLAYER_INVENTORY(playerId);
        if (inventory)
        {
            auto container = inventory->GetContainer(static_cast<InventoryType>(containerType));
            if (container)
            {
                // 创建Lua表返回背包信息
                lua_newtable(L);

                lua_pushstring(L, "width");
                lua_pushnumber(L, container->width);
                lua_settable(L, -3);

                lua_pushstring(L, "height");
                lua_pushnumber(L, container->height);
                lua_settable(L, -3);

                lua_pushstring(L, "currentWeight");
                lua_pushnumber(L, container->currentWeight);
                lua_settable(L, -3);

                lua_pushstring(L, "maxWeight");
                lua_pushnumber(L, container->maxWeight);
                lua_settable(L, -3);

                lua_pushstring(L, "freeSlots");
                lua_pushnumber(L, container->GetFreeSlotCount());
                lua_settable(L, -3);

                lua_pushstring(L, "usedSlots");
                lua_pushnumber(L, container->GetUsedSlotCount());
                lua_settable(L, -3);

                return 1;
            }
        }
    }

    lua_pushnil(L);
    return 1;
}

int AddItemToInventory(lua_State* L)
{
    if (lua_isnumber(L, 1) && lua_isnumber(L, 2))
    {
        uint32_t playerId = static_cast<uint32_t>(lua_tonumber(L, 1));
        uint32_t templateId = static_cast<uint32_t>(lua_tonumber(L, 2));
        uint32_t stackCount = lua_isnumber(L, 3) ? static_cast<uint32_t>(lua_tonumber(L, 3)) : 1;
        int containerType = lua_isnumber(L, 4) ? static_cast<int>(lua_tonumber(L, 4)) : 0;

        bool success = GIVE_ITEM_TO_PLAYER(playerId, templateId, stackCount, static_cast<InventoryType>(containerType));
        lua_pushboolean(L, success ? 1 : 0);

        if (success)
        {
            LOG_INFO("ITEM_API", "Added item template " + std::to_string(templateId) +
                    " to player " + std::to_string(playerId) + " inventory");
        }
    }
    else
    {
        lua_pushboolean(L, 0);
    }
    return 1;
}

int RemoveItemFromInventory(lua_State* L)
{
    if (lua_isnumber(L, 1) && lua_isnumber(L, 2) && lua_isnumber(L, 3))
    {
        uint32_t playerId = static_cast<uint32_t>(lua_tonumber(L, 1));
        uint32_t x = static_cast<uint32_t>(lua_tonumber(L, 2));
        uint32_t y = static_cast<uint32_t>(lua_tonumber(L, 3));
        int containerType = lua_isnumber(L, 4) ? static_cast<int>(lua_tonumber(L, 4)) : 0;

        auto inventory = GET_PLAYER_INVENTORY(playerId);
        if (inventory)
        {
            bool success = inventory->RemoveItem(x, y, static_cast<InventoryType>(containerType));
            lua_pushboolean(L, success ? 1 : 0);

            if (success)
            {
                LOG_INFO("ITEM_API", "Removed item from player " + std::to_string(playerId) +
                        " inventory at (" + std::to_string(x) + ", " + std::to_string(y) + ")");
            }
        }
        else
        {
            lua_pushboolean(L, 0);
        }
    }
    else
    {
        lua_pushboolean(L, 0);
    }
    return 1;
}

int GetInventorySpace(lua_State* L)
{
    if (lua_isnumber(L, 1))
    {
        uint32_t playerId = static_cast<uint32_t>(lua_tonumber(L, 1));
        int containerType = lua_isnumber(L, 2) ? static_cast<int>(lua_tonumber(L, 2)) : 0;

        auto inventory = GET_PLAYER_INVENTORY(playerId);
        if (inventory)
        {
            uint32_t freeSlots = inventory->GetFreeSlotCount(static_cast<InventoryType>(containerType));
            lua_pushnumber(L, freeSlots);
        }
        else
        {
            lua_pushnumber(L, 0);
        }
    }
    else
    {
        lua_pushnumber(L, 0);
    }
    return 1;
}

int GetInventoryWeight(lua_State* L)
{
    if (lua_isnumber(L, 1))
    {
        uint32_t playerId = static_cast<uint32_t>(lua_tonumber(L, 1));
        int containerType = lua_isnumber(L, 2) ? static_cast<int>(lua_tonumber(L, 2)) : 0;

        auto inventory = GET_PLAYER_INVENTORY(playerId);
        if (inventory)
        {
            uint32_t currentWeight = inventory->GetCurrentWeight(static_cast<InventoryType>(containerType));
            uint32_t maxWeight = inventory->GetMaxWeight(static_cast<InventoryType>(containerType));

            lua_pushnumber(L, currentWeight);
            lua_pushnumber(L, maxWeight);
            return 2;
        }
    }

    lua_pushnumber(L, 0);
    lua_pushnumber(L, 0);
    return 2;
}

int SortInventory(lua_State* L)
{
    if (lua_isnumber(L, 1))
    {
        uint32_t playerId = static_cast<uint32_t>(lua_tonumber(L, 1));
        int containerType = lua_isnumber(L, 2) ? static_cast<int>(lua_tonumber(L, 2)) : 0;

        auto inventory = GET_PLAYER_INVENTORY(playerId);
        if (inventory)
        {
            inventory->SortContainer(static_cast<InventoryType>(containerType));
            lua_pushboolean(L, 1);

            LOG_INFO("ITEM_API", "Sorted inventory for player " + std::to_string(playerId));
        }
        else
        {
            lua_pushboolean(L, 0);
        }
    }
    else
    {
        lua_pushboolean(L, 0);
    }
    return 1;
}

} // namespace ItemScriptAPI
} // namespace sword2
