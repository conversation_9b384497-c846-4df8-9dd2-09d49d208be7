//---------------------------------------------------------------------------
// Sword2 NPC Script API Implementation (c) 2024
//
// File:	NPCScriptAPI.cpp
// Date:	2024.07.02
// Code:	AI Assistant
// Desc:	Implementation of NPC-related Lua script API functions
//---------------------------------------------------------------------------

#include "NPCScriptIntegration.h"
#include "NPCManager.h"
#include "PlayerManager.h"
#include "LuaScriptEngine.h"

namespace sword2 {
namespace NPCScriptAPI {

// 辅助函数：获取NPC ID参数
uint32_t GetNPCIdFromLua(lua_State* L, int index = 1)
{
    if (lua_isnumber(L, index))
    {
        return static_cast<uint32_t>(lua_tonumber(L, index));
    }
    return 0;
}

// 辅助函数：获取NPC对象
std::shared_ptr<NPC> GetNPCFromLua(lua_State* L, int index = 1)
{
    uint32_t npcId = GetNPCIdFromLua(L, index);
    if (npcId == 0) return nullptr;

    return NPC_MANAGER().GetNPC(npcId);
}

// NPC基础信息API
int GetNPCName(lua_State* L)
{
    auto npc = GetNPCFromLua(L);
    if (npc)
    {
        lua_pushstring(L, npc->name.c_str());
    }
    else
    {
        lua_pushstring(L, "");
    }
    return 1;
}

int GetNPCLevel(lua_State* L)
{
    auto npc = GetNPCFromLua(L);
    if (npc)
    {
        lua_pushnumber(L, npc->attributes.level);
    }
    else
    {
        lua_pushnumber(L, 0);
    }
    return 1;
}

int GetNPCPosition(lua_State* L)
{
    auto npc = GetNPCFromLua(L);
    if (npc)
    {
        lua_pushnumber(L, npc->mapId);
        lua_pushnumber(L, npc->x);
        lua_pushnumber(L, npc->y);
        return 3;
    }
    else
    {
        lua_pushnumber(L, 0);
        lua_pushnumber(L, 0);
        lua_pushnumber(L, 0);
        return 3;
    }
}

int SetNPCPosition(lua_State* L)
{
    auto npc = GetNPCFromLua(L, 1);
    if (npc && lua_isnumber(L, 2) && lua_isnumber(L, 3) && lua_isnumber(L, 4))
    {
        uint32_t mapId = static_cast<uint32_t>(lua_tonumber(L, 2));
        int32_t x = static_cast<int32_t>(lua_tonumber(L, 3));
        int32_t y = static_cast<int32_t>(lua_tonumber(L, 4));

        npc->SetPosition(mapId, x, y);
        lua_pushboolean(L, 1);

        LOG_INFO("NPC_API", "Set NPC " + npc->name + " position to map " +
                std::to_string(mapId) + " (" + std::to_string(x) + ", " + std::to_string(y) + ")");
    }
    else
    {
        lua_pushboolean(L, 0);
    }
    return 1;
}

int GetNPCHP(lua_State* L)
{
    auto npc = GetNPCFromLua(L);
    if (npc)
    {
        lua_pushnumber(L, npc->attributes.currentLife);
        lua_pushnumber(L, npc->attributes.maxLife);
        return 2;
    }
    else
    {
        lua_pushnumber(L, 0);
        lua_pushnumber(L, 0);
        return 2;
    }
}

int SetNPCHP(lua_State* L)
{
    auto npc = GetNPCFromLua(L, 1);
    if (npc && lua_isnumber(L, 2))
    {
        uint32_t hp = static_cast<uint32_t>(lua_tonumber(L, 2));
        npc->attributes.currentLife = std::min(hp, npc->attributes.maxLife);
        lua_pushboolean(L, 1);
    }
    else
    {
        lua_pushboolean(L, 0);
    }
    return 1;
}

int GetNPCStatus(lua_State* L)
{
    auto npc = GetNPCFromLua(L);
    if (npc)
    {
        lua_pushnumber(L, static_cast<int>(npc->status));
        lua_pushstring(L, npc->GetStatusDescription().c_str());
        return 2;
    }
    else
    {
        lua_pushnumber(L, 0);
        lua_pushstring(L, "Unknown");
        return 2;
    }
}

int SetNPCStatus(lua_State* L)
{
    auto npc = GetNPCFromLua(L, 1);
    if (npc && lua_isnumber(L, 2))
    {
        int status = static_cast<int>(lua_tonumber(L, 2));
        npc->status = static_cast<NPCStatus>(status);
        lua_pushboolean(L, 1);
    }
    else
    {
        lua_pushboolean(L, 0);
    }
    return 1;
}

// NPC行为控制API
int NPCMoveTo(lua_State* L)
{
    auto npc = GetNPCFromLua(L, 1);
    if (npc && lua_isnumber(L, 2) && lua_isnumber(L, 3))
    {
        int32_t x = static_cast<int32_t>(lua_tonumber(L, 2));
        int32_t y = static_cast<int32_t>(lua_tonumber(L, 3));

        bool success = npc->MoveTo(x, y);
        lua_pushboolean(L, success ? 1 : 0);

        if (success)
        {
            LOG_DEBUG("NPC_API", "NPC " + npc->name + " moving to (" +
                     std::to_string(x) + ", " + std::to_string(y) + ")");
        }
    }
    else
    {
        lua_pushboolean(L, 0);
    }
    return 1;
}

int NPCPatrol(lua_State* L)
{
    auto npc = GetNPCFromLua(L, 1);
    if (npc)
    {
        npc->behavior = NPCBehavior::Patrol;

        // 如果提供了巡逻点参数
        if (lua_isnumber(L, 2) && lua_isnumber(L, 3))
        {
            int32_t x = static_cast<int32_t>(lua_tonumber(L, 2));
            int32_t y = static_cast<int32_t>(lua_tonumber(L, 3));
            uint32_t waitTime = lua_isnumber(L, 4) ? static_cast<uint32_t>(lua_tonumber(L, 4)) : 0;

            npc->AddPatrolPoint(x, y, waitTime);
        }

        lua_pushboolean(L, 1);
        LOG_DEBUG("NPC_API", "Set NPC " + npc->name + " to patrol mode");
    }
    else
    {
        lua_pushboolean(L, 0);
    }
    return 1;
}

int NPCAttackPlayer(lua_State* L)
{
    auto npc = GetNPCFromLua(L, 1);
    if (npc && lua_isnumber(L, 2))
    {
        uint32_t playerId = static_cast<uint32_t>(lua_tonumber(L, 2));

        bool success = NPC_MANAGER().NPCAttack(npc->npcId, playerId);
        lua_pushboolean(L, success ? 1 : 0);

        if (success)
        {
            LOG_INFO("NPC_API", "NPC " + npc->name + " attacking player " + std::to_string(playerId));
        }
    }
    else
    {
        lua_pushboolean(L, 0);
    }
    return 1;
}

int NPCFollowPlayer(lua_State* L)
{
    auto npc = GetNPCFromLua(L, 1);
    if (npc && lua_isnumber(L, 2))
    {
        uint32_t playerId = static_cast<uint32_t>(lua_tonumber(L, 2));

        npc->behavior = NPCBehavior::Follow;
        npc->targetPlayerId = playerId;

        lua_pushboolean(L, 1);
        LOG_DEBUG("NPC_API", "NPC " + npc->name + " following player " + std::to_string(playerId));
    }
    else
    {
        lua_pushboolean(L, 0);
    }
    return 1;
}

int NPCReturnToSpawn(lua_State* L)
{
    auto npc = GetNPCFromLua(L);
    if (npc)
    {
        npc->ReturnToSpawn();
        lua_pushboolean(L, 1);
        LOG_DEBUG("NPC_API", "NPC " + npc->name + " returning to spawn point");
    }
    else
    {
        lua_pushboolean(L, 0);
    }
    return 1;
}

// NPC对话API
int NPCChat(lua_State* L)
{
    if (lua_isnumber(L, 1) && lua_isstring(L, 2))
    {
        uint32_t npcId = static_cast<uint32_t>(lua_tonumber(L, 1));
        std::string message = lua_tostring(L, 2);

        // 这里应该实现实际的NPC聊天逻辑
        LOG_INFO("NPC_API", "NPC " + std::to_string(npcId) + " chat: " + message);
        lua_pushboolean(L, 1);
    }
    else
    {
        lua_pushboolean(L, 0);
    }
    return 1;
}

int NPCSay(lua_State* L)
{
    if (lua_isnumber(L, 1) && lua_isstring(L, 2))
    {
        uint32_t npcId = static_cast<uint32_t>(lua_tonumber(L, 1));
        std::string message = lua_tostring(L, 2);

        // 这里应该实现实际的NPC说话逻辑
        LOG_INFO("NPC_API", "NPC " + std::to_string(npcId) + " says: " + message);
        lua_pushboolean(L, 1);
    }
    else
    {
        lua_pushboolean(L, 0);
    }
    return 1;
}

int NPCAsk(lua_State* L)
{
    if (lua_isnumber(L, 1) && lua_isstring(L, 2))
    {
        uint32_t npcId = static_cast<uint32_t>(lua_tonumber(L, 1));
        std::string question = lua_tostring(L, 2);

        // 这里应该实现实际的NPC询问逻辑
        LOG_INFO("NPC_API", "NPC " + std::to_string(npcId) + " asks: " + question);
        lua_pushstring(L, "yes"); // 默认回答
    }
    else
    {
        lua_pushstring(L, "");
    }
    return 1;
}

int ShowNPCDialog(lua_State* L)
{
    if (lua_isnumber(L, 1) && lua_isnumber(L, 2) && lua_isstring(L, 3))
    {
        uint32_t npcId = static_cast<uint32_t>(lua_tonumber(L, 1));
        uint32_t playerId = static_cast<uint32_t>(lua_tonumber(L, 2));
        std::string text = lua_tostring(L, 3);

        bool success = START_NPC_DIALOG(npcId, playerId, text);
        lua_pushboolean(L, success ? 1 : 0);
    }
    else
    {
        lua_pushboolean(L, 0);
    }
    return 1;
}

int CloseNPCDialog(lua_State* L)
{
    if (lua_isnumber(L, 1) && lua_isnumber(L, 2))
    {
        uint32_t npcId = static_cast<uint32_t>(lua_tonumber(L, 1));
        uint32_t playerId = static_cast<uint32_t>(lua_tonumber(L, 2));

        bool success = END_NPC_DIALOG(npcId, playerId);
        lua_pushboolean(L, success ? 1 : 0);
    }
    else
    {
        lua_pushboolean(L, 0);
    }
    return 1;
}

// NPC商店API
int OpenShop(lua_State* L)
{
    if (lua_isnumber(L, 1) && lua_isnumber(L, 2))
    {
        uint32_t npcId = static_cast<uint32_t>(lua_tonumber(L, 1));
        uint32_t playerId = static_cast<uint32_t>(lua_tonumber(L, 2));
        uint32_t shopType = lua_isnumber(L, 3) ? static_cast<uint32_t>(lua_tonumber(L, 3)) : 0;

        bool success = OPEN_NPC_SHOP(npcId, playerId, shopType);
        lua_pushboolean(L, success ? 1 : 0);
    }
    else
    {
        lua_pushboolean(L, 0);
    }
    return 1;
}

int CloseShop(lua_State* L)
{
    if (lua_isnumber(L, 1) && lua_isnumber(L, 2))
    {
        uint32_t npcId = static_cast<uint32_t>(lua_tonumber(L, 1));
        uint32_t playerId = static_cast<uint32_t>(lua_tonumber(L, 2));

        bool success = CLOSE_NPC_SHOP(npcId, playerId);
        lua_pushboolean(L, success ? 1 : 0);
    }
    else
    {
        lua_pushboolean(L, 0);
    }
    return 1;
}

int AddShopItem(lua_State* L)
{
    auto npc = GetNPCFromLua(L, 1);
    if (npc && lua_isnumber(L, 2) && lua_isnumber(L, 3))
    {
        uint32_t itemId = static_cast<uint32_t>(lua_tonumber(L, 2));
        uint32_t price = static_cast<uint32_t>(lua_tonumber(L, 3));
        uint32_t stock = lua_isnumber(L, 4) ? static_cast<uint32_t>(lua_tonumber(L, 4)) : -1;
        bool limited = lua_isboolean(L, 5) ? (lua_toboolean(L, 5) != 0) : false;

        npc->AddShopItem(itemId, price, stock, limited);
        lua_pushboolean(L, 1);

        LOG_DEBUG("NPC_API", "Added shop item " + std::to_string(itemId) +
                 " to NPC " + npc->name + " for " + std::to_string(price) + " gold");
    }
    else
    {
        lua_pushboolean(L, 0);
    }
    return 1;
}

int RemoveShopItem(lua_State* L)
{
    auto npc = GetNPCFromLua(L, 1);
    if (npc && lua_isnumber(L, 2))
    {
        uint32_t itemId = static_cast<uint32_t>(lua_tonumber(L, 2));

        // 从商店物品列表中移除
        auto& shopItems = npc->shopItems;
        auto it = std::remove_if(shopItems.begin(), shopItems.end(),
            [itemId](const ShopItem& item) { return item.itemId == itemId; });

        if (it != shopItems.end())
        {
            shopItems.erase(it, shopItems.end());
            lua_pushboolean(L, 1);
            LOG_DEBUG("NPC_API", "Removed shop item " + std::to_string(itemId) + " from NPC " + npc->name);
        }
        else
        {
            lua_pushboolean(L, 0);
        }
    }
    else
    {
        lua_pushboolean(L, 0);
    }
    return 1;
}

int GetShopItemPrice(lua_State* L)
{
    auto npc = GetNPCFromLua(L, 1);
    if (npc && lua_isnumber(L, 2))
    {
        uint32_t itemId = static_cast<uint32_t>(lua_tonumber(L, 2));

        for (const auto& shopItem : npc->shopItems)
        {
            if (shopItem.itemId == itemId)
            {
                lua_pushnumber(L, shopItem.price);
                return 1;
            }
        }
    }

    lua_pushnumber(L, 0);
    return 1;
}

int SetShopItemPrice(lua_State* L)
{
    auto npc = GetNPCFromLua(L, 1);
    if (npc && lua_isnumber(L, 2) && lua_isnumber(L, 3))
    {
        uint32_t itemId = static_cast<uint32_t>(lua_tonumber(L, 2));
        uint32_t newPrice = static_cast<uint32_t>(lua_tonumber(L, 3));

        for (auto& shopItem : npc->shopItems)
        {
            if (shopItem.itemId == itemId)
            {
                shopItem.price = newPrice;
                lua_pushboolean(L, 1);
                LOG_DEBUG("NPC_API", "Set price of item " + std::to_string(itemId) +
                         " to " + std::to_string(newPrice) + " for NPC " + npc->name);
                return 1;
            }
        }
    }

    lua_pushboolean(L, 0);
    return 1;
}

// NPC任务API
int GiveQuest(lua_State* L)
{
    if (lua_isnumber(L, 1) && lua_isnumber(L, 2) && lua_isnumber(L, 3))
    {
        uint32_t npcId = static_cast<uint32_t>(lua_tonumber(L, 1));
        uint32_t playerId = static_cast<uint32_t>(lua_tonumber(L, 2));
        uint32_t questId = static_cast<uint32_t>(lua_tonumber(L, 3));

        // 这里应该实现实际的任务给予逻辑
        LOG_INFO("NPC_API", "NPC " + std::to_string(npcId) + " gave quest " +
                std::to_string(questId) + " to player " + std::to_string(playerId));
        lua_pushboolean(L, 1);
    }
    else
    {
        lua_pushboolean(L, 0);
    }
    return 1;
}

int CompleteQuest(lua_State* L)
{
    if (lua_isnumber(L, 1) && lua_isnumber(L, 2) && lua_isnumber(L, 3))
    {
        uint32_t npcId = static_cast<uint32_t>(lua_tonumber(L, 1));
        uint32_t playerId = static_cast<uint32_t>(lua_tonumber(L, 2));
        uint32_t questId = static_cast<uint32_t>(lua_tonumber(L, 3));

        // 这里应该实现实际的任务完成逻辑
        LOG_INFO("NPC_API", "Player " + std::to_string(playerId) + " completed quest " +
                std::to_string(questId) + " at NPC " + std::to_string(npcId));
        lua_pushboolean(L, 1);
    }
    else
    {
        lua_pushboolean(L, 0);
    }
    return 1;
}

int CheckQuestStatus(lua_State* L)
{
    if (lua_isnumber(L, 1) && lua_isnumber(L, 2))
    {
        uint32_t playerId = static_cast<uint32_t>(lua_tonumber(L, 1));
        uint32_t questId = static_cast<uint32_t>(lua_tonumber(L, 2));

        // 这里应该实现实际的任务状态查询逻辑
        // 0=未接受, 1=进行中, 2=已完成
        lua_pushnumber(L, 0);
    }
    else
    {
        lua_pushnumber(L, 0);
    }
    return 1;
}

int UpdateQuestProgress(lua_State* L)
{
    if (lua_isnumber(L, 1) && lua_isnumber(L, 2) && lua_isnumber(L, 3))
    {
        uint32_t playerId = static_cast<uint32_t>(lua_tonumber(L, 1));
        uint32_t questId = static_cast<uint32_t>(lua_tonumber(L, 2));
        uint32_t progress = static_cast<uint32_t>(lua_tonumber(L, 3));

        // 这里应该实现实际的任务进度更新逻辑
        LOG_DEBUG("NPC_API", "Updated quest " + std::to_string(questId) +
                 " progress to " + std::to_string(progress) + " for player " + std::to_string(playerId));
        lua_pushboolean(L, 1);
    }
    else
    {
        lua_pushboolean(L, 0);
    }
    return 1;
}

// NPC创建和管理API
int CreateNPCInstance(lua_State* L)
{
    if (lua_isnumber(L, 1) && lua_isnumber(L, 2) && lua_isnumber(L, 3) && lua_isnumber(L, 4))
    {
        uint32_t templateId = static_cast<uint32_t>(lua_tonumber(L, 1));
        uint32_t mapId = static_cast<uint32_t>(lua_tonumber(L, 2));
        int32_t x = static_cast<int32_t>(lua_tonumber(L, 3));
        int32_t y = static_cast<int32_t>(lua_tonumber(L, 4));
        uint32_t direction = lua_isnumber(L, 5) ? static_cast<uint32_t>(lua_tonumber(L, 5)) : 0;

        uint32_t npcId = CREATE_NPC(templateId, mapId, x, y, direction);
        lua_pushnumber(L, npcId);

        if (npcId != 0)
        {
            LOG_INFO("NPC_API", "Created NPC instance " + std::to_string(npcId) +
                    " from template " + std::to_string(templateId));
        }
    }
    else
    {
        lua_pushnumber(L, 0);
    }
    return 1;
}

int RemoveNPCInstance(lua_State* L)
{
    if (lua_isnumber(L, 1))
    {
        uint32_t npcId = static_cast<uint32_t>(lua_tonumber(L, 1));

        bool success = REMOVE_NPC(npcId);
        lua_pushboolean(L, success ? 1 : 0);

        if (success)
        {
            LOG_INFO("NPC_API", "Removed NPC instance " + std::to_string(npcId));
        }
    }
    else
    {
        lua_pushboolean(L, 0);
    }
    return 1;
}

int GetNearbyNPCs(lua_State* L)
{
    if (lua_isnumber(L, 1) && lua_isnumber(L, 2) && lua_isnumber(L, 3) && lua_isnumber(L, 4))
    {
        uint32_t mapId = static_cast<uint32_t>(lua_tonumber(L, 1));
        int32_t x = static_cast<int32_t>(lua_tonumber(L, 2));
        int32_t y = static_cast<int32_t>(lua_tonumber(L, 3));
        double range = lua_tonumber(L, 4);

        auto npcs = GET_NPCS_IN_RANGE(mapId, x, y, range);

        // 创建Lua表返回NPC列表
        lua_newtable(L);
        for (size_t i = 0; i < npcs.size(); ++i)
        {
            lua_pushnumber(L, i + 1);
            lua_pushnumber(L, npcs[i]->npcId);
            lua_settable(L, -3);
        }

        LOG_DEBUG("NPC_API", "Found " + std::to_string(npcs.size()) + " NPCs in range");
    }
    else
    {
        lua_newtable(L);
    }
    return 1;
}

int GetNPCsInMap(lua_State* L)
{
    if (lua_isnumber(L, 1))
    {
        uint32_t mapId = static_cast<uint32_t>(lua_tonumber(L, 1));

        auto npcs = GET_NPCS_IN_MAP(mapId);

        // 创建Lua表返回NPC列表
        lua_newtable(L);
        for (size_t i = 0; i < npcs.size(); ++i)
        {
            lua_pushnumber(L, i + 1);
            lua_pushnumber(L, npcs[i]->npcId);
            lua_settable(L, -3);
        }

        LOG_DEBUG("NPC_API", "Found " + std::to_string(npcs.size()) + " NPCs in map " + std::to_string(mapId));
    }
    else
    {
        lua_newtable(L);
    }
    return 1;
}

// NPC特效和动画API
int PlayNPCAnimation(lua_State* L)
{
    auto npc = GetNPCFromLua(L, 1);
    if (npc && lua_isstring(L, 2))
    {
        std::string animationName = lua_tostring(L, 2);

        // 这里应该实现实际的动画播放逻辑
        LOG_INFO("NPC_API", "NPC " + npc->name + " playing animation: " + animationName);
        lua_pushboolean(L, 1);
    }
    else
    {
        lua_pushboolean(L, 0);
    }
    return 1;
}

int ShowNPCEffect(lua_State* L)
{
    auto npc = GetNPCFromLua(L, 1);
    if (npc && lua_isstring(L, 2))
    {
        std::string effectName = lua_tostring(L, 2);
        uint32_t duration = lua_isnumber(L, 3) ? static_cast<uint32_t>(lua_tonumber(L, 3)) : 1000;

        // 这里应该实现实际的特效显示逻辑
        LOG_INFO("NPC_API", "NPC " + npc->name + " showing effect: " + effectName +
                " for " + std::to_string(duration) + "ms");
        lua_pushboolean(L, 1);
    }
    else
    {
        lua_pushboolean(L, 0);
    }
    return 1;
}

int SetNPCDirection(lua_State* L)
{
    auto npc = GetNPCFromLua(L, 1);
    if (npc && lua_isnumber(L, 2))
    {
        uint32_t direction = static_cast<uint32_t>(lua_tonumber(L, 2));
        npc->direction = direction;
        lua_pushboolean(L, 1);

        LOG_DEBUG("NPC_API", "Set NPC " + npc->name + " direction to " + std::to_string(direction));
    }
    else
    {
        lua_pushboolean(L, 0);
    }
    return 1;
}

int GetNPCDirection(lua_State* L)
{
    auto npc = GetNPCFromLua(L);
    if (npc)
    {
        lua_pushnumber(L, npc->direction);
    }
    else
    {
        lua_pushnumber(L, 0);
    }
    return 1;
}

// NPC AI控制API
int SetNPCBehavior(lua_State* L)
{
    auto npc = GetNPCFromLua(L, 1);
    if (npc && lua_isnumber(L, 2))
    {
        int behavior = static_cast<int>(lua_tonumber(L, 2));
        npc->behavior = static_cast<NPCBehavior>(behavior);
        lua_pushboolean(L, 1);

        LOG_DEBUG("NPC_API", "Set NPC " + npc->name + " behavior to " + std::to_string(behavior));
    }
    else
    {
        lua_pushboolean(L, 0);
    }
    return 1;
}

int GetNPCBehavior(lua_State* L)
{
    auto npc = GetNPCFromLua(L);
    if (npc)
    {
        lua_pushnumber(L, static_cast<int>(npc->behavior));
    }
    else
    {
        lua_pushnumber(L, 0);
    }
    return 1;
}

int SetNPCAIState(lua_State* L)
{
    auto npc = GetNPCFromLua(L, 1);
    if (npc && lua_isnumber(L, 2))
    {
        int aiState = static_cast<int>(lua_tonumber(L, 2));
        npc->aiState = static_cast<NPCAIState>(aiState);
        lua_pushboolean(L, 1);

        LOG_DEBUG("NPC_API", "Set NPC " + npc->name + " AI state to " + std::to_string(aiState));
    }
    else
    {
        lua_pushboolean(L, 0);
    }
    return 1;
}

int GetNPCAIState(lua_State* L)
{
    auto npc = GetNPCFromLua(L);
    if (npc)
    {
        lua_pushnumber(L, static_cast<int>(npc->aiState));
    }
    else
    {
        lua_pushnumber(L, 0);
    }
    return 1;
}

// NPC事件API
int TriggerNPCEvent(lua_State* L)
{
    if (lua_isnumber(L, 1) && lua_isnumber(L, 2))
    {
        uint32_t npcId = static_cast<uint32_t>(lua_tonumber(L, 1));
        int eventType = static_cast<int>(lua_tonumber(L, 2));
        uint32_t playerId = lua_isnumber(L, 3) ? static_cast<uint32_t>(lua_tonumber(L, 3)) : 0;

        bool success = TRIGGER_NPC_EVENT(npcId, static_cast<NPCEvent>(eventType), playerId);
        lua_pushboolean(L, success ? 1 : 0);
    }
    else
    {
        lua_pushboolean(L, 0);
    }
    return 1;
}

int RegisterNPCEventHandler(lua_State* L)
{
    auto npc = GetNPCFromLua(L, 1);
    if (npc && lua_isstring(L, 2) && lua_isstring(L, 3))
    {
        std::string eventName = lua_tostring(L, 2);
        std::string functionName = lua_tostring(L, 3);

        npc->SetScriptFunction(eventName, functionName);
        lua_pushboolean(L, 1);

        LOG_DEBUG("NPC_API", "Registered event handler for NPC " + npc->name +
                 ": " + eventName + " -> " + functionName);
    }
    else
    {
        lua_pushboolean(L, 0);
    }
    return 1;
}

int UnregisterNPCEventHandler(lua_State* L)
{
    auto npc = GetNPCFromLua(L, 1);
    if (npc && lua_isstring(L, 2))
    {
        std::string eventName = lua_tostring(L, 2);

        npc->SetScriptFunction(eventName, "");
        lua_pushboolean(L, 1);

        LOG_DEBUG("NPC_API", "Unregistered event handler for NPC " + npc->name + ": " + eventName);
    }
    else
    {
        lua_pushboolean(L, 0);
    }
    return 1;
}

} // namespace NPCScriptAPI
} // namespace sword2