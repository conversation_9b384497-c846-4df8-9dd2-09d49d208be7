//---------------------------------------------------------------------------
// Sword2 Combat System (c) 2024
//
// File:	CombatSystem.h
// Date:	2024.07.02
// Code:	AI Assistant
// Desc:	Comprehensive combat system compatible with existing game mechanics
//---------------------------------------------------------------------------
#ifndef COMBAT_SYSTEM_H
#define COMBAT_SYSTEM_H

#include "ModernCpp.h"
#include "UnifiedLoggingSystem.h"
#include "PlayerSystem.h"
#include "NPCSystem.h"
#include "SkillSystem.h"
#include "ItemSystem.h"
#include <string>
#include <unordered_map>
#include <vector>
#include <memory>
#include <chrono>
#include <atomic>
#include <mutex>
#include <functional>

namespace sword2 {

// 战斗状态（对应原有的FightState）
enum class CombatState : uint8_t
{
    Peace = 0,          // 和平状态
    Combat,             // 战斗状态
    PKMode,             // PK模式
    Exercise,           // 切磋模式
    Tournament,         // 比武模式
    TeamFight,          // 团队战斗
    GuildWar,           // 帮会战争
    Siege               // 攻城战
};

// 伤害类型（对应原有的damage类型）
enum class DamageType : uint8_t
{
    Physical = 0,       // 物理伤害
    Magic,              // 法术伤害
    Fire,               // 火焰伤害
    Ice,                // 冰霜伤害
    Lightning,          // 雷电伤害
    Poison,             // 毒素伤害
    True,               // 真实伤害
    Heal                // 治疗
};

// 攻击结果类型
enum class AttackResult : uint8_t
{
    Miss = 0,           // 未命中
    Hit,                // 命中
    Critical,           // 暴击
    Block,              // 格挡
    Dodge,              // 闪避
    Immune,             // 免疫
    Absorb              // 吸收
};

// 战斗事件类型
enum class CombatEvent : uint8_t
{
    AttackStart = 0,    // 攻击开始
    AttackHit,          // 攻击命中
    AttackMiss,         // 攻击未命中
    TakeDamage,         // 受到伤害
    Heal,               // 治疗
    Death,              // 死亡
    Revive,             // 复活
    StateApply,         // 状态施加
    StateRemove,        // 状态移除
    CombatStart,        // 战斗开始
    CombatEnd           // 战斗结束
};

// 战斗状态效果类型（对应原有的state类型）
enum class CombatStateType : uint8_t
{
    None = 0,           // 无状态
    
    // 属性增强
    StrengthBoost,      // 力量增强
    AgilityBoost,       // 敏捷增强
    VitalityBoost,      // 体质增强
    EnergyBoost,        // 精力增强
    
    // 攻击相关
    AttackBoost,        // 攻击力增强
    CriticalBoost,      // 暴击率增强
    HitBoost,           // 命中率增强
    AttackSpeedBoost,   // 攻击速度增强
    
    // 防御相关
    DefenseBoost,       // 防御力增强
    DodgeBoost,         // 闪避率增强
    BlockBoost,         // 格挡率增强
    ResistBoost,        // 抗性增强
    
    // 生命相关
    LifeBoost,          // 生命值增强
    LifeRegen,          // 生命恢复
    ManaBoost,          // 内力增强
    ManaRegen,          // 内力恢复
    
    // 负面状态
    Poison,             // 中毒
    Burn,               // 燃烧
    Freeze,             // 冰冻
    Stun,               // 眩晕
    Silence,            // 沉默
    Slow,               // 减速
    Weakness,           // 虚弱
    
    // 特殊状态
    Invisible,          // 隐身
    Invincible,         // 无敌
    Shield,             // 护盾
    Reflect,            // 反射
    Absorb,             // 吸收
    Immune              // 免疫
};

// 战斗状态效果
struct CombatStateEffect
{
    uint32_t effectId = 0;          // 效果ID
    CombatStateType type = CombatStateType::None;
    uint32_t casterId = 0;          // 施法者ID
    uint32_t targetId = 0;          // 目标ID
    uint32_t skillId = 0;           // 技能ID
    
    int32_t value = 0;              // 效果值
    uint32_t duration = 0;          // 持续时间（毫秒）
    uint32_t interval = 0;          // 触发间隔（毫秒）
    uint32_t stackCount = 1;        // 叠加层数
    uint32_t maxStack = 1;          // 最大叠加
    
    std::chrono::system_clock::time_point startTime;
    std::chrono::system_clock::time_point lastTrigger;
    
    bool isActive = true;           // 是否激活
    bool canDispel = true;          // 是否可驱散
    bool isDebuff = false;          // 是否为负面效果
    
    CombatStateEffect() = default;
    CombatStateEffect(uint32_t id, CombatStateType stateType, uint32_t caster, uint32_t target, 
                     int32_t val, uint32_t dur, uint32_t skill = 0)
        : effectId(id), type(stateType), casterId(caster), targetId(target), 
          value(val), duration(dur), skillId(skill)
    {
        startTime = std::chrono::system_clock::now();
        lastTrigger = startTime;
        
        // 设置负面效果标记
        isDebuff = (type >= CombatStateType::Poison && type <= CombatStateType::Weakness);
    }
    
    // 检查是否过期
    bool IsExpired() const
    {
        if (duration == 0) return false; // 永久效果
        
        auto now = std::chrono::system_clock::now();
        auto elapsed = std::chrono::duration_cast<std::chrono::milliseconds>(now - startTime);
        return elapsed.count() >= duration;
    }
    
    // 获取剩余时间
    uint32_t GetRemainingTime() const
    {
        if (duration == 0) return UINT32_MAX;
        
        auto now = std::chrono::system_clock::now();
        auto elapsed = std::chrono::duration_cast<std::chrono::milliseconds>(now - startTime);
        uint32_t elapsedMs = static_cast<uint32_t>(elapsed.count());
        
        return (elapsedMs >= duration) ? 0 : (duration - elapsedMs);
    }
    
    // 检查是否需要触发
    bool ShouldTrigger() const
    {
        if (interval == 0) return false;
        
        auto now = std::chrono::system_clock::now();
        auto elapsed = std::chrono::duration_cast<std::chrono::milliseconds>(now - lastTrigger);
        return elapsed.count() >= interval;
    }
    
    // 更新触发时间
    void UpdateTriggerTime()
    {
        lastTrigger = std::chrono::system_clock::now();
    }
    
    // 增加叠加层数
    bool AddStack(uint32_t count = 1)
    {
        if (stackCount + count <= maxStack)
        {
            stackCount += count;
            return true;
        }
        return false;
    }
    
    // 获取状态描述
    std::string GetDescription() const
    {
        switch (type)
        {
        case CombatStateType::StrengthBoost: return "力量增强 +" + std::to_string(value);
        case CombatStateType::AttackBoost: return "攻击力增强 +" + std::to_string(value);
        case CombatStateType::DefenseBoost: return "防御力增强 +" + std::to_string(value);
        case CombatStateType::LifeRegen: return "生命恢复 +" + std::to_string(value) + "/秒";
        case CombatStateType::Poison: return "中毒 -" + std::to_string(value) + "/秒";
        case CombatStateType::Stun: return "眩晕";
        case CombatStateType::Invisible: return "隐身";
        case CombatStateType::Invincible: return "无敌";
        default: return "未知状态";
        }
    }
};

// 伤害信息
struct DamageInfo
{
    uint32_t attackerId = 0;        // 攻击者ID
    uint32_t targetId = 0;          // 目标ID
    uint32_t skillId = 0;           // 技能ID
    
    DamageType damageType = DamageType::Physical;
    uint32_t baseDamage = 0;        // 基础伤害
    uint32_t finalDamage = 0;       // 最终伤害
    
    AttackResult result = AttackResult::Hit;
    bool isCritical = false;        // 是否暴击
    bool isBlocked = false;         // 是否被格挡
    bool isDodged = false;          // 是否被闪避
    
    float criticalMultiplier = 1.5f; // 暴击倍数
    float damageReduction = 0.0f;   // 伤害减免
    
    std::chrono::system_clock::time_point timestamp;
    
    DamageInfo() = default;
    DamageInfo(uint32_t attacker, uint32_t target, uint32_t damage, DamageType type = DamageType::Physical)
        : attackerId(attacker), targetId(target), baseDamage(damage), finalDamage(damage), damageType(type)
    {
        timestamp = std::chrono::system_clock::now();
    }
};

// 治疗信息
struct HealInfo
{
    uint32_t healerId = 0;          // 治疗者ID
    uint32_t targetId = 0;          // 目标ID
    uint32_t skillId = 0;           // 技能ID
    
    uint32_t baseHeal = 0;          // 基础治疗量
    uint32_t finalHeal = 0;         // 最终治疗量
    
    bool isCritical = false;        // 是否暴击治疗
    float criticalMultiplier = 1.5f; // 暴击倍数
    float healBonus = 0.0f;         // 治疗加成
    
    std::chrono::system_clock::time_point timestamp;
    
    HealInfo() = default;
    HealInfo(uint32_t healer, uint32_t target, uint32_t heal)
        : healerId(healer), targetId(target), baseHeal(heal), finalHeal(heal)
    {
        timestamp = std::chrono::system_clock::now();
    }
};

// 战斗参与者
class CombatParticipant
{
public:
    uint32_t entityId = 0;          // 实体ID
    bool isPlayer = true;           // 是否为玩家
    CombatState combatState = CombatState::Peace;
    
    // 战斗统计
    uint32_t totalDamageDealt = 0;  // 总伤害输出
    uint32_t totalDamageTaken = 0;  // 总伤害承受
    uint32_t totalHealingDone = 0;  // 总治疗量
    uint32_t killCount = 0;         // 击杀数
    uint32_t deathCount = 0;        // 死亡数
    
    // 状态效果
    std::vector<uint32_t> activeEffects; // 激活的状态效果ID
    
    // 战斗目标
    uint32_t currentTarget = 0;     // 当前目标
    std::chrono::system_clock::time_point lastAttackTime;
    std::chrono::system_clock::time_point combatStartTime;
    
    CombatParticipant() = default;
    CombatParticipant(uint32_t id, bool player) : entityId(id), isPlayer(player) {}
    
    // 进入战斗
    void EnterCombat(uint32_t targetId = 0)
    {
        if (combatState == CombatState::Peace)
        {
            combatState = CombatState::Combat;
            combatStartTime = std::chrono::system_clock::now();
            currentTarget = targetId;
        }
    }
    
    // 退出战斗
    void ExitCombat()
    {
        combatState = CombatState::Peace;
        currentTarget = 0;
    }
    
    // 检查是否在战斗中
    bool IsInCombat() const
    {
        return combatState != CombatState::Peace;
    }
    
    // 获取战斗时间
    uint32_t GetCombatDuration() const
    {
        if (!IsInCombat()) return 0;
        
        auto now = std::chrono::system_clock::now();
        auto duration = std::chrono::duration_cast<std::chrono::seconds>(now - combatStartTime);
        return static_cast<uint32_t>(duration.count());
    }
    
    // 添加状态效果
    void AddEffect(uint32_t effectId)
    {
        activeEffects.push_back(effectId);
    }
    
    // 移除状态效果
    void RemoveEffect(uint32_t effectId)
    {
        activeEffects.erase(std::remove(activeEffects.begin(), activeEffects.end(), effectId), 
                           activeEffects.end());
    }
    
    // 检查是否有特定状态
    bool HasEffectType(CombatStateType type) const;
    
    // 获取DPS
    float GetDPS() const
    {
        uint32_t duration = GetCombatDuration();
        return duration > 0 ? static_cast<float>(totalDamageDealt) / duration : 0.0f;
    }
    
    // 获取HPS
    float GetHPS() const
    {
        uint32_t duration = GetCombatDuration();
        return duration > 0 ? static_cast<float>(totalHealingDone) / duration : 0.0f;
    }
};

// 战斗计算器
class CombatCalculator
{
public:
    // 计算命中率
    static bool CalculateHit(const Player& attacker, const Player& target)
    {
        uint32_t attackerHit = attacker.attributes.hit;
        uint32_t targetDodge = target.attributes.dodge;
        
        // 基础命中率计算（参考原有算法）
        int32_t hitChance = 0;
        if (targetDodge < 0)
        {
            hitChance = 95; // 最大命中率
        }
        else if ((attackerHit + targetDodge) == 0)
        {
            hitChance = 50;
        }
        else
        {
            hitChance = attackerHit * 100 / (attackerHit + targetDodge);
        }
        
        // 限制命中率范围
        if (hitChance > 95) hitChance = 95;
        if (hitChance < 40) hitChance = 40;
        
        return (rand() % 100) < hitChance;
    }
    
    // 计算暴击
    static bool CalculateCritical(const Player& attacker, const Player& target)
    {
        uint32_t criticalRate = attacker.attributes.criticalRate;
        
        // 暴击率计算
        return (rand() % 100) < criticalRate;
    }
    
    // 计算格挡
    static bool CalculateBlock(const Player& target)
    {
        // 这里可以根据装备和技能计算格挡率
        uint32_t blockRate = 0; // 暂时设为0
        return (rand() % 100) < blockRate;
    }
    
    // 计算物理伤害
    static uint32_t CalculatePhysicalDamage(const Player& attacker, const Player& target, bool isCritical = false)
    {
        // 基础攻击力
        uint32_t minAttack = attacker.attributes.minAttack;
        uint32_t maxAttack = attacker.attributes.maxAttack;
        uint32_t damage = minAttack + (rand() % (maxAttack - minAttack + 1));
        
        // 暴击伤害
        if (isCritical)
        {
            damage = static_cast<uint32_t>(damage * (attacker.attributes.criticalDamage / 100.0f));
        }
        
        // 防御减免
        uint32_t defense = target.attributes.defense;
        if (damage > defense)
        {
            damage -= defense;
        }
        else
        {
            damage = 1; // 最少造成1点伤害
        }
        
        return damage;
    }
    
    // 计算法术伤害
    static uint32_t CalculateMagicDamage(const Player& attacker, const Player& target, bool isCritical = false)
    {
        // 基础法术攻击力
        uint32_t minMagicAttack = attacker.attributes.minMagicAttack;
        uint32_t maxMagicAttack = attacker.attributes.maxMagicAttack;
        uint32_t damage = minMagicAttack + (rand() % (maxMagicAttack - minMagicAttack + 1));
        
        // 暴击伤害
        if (isCritical)
        {
            damage = static_cast<uint32_t>(damage * (attacker.attributes.criticalDamage / 100.0f));
        }
        
        // 法术防御减免
        uint32_t magicDefense = target.attributes.magicDefense;
        if (damage > magicDefense)
        {
            damage -= magicDefense;
        }
        else
        {
            damage = 1; // 最少造成1点伤害
        }
        
        return damage;
    }
    
    // 计算技能伤害
    static uint32_t CalculateSkillDamage(uint32_t skillId, uint32_t level, const Player& caster, const Player& target)
    {
        // 这里应该调用技能系统的伤害计算
        auto skillTemplate = GET_SKILL_TEMPLATE(skillId);
        if (!skillTemplate) return 0;
        
        const auto* levelData = skillTemplate->GetLevelData(level);
        if (!levelData) return 0;
        
        // 基础伤害
        uint32_t baseDamage = levelData->damage;
        
        // 属性加成
        uint32_t attributeBonus = 0;
        switch (skillTemplate->series)
        {
        case PlayerSeries::Shaolin:
        case PlayerSeries::Wudang:
            attributeBonus = caster.attributes.strength / 5 + caster.attributes.energy / 5;
            break;
        case PlayerSeries::Emei:
        case PlayerSeries::Kunlun:
            attributeBonus = caster.attributes.energy / 3 + caster.attributes.vitality / 5;
            break;
        case PlayerSeries::Tangmen:
        case PlayerSeries::Wudu:
            attributeBonus = caster.attributes.agility / 3 + caster.attributes.energy / 5;
            break;
        default:
            attributeBonus = (caster.attributes.strength + caster.attributes.energy) / 8;
            break;
        }
        
        // 等级加成
        uint32_t levelBonus = level * 10;
        
        uint32_t totalDamage = baseDamage + attributeBonus + levelBonus;
        
        // 应用目标抗性
        if (skillTemplate->damageType == DamageType::Fire)
        {
            totalDamage = totalDamage * (100 - target.attributes.fireResist) / 100;
        }
        else if (skillTemplate->damageType == DamageType::Ice)
        {
            totalDamage = totalDamage * (100 - target.attributes.iceResist) / 100;
        }
        else if (skillTemplate->damageType == DamageType::Lightning)
        {
            totalDamage = totalDamage * (100 - target.attributes.lightningResist) / 100;
        }
        else if (skillTemplate->damageType == DamageType::Poison)
        {
            totalDamage = totalDamage * (100 - target.attributes.poisonResist) / 100;
        }
        
        return totalDamage;
    }
    
    // 计算治疗量
    static uint32_t CalculateHealAmount(uint32_t skillId, uint32_t level, const Player& healer)
    {
        auto skillTemplate = GET_SKILL_TEMPLATE(skillId);
        if (!skillTemplate) return 0;
        
        const auto* levelData = skillTemplate->GetLevelData(level);
        if (!levelData) return 0;
        
        // 基础治疗量
        uint32_t baseHeal = levelData->heal;
        
        // 属性加成（主要基于精力和体质）
        uint32_t attributeBonus = healer.attributes.energy / 3 + healer.attributes.vitality / 5;
        
        // 等级加成
        uint32_t levelBonus = level * 8;
        
        return baseHeal + attributeBonus + levelBonus;
    }
};

} // namespace sword2

#endif // COMBAT_SYSTEM_H
