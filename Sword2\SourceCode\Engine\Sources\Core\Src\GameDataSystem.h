//---------------------------------------------------------------------------
// Sword2 Game Data System (c) 2024
//
// File:	GameDataSystem.h
// Date:	2024.07.02
// Code:	AI Assistant
// Desc:	Comprehensive game data management system for Sword2
//---------------------------------------------------------------------------
#ifndef GAME_DATA_SYSTEM_H
#define GAME_DATA_SYSTEM_H

#include "ModernCpp.h"
#include "UnifiedLoggingSystem.h"
#include <string>
#include <unordered_map>
#include <vector>
#include <fstream>
#include <sstream>
#include <memory>
#include <functional>

namespace sword2 {

// 游戏数据类型枚举
enum class GameDataType : uint8_t
{
    Weapon = 0,         // 武器
    Armor,              // 防具
    Item,               // 道具
    Skill,              // 技能
    NPC,                // NPC
    Map,                // 地图
    Quest,              // 任务
    DropRate,           // 掉落率
    Setting             // 设置
};

// 数据文件格式
enum class DataFileFormat : uint8_t
{
    TXT = 0,            // 文本文件
    INI,                // INI配置文件
    LUA                 // Lua脚本文件
};

// 基础游戏数据项
struct GameDataItem
{
    uint32_t id = 0;
    std::string name;
    std::string description;
    std::unordered_map<std::string, std::string> properties;
    
    GameDataItem() = default;
    GameDataItem(uint32_t itemId, const std::string& itemName) 
        : id(itemId), name(itemName) {}
    
    // 获取属性值
    template<typename T>
    T GetProperty(const std::string& key, const T& defaultValue = T{}) const
    {
        auto it = properties.find(key);
        if (it != properties.end())
        {
            if constexpr (std::is_same_v<T, std::string>)
            {
                return it->second;
            }
            else if constexpr (std::is_same_v<T, int>)
            {
                return std::stoi(it->second);
            }
            else if constexpr (std::is_same_v<T, float>)
            {
                return std::stof(it->second);
            }
            else if constexpr (std::is_same_v<T, double>)
            {
                return std::stod(it->second);
            }
            else if constexpr (std::is_same_v<T, bool>)
            {
                return it->second == "1" || it->second == "true";
            }
        }
        return defaultValue;
    }
    
    // 设置属性值
    template<typename T>
    void SetProperty(const std::string& key, const T& value)
    {
        if constexpr (std::is_same_v<T, std::string>)
        {
            properties[key] = value;
        }
        else
        {
            properties[key] = std::to_string(value);
        }
    }
};

// 武器数据结构
struct WeaponData : public GameDataItem
{
    uint32_t weaponType = 0;        // 武器类型
    uint32_t subType = 0;           // 子类型
    uint32_t level = 1;             // 等级
    uint32_t price = 0;             // 价格
    uint32_t durability = 100;      // 耐久度
    uint32_t minDamage = 0;         // 最小伤害
    uint32_t maxDamage = 0;         // 最大伤害
    uint32_t minMagicDamage = 0;    // 最小法术伤害
    uint32_t maxMagicDamage = 0;    // 最大法术伤害
    uint32_t attackSpeed = 100;     // 攻击速度
    uint32_t requiredLevel = 1;     // 需求等级
    uint32_t requiredSeries = 0;    // 需求门派
    std::string imagePath;          // 图像路径
    std::string scriptFile;         // 脚本文件
    
    WeaponData() = default;
    WeaponData(uint32_t id, const std::string& name) : GameDataItem(id, name) {}
};

// 地图数据结构
struct MapData : public GameDataItem
{
    uint32_t worldId = 0;           // 世界ID
    std::string mapFile;            // 地图文件
    uint32_t maxPlayers = 100;      // 最大玩家数
    bool isPvpEnabled = false;      // 是否允许PVP
    std::vector<std::pair<int, int>> spawnPoints; // 出生点
    std::vector<std::pair<int, int>> npcPositions; // NPC位置
    
    MapData() = default;
    MapData(uint32_t id, const std::string& name) : GameDataItem(id, name) {}
};

// 数据文件解析器接口
class IDataFileParser
{
public:
    virtual ~IDataFileParser() = default;
    virtual bool ParseFile(const std::string& filePath, std::vector<GameDataItem>& items) = 0;
    virtual DataFileFormat GetSupportedFormat() const = 0;
};

// TXT文件解析器
class TxtFileParser : public IDataFileParser
{
public:
    bool ParseFile(const std::string& filePath, std::vector<GameDataItem>& items) override
    {
        std::ifstream file(filePath);
        if (!file.is_open())
        {
            LOG_ERROR("DATA", "Failed to open file: " + filePath);
            return false;
        }
        
        std::string line;
        std::vector<std::string> headers;
        bool isFirstLine = true;
        uint32_t lineNumber = 0;
        
        while (std::getline(file, line))
        {
            lineNumber++;
            
            // 跳过空行和注释行
            if (line.empty() || line[0] == '#' || line[0] == ';')
                continue;
            
            // 解析制表符分隔的数据
            std::vector<std::string> columns = SplitString(line, '\t');
            
            if (isFirstLine)
            {
                // 第一行是表头
                headers = columns;
                isFirstLine = false;
                continue;
            }
            
            if (columns.size() < 2)
                continue;
            
            // 创建数据项
            GameDataItem item;
            
            // 尝试解析ID（第一列通常是名称，第二列可能是ID）
            if (columns.size() > 1 && !columns[1].empty())
            {
                try
                {
                    item.id = std::stoul(columns[1]);
                }
                catch (...)
                {
                    item.id = lineNumber; // 使用行号作为ID
                }
            }
            else
            {
                item.id = lineNumber;
            }
            
            // 设置名称
            item.name = columns[0];
            
            // 设置属性
            for (size_t i = 0; i < columns.size() && i < headers.size(); ++i)
            {
                if (!columns[i].empty())
                {
                    item.properties[headers[i]] = columns[i];
                }
            }
            
            items.push_back(std::move(item));
        }
        
        LOG_INFO("DATA", "Parsed " + std::to_string(items.size()) + " items from " + filePath);
        return true;
    }
    
    DataFileFormat GetSupportedFormat() const override
    {
        return DataFileFormat::TXT;
    }

private:
    std::vector<std::string> SplitString(const std::string& str, char delimiter)
    {
        std::vector<std::string> result;
        std::stringstream ss(str);
        std::string item;
        
        while (std::getline(ss, item, delimiter))
        {
            result.push_back(item);
        }
        
        return result;
    }
};

// INI文件解析器
class IniFileParser : public IDataFileParser
{
public:
    bool ParseFile(const std::string& filePath, std::vector<GameDataItem>& items) override
    {
        std::ifstream file(filePath);
        if (!file.is_open())
        {
            LOG_ERROR("DATA", "Failed to open INI file: " + filePath);
            return false;
        }
        
        std::string line;
        std::string currentSection;
        GameDataItem currentItem;
        bool hasCurrentItem = false;
        
        while (std::getline(file, line))
        {
            // 移除前后空白
            line = TrimString(line);
            
            // 跳过空行和注释
            if (line.empty() || line[0] == ';' || line[0] == '#')
                continue;
            
            // 检查是否是节
            if (line[0] == '[' && line.back() == ']')
            {
                // 保存之前的项
                if (hasCurrentItem)
                {
                    items.push_back(std::move(currentItem));
                    currentItem = GameDataItem();
                }
                
                currentSection = line.substr(1, line.length() - 2);
                currentItem.name = currentSection;
                currentItem.id = static_cast<uint32_t>(items.size() + 1);
                hasCurrentItem = true;
                continue;
            }
            
            // 解析键值对
            size_t equalPos = line.find('=');
            if (equalPos != std::string::npos)
            {
                std::string key = TrimString(line.substr(0, equalPos));
                std::string value = TrimString(line.substr(equalPos + 1));
                
                if (hasCurrentItem)
                {
                    currentItem.properties[key] = value;
                }
            }
        }
        
        // 保存最后一个项
        if (hasCurrentItem)
        {
            items.push_back(std::move(currentItem));
        }
        
        LOG_INFO("DATA", "Parsed " + std::to_string(items.size()) + " sections from " + filePath);
        return true;
    }
    
    DataFileFormat GetSupportedFormat() const override
    {
        return DataFileFormat::INI;
    }

private:
    std::string TrimString(const std::string& str)
    {
        size_t start = str.find_first_not_of(" \t\r\n");
        if (start == std::string::npos)
            return "";
        
        size_t end = str.find_last_not_of(" \t\r\n");
        return str.substr(start, end - start + 1);
    }
};

// 游戏数据管理器
class GameDataManager : public Singleton<GameDataManager>
{
public:
    GameDataManager()
    {
        // 注册默认解析器
        RegisterParser(std::make_unique<TxtFileParser>());
        RegisterParser(std::make_unique<IniFileParser>());
    }
    
    ~GameDataManager() = default;
    
    // 注册数据文件解析器
    void RegisterParser(std::unique_ptr<IDataFileParser> parser)
    {
        m_parsers[parser->GetSupportedFormat()] = std::move(parser);
    }
    
    // 加载数据文件
    bool LoadDataFile(const std::string& filePath, GameDataType dataType)
    {
        // 确定文件格式
        DataFileFormat format = DetermineFileFormat(filePath);
        
        auto parserIt = m_parsers.find(format);
        if (parserIt == m_parsers.end())
        {
            LOG_ERROR("DATA", "No parser available for file format: " + filePath);
            return false;
        }
        
        std::vector<GameDataItem> items;
        if (!parserIt->second->ParseFile(filePath, items))
        {
            LOG_ERROR("DATA", "Failed to parse file: " + filePath);
            return false;
        }
        
        // 存储数据
        m_gameData[dataType] = std::move(items);
        
        LOG_INFO("DATA", "Loaded " + std::to_string(m_gameData[dataType].size()) + 
                " items of type " + std::to_string(static_cast<int>(dataType)));
        
        return true;
    }
    
    // 获取指定类型的所有数据
    const std::vector<GameDataItem>& GetDataByType(GameDataType dataType) const
    {
        static std::vector<GameDataItem> emptyVector;
        auto it = m_gameData.find(dataType);
        return (it != m_gameData.end()) ? it->second : emptyVector;
    }
    
    // 根据ID获取数据项
    const GameDataItem* GetDataById(GameDataType dataType, uint32_t id) const
    {
        auto it = m_gameData.find(dataType);
        if (it != m_gameData.end())
        {
            for (const auto& item : it->second)
            {
                if (item.id == id)
                {
                    return &item;
                }
            }
        }
        return nullptr;
    }
    
    // 根据名称获取数据项
    const GameDataItem* GetDataByName(GameDataType dataType, const std::string& name) const
    {
        auto it = m_gameData.find(dataType);
        if (it != m_gameData.end())
        {
            for (const auto& item : it->second)
            {
                if (item.name == name)
                {
                    return &item;
                }
            }
        }
        return nullptr;
    }
    
    // 重新加载所有数据
    bool ReloadAllData()
    {
        LOG_INFO("DATA", "Reloading all game data...");
        
        bool success = true;
        for (const auto& [filePath, dataType] : m_loadedFiles)
        {
            if (!LoadDataFile(filePath, dataType))
            {
                success = false;
            }
        }
        
        LOG_INFO("DATA", "Data reload " + std::string(success ? "completed" : "failed"));
        return success;
    }
    
    // 获取数据统计信息
    struct DataStatistics
    {
        size_t totalItems = 0;
        std::unordered_map<GameDataType, size_t> itemsByType;
    };
    
    DataStatistics GetStatistics() const
    {
        DataStatistics stats;
        
        for (const auto& [dataType, items] : m_gameData)
        {
            stats.itemsByType[dataType] = items.size();
            stats.totalItems += items.size();
        }
        
        return stats;
    }

private:
    std::unordered_map<DataFileFormat, std::unique_ptr<IDataFileParser>> m_parsers;
    std::unordered_map<GameDataType, std::vector<GameDataItem>> m_gameData;
    std::unordered_map<std::string, GameDataType> m_loadedFiles;
    
    DataFileFormat DetermineFileFormat(const std::string& filePath)
    {
        size_t dotPos = filePath.find_last_of('.');
        if (dotPos != std::string::npos)
        {
            std::string extension = filePath.substr(dotPos + 1);
            std::transform(extension.begin(), extension.end(), extension.begin(), ::tolower);
            
            if (extension == "ini")
                return DataFileFormat::INI;
            else if (extension == "lua")
                return DataFileFormat::LUA;
            else
                return DataFileFormat::TXT;
        }
        
        return DataFileFormat::TXT;
    }
};

} // namespace sword2

// 全局游戏数据管理器访问
#define GAME_DATA_MANAGER() sword2::GameDataManager::getInstance()

// 便捷宏定义
#define LOAD_GAME_DATA(filePath, dataType) \
    GAME_DATA_MANAGER().LoadDataFile(filePath, dataType)

#define GET_GAME_DATA(dataType) \
    GAME_DATA_MANAGER().GetDataByType(dataType)

#define GET_DATA_BY_ID(dataType, id) \
    GAME_DATA_MANAGER().GetDataById(dataType, id)

#define GET_DATA_BY_NAME(dataType, name) \
    GAME_DATA_MANAGER().GetDataByName(dataType, name)

#define RELOAD_ALL_DATA() \
    GAME_DATA_MANAGER().ReloadAllData()

#endif // GAME_DATA_SYSTEM_H
