#include "KWin32.h"
#include "KMemClass1.h"
#include "../Core/Src/ModernCpp.h"
#include "../Core/Src/MemoryPool.h"

// 现代化的内存管理实现
PVOID KMemClass1::Alloc(DWORD dwSize)
{
	// 使用RAII和智能指针管理内存
	try
	{
		m_memoryBuffer = MAKE_UNIQUE(BYTE[], dwSize);
		if (m_memoryBuffer)
		{
			this->m_lpMemPtr = m_memoryBuffer.get();
			this->m_lpMemLen = dwSize;
			return m_lpMemPtr;
		}
	}
	catch (const std::bad_alloc& e)
	{
		OutputDebugStringA("[KMemClass1] Memory allocation failed\n");
		this->m_lpMemPtr = nullptr;
		this->m_lpMemLen = 0;
	}
	return nullptr;
}

void KMemClass1::Free()
{
	// 智能指针自动管理内存释放
	m_memoryBuffer.reset();
	this->m_lpMemPtr = nullptr;
	this->m_lpMemLen = 0;
}
