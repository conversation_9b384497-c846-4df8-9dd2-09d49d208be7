//---------------------------------------------------------------------------
// Sword2 Monitoring System Integration (c) 2024
//
// File:	MonitoringSystemIntegration.cpp
// Date:	2024.07.02
// Code:	AI Assistant
// Desc:	Complete monitoring system integration and demonstration
//---------------------------------------------------------------------------

#include "KCore.h"
#include "UnifiedLoggingSystem.h"
#include "ModernPerformanceMonitor.h"
#include "BusinessMonitor.h"
#include "AlertNotificationSystem.h"
#include "OperationsDashboard.h"

namespace sword2 {

// 监控系统集成管理器
class MonitoringSystemIntegration
{
public:
    MonitoringSystemIntegration()
    {
        m_initialized = false;
    }
    
    ~MonitoringSystemIntegration()
    {
        Shutdown();
    }
    
    bool Initialize()
    {
        if (m_initialized) return true;
        
        printf("[MONITORING] Initializing comprehensive monitoring system...\n");
        
        // 1. 初始化统一日志系统
        if (!InitializeLoggingSystem())
        {
            printf("[MONITORING] Failed to initialize logging system\n");
            return false;
        }
        
        // 2. 初始化性能监控系统
        if (!InitializePerformanceMonitoring())
        {
            printf("[MONITORING] Failed to initialize performance monitoring\n");
            return false;
        }
        
        // 3. 初始化业务监控系统
        if (!InitializeBusinessMonitoring())
        {
            printf("[MONITORING] Failed to initialize business monitoring\n");
            return false;
        }
        
        // 4. 初始化告警通知系统
        if (!InitializeAlertSystem())
        {
            printf("[MONITORING] Failed to initialize alert system\n");
            return false;
        }
        
        // 5. 初始化运维仪表板
        if (!InitializeDashboard())
        {
            printf("[MONITORING] Failed to initialize dashboard\n");
            return false;
        }
        
        // 6. 设置系统集成
        SetupSystemIntegration();
        
        m_initialized = true;
        LOG_INFO("MONITORING", "Complete monitoring system initialized successfully");
        
        return true;
    }
    
    void Shutdown()
    {
        if (!m_initialized) return;
        
        LOG_INFO("MONITORING", "Shutting down monitoring system...");
        
        // 按相反顺序关闭系统
        STOP_DASHBOARD();
        STOP_ALERT_SYSTEM();
        STOP_BUSINESS_MONITORING();
        STOP_MODERN_PERF_MONITORING();
        
        // 最后关闭日志系统
        LOGGER().Shutdown();
        
        m_initialized = false;
        printf("[MONITORING] Monitoring system shutdown complete\n");
    }
    
    void DemonstrateMonitoringSystem()
    {
        if (!m_initialized)
        {
            printf("[MONITORING] System not initialized\n");
            return;
        }
        
        LOG_INFO("MONITORING", "Starting monitoring system demonstration...");
        
        // 演示各个监控组件
        DemonstrateLogging();
        DemonstratePerformanceMonitoring();
        DemonstrateBusinessMonitoring();
        DemonstrateAlertSystem();
        DemonstrateDashboard();
        
        LOG_INFO("MONITORING", "Monitoring system demonstration completed");
    }

private:
    bool m_initialized;
    
    bool InitializeLoggingSystem()
    {
        printf("[MONITORING] Initializing unified logging system...\n");
        
        // 初始化日志系统
        LOGGER().Initialize();
        
        // 添加文件输出器
        LOGGER().AddFileAppender("sword2_system.log", LogLevel::Info);
        LOGGER().AddFileAppender("sword2_error.log", LogLevel::Error);
        LOGGER().AddFileAppender("sword2_debug.log", LogLevel::Debug);
        
        // 设置全局日志级别
        LOGGER().SetGlobalLevel(LogLevel::Debug);
        
        LOG_INFO("LOGGING", "Unified logging system initialized");
        return true;
    }
    
    bool InitializePerformanceMonitoring()
    {
        printf("[MONITORING] Initializing performance monitoring...\n");
        
        // 启动性能监控
        if (!START_MODERN_PERF_MONITORING())
        {
            return false;
        }
        
        // 设置收集间隔
        MODERN_PERF_MONITOR().SetCollectInterval(std::chrono::seconds(5));
        
        // 添加性能告警规则
        ADD_PERF_ALERT(MetricType::CPU_Usage, 80.0, true, 
                      std::chrono::seconds(300), "High CPU usage detected");
        ADD_PERF_ALERT(MetricType::Memory_Usage, 85.0, true, 
                      std::chrono::seconds(300), "High memory usage detected");
        
        LOG_INFO("PERF", "Performance monitoring initialized");
        return true;
    }
    
    bool InitializeBusinessMonitoring()
    {
        printf("[MONITORING] Initializing business monitoring...\n");
        
        // 启动业务监控
        if (!START_BUSINESS_MONITORING())
        {
            return false;
        }
        
        LOG_INFO("BUSINESS", "Business monitoring initialized");
        return true;
    }
    
    bool InitializeAlertSystem()
    {
        printf("[MONITORING] Initializing alert system...\n");
        
        // 启动告警系统
        if (!START_ALERT_SYSTEM())
        {
            return false;
        }
        
        // 添加通知发送器
        ALERT_MANAGER().AddNotificationSender(
            std::make_unique<ConsoleNotificationSender>());
        
        ALERT_MANAGER().AddNotificationSender(
            std::make_unique<FileNotificationSender>("alerts.log"));
        
        // 添加告警规则
        SetupAlertRules();
        
        LOG_INFO("ALERT", "Alert system initialized");
        return true;
    }
    
    bool InitializeDashboard()
    {
        printf("[MONITORING] Initializing operations dashboard...\n");
        
        // 配置仪表板
        DashboardConfig config;
        config.title = "Sword2 Game Server Dashboard";
        config.refreshInterval = std::chrono::seconds(10);
        config.enableRealTimeUpdates = true;
        config.theme = "dark";
        
        // 启动仪表板
        if (!START_DASHBOARD(config))
        {
            return false;
        }
        
        LOG_INFO("DASHBOARD", "Operations dashboard initialized");
        return true;
    }
    
    void SetupAlertRules()
    {
        // 系统性能告警规则
        AlertRule cpuRule("cpu_high", "High CPU Usage", AlertLevel::Warning);
        cpuRule.description = "CPU usage exceeds 80% for more than 5 minutes";
        cpuRule.channels = {NotificationChannel::Console, NotificationChannel::File};
        ALERT_MANAGER().AddAlertRule(cpuRule);
        
        AlertRule memoryRule("memory_high", "High Memory Usage", AlertLevel::Warning);
        memoryRule.description = "Memory usage exceeds 85% for more than 5 minutes";
        memoryRule.channels = {NotificationChannel::Console, NotificationChannel::File};
        ALERT_MANAGER().AddAlertRule(memoryRule);
        
        // 业务告警规则
        AlertRule userRule("users_low", "Low User Activity", AlertLevel::Info);
        userRule.description = "Online user count is below normal levels";
        userRule.channels = {NotificationChannel::Console};
        ALERT_MANAGER().AddAlertRule(userRule);
        
        AlertRule errorRule("error_rate_high", "High Error Rate", AlertLevel::Critical);
        errorRule.description = "Error rate exceeds acceptable threshold";
        errorRule.channels = {NotificationChannel::Console, NotificationChannel::File};
        ALERT_MANAGER().AddAlertRule(errorRule);
    }
    
    void SetupSystemIntegration()
    {
        // 设置系统间的集成和数据流
        LOG_INFO("MONITORING", "Setting up system integration...");
        
        // 这里可以设置各系统间的数据共享和事件传递
        // 例如：性能监控触发告警、业务事件记录日志等
    }
    
    void DemonstrateLogging()
    {
        LOG_INFO("DEMO", "=== Logging System Demonstration ===");
        
        // 演示不同级别的日志
        LOG_TRACE("DEMO", "This is a trace message");
        LOG_DEBUG("DEMO", "This is a debug message");
        LOG_INFO("DEMO", "This is an info message");
        LOG_WARNING("DEMO", "This is a warning message");
        LOG_ERROR("DEMO", "This is an error message");
        LOG_CRITICAL("DEMO", "This is a critical message");
        
        // 演示带元数据的日志
        std::unordered_map<std::string, std::string> metadata;
        metadata["user_id"] = "12345";
        metadata["action"] = "login";
        metadata["ip_address"] = "*************";
        LOG_WITH_META(LogLevel::Info, "USER_ACTION", "User login successful", metadata);
        
        // 演示格式化日志
        LOG_INFO_F("DEMO", "User %s performed action %s at %s", "Alice", "purchase", "2024-07-02 10:30:00");
        
        LOG_INFO("DEMO", "Logging demonstration completed");
    }
    
    void DemonstratePerformanceMonitoring()
    {
        LOG_INFO("DEMO", "=== Performance Monitoring Demonstration ===");
        
        // 记录自定义性能指标
        RECORD_CUSTOM_METRIC("demo_metric_1", 75.5, "percent");
        RECORD_CUSTOM_METRIC("demo_metric_2", 1024.0, "bytes");
        RECORD_CUSTOM_METRIC("demo_response_time", 150.0, "ms");
        
        // 获取当前性能统计
        auto cpuStats = MODERN_PERF_MONITOR().GetStatistics(MetricType::CPU_Usage);
        LOG_INFO_F("DEMO", "Current CPU usage: %.2f%% (avg: %.2f%%, max: %.2f%%)", 
                  cpuStats.current, cpuStats.average, cpuStats.max);
        
        auto memStats = MODERN_PERF_MONITOR().GetStatistics(MetricType::Process_MemoryUsage);
        LOG_INFO_F("DEMO", "Process memory usage: %.2f MB", memStats.current / (1024.0 * 1024.0));
        
        LOG_INFO("DEMO", "Performance monitoring demonstration completed");
    }
    
    void DemonstrateBusinessMonitoring()
    {
        LOG_INFO("DEMO", "=== Business Monitoring Demonstration ===");
        
        // 模拟用户会话
        std::string sessionId1 = RECORD_USER_LOGIN("user_001", "*************");
        std::string sessionId2 = RECORD_USER_LOGIN("user_002", "192.168.1.101");
        std::string sessionId3 = RECORD_USER_LOGIN("user_003", "192.168.1.102");
        
        // 记录业务事件
        RECORD_LEVEL_UP("user_001", sessionId1);
        RECORD_ITEM_TRANSACTION("user_002", sessionId2, 5.0);
        RECORD_CHAT_MESSAGE("user_003", sessionId3);
        
        // 记录技能使用
        RECORD_SKILL_USAGE("user_001", sessionId1, "fireball");
        
        // 获取业务统计
        size_t onlineUsers = BUSINESS_MONITOR().GetOnlineUserCount();
        size_t activeSessions = BUSINESS_MONITOR().GetActiveSessionCount();
        
        LOG_INFO_F("DEMO", "Online users: %zu, Active sessions: %zu", onlineUsers, activeSessions);
        
        // 模拟用户登出
        RECORD_USER_LOGOUT(sessionId1);
        
        LOG_INFO("DEMO", "Business monitoring demonstration completed");
    }
    
    void DemonstrateAlertSystem()
    {
        LOG_INFO("DEMO", "=== Alert System Demonstration ===");
        
        // 触发不同级别的告警
        TRIGGER_ALERT("cpu_high", "High CPU Usage Alert", 
                     "CPU usage has exceeded 80% for the past 5 minutes");
        
        std::unordered_map<std::string, std::string> labels;
        labels["server"] = "game-server-01";
        labels["region"] = "us-east-1";
        TRIGGER_ALERT_WITH_LABELS("memory_high", "High Memory Usage Alert", 
                                 "Memory usage has exceeded 85%", labels);
        
        TRIGGER_ALERT("error_rate_high", "Critical Error Rate", 
                     "Error rate has exceeded 5% in the last 10 minutes");
        
        // 获取告警统计
        auto alertStats = ALERT_MANAGER().GetAlertStatistics();
        LOG_INFO_F("DEMO", "Active alerts: %zu, Critical: %zu, Acknowledged: %zu", 
                  alertStats.totalActive, alertStats.criticalAlerts, alertStats.totalAcknowledged);
        
        LOG_INFO("DEMO", "Alert system demonstration completed");
    }
    
    void DemonstrateDashboard()
    {
        LOG_INFO("DEMO", "=== Dashboard Demonstration ===");
        
        // 生成仪表板HTML
        std::string dashboardHTML = OPERATIONS_DASHBOARD().GenerateDashboardHTML();
        
        // 保存到文件
        SAVE_DASHBOARD("sword2_dashboard.html");
        
        LOG_INFO("DEMO", "Dashboard HTML generated and saved to sword2_dashboard.html");
        LOG_INFO("DEMO", "Open the file in a web browser to view the dashboard");
        LOG_INFO("DEMO", "Dashboard demonstration completed");
    }
};

} // namespace sword2

// 全局监控系统集成实例
sword2::MonitoringSystemIntegration g_MonitoringSystem;

// 初始化完整监控系统
bool InitializeMonitoringSystem()
{
    return g_MonitoringSystem.Initialize();
}

// 关闭监控系统
void ShutdownMonitoringSystem()
{
    g_MonitoringSystem.Shutdown();
}

// 运行监控系统演示
void RunMonitoringSystemDemo()
{
    g_MonitoringSystem.DemonstrateMonitoringSystem();
}

// 获取监控系统状态
bool IsMonitoringSystemRunning()
{
    // 检查各个组件是否正在运行
    return true; // 简化实现
}
