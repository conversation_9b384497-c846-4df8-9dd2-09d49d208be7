//---------------------------------------------------------------------------
// Sword2 High-Performance Memory Pool (c) 2024
//
// File:	MemoryPool.h
// Date:	2024.07.02
// Code:	AI Assistant
// Desc:	High-performance memory pool for game objects
//---------------------------------------------------------------------------
#ifndef MEMORY_POOL_H
#define MEMORY_POOL_H

#include <windows.h>
#include <vector>

// 内存块大小枚举
enum MEMORY_BLOCK_SIZE
{
    MBS_TINY    = 16,    // 小对象 (16字节)
    MBS_SMALL   = 64,    // 小对象 (64字节)
    MBS_MEDIUM  = 256,   // 中等对象 (256字节)
    MBS_LARGE   = 1024,  // 大对象 (1KB)
    MBS_HUGE    = 4096,  // 巨大对象 (4KB)
    MBS_COUNT   = 5      // 池的数量
};

// 内存池统计信息
struct MemoryPoolStats
{
    DWORD dwTotalAllocated;     // 总分配字节数
    DWORD dwTotalFreed;         // 总释放字节数
    DWORD dwCurrentUsage;       // 当前使用量
    DWORD dwPeakUsage;          // 峰值使用量
    DWORD dwAllocationCount;    // 分配次数
    DWORD dwFreeCount;          // 释放次数
    DWORD dwPoolHits;           // 池命中次数
    DWORD dwPoolMisses;         // 池未命中次数
};

// 内存块头部信息
struct MemoryBlockHeader
{
    DWORD dwSize;               // 块大小
    DWORD dwMagic;              // 魔数，用于检测内存损坏
    MemoryBlockHeader* pNext;   // 下一个空闲块
};

// 单个内存池类
class CMemorySubPool
{
public:
    CMemorySubPool();
    ~CMemorySubPool();

    BOOL Initialize(DWORD dwBlockSize, DWORD dwInitialBlocks = 64);
    void Cleanup();

    void* Allocate();
    BOOL Free(void* pMemory);

    DWORD GetBlockSize() const { return m_dwBlockSize; }
    DWORD GetFreeBlocks() const { return m_dwFreeBlocks; }
    DWORD GetTotalBlocks() const { return m_dwTotalBlocks; }

private:
    DWORD m_dwBlockSize;                    // 块大小
    DWORD m_dwTotalBlocks;                  // 总块数
    DWORD m_dwFreeBlocks;                   // 空闲块数
    MemoryBlockHeader* m_pFreeList;         // 空闲链表头
    std::vector<void*> m_Chunks;            // 内存块列表
    CRITICAL_SECTION m_CriticalSection;    // 线程安全

    BOOL AllocateNewChunk();
    void* GetBlockFromChunk(void* pChunk, DWORD dwIndex);
};

// 高性能内存池管理器
class CMemoryPool
{
public:
    CMemoryPool();
    ~CMemoryPool();

    BOOL Initialize();
    void Cleanup();

    // 内存分配和释放
    void* Allocate(DWORD dwSize);
    BOOL Free(void* pMemory);

    // 对象池分配 - 模板函数
    template<typename T>
    T* AllocateObject()
    {
        void* pMemory = Allocate(sizeof(T));
        return pMemory ? new(pMemory) T() : NULL;
    }

    template<typename T>
    BOOL FreeObject(T* pObject)
    {
        if (pObject)
        {
            pObject->~T();
            return Free(pObject);
        }
        return FALSE;
    }

    // 批量分配
    BOOL AllocateArray(void** ppArray, DWORD dwElementSize, DWORD dwCount);
    BOOL FreeArray(void** ppArray, DWORD dwCount);

    // 统计信息
    void GetStats(MemoryPoolStats* pStats);
    void ResetStats();
    void LogStats();

    // 内存整理
    void Defragment();
    void TrimMemory();

private:
    CMemorySubPool m_SubPools[MBS_COUNT];   // 子内存池
    MemoryPoolStats m_Stats;                // 统计信息
    CRITICAL_SECTION m_CriticalSection;    // 线程安全
    BOOL m_bInitialized;

    int GetPoolIndex(DWORD dwSize);
    void UpdateStats(DWORD dwSize, BOOL bAllocate);
};

// 全局内存池实例
extern CMemoryPool g_MemoryPool;

// 便捷宏定义
#define POOL_ALLOC(size)        g_MemoryPool.Allocate(size)
#define POOL_FREE(ptr)          g_MemoryPool.Free(ptr)
#define POOL_NEW(type)          g_MemoryPool.AllocateObject<type>()
#define POOL_DELETE(ptr)        g_MemoryPool.FreeObject(ptr)

// 内存对齐宏
#define ALIGN_SIZE(size, align) (((size) + (align) - 1) & ~((align) - 1))
#define CACHE_LINE_SIZE         64

// 智能指针类 - 自动内存管理
template<typename T>
class CPoolPtr
{
public:
    CPoolPtr() : m_pPtr(NULL) {}
    explicit CPoolPtr(T* pPtr) : m_pPtr(pPtr) {}
    ~CPoolPtr() { Reset(); }

    T* Get() const { return m_pPtr; }
    T* operator->() const { return m_pPtr; }
    T& operator*() const { return *m_pPtr; }
    
    void Reset(T* pPtr = NULL)
    {
        if (m_pPtr)
            POOL_DELETE(m_pPtr);
        m_pPtr = pPtr;
    }

    T* Release()
    {
        T* pTemp = m_pPtr;
        m_pPtr = NULL;
        return pTemp;
    }

private:
    T* m_pPtr;
    
    // 禁止拷贝
    CPoolPtr(const CPoolPtr&);
    CPoolPtr& operator=(const CPoolPtr&);
};

#endif // MEMORY_POOL_H
