//---------------------------------------------------------------------------
// Sword2 Item System Demo (c) 2024
//
// File:	ItemSystemDemo.cpp
// Date:	2024.07.02
// Code:	AI Assistant
// Desc:	Comprehensive demonstration of the item and equipment system
//---------------------------------------------------------------------------

#include "KCore.h"
#include "ItemSystem.h"
#include "ItemManager.h"
#include "InventoryManager.h"
#include "ItemScriptIntegration.h"
#include "PlayerSystem.h"
#include "PlayerManager.h"
#include "LuaScriptEngine.h"
#include "UnifiedLoggingSystem.h"

namespace sword2 {

// 物品装备系统演示类
class ItemSystemDemo
{
public:
    ItemSystemDemo()
    {
        m_initialized = false;
    }
    
    bool Initialize()
    {
        if (m_initialized) return true;
        
        printf("[ITEM_DEMO] Initializing item system demo...\n");
        
        // 初始化日志系统
        LOGGER().Initialize();
        LOGGER().AddConsoleAppender(LogLevel::Debug);
        
        // 启动玩家管理器
        if (!START_PLAYER_SYSTEM())
        {
            printf("[ITEM_DEMO] Failed to start player system\n");
            return false;
        }
        
        // 初始化脚本引擎
        if (!INIT_SCRIPT_ENGINE("../../../Server/script"))
        {
            printf("[ITEM_DEMO] Failed to initialize script engine\n");
            return false;
        }
        
        // 启动物品管理器
        if (!START_ITEM_SYSTEM())
        {
            printf("[ITEM_DEMO] Failed to start item system\n");
            return false;
        }
        
        // 初始化物品脚本集成
        if (!INIT_ITEM_SCRIPT_INTEGRATION())
        {
            printf("[ITEM_DEMO] Failed to initialize item script integration\n");
            return false;
        }
        
        m_initialized = true;
        LOG_INFO("ITEM_DEMO", "Item system demo initialized successfully");
        
        return true;
    }
    
    void RunDemo()
    {
        if (!m_initialized)
        {
            printf("[ITEM_DEMO] System not initialized\n");
            return;
        }
        
        LOG_INFO("ITEM_DEMO", "Starting item system demonstration...");
        
        // 演示各个功能
        DemoItemTemplateCreation();
        DemoItemInstanceCreation();
        DemoInventoryManagement();
        DemoEquipmentSystem();
        DemoItemEnhancement();
        DemoItemTrading();
        DemoItemScripting();
        DemoItemStatistics();
        
        LOG_INFO("ITEM_DEMO", "Item system demonstration completed");
    }
    
    void Cleanup()
    {
        if (!m_initialized) return;
        
        LOG_INFO("ITEM_DEMO", "Cleaning up item system demo...");
        
        // 清理创建的玩家
        for (uint32_t playerId : m_createdPlayers)
        {
            REMOVE_PLAYER_INVENTORY(playerId);
            PLAYER_LOGOUT(playerId);
        }
        m_createdPlayers.clear();
        
        // 清理创建的物品实例
        for (uint32_t instanceId : m_createdInstances)
        {
            DESTROY_ITEM_INSTANCE(instanceId);
        }
        m_createdInstances.clear();
        
        // 停止系统
        STOP_ITEM_SYSTEM();
        SHUTDOWN_SCRIPT_ENGINE();
        STOP_PLAYER_SYSTEM();
        
        m_initialized = false;
        LOG_INFO("ITEM_DEMO", "Item system demo cleanup completed");
    }

private:
    bool m_initialized;
    std::vector<uint32_t> m_createdPlayers;
    std::vector<uint32_t> m_createdInstances;
    
    void DemoItemTemplateCreation()
    {
        LOG_INFO("ITEM_DEMO", "=== Item Template Creation Demo ===");
        
        // 创建武器模板
        {
            ItemTemplate item(10001, "新手剑", ItemGenre::Equipment);
            item.description = "新手专用的基础剑，攻击力一般但耐用";
            item.detailType = EquipDetailType::MeleeWeapon;
            item.equipPart = ItemPart::Weapon;
            item.nature = EquipNature::Normal;
            item.level = 1;
            item.price = 100;
            item.weight = 5;
            item.maxDurability = 100;
            item.requirement.requiredLevel = 1;
            item.requirement.requiredStrength = 10;
            item.baseAttributes.emplace_back(ItemAttributeType::Damage, 10, 15);
            item.baseAttributes.emplace_back(ItemAttributeType::AttackSpeed, 5, false);
            item.scriptPath = "items/weapon_basic_sword.lua";
            
            REGISTER_ITEM_TEMPLATE(item);
            LOG_INFO("ITEM_DEMO", "Created weapon template: " + item.name);
        }
        
        // 创建护甲模板
        {
            ItemTemplate item(20001, "布衣", ItemGenre::Equipment);
            item.description = "简单的布制衣服，提供基础防护";
            item.detailType = EquipDetailType::Armor;
            item.equipPart = ItemPart::Body;
            item.nature = EquipNature::Normal;
            item.level = 1;
            item.price = 50;
            item.weight = 3;
            item.maxDurability = 80;
            item.requirement.requiredLevel = 1;
            item.baseAttributes.emplace_back(ItemAttributeType::Defense, 5, 8);
            item.baseAttributes.emplace_back(ItemAttributeType::Health, 20);
            item.scriptPath = "items/armor_basic_cloth.lua";
            
            REGISTER_ITEM_TEMPLATE(item);
            LOG_INFO("ITEM_DEMO", "Created armor template: " + item.name);
        }
        
        // 创建药品模板
        {
            ItemTemplate item(30001, "小还丹", ItemGenre::Medicine);
            item.description = "恢复少量生命值的丹药";
            item.level = 1;
            item.price = 10;
            item.weight = 1;
            item.maxStack = 100;
            item.baseAttributes.emplace_back(ItemAttributeType::Health, 50);
            item.scriptPath = "items/medicine_small_heal.lua";
            
            REGISTER_ITEM_TEMPLATE(item);
            LOG_INFO("ITEM_DEMO", "Created medicine template: " + item.name);
        }
        
        // 创建高级装备模板
        {
            ItemTemplate item(10002, "青钢剑", ItemGenre::Equipment);
            item.description = "用青钢锻造的利剑，锋利无比";
            item.detailType = EquipDetailType::MeleeWeapon;
            item.equipPart = ItemPart::Weapon;
            item.nature = EquipNature::Violet;
            item.level = 10;
            item.price = 1000;
            item.weight = 8;
            item.maxDurability = 150;
            item.requirement.requiredLevel = 10;
            item.requirement.requiredStrength = 25;
            item.requirement.requiredSeries = PlayerSeries::Wudang;
            item.baseAttributes.emplace_back(ItemAttributeType::Damage, 25, 35);
            item.baseAttributes.emplace_back(ItemAttributeType::CriticalRate, 5, true);
            item.magicAttributes.emplace_back(ItemAttributeType::AttackSpeed, 10, true);
            item.scriptPath = "items/weapon_steel_sword.lua";
            
            REGISTER_ITEM_TEMPLATE(item);
            LOG_INFO("ITEM_DEMO", "Created advanced weapon template: " + item.name);
        }
        
        // 创建戒指模板
        {
            ItemTemplate item(40001, "力量戒指", ItemGenre::Equipment);
            item.description = "增强力量的神秘戒指";
            item.detailType = EquipDetailType::Ring;
            item.equipPart = ItemPart::Ring1;
            item.nature = EquipNature::Gold;
            item.level = 15;
            item.price = 2000;
            item.weight = 1;
            item.maxDurability = 200;
            item.requirement.requiredLevel = 15;
            item.baseAttributes.emplace_back(ItemAttributeType::Strength, 10);
            item.baseAttributes.emplace_back(ItemAttributeType::Damage, 5);
            item.magicAttributes.emplace_back(ItemAttributeType::CriticalDamage, 15, true);
            item.scriptPath = "items/ring_strength.lua";
            
            REGISTER_ITEM_TEMPLATE(item);
            LOG_INFO("ITEM_DEMO", "Created ring template: " + item.name);
        }
        
        LOG_INFO("ITEM_DEMO", "Created 5 item templates");
    }
    
    void DemoItemInstanceCreation()
    {
        LOG_INFO("ITEM_DEMO", "=== Item Instance Creation Demo ===");
        
        // 创建测试玩家
        PlayerCreationParams params1("物品测试玩家1", "item_test1");
        params1.series = PlayerSeries::Wudang;
        uint32_t playerId1 = CREATE_PLAYER(params1);
        
        PlayerCreationParams params2("物品测试玩家2", "item_test2");
        params2.series = PlayerSeries::Shaolin;
        uint32_t playerId2 = CREATE_PLAYER(params2);
        
        if (playerId1 != 0 && playerId2 != 0)
        {
            m_createdPlayers.push_back(playerId1);
            m_createdPlayers.push_back(playerId2);
            
            // 玩家登录
            uint32_t sessionId1, sessionId2;
            PLAYER_LOGIN(playerId1, "127.0.0.1", 4001, sessionId1);
            PLAYER_LOGIN(playerId2, "127.0.0.1", 4002, sessionId2);
            
            // 为玩家创建物品实例
            std::vector<uint32_t> templateIds = {10001, 20001, 30001, 10002, 40001};
            
            for (uint32_t templateId : templateIds)
            {
                // 为玩家1创建物品
                uint32_t instanceId1 = CREATE_ITEM_INSTANCE(templateId, playerId1, 1);
                if (instanceId1 != 0)
                {
                    m_createdInstances.push_back(instanceId1);
                    LOG_INFO("ITEM_DEMO", "Created item instance " + std::to_string(instanceId1) + 
                            " for player " + std::to_string(playerId1));
                }
                
                // 为玩家2创建物品
                uint32_t instanceId2 = CREATE_ITEM_INSTANCE(templateId, playerId2, 1);
                if (instanceId2 != 0)
                {
                    m_createdInstances.push_back(instanceId2);
                    LOG_INFO("ITEM_DEMO", "Created item instance " + std::to_string(instanceId2) + 
                            " for player " + std::to_string(playerId2));
                }
            }
            
            // 创建堆叠物品（药品）
            uint32_t medicineStack = CREATE_ITEM_INSTANCE(30001, playerId1, 50);
            if (medicineStack != 0)
            {
                m_createdInstances.push_back(medicineStack);
                LOG_INFO("ITEM_DEMO", "Created medicine stack of 50 for player " + std::to_string(playerId1));
            }
        }
        
        LOG_INFO("ITEM_DEMO", "Created " + std::to_string(m_createdInstances.size()) + " item instances");
    }
    
    void DemoInventoryManagement()
    {
        LOG_INFO("ITEM_DEMO", "=== Inventory Management Demo ===");
        
        if (m_createdPlayers.empty()) return;
        
        uint32_t playerId = m_createdPlayers[0];
        
        // 获取玩家背包
        auto inventory = GET_PLAYER_INVENTORY(playerId);
        if (!inventory)
        {
            LOG_ERROR("ITEM_DEMO", "Failed to get player inventory");
            return;
        }
        
        // 给玩家添加物品到背包
        bool result1 = GIVE_ITEM_TO_PLAYER(playerId, 10001, 1, InventoryType::Equipment);
        bool result2 = GIVE_ITEM_TO_PLAYER(playerId, 20001, 1, InventoryType::Equipment);
        bool result3 = GIVE_ITEM_TO_PLAYER(playerId, 30001, 20, InventoryType::Equipment);
        
        if (result1 && result2 && result3)
        {
            LOG_INFO("ITEM_DEMO", "Successfully added items to player inventory");
            
            // 检查背包状态
            auto container = inventory->GetContainer(InventoryType::Equipment);
            if (container)
            {
                LOG_INFO("ITEM_DEMO", "Inventory status:");
                LOG_INFO("ITEM_DEMO", "  Size: " + std::to_string(container->width) + "x" + std::to_string(container->height));
                LOG_INFO("ITEM_DEMO", "  Free slots: " + std::to_string(container->GetFreeSlotCount()));
                LOG_INFO("ITEM_DEMO", "  Used slots: " + std::to_string(container->GetUsedSlotCount()));
                LOG_INFO("ITEM_DEMO", "  Weight: " + std::to_string(container->currentWeight) + "/" + std::to_string(container->maxWeight));
            }
            
            // 整理背包
            inventory->SortContainer(InventoryType::Equipment);
            LOG_INFO("ITEM_DEMO", "Sorted player inventory");
            
            // 移动物品
            bool moveResult = MOVE_INVENTORY_ITEM(playerId, 0, 0, InventoryType::Equipment, 1, 1, InventoryType::Equipment);
            if (moveResult)
            {
                LOG_INFO("ITEM_DEMO", "Successfully moved item in inventory");
            }
        }
        
        // 演示仓库功能
        bool warehouseResult = GIVE_ITEM_TO_PLAYER(playerId, 40001, 1, InventoryType::Repository);
        if (warehouseResult)
        {
            LOG_INFO("ITEM_DEMO", "Added item to warehouse");
        }
    }

    void DemoEquipmentSystem()
    {
        LOG_INFO("ITEM_DEMO", "=== Equipment System Demo ===");

        if (m_createdPlayers.empty()) return;

        uint32_t playerId = m_createdPlayers[0];
        auto inventory = GET_PLAYER_INVENTORY(playerId);
        if (!inventory) return;

        // 装备武器
        bool equipResult1 = EQUIP_INVENTORY_ITEM(playerId, 0, 0, InventoryType::Equipment);
        if (equipResult1)
        {
            LOG_INFO("ITEM_DEMO", "Successfully equipped weapon");

            // 检查装备槽位
            uint32_t weaponId = inventory->GetEquippedItem(ItemPart::Weapon);
            if (weaponId != 0)
            {
                LOG_INFO("ITEM_DEMO", "Weapon equipped in slot: " + std::to_string(weaponId));
            }
        }

        // 装备护甲
        bool equipResult2 = EQUIP_INVENTORY_ITEM(playerId, 0, 1, InventoryType::Equipment);
        if (equipResult2)
        {
            LOG_INFO("ITEM_DEMO", "Successfully equipped armor");
        }

        // 显示所有装备
        LOG_INFO("ITEM_DEMO", "Current equipment:");
        for (size_t i = 0; i < inventory->equipmentSlots.size(); ++i)
        {
            uint32_t instanceId = inventory->equipmentSlots[i];
            if (instanceId != 0)
            {
                auto instance = GET_ITEM_INSTANCE(instanceId);
                if (instance)
                {
                    auto itemTemplate = GET_ITEM_TEMPLATE(instance->templateId);
                    if (itemTemplate)
                    {
                        LOG_INFO("ITEM_DEMO", "  Slot " + std::to_string(i) + ": " + itemTemplate->name +
                                " (ID: " + std::to_string(instanceId) + ")");
                    }
                }
            }
        }

        // 卸下装备
        bool unequipResult = UNEQUIP_INVENTORY_ITEM(playerId, ItemPart::Weapon);
        if (unequipResult)
        {
            LOG_INFO("ITEM_DEMO", "Successfully unequipped weapon");
        }
    }

    void DemoItemEnhancement()
    {
        LOG_INFO("ITEM_DEMO", "=== Item Enhancement Demo ===");

        if (m_createdInstances.empty()) return;

        uint32_t instanceId = m_createdInstances[0];
        auto instance = GET_ITEM_INSTANCE(instanceId);
        if (!instance) return;

        LOG_INFO("ITEM_DEMO", "Enhancing item " + std::to_string(instanceId));
        LOG_INFO("ITEM_DEMO", "Initial enhance level: " + std::to_string(instance->enhanceLevel));
        LOG_INFO("ITEM_DEMO", "Initial durability: " + std::to_string(instance->durability));

        // 强化物品
        for (int i = 0; i < 3; ++i)
        {
            ItemOperationResult result = ENHANCE_ITEM(instanceId, instance->ownerId);
            if (result == ItemOperationResult::Success)
            {
                LOG_INFO("ITEM_DEMO", "Enhancement " + std::to_string(i + 1) + " successful, level: " +
                        std::to_string(instance->enhanceLevel));
            }
            else
            {
                LOG_WARNING("ITEM_DEMO", "Enhancement " + std::to_string(i + 1) + " failed");
                break;
            }
        }

        // 损坏物品
        instance->ReduceDurability(50);
        LOG_INFO("ITEM_DEMO", "Reduced durability to: " + std::to_string(instance->durability));

        // 修理物品
        instance->Repair();
        LOG_INFO("ITEM_DEMO", "Repaired item, durability: " + std::to_string(instance->durability));

        // 镶嵌宝石
        ItemOperationResult socketResult = SOCKET_GEM(instanceId, 50001, instance->ownerId);
        if (socketResult == ItemOperationResult::Success)
        {
            LOG_INFO("ITEM_DEMO", "Successfully socketed gem");
            LOG_INFO("ITEM_DEMO", "Socketed gems count: " + std::to_string(instance->socketGems.size()));
        }

        // 设置过期时间
        instance->SetExpireTime(3600); // 1小时后过期
        LOG_INFO("ITEM_DEMO", "Set item expiration time, remaining: " + std::to_string(instance->GetRemainingTime()) + " seconds");
    }

    void DemoItemTrading()
    {
        LOG_INFO("ITEM_DEMO", "=== Item Trading Demo ===");

        if (m_createdPlayers.size() < 2 || m_createdInstances.empty()) return;

        uint32_t player1Id = m_createdPlayers[0];
        uint32_t player2Id = m_createdPlayers[1];
        uint32_t instanceId = m_createdInstances[0];

        auto instance = GET_ITEM_INSTANCE(instanceId);
        if (!instance) return;

        LOG_INFO("ITEM_DEMO", "Trading item " + std::to_string(instanceId) +
                " from player " + std::to_string(player1Id) + " to player " + std::to_string(player2Id));

        // 开始交易
        uint32_t tradeSessionId = START_ITEM_TRADE(player1Id, player2Id);
        if (tradeSessionId != 0)
        {
            LOG_INFO("ITEM_DEMO", "Started trade session: " + std::to_string(tradeSessionId));

            // 添加交易物品
            auto& tradeManager = ITEM_TRADE_MANAGER();
            bool addResult = tradeManager.AddTradeItem(tradeSessionId, player1Id, instanceId);
            if (addResult)
            {
                LOG_INFO("ITEM_DEMO", "Added item to trade");

                // 确认交易
                bool confirm1 = tradeManager.ConfirmTrade(tradeSessionId, player1Id);
                bool confirm2 = tradeManager.ConfirmTrade(tradeSessionId, player2Id);

                if (confirm1 && confirm2)
                {
                    LOG_INFO("ITEM_DEMO", "Trade completed successfully");

                    // 检查物品所有者
                    auto updatedInstance = GET_ITEM_INSTANCE(instanceId);
                    if (updatedInstance)
                    {
                        LOG_INFO("ITEM_DEMO", "Item owner changed to: " + std::to_string(updatedInstance->ownerId));
                    }
                }
            }
        }

        // 演示直接交易
        if (m_createdInstances.size() > 1)
        {
            uint32_t anotherInstanceId = m_createdInstances[1];
            ItemOperationResult directTradeResult = TRADE_ITEM(anotherInstanceId, player2Id, player1Id);
            if (directTradeResult == ItemOperationResult::Success)
            {
                LOG_INFO("ITEM_DEMO", "Direct trade successful");
            }
        }
    }

    void DemoItemScripting()
    {
        LOG_INFO("ITEM_DEMO", "=== Item Scripting Demo ===");

        // 创建测试脚本
        std::string testScript = R"(
            function item_test_function(itemId, playerId)
                WriteLog("Item script function called!")
                WriteLog("Item ID: " .. itemId)
                WriteLog("Player ID: " .. playerId)

                local itemName = GetItemName(itemId)
                local itemLevel = GetItemLevel(itemId)
                local itemType = GetItemType(itemId)
                local itemGenre = GetItemGenre(itemId)

                WriteLog("Item Name: " .. itemName)
                WriteLog("Item Level: " .. itemLevel)
                WriteLog("Item Type: " .. itemType)
                WriteLog("Item Genre: " .. itemGenre)

                return true
            end

            function use_healing_potion(itemId, playerId)
                WriteLog("Using healing potion")

                local healAmount = 50
                local playerHealth = GetPlayerAttribute(playerId, "health")
                local maxHealth = GetPlayerAttribute(playerId, "maxHealth")

                WriteLog("Current health: " .. playerHealth .. "/" .. maxHealth)

                if playerHealth < maxHealth then
                    local newHealth = math.min(playerHealth + healAmount, maxHealth)
                    SetPlayerAttribute(playerId, "health", newHealth)
                    WriteLog("Healed for " .. healAmount .. " HP, new health: " .. newHealth)

                    -- 应用治疗效果
                    ApplyItemEffect(itemId, playerId, "heal", healAmount, 0)
                else
                    WriteLog("Player is already at full health")
                end

                return true
            end

            function equip_weapon(itemId, playerId)
                WriteLog("Equipping weapon")

                local weaponDamage = GetItemAttributes(itemId)
                WriteLog("Weapon damage: " .. (weaponDamage.damage or 0))

                -- 应用武器属性
                ApplyItemEffect(itemId, playerId, "damage_bonus", weaponDamage.damage or 0, 0)

                return true
            end

            function enhance_item_script(itemId, playerId, enhanceLevel)
                WriteLog("Enhancing item to level " .. enhanceLevel)

                local successRate = math.max(100 - enhanceLevel * 10, 10)
                local random = math.random(1, 100)

                WriteLog("Enhancement success rate: " .. successRate .. "%")
                WriteLog("Random roll: " .. random)

                if random <= successRate then
                    WriteLog("Enhancement successful!")
                    return true
                else
                    WriteLog("Enhancement failed!")
                    return false
                end
            end
        )";

        // 执行测试脚本
        auto context = std::make_unique<LuaScriptContext>();
        context->Initialize();

        ScriptResult result = context->ExecuteString(testScript);
        if (result == ScriptResult::Success)
        {
            LOG_INFO("ITEM_DEMO", "Successfully loaded item test script");

            if (!m_createdPlayers.empty() && !m_createdInstances.empty())
            {
                uint32_t playerId = m_createdPlayers[0];
                uint32_t instanceId = m_createdInstances[0];

                auto instance = GET_ITEM_INSTANCE(instanceId);
                if (instance)
                {
                    // 测试脚本函数调用
                    std::vector<LuaValue> args = {
                        LuaValue(static_cast<double>(instance->templateId)),
                        LuaValue(static_cast<double>(playerId))
                    };

                    result = context->CallFunction("item_test_function", args);
                    if (result == ScriptResult::Success)
                    {
                        LOG_INFO("ITEM_DEMO", "Successfully executed item script function");
                    }

                    // 测试具体物品脚本
                    context->CallFunction("use_healing_potion", args);
                    context->CallFunction("equip_weapon", args);

                    // 测试强化脚本
                    std::vector<LuaValue> enhanceArgs = {
                        LuaValue(static_cast<double>(instanceId)),
                        LuaValue(static_cast<double>(playerId)),
                        LuaValue(static_cast<double>(instance->enhanceLevel + 1))
                    };
                    context->CallFunction("enhance_item_script", enhanceArgs);
                }
            }
        }
        else
        {
            LOG_ERROR("ITEM_DEMO", "Failed to load item test script");
        }
    }

    void DemoItemStatistics()
    {
        LOG_INFO("ITEM_DEMO", "=== Item Statistics Demo ===");

        auto itemStats = ITEM_MANAGER().GetStatistics();
        auto inventoryStats = INVENTORY_MANAGER().GetStatistics();

        LOG_INFO("ITEM_DEMO", "Item system statistics:");
        LOG_INFO("ITEM_DEMO", "  Total templates: " + std::to_string(itemStats.totalTemplates));
        LOG_INFO("ITEM_DEMO", "  Total instances: " + std::to_string(itemStats.totalInstances));

        LOG_INFO("ITEM_DEMO", "Items by genre:");
        for (const auto& [genre, count] : itemStats.itemsByGenre)
        {
            std::string genreName;
            switch (genre)
            {
            case ItemGenre::Equipment: genreName = "Equipment"; break;
            case ItemGenre::Medicine: genreName = "Medicine"; break;
            case ItemGenre::Event: genreName = "Event"; break;
            case ItemGenre::Materials: genreName = "Materials"; break;
            case ItemGenre::Task: genreName = "Task"; break;
            default: genreName = "Other"; break;
            }

            LOG_INFO("ITEM_DEMO", "  " + genreName + ": " + std::to_string(count));
        }

        LOG_INFO("ITEM_DEMO", "Equipment by type:");
        for (const auto& [type, count] : itemStats.equipmentByType)
        {
            std::string typeName;
            switch (type)
            {
            case EquipDetailType::MeleeWeapon: typeName = "Melee Weapon"; break;
            case EquipDetailType::RangeWeapon: typeName = "Range Weapon"; break;
            case EquipDetailType::Armor: typeName = "Armor"; break;
            case EquipDetailType::Ring: typeName = "Ring"; break;
            case EquipDetailType::Amulet: typeName = "Amulet"; break;
            default: typeName = "Other"; break;
            }

            LOG_INFO("ITEM_DEMO", "  " + typeName + ": " + std::to_string(count));
        }

        LOG_INFO("ITEM_DEMO", "Equipment by nature:");
        for (const auto& [nature, count] : itemStats.equipmentByNature)
        {
            std::string natureName;
            switch (nature)
            {
            case EquipNature::Normal: natureName = "Normal"; break;
            case EquipNature::Violet: natureName = "Violet"; break;
            case EquipNature::Gold: natureName = "Gold"; break;
            case EquipNature::Platina: natureName = "Platina"; break;
            default: natureName = "Other"; break;
            }

            LOG_INFO("ITEM_DEMO", "  " + natureName + ": " + std::to_string(count));
        }

        LOG_INFO("ITEM_DEMO", "Inventory statistics:");
        LOG_INFO("ITEM_DEMO", "  Total players with inventory: " + std::to_string(inventoryStats.totalPlayers));

        LOG_INFO("ITEM_DEMO", "Items per player:");
        for (const auto& [playerId, itemCount] : inventoryStats.itemsPerPlayer)
        {
            LOG_INFO("ITEM_DEMO", "  Player " + std::to_string(playerId) + ": " + std::to_string(itemCount) + " items");
        }
    }
};

} // namespace sword2

// 全局物品系统演示实例
sword2::ItemSystemDemo g_ItemDemo;

// 初始化物品系统
bool InitializeItemSystem()
{
    return g_ItemDemo.Initialize();
}

// 运行物品系统演示
void RunItemSystemDemo()
{
    g_ItemDemo.RunDemo();
}

// 清理物品系统
void CleanupItemSystem()
{
    g_ItemDemo.Cleanup();
}
