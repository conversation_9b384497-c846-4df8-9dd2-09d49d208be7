////////////////////////////////////////////////////////////////////////////////
//
//  FileName    : KG_RelayDatabase.cpp
//  Version     : 1.0
//  Creater     : <PERSON>
//  Create Date : 2005-6-13 17:43:05
//  Comment     : 
//
////////////////////////////////////////////////////////////////////////////////

#include "KCore.h"

#include "KG_RelayDatabase.h"
#include "Engine.h"

#ifdef _SERVER

#ifdef _DEBUG
#pragma comment(lib, "../../../../Base/Lib/debug/KGD_Local.lib")
#pragma comment(lib, "../../../../DevEnv/mysql/lib/debug/libmysql.lib")
#else
#pragma comment(lib, "../../../../../Base/Lib/release/KGD_Local.lib")
#pragma comment(lib, "../../../../DevEnv/mysql/lib/release/libmysql.lib")
#endif	//_DEBUG

const char KG_DATABASE_CONFIG_FILE[] = "\\Settings\\Shop\\Database.ini";
const char KG_DATABASE_SECTION_NAME[] = "Database";

////////////////////////////////////////////////////////////////////////////////
const unsigned KG_DATABASE_PARAM_STRING_SIZE = 64;
const unsigned KG_DATABASE_PORT = 3306;

struct KG_DATABASE_PARAM
{
    char szServer[KG_DATABASE_PARAM_STRING_SIZE];
    char szDatabase[KG_DATABASE_PARAM_STRING_SIZE];
    char szUser[KG_DATABASE_PARAM_STRING_SIZE];
    char szPassword[KG_DATABASE_PARAM_STRING_SIZE];
	char szGroup[GROUP_NAME_MAX_SIZE];
};

static KG_DATABASE_PARAM g_DatabaseParam = { 0 };
static unsigned g_nLoadDatabaseParamFlag = false;
KGD_FIELD g_GroupField = {GROUP_FIELD_NAME};

static int _StoreMatchCondition(
    KG_DATABASE_MATCH *pMatch,
    unsigned char **ppbyWritePos, // [in] write position, [out] next write position
    const KGD_FIELD *pcMatchField1, 
    KGD_OPERATOR Operator1,
    const KGD_FIELD *pcMatchField2, 
    KGD_OPERATOR Operator2
)
{
    int nResult = false;
    unsigned char *pbyWritePos = NULL;

    ASSERT(pMatch);
    ASSERT(ppbyWritePos);

    pbyWritePos = *ppbyWritePos;
    
    pMatch->Operator1 = Operator1;
    pMatch->Operator2 = Operator2;

    if (pcMatchField1)
    {
        pMatch->pField1 = (KGD_FIELD *)pbyWritePos;
        *(pMatch->pField1) = *pcMatchField1;
        pbyWritePos += sizeof(KGD_FIELD);

        pMatch->pField1->pvData = pbyWritePos;
        memcpy(pbyWritePos, pcMatchField1->pvData, pcMatchField1->uDataSize);
        pbyWritePos += pcMatchField1->uDataSize;

        if (pcMatchField2)
        {
            pMatch->pField2 = (KGD_FIELD *)pbyWritePos;
            *(pMatch->pField2) = *pcMatchField2;
            pbyWritePos += sizeof(KGD_FIELD);

            pMatch->pField2->pvData = pbyWritePos;
            memcpy(pbyWritePos, pcMatchField2->pvData, pcMatchField2->uDataSize);
            pbyWritePos += pcMatchField2->uDataSize;
        }
        else
        {
            pMatch->pField2 = NULL;
        }
    }
    else
    {
        KGLOG_PROCESS_ERROR(!pcMatchField2);
        pMatch->pField1 = NULL;
        pMatch->pField2 = NULL;
    }
    
    *ppbyWritePos = pbyWritePos;
    nResult = true;
Exit0:
    return nResult;
}

////////////////////////////////////////////////////////////////////////////////
static int _ValidateConfigParam()
{
    int nResult = false;

    // 验证服务器地址
    if (strlen(g_DatabaseParam.szServer) == 0)
    {
        printf("Error: Database server address is empty\n");
        goto Exit0;
    }

    // 验证数据库名称
    if (strlen(g_DatabaseParam.szDatabase) == 0)
    {
        printf("Error: Database name is empty\n");
        goto Exit0;
    }

    // 验证用户名
    if (strlen(g_DatabaseParam.szUser) == 0)
    {
        printf("Error: Database user is empty\n");
        goto Exit0;
    }

    // 验证服务器地址格式（简单检查）
    if (strstr(g_DatabaseParam.szServer, "..") != NULL ||
        strstr(g_DatabaseParam.szServer, "//") != NULL)
    {
        printf("Error: Invalid server address format\n");
        goto Exit0;
    }

    // 验证数据库名称格式（不能包含特殊字符）
    const char* pInvalidChars = "\\/:*?\"<>|";
    for (int i = 0; i < strlen(pInvalidChars); i++)
    {
        if (strchr(g_DatabaseParam.szDatabase, pInvalidChars[i]) != NULL)
        {
            printf("Error: Database name contains invalid character '%c'\n", pInvalidChars[i]);
            goto Exit0;
        }
    }

    nResult = true;
Exit0:
    return nResult;
}

////////////////////////////////////////////////////////////////////////////////
static int _LoadDatabaseParam()
{
    int nRetCode = false;
    int nResult  = false;
    IIniFile *piConfigFile = NULL;

    piConfigFile = g_OpenIniFile(KG_DATABASE_CONFIG_FILE);
    KGLOG_PROCESS_ERROR(piConfigFile);
    
    nRetCode = piConfigFile->GetString(
        KG_DATABASE_SECTION_NAME, 
        "Server", 
        "localhost", 
        g_DatabaseParam.szServer, 
        sizeof(g_DatabaseParam.szServer)
    );
    KGLOG_PROCESS_ERROR(nRetCode);

    nRetCode = piConfigFile->GetString(
        KG_DATABASE_SECTION_NAME, 
        "Database", 
        "sowrd2_Relay", 
        g_DatabaseParam.szDatabase, 
        sizeof(g_DatabaseParam.szDatabase)
    );
    KGLOG_PROCESS_ERROR(nRetCode);

    nRetCode = piConfigFile->GetString(
        KG_DATABASE_SECTION_NAME, 
        "User", 
        "", 
        g_DatabaseParam.szUser, 
        sizeof(g_DatabaseParam.szUser)
    );
    KGLOG_PROCESS_ERROR(nRetCode);

    nRetCode = piConfigFile->GetString(
        KG_DATABASE_SECTION_NAME, 
        "Password", 
        "", 
        g_DatabaseParam.szPassword, 
        sizeof(g_DatabaseParam.szPassword)
    );
    KGLOG_PROCESS_ERROR(nRetCode);

	nRetCode = piConfigFile->GetString(
		KG_DATABASE_SECTION_NAME, 
		"Group", 
		"", 
		g_DatabaseParam.szGroup,
		sizeof(g_DatabaseParam.szGroup)
	);
	KGLOG_PROCESS_ERROR(nRetCode);

	g_GroupField.uDataSize = (unsigned)strlen(g_DatabaseParam.szGroup) + 1;
	g_GroupField.pvData = g_DatabaseParam.szGroup;

    // 验证配置参数
    nRetCode = _ValidateConfigParam();
    KGLOG_PROCESS_ERROR(nRetCode);

    // 记录配置信息（不包含密码）
    printf("Database config loaded: Server=%s, Database=%s, User=%s\n",
           g_DatabaseParam.szServer, g_DatabaseParam.szDatabase, g_DatabaseParam.szUser);

    nResult = true;
Exit0:
    if (!nResult)
    {
        printf("Load config file \"%s\" failed!", KG_DATABASE_CONFIG_FILE);
    }
    KG_COM_RELEASE(piConfigFile);
    return nResult;
}

////////////////////////////////////////////////////////////////////////////////
int InitRelayDatabase(void *pvContext)
{
    int nRetCode = false;
    int nResult  = false;
    int nKGDInitFlag = false;
    IKGD_Database *piDatabase = NULL;

    KG_USE_ARGUMENT(pvContext);
    
    nRetCode = _LoadDatabaseParam();
    KGLOG_PROCESS_ERROR(nRetCode);
#ifdef _WIN32		// to do Jeep
    nRetCode = KGD_Init(NULL);
#endif
    KGLOG_PROCESS_ERROR(nRetCode);
    nKGDInitFlag = true;
#ifdef _WIN32		// to do Jeep
    piDatabase = KGD_Connect(
        KGD_ENGINE_DEFAULT,
        g_DatabaseParam.szServer,
        KG_DATABASE_PORT,
        g_DatabaseParam.szUser,
        g_DatabaseParam.szPassword,
        NULL
    );
#endif
    KGLOG_PROCESS_ERROR(piDatabase);

    nRetCode = piDatabase->CreateDatabase(
        g_DatabaseParam.szDatabase, KGD_CREATE_IF_NOT_EXIST
    );
    KGLOG_PROCESS_ERROR(nRetCode);

    nResult = true;
Exit0:
    if (!nResult)
    {
        if (nKGDInitFlag)
        {
#ifdef _WIN32	// to do Jeep
            KGD_Uninit(NULL);
#endif
            nKGDInitFlag = false;
        }
        printf("InitRelayDatabase() failed!");
    }
    KG_COM_RELEASE(piDatabase);
    return nResult;
}

////////////////////////////////////////////////////////////////////////////////
int UninitRelayDatabase(void *pvContext)
{
#ifdef _WIN32	//to do Jeep
    KG_USE_ARGUMENT(pvContext);
    KGD_Uninit(NULL);
#endif
    return true;
}

IKGD_Database *ConnectDatabase()
{
    int nRetCode = false;
    int nResult  = false;
    IKGD_Database *piDatabase = NULL;
    int nRetryCount = 0;
    const int MAX_RETRY_COUNT = 3;
    const int RETRY_DELAY_MS = 1000;

#ifdef _WIN32		// to do Jeep
    // 添加重连机制
    while (nRetryCount < MAX_RETRY_COUNT && !piDatabase)
    {
        piDatabase = KGD_Connect(
            KGD_ENGINE_DEFAULT,
            g_DatabaseParam.szServer,
            KG_DATABASE_PORT,
            g_DatabaseParam.szUser,
            g_DatabaseParam.szPassword,
            g_DatabaseParam.szDatabase
        );

        if (!piDatabase)
        {
            nRetryCount++;
            printf(
                "Database connection attempt %d failed, retrying in %d ms...\n",
                nRetryCount, RETRY_DELAY_MS
            );

            if (nRetryCount < MAX_RETRY_COUNT)
            {
                Sleep(RETRY_DELAY_MS);
            }
        }
    }
#endif
    KGLOG_PROCESS_ERROR(piDatabase);

    // 测试连接有效性
    if (piDatabase)
    {
        // 执行一个简单的查询来验证连接
        nRetCode = piDatabase->Query("SELECT 1", NULL, 0, NULL, KGD_OPERATOR_NONE, NULL, KGD_OPERATOR_NONE);
        if (!nRetCode)
        {
            printf("Database connection test failed\n");
            piDatabase->Release();
            piDatabase = NULL;
            goto Exit0;
        }
    }

    nResult = true;
Exit0:
    if (!nResult)
    {
        printf(
            "Unable connect database %s at server %s after %d attempts",
            g_DatabaseParam.szDatabase,
            g_DatabaseParam.szServer,
            nRetryCount
        );
    }
    return piDatabase;
}

////////////////////////////////////////////////////////////////////////////////
KG_RelayDatabase::PCommandProcessFunction KG_RelayDatabase::ms_apfnCmdProcFunction[CMD_COUNT];
////////////////////////////////////////////////////////////////////////////////
KG_RelayDatabase::KG_RelayDatabase(void):
    m_piDatabase(NULL)
{
    static int s_nCmdProcFunctionInitFlag = false;

    SetExitFlag(false);
    
    if (!s_nCmdProcFunctionInitFlag)
    {
        ms_apfnCmdProcFunction[CMD_CREATE_TABLE] = &KG_RelayDatabase::_create_table;
        ms_apfnCmdProcFunction[CMD_INSERT]       = &KG_RelayDatabase::_insert;
        ms_apfnCmdProcFunction[CMD_QUERY]        = &KG_RelayDatabase::_query;
        ms_apfnCmdProcFunction[CMD_UPDATE]       = &KG_RelayDatabase::_update;
        ms_apfnCmdProcFunction[CMD_DELETE]       = &KG_RelayDatabase::_delete;

        s_nCmdProcFunctionInitFlag = true;
    }
}

KG_RelayDatabase::~KG_RelayDatabase(void)
{
    ASSERT(!m_piDatabase);
}

////////////////////////////////////////////////////////////////////////////////
int KG_RelayDatabase::Init(void *pvContext)
{
    int nResult  = false;
    int nRetCode = false;
    int nWorkThreadCreateFlag = false;

    KG_USE_ARGUMENT(pvContext);
   
    // create working thread
    SetExitFlag(false);

    nRetCode = m_WorkThread.Create(_WorkThreadFunction, this);
    KGLOG_PROCESS_ERROR(nRetCode);
    nWorkThreadCreateFlag = true;

//Exit1:
    nResult = true;
Exit0:
    if (!nResult)
    {
        if (nWorkThreadCreateFlag)
        {
            SetExitFlag(true);
            m_WorkThread.Destroy();
            nWorkThreadCreateFlag = false;
        }
        printf("%s failed", KG_FUNCTION);
    }
    return nResult;
}

////////////////////////////////////////////////////////////////////////////////
int KG_RelayDatabase::Uninit(void *pvContext)
{
    KG_USE_ARGUMENT(pvContext);

    SetExitFlag(true);
    m_WorkThread.Destroy();

	m_CmdQueue.Destory();
    m_RetQueue.Destory();

    return true;
}                          

////////////////////////////////////////////////////////////////////////////////
int KG_RelayDatabase::PushCommand(
    IKG_Buffer *piCmdBuffer, unsigned long *pulRetDataID
)
{
    ASSERT(piCmdBuffer);
    ASSERT(pulRetDataID);

    return m_CmdQueue.push(piCmdBuffer, pulRetDataID);
}

////////////////////////////////////////////////////////////////////////////////
int KG_RelayDatabase::PopResult(
    IKG_Buffer **ppiRetBuffer, unsigned long ulRetDataID
)
{
    int nResult  = false;
    HRESULT hrRetCode  = false;

    ASSERT(ppiRetBuffer);

    hrRetCode = m_RetQueue.pop((IUnknown **)ppiRetBuffer, ulRetDataID);
    KG_COM_PROCESS_ERROR(hrRetCode);

//Exit1:
    nResult = true;
Exit0:
    return nResult;
}
////////////////////////////////////////////////////////////////////////////////
void KG_RelayDatabase::_WorkThreadFunction(void *pvParam)
{
    KG_RelayDatabase *pThis = (KG_RelayDatabase *)pvParam;

    ASSERT(pThis);

    pThis->_ThreadFunction();
}

////////////////////////////////////////////////////////////////////////////////
int KG_RelayDatabase::_ThreadFunction()
{
    int nResult  = false;
    int nRetCode = false;
    KG_DATABASE_HEADER *pHeader = NULL;
    IKG_Buffer *piCmdBuffer = NULL;
    unsigned uBufferSize    = 0;
    unsigned long ulDataID = 0;
    unsigned i = 0;

    m_piDatabase = ConnectDatabase();
    KGLOG_PROCESS_ERROR(m_piDatabase);

    while (true)
    {
        // get command
        KG_COM_RELEASE(piCmdBuffer);
        nRetCode = m_CmdQueue.pop((IUnknown **)&piCmdBuffer, &ulDataID);
        if (!nRetCode)
        {
            // is empty,

            // just check this flag when queue is empty
            // so this loop just quit when queue is empty and set exist flag
            if (GetExitFlag())
                break;

            // sleep some time
            KGThread_Sleep(10);
            continue;
        }

        uBufferSize = piCmdBuffer->GetSize();
        ASSERT(uBufferSize >= sizeof(KG_DATABASE_HEADER));
        pHeader = (KG_DATABASE_HEADER *)piCmdBuffer->GetData();
        ASSERT(pHeader);

        // do command
        if (
            (pHeader->cmd < CMD_COUNT) &&
            (ms_apfnCmdProcFunction[pHeader->cmd])
        )
        {
            nRetCode = (this->*ms_apfnCmdProcFunction[pHeader->cmd])(
                piCmdBuffer, ulDataID
            );
        }
        else
        {
            printf("Unknown database command: %d", pHeader->cmd);
        }
    }// while (true)

    nResult = true;
Exit0:
    KG_COM_RELEASE(piCmdBuffer);
    KG_COM_RELEASE(m_piDatabase);
    return nResult;
}
////////////////////////////////////////////////////////////////////////////////
int KG_RelayDatabase::_PushSimpleResult(unsigned long ulRetDataID, int nRetResult)
{
    int nResult  = false;
    int nRetCode = false;
    IKG_Buffer *piRetBuffer = NULL;
    KG_DATABASE_SIMPLE_RET *pResult = NULL;
#ifdef _WIN32	// to do Jeep
    piRetBuffer = KG_MemoryCreateBuffer(sizeof(KG_DATABASE_SIMPLE_RET));
#endif
    KGLOG_PROCESS_ERROR(piRetBuffer);
    
    pResult = (KG_DATABASE_SIMPLE_RET *)piRetBuffer->GetData();
    ASSERT(pResult);
    pResult->nResult = nRetResult;

    nRetCode = _PushResult(ulRetDataID, piRetBuffer);
    KGLOG_PROCESS_ERROR(nRetCode);

    nResult = true;
Exit0:
    KG_COM_RELEASE(piRetBuffer);
    return nResult;
}
////////////////////////////////////////////////////////////////////////////////
int KG_RelayDatabase::_create_table(
    IKG_Buffer *piCmdBuffer, unsigned long ulRetDataID)
{
    int nResult  = false;
    int nRetCode = false;
    unsigned uBufferSize = 0;
    KG_DATABASE_CREATE_INSERT_HEADER *pHeader = NULL;

    ASSERT(piCmdBuffer);

    uBufferSize = piCmdBuffer->GetSize();
    ASSERT(uBufferSize >= sizeof(KG_DATABASE_CREATE_INSERT_HEADER));
    pHeader = (KG_DATABASE_CREATE_INSERT_HEADER *)piCmdBuffer->GetData();
    ASSERT(pHeader);

    ASSERT(uBufferSize == sizeof(KG_DATABASE_CREATE_INSERT_HEADER) + 
                          sizeof(KGD_FIELD) * (pHeader->uFieldCount - 1)
    );
    
    ASSERT(m_piDatabase);
    nRetCode = m_piDatabase->CreateTable(
        pHeader->szTableName, pHeader->uFieldCount, pHeader->aField, KGD_CREATE_IF_NOT_EXIST
    );
    KGLOG_PROCESS_ERROR(nRetCode);
    
//Exit1:
    nResult = true;
Exit0:
    _PushSimpleResult(ulRetDataID, nResult);
    return nResult;
}

////////////////////////////////////////////////////////////////////////////////
int KG_RelayDatabase::_insert(IKG_Buffer *piCmdBuffer, unsigned long ulRetDataID)
{
    int nResult  = false;
    int nRetCode = false;
    unsigned uBufferSize = 0;
    KG_DATABASE_CREATE_INSERT_HEADER *pHeader = NULL;

    ASSERT(piCmdBuffer);

    uBufferSize = piCmdBuffer->GetSize();
    ASSERT(uBufferSize >= sizeof(KG_DATABASE_CREATE_INSERT_HEADER));
    pHeader = (KG_DATABASE_CREATE_INSERT_HEADER *)piCmdBuffer->GetData();
    ASSERT(pHeader);

    ASSERT(uBufferSize >= sizeof(KG_DATABASE_CREATE_INSERT_HEADER) + 
                          sizeof(KGD_FIELD) * (pHeader->uFieldCount - 1)
    );
    
    ASSERT(m_piDatabase);
    nRetCode = m_piDatabase->Insert(
        pHeader->szTableName, pHeader->uFieldCount, pHeader->aField
    );
    KGLOG_PROCESS_ERROR(nRetCode);

//Exit1:
    nResult = true;
Exit0:
    _PushSimpleResult(ulRetDataID, nResult);
    return nResult;
}

////////////////////////////////////////////////////////////////////////////////
int KG_RelayDatabase::_query(IKG_Buffer *piCmdBuffer, unsigned long ulRetDataID)
{
    int nResult  = false;
    int nRetCode = false;
    unsigned uBufferSize = 0;
    unsigned uHeaderSize = 0;
    unsigned uDataSize   = 0;
    KG_DATABASE_QUERY_HEADER *pHeader = NULL;
    IKGD_Result *piResult = NULL;
    IKG_Buffer *piRetPacket = NULL;
    KG_DATABASE_QUERY_RET *pRetPacket = NULL;
    unsigned char *pbyWritePos = NULL;
    unsigned i = 0;

    ASSERT(piCmdBuffer);

    uBufferSize = piCmdBuffer->GetSize();
    ASSERT(uBufferSize >= sizeof(KG_DATABASE_QUERY_HEADER));
    pHeader = (KG_DATABASE_QUERY_HEADER *)piCmdBuffer->GetData();
    ASSERT(pHeader);

    ASSERT(uBufferSize >= sizeof(KG_DATABASE_QUERY_HEADER) + 
                          sizeof(KGD_FIELD) * (pHeader->uFieldCount - 1)
    );
    
    ASSERT(m_piDatabase);
    nRetCode = m_piDatabase->Query(
        pHeader->szTableName, 
        pHeader->uFieldCount, 
        pHeader->aField, 
        pHeader->Match.pField1, 
        pHeader->Match.Operator1, 
		pHeader->Match.pField2, 
		pHeader->Match.Operator2
    );
    KGLOG_PROCESS_ERROR(nRetCode);

    piResult = m_piDatabase->GetQueryResult(pHeader->StoreFlag);
    KGLOG_PROCESS_ERROR(piResult);

    
    uHeaderSize = sizeof(KG_DATABASE_QUERY_RET) + 
        sizeof(KG_DATABASE_QUERY_RET::_KG_FIELD_DATA) * (pHeader->uFieldCount - 1);

    while (piResult->NextRow())
    {
        // for each row, we put a result packet to result queue will same data ID
        KG_COM_RELEASE(piRetPacket);
        uDataSize = 0;
        for (i = 0; i < pHeader->uFieldCount; ++i)
        {
            nRetCode = piResult->GetCurrentRowField(i, &pHeader->aField[i]);
            if (!nRetCode)
                break;
            uDataSize += pHeader->aField[i].uDataSize;
        }
        if (i < pHeader->uFieldCount)
            continue;
#ifdef _WIN32	// to do Jeep
        piRetPacket = KG_MemoryCreateBuffer(uHeaderSize + uBufferSize);
#endif
        if (!piRetPacket)
            continue;

        pRetPacket = (KG_DATABASE_QUERY_RET *)piRetPacket->GetData();
        ASSERT(pRetPacket);
        pRetPacket->uFieldCount = pHeader->uFieldCount;

        pbyWritePos = (unsigned char *)pRetPacket + uHeaderSize;
        for (i = 0; i < pHeader->uFieldCount; ++i)
        {
            pRetPacket->aFieldData[i].uSize = pHeader->aField[i].uDataSize;
            pRetPacket->aFieldData[i].pvData    = pbyWritePos;

            memcpy(pbyWritePos, pHeader->aField[i].pvData, pHeader->aField[i].uDataSize);
            pbyWritePos += pHeader->aField[i].uDataSize;
        }

        nRetCode = _PushResult(ulRetDataID, piRetPacket);
    }

//Exit1:
    nResult = true;
Exit0:
    _PushSimpleResult(ulRetDataID, nResult);

    KG_COM_RELEASE(piRetPacket);
    KG_COM_RELEASE(piResult);
    return nResult;
}

////////////////////////////////////////////////////////////////////////////////
int KG_RelayDatabase::_update(IKG_Buffer *piCmdBuffer, unsigned long ulRetDataID)
{
    int nResult  = false;
    int nRetCode = false;
    unsigned uBufferSize = 0;
    KG_DATABASE_UPDATE_HEADER *pHeader = NULL;

    ASSERT(piCmdBuffer);

    uBufferSize = piCmdBuffer->GetSize();
    ASSERT(uBufferSize >= sizeof(KG_DATABASE_UPDATE_HEADER));
    pHeader = (KG_DATABASE_UPDATE_HEADER *)piCmdBuffer->GetData();
    ASSERT(pHeader);

    ASSERT(uBufferSize >= sizeof(KG_DATABASE_UPDATE_HEADER) + 
                          sizeof(KGD_FIELD) * (pHeader->uFieldCount - 1)
    );
    
    ASSERT(m_piDatabase);
    nRetCode = m_piDatabase->Update(
        pHeader->szTableName, 
        pHeader->uFieldCount, 
        pHeader->aField, 
        pHeader->Match.pField1, 
        pHeader->Match.Operator1, 
        pHeader->Match.pField2, 
        pHeader->Match.Operator2 
    );
    KGLOG_PROCESS_ERROR(nRetCode);

//Exit1:
    nResult = true;
Exit0:
    _PushSimpleResult(ulRetDataID, nResult);
    return nResult;
}

////////////////////////////////////////////////////////////////////////////////
int KG_RelayDatabase::_delete(IKG_Buffer *piCmdBuffer, unsigned long ulRetDataID)
{
    int nResult  = false;
    int nRetCode = false;
    unsigned uBufferSize = 0;
    KG_DATABASE_DELETE_HEADER *pHeader = NULL;

    ASSERT(piCmdBuffer);

    uBufferSize = piCmdBuffer->GetSize();
    ASSERT(uBufferSize >= sizeof(KG_DATABASE_DELETE_HEADER));
    pHeader = (KG_DATABASE_DELETE_HEADER *)piCmdBuffer->GetData();
    ASSERT(pHeader);

    ASSERT(m_piDatabase);
    nRetCode = m_piDatabase->Delete(
        pHeader->szTableName,
        pHeader->Match.pField1, 
        pHeader->Match.Operator1, 
        pHeader->Match.pField2, 
        pHeader->Match.Operator2 
    );
    KGLOG_PROCESS_ERROR(nRetCode);

//Exit1:
    nResult = true;
Exit0:
    _PushSimpleResult(ulRetDataID, nResult);
    return nResult;
}

IKG_Buffer *KG_RelayDatabase::PacketInsertCommand(
	const char cszTableName[KGD_NAME_BUFFER_MAX_SIZE], 
	unsigned uFieldCount, 
	const KGD_FIELD caFields[]
)
{
	int nResult  = false;
	int nRetCode = false;

	IKG_Buffer *piCmdBuffer = NULL;
	unsigned uBufferSize = 0;
	unsigned char *pbyWritePos = NULL;
	KG_DATABASE_CREATE_INSERT_HEADER *pInsertHeader = NULL;
	unsigned i = 0;

	uBufferSize = sizeof(KG_DATABASE_CREATE_INSERT_HEADER) + 
		(uFieldCount - 1) * sizeof(KGD_FIELD);

	for (i = 0; i < uFieldCount; ++i)
	{
		uBufferSize += caFields[i].uDataSize;
	}
#ifdef _WIN32	// to do Jeep
	piCmdBuffer = KG_MemoryCreateBuffer(uBufferSize);
#endif
	KGLOG_PROCESS_ERROR(piCmdBuffer);

	pInsertHeader = (KG_DATABASE_CREATE_INSERT_HEADER *)piCmdBuffer->GetData();
	ASSERT(pInsertHeader);

	pInsertHeader->cmd = CMD_INSERT;
	strncpy(pInsertHeader->szTableName, cszTableName, sizeof(pInsertHeader->szTableName) - 1);
	pInsertHeader->szTableName[sizeof(pInsertHeader->szTableName) - 1] = '\0';

	pInsertHeader->uFieldCount = uFieldCount;

 	pbyWritePos = (unsigned char *)pInsertHeader + 
				  sizeof(KG_DATABASE_CREATE_INSERT_HEADER) + 
		          sizeof(KGD_FIELD) * (uFieldCount - 1);

	for (i = 0; i < uFieldCount; ++i)
	{
		pInsertHeader->aField[i] = caFields[i];
		pInsertHeader->aField[i].pvData = pbyWritePos;

		memcpy(pbyWritePos, caFields[i].pvData, caFields[i].uDataSize);
		pbyWritePos += caFields[i].uDataSize;
	}

//Exit1:
	nResult = true;
Exit0:
	if (!nResult)
	{
		KG_COM_RELEASE(piCmdBuffer);
	}
	return piCmdBuffer;
}


IKG_Buffer *KG_RelayDatabase::PacketUpdateCommand(
	const char cszTableName[KGD_NAME_BUFFER_MAX_SIZE], 
	unsigned  uFieldCount,
	const KGD_FIELD caFields[],
	const KGD_FIELD *pcMatchField1, 
	KGD_OPERATOR Operator1,
	const KGD_FIELD *pcMatchField2, 
	KGD_OPERATOR Operator2
)
{
	int nResult  = false;
	int nRetCode = false;

	IKG_Buffer *piCmdBuffer = NULL;
	unsigned uBufferSize = 0;
	unsigned char *pbyWritePos = NULL;
	KG_DATABASE_UPDATE_HEADER *pUpdateHeader = NULL;
	unsigned i = 0;

	uBufferSize = sizeof(KG_DATABASE_UPDATE_HEADER) + 
		(uFieldCount - 1) * sizeof(KGD_FIELD);

	for (i = 0; i < uFieldCount; ++i)
	{
		uBufferSize += caFields[i].uDataSize;
	}

	if (pcMatchField1)
	{
		uBufferSize += sizeof(KGD_FIELD) + pcMatchField1->uDataSize;
		if (pcMatchField2)
			uBufferSize += sizeof(KGD_FIELD) + pcMatchField2->uDataSize;
	}
	else
	{
		KGLOG_PROCESS_ERROR(!pcMatchField2);
	}
#ifdef _WIN32	// to do Jeep
	piCmdBuffer = KG_MemoryCreateBuffer(uBufferSize);
#endif
	KGLOG_PROCESS_ERROR(piCmdBuffer);

	pUpdateHeader = (KG_DATABASE_UPDATE_HEADER *)piCmdBuffer->GetData();
	ASSERT(pUpdateHeader);

	pUpdateHeader->cmd = CMD_UPDATE;
	strncpy(pUpdateHeader->szTableName, cszTableName, sizeof(pUpdateHeader->szTableName) - 1);
	pUpdateHeader->szTableName[sizeof(pUpdateHeader->szTableName) - 1] = '\0';

	pUpdateHeader->uFieldCount = uFieldCount;

	pbyWritePos = (unsigned char *)pUpdateHeader + 
				   sizeof(KG_DATABASE_UPDATE_HEADER) + 
				   sizeof(KGD_FIELD) * (uFieldCount - 1);

	for (i = 0; i < uFieldCount; ++i)
	{
		pUpdateHeader->aField[i] = caFields[i];
		pUpdateHeader->aField[i].pvData = pbyWritePos;
		memcpy(pbyWritePos, caFields[i].pvData, caFields[i].uDataSize);
		pbyWritePos += caFields[i].uDataSize;
	}

	nRetCode = _StoreMatchCondition(
		&(pUpdateHeader->Match), 
		&pbyWritePos, 
		pcMatchField1,
		Operator1,
		pcMatchField2,
		Operator2
	);
	KGLOG_PROCESS_ERROR(nRetCode);

//Exit1:
	nResult = true;
Exit0:
	if (!nResult)
	{
		KG_COM_RELEASE(piCmdBuffer);
	}
	return piCmdBuffer;
}

IKG_Buffer *KG_RelayDatabase::PacketQueryCommand(
	const char cszTableName[KGD_NAME_BUFFER_MAX_SIZE], 
	unsigned  uFieldCount,
	const KGD_FIELD caFields[],
	const KGD_FIELD *pcMatchField1, 
	KGD_OPERATOR Operator1,
	const KGD_FIELD *pcMatchField2, 
	KGD_OPERATOR Operator2,
	KGD_RESULT_FLAG StoreFlag
)
{
	int nResult  = false;
	int nRetCode = false;

	IKG_Buffer *piCmdBuffer = NULL;
	unsigned uBufferSize = 0;
	unsigned char *pbyWritePos = NULL;
	KG_DATABASE_QUERY_HEADER *pQueryHeader = NULL;
	unsigned i = 0;

	uBufferSize = sizeof(KG_DATABASE_QUERY_HEADER) + 
		(uFieldCount - 1) * sizeof(KGD_FIELD);

	for (i = 0; i < uFieldCount; ++i)
	{
		uBufferSize += caFields[i].uDataSize;
	}

	if (pcMatchField1)
	{
		uBufferSize += sizeof(KGD_FIELD) + pcMatchField1->uDataSize;
		if (pcMatchField2)
			uBufferSize += sizeof(KGD_FIELD) + pcMatchField2->uDataSize;
	}
	else
	{
		KGLOG_PROCESS_ERROR(!pcMatchField2);
	}
#ifdef _WIN32	// to do Jeep
	piCmdBuffer = KG_MemoryCreateBuffer(uBufferSize);
#endif
	KGLOG_PROCESS_ERROR(piCmdBuffer);

	pQueryHeader = (KG_DATABASE_QUERY_HEADER *)piCmdBuffer->GetData();
	ASSERT(pQueryHeader);

	pQueryHeader->cmd = CMD_QUERY;
	pQueryHeader->StoreFlag = StoreFlag;

	strncpy(pQueryHeader->szTableName, cszTableName, sizeof(pQueryHeader->szTableName) - 1);
	pQueryHeader->szTableName[sizeof(pQueryHeader->szTableName) - 1] = '\0';
	
	pQueryHeader->uFieldCount = uFieldCount;

	pbyWritePos = (unsigned char *)pQueryHeader + 
                  sizeof(KG_DATABASE_QUERY_HEADER) + 
                  sizeof(KGD_FIELD) * (uFieldCount - 1);

	for (i = 0; i < uFieldCount; ++i)
	{
        pQueryHeader->aField[i] = caFields[i];
	}

	nRetCode = _StoreMatchCondition(
		&(pQueryHeader->Match), 
		&pbyWritePos, 
		pcMatchField1,
		Operator1,
		pcMatchField2,
		Operator2
	);
	KGLOG_PROCESS_ERROR(nRetCode);

//Exit1:
	nResult = true;
Exit0:
	if (!nResult)
	{
		KG_COM_RELEASE(piCmdBuffer);
	}
	return piCmdBuffer;
}
////////////////////////////////////////////////////////////////////////////////
IKG_Buffer *KG_RelayDatabase::PacketDeleteCommand(
    const char cszTableName[KGD_NAME_BUFFER_MAX_SIZE], 
    const KGD_FIELD *pcMatchField1, 
    KGD_OPERATOR Operator1,
    const KGD_FIELD *pcMatchField2, 
    KGD_OPERATOR Operator2
)
{
    int nResult  = false;
    int nRetCode = false;
    IKG_Buffer *piCmdBuffer = NULL;
    unsigned uBufferSize = 0;
    unsigned char *pbyWritePos = NULL;
    KG_DATABASE_DELETE_HEADER *pDeleteHeader = NULL;
    KGD_FIELD *pField = NULL;

    uBufferSize = sizeof(KG_DATABASE_DELETE_HEADER);
    if (pcMatchField1)
    {
        uBufferSize += sizeof(KGD_FIELD) + pcMatchField1->uDataSize;
        if (pcMatchField2)
            uBufferSize += sizeof(KGD_FIELD) + pcMatchField2->uDataSize;
    }
    else
    {
        KGLOG_PROCESS_ERROR(!pcMatchField2);
    }

#ifdef _WIN32	// to do Jeep
    piCmdBuffer = KG_MemoryCreateBuffer(uBufferSize);
#endif

    KGLOG_PROCESS_ERROR(piCmdBuffer);

    pDeleteHeader = (KG_DATABASE_DELETE_HEADER *)piCmdBuffer->GetData();
    ASSERT(pDeleteHeader);
    pbyWritePos = (unsigned char *)pDeleteHeader + sizeof(KG_DATABASE_DELETE_HEADER);

    pDeleteHeader->cmd = CMD_DELETE;

	strncpy(pDeleteHeader->szTableName, cszTableName, sizeof(pDeleteHeader->szTableName) - 1);
	pDeleteHeader->szTableName[sizeof(pDeleteHeader->szTableName) - 1] = '\0';

    nRetCode = _StoreMatchCondition(
        &(pDeleteHeader->Match), 
        &pbyWritePos, 
        pcMatchField1,
        Operator1,
        pcMatchField2,
        Operator2
    );
    KGLOG_PROCESS_ERROR(nRetCode);
//Exit1:
    nResult = true;
Exit0:
    if (!nResult)
    {
        KG_COM_RELEASE(piCmdBuffer);
    }
    return piCmdBuffer;
}
#endif	//_SERVER


