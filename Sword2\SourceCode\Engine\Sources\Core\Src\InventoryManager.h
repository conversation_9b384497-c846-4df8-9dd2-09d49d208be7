//---------------------------------------------------------------------------
// Sword2 Inventory Manager (c) 2024
//
// File:	InventoryManager.h
// Date:	2024.07.02
// Code:	AI Assistant
// Desc:	Comprehensive inventory and bag management system
//---------------------------------------------------------------------------
#ifndef INVENTORY_MANAGER_H
#define INVENTORY_MANAGER_H

#include "ItemSystem.h"
#include "ItemManager.h"
#include "PlayerManager.h"
#include "UnifiedLoggingSystem.h"
#include <unordered_map>
#include <memory>
#include <mutex>
#include <shared_mutex>

namespace sword2 {

// 背包类型（对应原有的INVENTORY_ROOM）
enum class InventoryType : uint8_t
{
    Equipment = 0,      // 装备栏
    Repository,         // 仓库
    Repository1,        // 仓库1
    Repository2,        // 仓库2
    Repository3,        // 仓库3
    Repository4,        // 仓库4
    Repository5,        // 仓库5
    EquipmentEx,        // 扩展装备栏
    Trade,              // 交易栏
    TradeBack,          // 交易返回
    Trade1,             // 交易1
    Immediacy,          // 即时
    Give,               // 给予
    Compound,           // 合成
    Number              // 数量
};

// 玩家背包系统
class PlayerInventory
{
public:
    uint32_t playerId = 0;          // 玩家ID
    
    // 各种背包容器
    std::unordered_map<InventoryType, std::unique_ptr<InventoryContainer>> containers;
    
    // 装备槽位
    std::array<uint32_t, static_cast<size_t>(ItemPart::Number)> equipmentSlots;
    
    PlayerInventory() = default;
    PlayerInventory(uint32_t id) : playerId(id)
    {
        InitializeContainers();
        InitializeEquipmentSlots();
    }
    
    // 初始化容器
    void InitializeContainers()
    {
        // 主背包 (6x10)
        containers[InventoryType::Equipment] = std::make_unique<InventoryContainer>(6, 10, 1000);
        
        // 仓库 (8x12)
        containers[InventoryType::Repository] = std::make_unique<InventoryContainer>(8, 12, 2000);
        containers[InventoryType::Repository1] = std::make_unique<InventoryContainer>(8, 12, 2000);
        containers[InventoryType::Repository2] = std::make_unique<InventoryContainer>(8, 12, 2000);
        containers[InventoryType::Repository3] = std::make_unique<InventoryContainer>(8, 12, 2000);
        containers[InventoryType::Repository4] = std::make_unique<InventoryContainer>(8, 12, 2000);
        containers[InventoryType::Repository5] = std::make_unique<InventoryContainer>(8, 12, 2000);
        
        // 扩展装备栏 (4x6)
        containers[InventoryType::EquipmentEx] = std::make_unique<InventoryContainer>(4, 6, 500);
        
        // 交易栏 (4x4)
        containers[InventoryType::Trade] = std::make_unique<InventoryContainer>(4, 4, 200);
        containers[InventoryType::Trade1] = std::make_unique<InventoryContainer>(4, 4, 200);
        
        // 其他容器
        containers[InventoryType::Immediacy] = std::make_unique<InventoryContainer>(2, 2, 50);
        containers[InventoryType::Give] = std::make_unique<InventoryContainer>(3, 3, 100);
        containers[InventoryType::Compound] = std::make_unique<InventoryContainer>(3, 3, 100);
    }
    
    // 初始化装备槽位
    void InitializeEquipmentSlots()
    {
        for (size_t i = 0; i < equipmentSlots.size(); ++i)
        {
            equipmentSlots[i] = 0; // 0表示空槽位
        }
    }
    
    // 获取容器
    InventoryContainer* GetContainer(InventoryType type)
    {
        auto it = containers.find(type);
        return (it != containers.end()) ? it->second.get() : nullptr;
    }
    
    // 添加物品到背包
    bool AddItem(uint32_t instanceId, InventoryType containerType = InventoryType::Equipment)
    {
        auto container = GetContainer(containerType);
        if (!container)
            return false;
        
        auto instance = GET_ITEM_INSTANCE(instanceId);
        if (!instance)
            return false;
        
        // 查找空位
        auto [x, y] = container->FindEmptySlot();
        if (x == UINT32_MAX || y == UINT32_MAX)
            return false; // 没有空位
        
        // 创建物品实例的副本用于放入背包
        auto itemCopy = std::make_unique<ItemInstance>(*instance);
        
        if (container->PlaceItem(std::move(itemCopy), x, y))
        {
            // 更新物品位置
            instance->position = static_cast<ItemPosition>(containerType);
            
            LOG_DEBUG("INVENTORY", "Added item " + std::to_string(instanceId) + 
                     " to player " + std::to_string(playerId) + " container " + std::to_string(static_cast<int>(containerType)));
            return true;
        }
        
        return false;
    }
    
    // 从背包移除物品
    bool RemoveItem(uint32_t x, uint32_t y, InventoryType containerType = InventoryType::Equipment)
    {
        auto container = GetContainer(containerType);
        if (!container)
            return false;
        
        auto item = container->RemoveItem(x, y);
        if (item)
        {
            LOG_DEBUG("INVENTORY", "Removed item from player " + std::to_string(playerId) + 
                     " container " + std::to_string(static_cast<int>(containerType)) + 
                     " position (" + std::to_string(x) + ", " + std::to_string(y) + ")");
            return true;
        }
        
        return false;
    }
    
    // 装备物品
    bool EquipItem(uint32_t instanceId, ItemPart slot)
    {
        auto instance = GET_ITEM_INSTANCE(instanceId);
        if (!instance)
            return false;
        
        auto itemTemplate = GET_ITEM_TEMPLATE(instance->templateId);
        if (!itemTemplate)
            return false;
        
        if (itemTemplate->genre != ItemGenre::Equipment)
            return false;
        
        if (itemTemplate->equipPart != slot)
            return false; // 槽位不匹配
        
        size_t slotIndex = static_cast<size_t>(slot);
        if (slotIndex >= equipmentSlots.size())
            return false;
        
        // 卸下原有装备
        if (equipmentSlots[slotIndex] != 0)
        {
            UnequipItem(slot);
        }
        
        // 装备新物品
        equipmentSlots[slotIndex] = instanceId;
        instance->position = ItemPosition::Equip;
        
        LOG_INFO("INVENTORY", "Player " + std::to_string(playerId) + " equipped item " + std::to_string(instanceId) + 
                " in slot " + std::to_string(slotIndex));
        return true;
    }
    
    // 卸下装备
    bool UnequipItem(ItemPart slot)
    {
        size_t slotIndex = static_cast<size_t>(slot);
        if (slotIndex >= equipmentSlots.size())
            return false;
        
        uint32_t instanceId = equipmentSlots[slotIndex];
        if (instanceId == 0)
            return false; // 槽位为空
        
        auto instance = GET_ITEM_INSTANCE(instanceId);
        if (instance)
        {
            instance->position = ItemPosition::EquipRoom;
        }
        
        equipmentSlots[slotIndex] = 0;
        
        LOG_INFO("INVENTORY", "Player " + std::to_string(playerId) + " unequipped item " + std::to_string(instanceId) + 
                " from slot " + std::to_string(slotIndex));
        return true;
    }
    
    // 获取装备
    uint32_t GetEquippedItem(ItemPart slot) const
    {
        size_t slotIndex = static_cast<size_t>(slot);
        if (slotIndex >= equipmentSlots.size())
            return 0;
        
        return equipmentSlots[slotIndex];
    }
    
    // 检查是否有空间
    bool HasSpace(InventoryType containerType, uint32_t itemWidth = 1, uint32_t itemHeight = 1) const
    {
        auto it = containers.find(containerType);
        if (it == containers.end())
            return false;
        
        auto [x, y] = it->second->FindEmptySlot(itemWidth, itemHeight);
        return x != UINT32_MAX && y != UINT32_MAX;
    }
    
    // 获取空闲格子数
    uint32_t GetFreeSlotCount(InventoryType containerType) const
    {
        auto it = containers.find(containerType);
        if (it == containers.end())
            return 0;
        
        return it->second->GetFreeSlotCount();
    }
    
    // 获取当前重量
    uint32_t GetCurrentWeight(InventoryType containerType) const
    {
        auto it = containers.find(containerType);
        if (it == containers.end())
            return 0;
        
        return it->second->currentWeight;
    }
    
    // 获取最大重量
    uint32_t GetMaxWeight(InventoryType containerType) const
    {
        auto it = containers.find(containerType);
        if (it == containers.end())
            return 0;
        
        return it->second->maxWeight;
    }
    
    // 整理背包
    void SortContainer(InventoryType containerType)
    {
        auto container = GetContainer(containerType);
        if (container)
        {
            container->SortItems();
            LOG_INFO("INVENTORY", "Sorted container " + std::to_string(static_cast<int>(containerType)) + 
                    " for player " + std::to_string(playerId));
        }
    }
    
    // 获取所有物品
    std::vector<uint32_t> GetAllItems(InventoryType containerType) const
    {
        std::vector<uint32_t> items;
        
        auto it = containers.find(containerType);
        if (it != containers.end())
        {
            const auto& container = it->second;
            for (uint32_t y = 0; y < container->height; ++y)
            {
                for (uint32_t x = 0; x < container->width; ++x)
                {
                    const auto& slot = container->slots[y][x];
                    if (!slot.IsEmpty())
                    {
                        items.push_back(slot.item->instanceId);
                    }
                }
            }
        }
        
        return items;
    }
};

// 背包管理器
class InventoryManager : public Singleton<InventoryManager>
{
public:
    InventoryManager() = default;
    ~InventoryManager() = default;
    
    // 获取玩家背包
    std::shared_ptr<PlayerInventory> GetPlayerInventory(uint32_t playerId)
    {
        std::shared_lock<std::shared_mutex> lock(m_inventoryMutex);
        auto it = m_playerInventories.find(playerId);
        if (it != m_playerInventories.end())
        {
            return it->second;
        }
        
        // 创建新的背包
        std::unique_lock<std::shared_mutex> writeLock(m_inventoryMutex);
        auto inventory = std::make_shared<PlayerInventory>(playerId);
        m_playerInventories[playerId] = inventory;
        
        LOG_INFO("INVENTORY_MGR", "Created inventory for player " + std::to_string(playerId));
        return inventory;
    }
    
    // 移除玩家背包
    void RemovePlayerInventory(uint32_t playerId)
    {
        std::unique_lock<std::shared_mutex> lock(m_inventoryMutex);
        auto it = m_playerInventories.find(playerId);
        if (it != m_playerInventories.end())
        {
            m_playerInventories.erase(it);
            LOG_INFO("INVENTORY_MGR", "Removed inventory for player " + std::to_string(playerId));
        }
    }
    
    // 给玩家添加物品
    bool GiveItemToPlayer(uint32_t playerId, uint32_t templateId, uint32_t stackCount = 1, 
                         InventoryType containerType = InventoryType::Equipment)
    {
        auto inventory = GetPlayerInventory(playerId);
        if (!inventory)
            return false;
        
        // 检查是否有空间
        if (!inventory->HasSpace(containerType))
        {
            LOG_WARNING("INVENTORY_MGR", "No space in container " + std::to_string(static_cast<int>(containerType)) + 
                       " for player " + std::to_string(playerId));
            return false;
        }
        
        // 创建物品实例
        uint32_t instanceId = CREATE_ITEM_INSTANCE(templateId, playerId, stackCount);
        if (instanceId == 0)
            return false;
        
        // 添加到背包
        if (inventory->AddItem(instanceId, containerType))
        {
            LOG_INFO("INVENTORY_MGR", "Gave item template " + std::to_string(templateId) + 
                    " to player " + std::to_string(playerId));
            return true;
        }
        else
        {
            // 添加失败，销毁物品实例
            DESTROY_ITEM_INSTANCE(instanceId);
            return false;
        }
    }
    
    // 移动物品
    bool MoveItem(uint32_t playerId, uint32_t fromX, uint32_t fromY, InventoryType fromContainer,
                  uint32_t toX, uint32_t toY, InventoryType toContainer)
    {
        auto inventory = GetPlayerInventory(playerId);
        if (!inventory)
            return false;
        
        auto fromCont = inventory->GetContainer(fromContainer);
        auto toCont = inventory->GetContainer(toContainer);
        
        if (!fromCont || !toCont)
            return false;
        
        // 获取源物品
        auto fromSlot = fromCont->GetSlot(fromX, fromY);
        if (!fromSlot || fromSlot->IsEmpty())
            return false;
        
        // 检查目标位置
        auto toSlot = toCont->GetSlot(toX, toY);
        if (!toSlot)
            return false;
        
        // 移动物品
        auto item = fromSlot->TakeItem();
        if (toSlot->PlaceItem(std::move(item)))
        {
            LOG_DEBUG("INVENTORY_MGR", "Moved item for player " + std::to_string(playerId) + 
                     " from (" + std::to_string(fromX) + "," + std::to_string(fromY) + ") to (" + 
                     std::to_string(toX) + "," + std::to_string(toY) + ")");
            return true;
        }
        else
        {
            // 移动失败，放回原位
            fromSlot->PlaceItem(std::move(item));
            return false;
        }
    }
    
    // 使用物品
    bool UseItem(uint32_t playerId, uint32_t x, uint32_t y, InventoryType containerType)
    {
        auto inventory = GetPlayerInventory(playerId);
        if (!inventory)
            return false;
        
        auto container = inventory->GetContainer(containerType);
        if (!container)
            return false;
        
        auto slot = container->GetSlot(x, y);
        if (!slot || slot->IsEmpty())
            return false;
        
        uint32_t instanceId = slot->item->instanceId;
        
        // 使用物品
        ItemOperationResult result = USE_ITEM(instanceId, playerId);
        if (result == ItemOperationResult::Success)
        {
            LOG_INFO("INVENTORY_MGR", "Player " + std::to_string(playerId) + " used item at (" + 
                    std::to_string(x) + "," + std::to_string(y) + ")");
            return true;
        }
        
        return false;
    }
    
    // 装备物品
    bool EquipItem(uint32_t playerId, uint32_t x, uint32_t y, InventoryType containerType)
    {
        auto inventory = GetPlayerInventory(playerId);
        if (!inventory)
            return false;
        
        auto container = inventory->GetContainer(containerType);
        if (!container)
            return false;
        
        auto slot = container->GetSlot(x, y);
        if (!slot || slot->IsEmpty())
            return false;
        
        uint32_t instanceId = slot->item->instanceId;
        auto instance = GET_ITEM_INSTANCE(instanceId);
        if (!instance)
            return false;
        
        auto itemTemplate = GET_ITEM_TEMPLATE(instance->templateId);
        if (!itemTemplate)
            return false;
        
        // 装备物品
        if (inventory->EquipItem(instanceId, itemTemplate->equipPart))
        {
            // 从背包中移除
            container->RemoveItem(x, y);
            
            LOG_INFO("INVENTORY_MGR", "Player " + std::to_string(playerId) + " equipped item " + std::to_string(instanceId));
            return true;
        }
        
        return false;
    }
    
    // 卸下装备
    bool UnequipItem(uint32_t playerId, ItemPart slot)
    {
        auto inventory = GetPlayerInventory(playerId);
        if (!inventory)
            return false;
        
        uint32_t instanceId = inventory->GetEquippedItem(slot);
        if (instanceId == 0)
            return false;
        
        // 检查背包是否有空间
        if (!inventory->HasSpace(InventoryType::Equipment))
        {
            LOG_WARNING("INVENTORY_MGR", "No space to unequip item for player " + std::to_string(playerId));
            return false;
        }
        
        // 卸下装备
        if (inventory->UnequipItem(slot))
        {
            // 添加到背包
            inventory->AddItem(instanceId, InventoryType::Equipment);
            
            LOG_INFO("INVENTORY_MGR", "Player " + std::to_string(playerId) + " unequipped item " + std::to_string(instanceId));
            return true;
        }
        
        return false;
    }
    
    // 获取背包统计信息
    struct InventoryStatistics
    {
        size_t totalPlayers = 0;
        std::unordered_map<InventoryType, size_t> containerUsage;
        std::unordered_map<uint32_t, size_t> itemsPerPlayer;
    };
    
    InventoryStatistics GetStatistics()
    {
        std::shared_lock<std::shared_mutex> lock(m_inventoryMutex);
        
        InventoryStatistics stats;
        stats.totalPlayers = m_playerInventories.size();
        
        for (const auto& [playerId, inventory] : m_playerInventories)
        {
            if (!inventory) continue;
            
            size_t totalItems = 0;
            for (const auto& [containerType, container] : inventory->containers)
            {
                if (!container) continue;
                
                size_t usedSlots = container->GetUsedSlotCount();
                stats.containerUsage[containerType] += usedSlots;
                totalItems += usedSlots;
            }
            
            stats.itemsPerPlayer[playerId] = totalItems;
        }
        
        return stats;
    }

private:
    mutable std::shared_mutex m_inventoryMutex;
    std::unordered_map<uint32_t, std::shared_ptr<PlayerInventory>> m_playerInventories;
};

} // namespace sword2

// 全局背包管理器访问
#define INVENTORY_MANAGER() sword2::InventoryManager::getInstance()

// 便捷宏定义
#define GET_PLAYER_INVENTORY(playerId) INVENTORY_MANAGER().GetPlayerInventory(playerId)
#define REMOVE_PLAYER_INVENTORY(playerId) INVENTORY_MANAGER().RemovePlayerInventory(playerId)

#define GIVE_ITEM_TO_PLAYER(playerId, templateId, stackCount, containerType) INVENTORY_MANAGER().GiveItemToPlayer(playerId, templateId, stackCount, containerType)
#define MOVE_INVENTORY_ITEM(playerId, fromX, fromY, fromContainer, toX, toY, toContainer) INVENTORY_MANAGER().MoveItem(playerId, fromX, fromY, fromContainer, toX, toY, toContainer)

#define USE_INVENTORY_ITEM(playerId, x, y, containerType) INVENTORY_MANAGER().UseItem(playerId, x, y, containerType)
#define EQUIP_INVENTORY_ITEM(playerId, x, y, containerType) INVENTORY_MANAGER().EquipItem(playerId, x, y, containerType)
#define UNEQUIP_INVENTORY_ITEM(playerId, slot) INVENTORY_MANAGER().UnequipItem(playerId, slot)

#endif // INVENTORY_MANAGER_H
