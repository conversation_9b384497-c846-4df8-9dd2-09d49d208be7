﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="4.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <ItemGroup>
    <Filter Include="½çĂæ\½çĂæÓ¦ÓĂ\̀áÊ¾´°¿Ú">
      <UniqueIdentifier>{00ae3e53-2957-4b69-8dec-b7251a18d8c0}</UniqueIdentifier>
    </Filter>
    <Filter Include="½çĂæ\½çĂæÓ¦ÓĂ\̀áÊ¾´°¿Ú">
      <UniqueIdentifier>{175e7a37-0969-4787-ac41-b0639ab7ef34}</UniqueIdentifier>
    </Filter>
    <Filter Include="½çĂæ\½çĂæÓ¦ÓĂ\̀áÊ¾´°¿Ú">
      <UniqueIdentifier>{6e535939-95ba-4c06-bb26-637f9da44c81}</UniqueIdentifier>
    </Filter>
    <Filter Include="½çĂæ\½çĂæÓ¦ÓĂ\̀áÊ¾´°¿Ú">
      <UniqueIdentifier>{e396818b-e605-4f2b-a17c-cd49e7c8cc11}</UniqueIdentifier>
    </Filter>
    <Filter Include="ÆäËü">
      <UniqueIdentifier>{308a33f0-5e1a-4588-98d8-a9b657a38f0c}</UniqueIdentifier>
    </Filter>
    <Filter Include="ÍøÂçÁ¬½Ó">
      <UniqueIdentifier>{f71a11f0-32cd-4e44-9078-8f1ee4c4fe9b}</UniqueIdentifier>
    </Filter>
    <Filter Include="ÎÄ×ÖĂüÁî¿ØÖÆ">
      <UniqueIdentifier>{523aadf8-f13c-4549-afac-fbc35ff46d5a}</UniqueIdentifier>
    </Filter>
    <Filter Include="lib">
      <UniqueIdentifier>{b91fa715-be8d-4a54-8bc7-967fec0b8f07}</UniqueIdentifier>
    </Filter>
    <Filter Include="lib\debug">
      <UniqueIdentifier>{526e8403-5f4a-4d64-ac8a-93db35361e4f}</UniqueIdentifier>
    </Filter>
    <Filter Include="lib\release">
      <UniqueIdentifier>{b967ec6a-2cf3-4faa-ae4d-17083f983f0f}</UniqueIdentifier>
    </Filter>
    <Filter Include="login">
      <UniqueIdentifier>{a092d6ce-b961-4035-90af-28c255b37817}</UniqueIdentifier>
    </Filter>
    <Filter Include="ui">
      <UniqueIdentifier>{d3700c94-99f3-4efb-a327-1ea5f09d03dd}</UniqueIdentifier>
    </Filter>
    <Filter Include="ui\elem">
      <UniqueIdentifier>{178978a9-7662-4333-bdf7-841d56bfe64e}</UniqueIdentifier>
    </Filter>
    <Filter Include="ui\sound">
      <UniqueIdentifier>{a27e9c5b-27dc-4684-aa8a-4ca5467cba50}</UniqueIdentifier>
    </Filter>
    <Filter Include="ui\uicase">
      <UniqueIdentifier>{f508d595-5264-478b-b031-96bddad5a3db}</UniqueIdentifier>
    </Filter>
    <Filter Include="ui\uicase\µÇÂ½½çĂæ">
      <UniqueIdentifier>{b8db5dda-101f-4c0e-b787-ace3942cd2c9}</UniqueIdentifier>
    </Filter>
    <Filter Include="ui\uicase\Ö÷½ÇĐÅÏ¢,×´̀¬½çĂæ">
      <UniqueIdentifier>{d915d22f-cded-47b6-819f-e4d8b3b6e3c6}</UniqueIdentifier>
    </Filter>
    <Filter Include="ui\uicase\Ñ¡Ïî½çĂæ">
      <UniqueIdentifier>{eb5027c1-8015-4dbb-a16c-336fb1154206}</UniqueIdentifier>
    </Filter>
    <Filter Include="ui\uicase\³ơÊ¼½çĂæ">
      <UniqueIdentifier>{268f1e7e-4eb2-4f14-98c1-b424056118d3}</UniqueIdentifier>
    </Filter>
    <Filter Include="ui\uicase\µÀ¾ß,×°±¸,¼¼ÄÜ½çĂæ¡¢´¢ÎïÏä">
      <UniqueIdentifier>{f09e4dea-ce32-481f-9476-774646e8f1cc}</UniqueIdentifier>
    </Filter>
    <Filter Include="ui\uicase\ÆäËû½çĂæ">
      <UniqueIdentifier>{d58c5124-78e4-4cac-a63d-8edf5640f7b8}</UniqueIdentifier>
    </Filter>
    <Filter Include="ui\uicase\½ÇÉ«Ñ¡Ôñ">
      <UniqueIdentifier>{c22a5e2e-1482-46af-b69c-cc32100733b7}</UniqueIdentifier>
    </Filter>
    <Filter Include="ui\uicase\ÁÄ̀́">
      <UniqueIdentifier>{c1f819cf-7c47-4931-8f2f-5fdf3340d386}</UniqueIdentifier>
    </Filter>
    <Filter Include="ui\uicase\̀áÊ¾´°¿Ú">
      <UniqueIdentifier>{0d1098df-e297-4c7b-ac0f-d666d706398a}</UniqueIdentifier>
    </Filter>
    <Filter Include="ui\uicase\ÆÁÄ»¶¥ĐĐ¿ØÖÆ̀ơÓëÏûÏ¢½çĂæ">
      <UniqueIdentifier>{cc63ea4a-fadf-4d41-93a8-8f54145c3f4f}</UniqueIdentifier>
    </Filter>
    <Filter Include="ui\uicase\½»̉×">
      <UniqueIdentifier>{4b0828b8-1a34-4a99-8e98-037e547b31b3}</UniqueIdentifier>
    </Filter>
    <Filter Include="ui\uicase\ÓÎÏ·ÊÀ½ç´°¿Ú">
      <UniqueIdentifier>{fe2b8e0a-3b44-4fd7-ab2f-7a0817a222af}</UniqueIdentifier>
    </Filter>
    <Filter Include="ui\uicase\ÏµÍ³ÏûÏ¢">
      <UniqueIdentifier>{4140a8e6-083b-4274-82c2-49abebc04e98}</UniqueIdentifier>
    </Filter>
    <Filter Include="ui\uicase\Đ¡µØÍ¼">
      <UniqueIdentifier>{1fdafb7a-b15f-47c7-8a51-0ec2d1e81516}</UniqueIdentifier>
    </Filter>
    <Filter Include="ui\uicase\Ñ¡ÑƠÉ«">
      <UniqueIdentifier>{1299b5e4-85b5-45aa-9873-253136fbc577}</UniqueIdentifier>
    </Filter>
    <Filter Include="ui\uicase\ĐÂÊÖ½øÈë½çĂæ">
      <UniqueIdentifier>{c044d2eb-8438-438d-98d2-3ef5822374f9}</UniqueIdentifier>
    </Filter>
    <Filter Include="ui\uicase\ÈÎÎñ¼ÇÂ¼">
      <UniqueIdentifier>{d82340ff-3cdf-4a17-828d-4ed7bbbc5c73}</UniqueIdentifier>
    </Filter>
    <Filter Include="ui\uicase\ÊÀ½çµØÍ¼">
      <UniqueIdentifier>{ed5b087c-93a0-4430-a9e2-c50a093517d8}</UniqueIdentifier>
    </Filter>
    <Filter Include="ui\uicase\ĐÂÎÅ´°¿Ú">
      <UniqueIdentifier>{e3424da9-069a-470a-ba90-bc01d2e42e78}</UniqueIdentifier>
    </Filter>
    <Filter Include="ui\uicase\ÅÅĂû½çĂæ">
      <UniqueIdentifier>{78672e42-f56a-40da-baf0-16cb35167e84}</UniqueIdentifier>
    </Filter>
    <Filter Include="ui\uicase\team">
      <UniqueIdentifier>{ee95b464-2326-4658-95b0-614bc5abdbe5}</UniqueIdentifier>
    </Filter>
    <Filter Include="ui\uicase\reconnect">
      <UniqueIdentifier>{d97be644-8b03-492a-8f5d-60cb40446f2d}</UniqueIdentifier>
    </Filter>
    <Filter Include="ui\uicase\enchase">
      <UniqueIdentifier>{43b0ed85-ef18-4708-9623-eb3ec5142093}</UniqueIdentifier>
    </Filter>
    <Filter Include="ui\uicase\tong">
      <UniqueIdentifier>{836b4334-8fa4-4980-a374-6114f4fefc10}</UniqueIdentifier>
    </Filter>
    <Filter Include="ui\uicase\helper">
      <UniqueIdentifier>{0df177f8-be40-4dbf-8149-ebd8e9a97817}</UniqueIdentifier>
    </Filter>
  </ItemGroup>
  <ItemGroup>
    <ClCompile Include="Ui\Elem\AutoLocateWnd.cpp">
      <Filter>ui\elem</Filter>
    </ClCompile>
    <ClCompile Include="Ui\Elem\ComWindow.cpp">
      <Filter>ui\elem</Filter>
    </ClCompile>
    <ClCompile Include="Ui\Elem\MouseHover.cpp">
      <Filter>ui\elem</Filter>
    </ClCompile>
    <ClCompile Include="Ui\Elem\PopupMenu.cpp">
      <Filter>ui\elem</Filter>
    </ClCompile>
    <ClCompile Include="Ui\Elem\SpaceOver.cpp">
      <Filter>ui\elem</Filter>
    </ClCompile>
    <ClCompile Include="Ui\Elem\SpecialFuncs.cpp">
      <Filter>ui\elem</Filter>
    </ClCompile>
    <ClCompile Include="Ui\Elem\TextPic.cpp">
      <Filter>ui\elem</Filter>
    </ClCompile>
    <ClCompile Include="Ui\Elem\UiCursor.cpp">
      <Filter>ui\elem</Filter>
    </ClCompile>
    <ClCompile Include="Ui\Elem\UiImage.cpp">
      <Filter>ui\elem</Filter>
    </ClCompile>
    <ClCompile Include="Ui\Elem\WndBorder.cpp">
      <Filter>ui\elem</Filter>
    </ClCompile>
    <ClCompile Include="Ui\Elem\WndButton.cpp">
      <Filter>ui\elem</Filter>
    </ClCompile>
    <ClCompile Include="Ui\Elem\WndEdit.cpp">
      <Filter>ui\elem</Filter>
    </ClCompile>
    <ClCompile Include="Ui\Elem\WndImage.cpp">
      <Filter>ui\elem</Filter>
    </ClCompile>
    <ClCompile Include="Ui\Elem\WndImagePart.cpp">
      <Filter>ui\elem</Filter>
    </ClCompile>
    <ClCompile Include="Ui\Elem\WndLabeledButton.cpp">
      <Filter>ui\elem</Filter>
    </ClCompile>
    <ClCompile Include="Ui\Elem\WndList.cpp">
      <Filter>ui\elem</Filter>
    </ClCompile>
    <ClCompile Include="Ui\Elem\WndList2.cpp">
      <Filter>ui\elem</Filter>
    </ClCompile>
    <ClCompile Include="Ui\Elem\WndMessageListBox.cpp">
      <Filter>ui\elem</Filter>
    </ClCompile>
    <ClCompile Include="Ui\Elem\WndMovingImage.cpp">
      <Filter>ui\elem</Filter>
    </ClCompile>
    <ClCompile Include="Ui\Elem\WndObjContainer.cpp">
      <Filter>ui\elem</Filter>
    </ClCompile>
    <ClCompile Include="Ui\Elem\WndPage.cpp">
      <Filter>ui\elem</Filter>
    </ClCompile>
    <ClCompile Include="Ui\Elem\WndPureTextBtn.cpp">
      <Filter>ui\elem</Filter>
    </ClCompile>
    <ClCompile Include="Ui\Elem\Wnds.cpp">
      <Filter>ui\elem</Filter>
    </ClCompile>
    <ClCompile Include="Ui\Elem\WndScrollBar.cpp">
      <Filter>ui\elem</Filter>
    </ClCompile>
    <ClCompile Include="Ui\elem\WndShadow.cpp">
      <Filter>ui\elem</Filter>
    </ClCompile>
    <ClCompile Include="Ui\Elem\WndShowAnimate.cpp">
      <Filter>ui\elem</Filter>
    </ClCompile>
    <ClCompile Include="Ui\Elem\WndText.cpp">
      <Filter>ui\elem</Filter>
    </ClCompile>
    <ClCompile Include="Ui\Elem\WndToolBar.cpp">
      <Filter>ui\elem</Filter>
    </ClCompile>
    <ClCompile Include="Ui\Elem\WndValueImage.cpp">
      <Filter>ui\elem</Filter>
    </ClCompile>
    <ClCompile Include="Ui\Elem\WndWindow.cpp">
      <Filter>ui\elem</Filter>
    </ClCompile>
    <ClCompile Include="Ui\UiCase\UiConnectInfo.cpp">
      <Filter>ui\uicase\µÇÂ½½çĂæ</Filter>
    </ClCompile>
    <ClCompile Include="Ui\UiCase\UiLogin.cpp">
      <Filter>ui\uicase\µÇÂ½½çĂæ</Filter>
    </ClCompile>
    <ClCompile Include="Ui\UiCase\UiLoginBg.cpp">
      <Filter>ui\uicase\µÇÂ½½çĂæ</Filter>
    </ClCompile>
    <ClCompile Include="Ui\UiCase\UiNotice.cpp">
      <Filter>ui\uicase\µÇÂ½½çĂæ</Filter>
    </ClCompile>
    <ClCompile Include="Ui\UiCase\UiSelServer.cpp">
      <Filter>ui\uicase\µÇÂ½½çĂæ</Filter>
    </ClCompile>
    <ClCompile Include="Ui\UiCase\UiChooseFace.cpp">
      <Filter>ui\uicase\Ö÷½ÇĐÅÏ¢,×´̀¬½çĂæ</Filter>
    </ClCompile>
    <ClCompile Include="Ui\UiCase\UiContainer.cpp">
      <Filter>ui\uicase\Ö÷½ÇĐÅÏ¢,×´̀¬½çĂæ</Filter>
    </ClCompile>
    <ClCompile Include="Ui\UiCase\UiFaceSelector.cpp">
      <Filter>ui\uicase\Ö÷½ÇĐÅÏ¢,×´̀¬½çĂæ</Filter>
    </ClCompile>
    <ClCompile Include="Ui\UiCase\UiPlayerBar.cpp">
      <Filter>ui\uicase\Ö÷½ÇĐÅÏ¢,×´̀¬½çĂæ</Filter>
    </ClCompile>
    <ClCompile Include="Ui\UiCase\UiPlayerControlBar.cpp">
      <Filter>ui\uicase\Ö÷½ÇĐÅÏ¢,×´̀¬½çĂæ</Filter>
    </ClCompile>
    <ClCompile Include="Ui\UiCase\UiStatus.cpp">
      <Filter>ui\uicase\Ö÷½ÇĐÅÏ¢,×´̀¬½çĂæ</Filter>
    </ClCompile>
    <ClCompile Include="Ui\UiCase\UiOptions.cpp">
      <Filter>ui\uicase\Ñ¡Ïî½çĂæ</Filter>
    </ClCompile>
    <ClCompile Include="Ui\UiCase\UiInit.cpp">
      <Filter>ui\uicase\³ơÊ¼½çĂæ</Filter>
    </ClCompile>
    <ClCompile Include="Ui\UiCase\UiAutoPlay.cpp">
      <Filter>ui\uicase\µÀ¾ß,×°±¸,¼¼ÄÜ½çĂæ¡¢´¢ÎïÏä</Filter>
    </ClCompile>
    <ClCompile Include="Ui\UiCase\UiPlayerLock.cpp">
      <Filter>ui\uicase\µÀ¾ß,×°±¸,¼¼ÄÜ½çĂæ¡¢´¢ÎïÏä</Filter>
    </ClCompile>
    <ClCompile Include="Ui\UiCase\UiBreakItem.cpp">
      <Filter>ui\uicase\µÀ¾ß,×°±¸,¼¼ÄÜ½çĂæ¡¢´¢ÎïÏä</Filter>
    </ClCompile>
    <ClCompile Include="Ui\UiCase\UiExpandItem.cpp">
      <Filter>ui\uicase\µÀ¾ß,×°±¸,¼¼ÄÜ½çĂæ¡¢´¢ÎïÏä</Filter>
    </ClCompile>
    <ClCompile Include="Ui\UiCase\UiGetMoney.cpp">
      <Filter>ui\uicase\µÀ¾ß,×°±¸,¼¼ÄÜ½çĂæ¡¢´¢ÎïÏä</Filter>
    </ClCompile>
    <ClCompile Include="Ui\UiCase\UiGive.cpp">
      <Filter>ui\uicase\µÀ¾ß,×°±¸,¼¼ÄÜ½çĂæ¡¢´¢ÎïÏä</Filter>
    </ClCompile>
    <ClCompile Include="Ui\UiCase\UiItem.cpp">
      <Filter>ui\uicase\µÀ¾ß,×°±¸,¼¼ÄÜ½çĂæ¡¢´¢ÎïÏä</Filter>
    </ClCompile>
    <ClCompile Include="Ui\UiCase\UiSkills.cpp">
      <Filter>ui\uicase\µÀ¾ß,×°±¸,¼¼ÄÜ½çĂæ¡¢´¢ÎïÏä</Filter>
    </ClCompile>
    <ClCompile Include="Ui\UiCase\UiSkillTree.cpp">
      <Filter>ui\uicase\µÀ¾ß,×°±¸,¼¼ÄÜ½çĂæ¡¢´¢ÎïÏä</Filter>
    </ClCompile>
    <ClCompile Include="Ui\uicase\UiStoreBox.cpp">
      <Filter>ui\uicase\µÀ¾ß,×°±¸,¼¼ÄÜ½çĂæ¡¢´¢ÎïÏä</Filter>
    </ClCompile>
    <ClCompile Include="Ui\UiCase\UiESCDlg.cpp">
      <Filter>ui\uicase\ÆäËû½çĂæ</Filter>
    </ClCompile>
    <ClCompile Include="Ui\UiCase\UiGetString.cpp">
      <Filter>ui\uicase\ÆäËû½çĂæ</Filter>
    </ClCompile>
    <ClCompile Include="Ui\UiCase\UiMsgSel.cpp">
      <Filter>ui\uicase\ÆäËû½çĂæ</Filter>
    </ClCompile>
    <ClCompile Include="Ui\UiCase\UiParadeItem.cpp">
      <Filter>ui\uicase\ÆäËû½çĂæ</Filter>
    </ClCompile>
    <ClCompile Include="Ui\UiCase\UiSelPlayerNearby.cpp">
      <Filter>ui\uicase\ÆäËû½çĂæ</Filter>
    </ClCompile>
    <ClCompile Include="Ui\UiCase\UiNewPlayer.cpp">
      <Filter>ui\uicase\½ÇÉ«Ñ¡Ôñ</Filter>
    </ClCompile>
    <ClCompile Include="Ui\UiCase\UiSelNativePlace.cpp">
      <Filter>ui\uicase\½ÇÉ«Ñ¡Ôñ</Filter>
    </ClCompile>
    <ClCompile Include="Ui\UiCase\UiSelPlayer.cpp">
      <Filter>ui\uicase\½ÇÉ«Ñ¡Ôñ</Filter>
    </ClCompile>
    <ClCompile Include="Ui\UiCase\UiChatCentre.cpp">
      <Filter>ui\uicase\ÁÄ̀́</Filter>
    </ClCompile>
    <ClCompile Include="Ui\UiChatPhrase.cpp">
      <Filter>ui\uicase\ÁÄ̀́</Filter>
    </ClCompile>
    <ClCompile Include="Ui\UiCase\UiChatRoom.cpp">
      <Filter>ui\uicase\ÁÄ̀́</Filter>
    </ClCompile>
    <ClCompile Include="Ui\UiCase\UiInformation.cpp">
      <Filter>ui\uicase\̀áÊ¾´°¿Ú</Filter>
    </ClCompile>
    <ClCompile Include="Ui\UiCase\UiInformation1.cpp">
      <Filter>ui\uicase\̀áÊ¾´°¿Ú</Filter>
    </ClCompile>
    <ClCompile Include="Ui\UiCase\UiInformation2.cpp">
      <Filter>ui\uicase\̀áÊ¾´°¿Ú</Filter>
    </ClCompile>
    <ClCompile Include="Ui\UiCase\UiTeamManage.cpp">
      <Filter>ui\uicase\team</Filter>
    </ClCompile>
    <ClCompile Include="Ui\UiCase\UiChatItem.cpp">
      <Filter>ui\uicase\ÆÁÄ»¶¥ĐĐ¿ØÖÆ̀ơÓëÏûÏ¢½çĂæ</Filter>
    </ClCompile>
    <ClCompile Include="Ui\UiCase\UiHeaderControlBar.cpp">
      <Filter>ui\uicase\ÆÁÄ»¶¥ĐĐ¿ØÖÆ̀ơÓëÏûÏ¢½çĂæ</Filter>
    </ClCompile>
    <ClCompile Include="Ui\uicase\UiMsgCentrePad.cpp">
      <Filter>ui\uicase\ÆÁÄ»¶¥ĐĐ¿ØÖÆ̀ơÓëÏûÏ¢½çĂæ</Filter>
    </ClCompile>
    <ClCompile Include="Ui\UiCase\UiRankData.cpp">
      <Filter>ui\uicase\ÆÁÄ»¶¥ĐĐ¿ØÖÆ̀ơÓëÏûÏ¢½çĂæ</Filter>
    </ClCompile>
    <ClCompile Include="Ui\UiCase\UiToolsControlBar.cpp">
      <Filter>ui\uicase\ÆÁÄ»¶¥ĐĐ¿ØÖÆ̀ơÓëÏûÏ¢½çĂæ</Filter>
    </ClCompile>
    <ClCompile Include="Ui\UiCase\UiPlayerShop.cpp">
      <Filter>ui\uicase\½»̉×</Filter>
    </ClCompile>
    <ClCompile Include="Ui\UiCase\UiSetPrice.cpp">
      <Filter>ui\uicase\½»̉×</Filter>
    </ClCompile>
    <ClCompile Include="Ui\UIcase\UiShop.cpp">
      <Filter>ui\uicase\½»̉×</Filter>
    </ClCompile>
    <ClCompile Include="Ui\UiCase\UiSuperShop.cpp">
      <Filter>ui\uicase\½»̉×</Filter>
    </ClCompile>
    <ClCompile Include="Ui\UiCase\UiTrade.cpp">
      <Filter>ui\uicase\½»̉×</Filter>
    </ClCompile>
    <ClCompile Include="Ui\UiCase\UiTradeConfirmWnd.cpp">
      <Filter>ui\uicase\½»̉×</Filter>
    </ClCompile>
    <ClCompile Include="Ui\UiCase\UiGame.cpp">
      <Filter>ui\uicase\ÓÎÏ·ÊÀ½ç´°¿Ú</Filter>
    </ClCompile>
    <ClCompile Include="Ui\UiCase\UiSysMsgCentre.cpp">
      <Filter>ui\uicase\ÏµÍ³ÏûÏ¢</Filter>
    </ClCompile>
    <ClCompile Include="Ui\UiCase\UiCaveList.cpp">
      <Filter>ui\uicase\Đ¡µØÍ¼</Filter>
    </ClCompile>
    <ClCompile Include="Ui\uicase\UiMiniMap.cpp">
      <Filter>ui\uicase\Đ¡µØÍ¼</Filter>
    </ClCompile>
    <ClCompile Include="Ui\UiCase\UiSelColor.cpp">
      <Filter>ui\uicase\Ñ¡ÑƠÉ«</Filter>
    </ClCompile>
    <ClCompile Include="Ui\UiCase\UiNewPlayerStartMsg.cpp">
      <Filter>ui\uicase\ĐÂÊÖ½øÈë½çĂæ</Filter>
    </ClCompile>
    <ClCompile Include="Ui\UiCase\UiHelper.cpp">
      <Filter>ui\uicase\helper</Filter>
    </ClCompile>
    <ClCompile Include="Ui\UiCase\UiHelper2.cpp">
      <Filter>ui\uicase\helper</Filter>
    </ClCompile>
    <ClCompile Include="Ui\UiCase\UiReconnect.cpp">
      <Filter>ui\uicase\reconnect</Filter>
    </ClCompile>
    <ClCompile Include="Ui\UiCase\UiTaskDataFile.cpp">
      <Filter>ui\uicase\ÈÎÎñ¼ÇÂ¼</Filter>
    </ClCompile>
    <ClCompile Include="Ui\UiCase\UiTaskNote.cpp">
      <Filter>ui\uicase\ÈÎÎñ¼ÇÂ¼</Filter>
    </ClCompile>
    <ClCompile Include="Ui\UiCase\UiWorldMap.cpp">
      <Filter>ui\uicase\ÊÀ½çµØÍ¼</Filter>
    </ClCompile>
    <ClCompile Include="Ui\UiCase\UiNewsMessage.cpp">
      <Filter>ui\uicase\ĐÂÎÅ´°¿Ú</Filter>
    </ClCompile>
    <ClCompile Include="Ui\UiCase\UiNewsMessage2.cpp">
      <Filter>ui\uicase\ĐÂÎÅ´°¿Ú</Filter>
    </ClCompile>
    <ClCompile Include="Ui\UiCase\UiNewsSysMsg.cpp">
      <Filter>ui\uicase\ĐÂÎÅ´°¿Ú</Filter>
    </ClCompile>
    <ClCompile Include="Ui\UiCase\UiStrengthRank.cpp">
      <Filter>ui\uicase\ÅÅĂû½çĂæ</Filter>
    </ClCompile>
    <ClCompile Include="ui\uicase\UiTongAssignBox.cpp">
      <Filter>ui\uicase\tong</Filter>
    </ClCompile>
    <ClCompile Include="ui\uicase\UiTongCreateSheet.cpp">
      <Filter>ui\uicase\tong</Filter>
    </ClCompile>
    <ClCompile Include="Ui\UiCase\UiTongGetString.cpp">
      <Filter>ui\uicase\tong</Filter>
    </ClCompile>
    <ClCompile Include="ui\uicase\UiTongManager.cpp">
      <Filter>ui\uicase\tong</Filter>
    </ClCompile>
    <ClCompile Include="Ui\UiCase\UiEnchase.cpp">
      <Filter>ui\uicase\enchase</Filter>
    </ClCompile>
    <ClCompile Include="Ui\UiSoundSetting.cpp">
      <Filter>ui\sound</Filter>
    </ClCompile>
    <ClCompile Include="Ui\GameSpaceChangedNotify.cpp">
      <Filter>ui</Filter>
    </ClCompile>
    <ClCompile Include="Ui\ShortcutKey.cpp">
      <Filter>ui</Filter>
    </ClCompile>
    <ClCompile Include="Ui\UiBase.cpp">
      <Filter>ui</Filter>
    </ClCompile>
    <ClCompile Include="Ui\UiShell.cpp">
      <Filter>ui</Filter>
    </ClCompile>
    <ClCompile Include="Ui\ChatFilter.cpp">
      <Filter>ÆäËü</Filter>
    </ClCompile>
    <ClCompile Include="Ui\FilterTextLib.cpp">
      <Filter>ÆäËü</Filter>
    </ClCompile>
    <ClCompile Include="S3Client.cpp">
      <Filter>ÆäËü</Filter>
    </ClCompile>
    <ClCompile Include="NetConnect\NetConnectAgent.cpp">
      <Filter>ÍøÂçÁ¬½Ó</Filter>
    </ClCompile>
    <ClCompile Include="Login\Login.cpp">
      <Filter>login</Filter>
    </ClCompile>
    <ClCompile Include="TextCtrlCmd\TextCtrlCmd.cpp">
      <Filter>ÎÄ×ÖĂüÁî¿ØÖÆ</Filter>
    </ClCompile>
    <ClCompile Include="ErrorCode.cpp" />
    <ClCompile Include="stdafx.cpp" />
  </ItemGroup>
  <ItemGroup>
    <ClInclude Include="Ui\Elem\AutoLocateWnd.h">
      <Filter>ui\elem</Filter>
    </ClInclude>
    <ClInclude Include="Ui\Elem\ComWindow.h">
      <Filter>ui\elem</Filter>
    </ClInclude>
    <ClInclude Include="Ui\Elem\MouseHover.h">
      <Filter>ui\elem</Filter>
    </ClInclude>
    <ClInclude Include="Ui\Elem\PopupMenu.h">
      <Filter>ui\elem</Filter>
    </ClInclude>
    <ClInclude Include="Ui\Elem\SpaceOver.h">
      <Filter>ui\elem</Filter>
    </ClInclude>
    <ClInclude Include="Ui\Elem\SpecialFuncs.h">
      <Filter>ui\elem</Filter>
    </ClInclude>
    <ClInclude Include="Ui\Elem\TextPic.h">
      <Filter>ui\elem</Filter>
    </ClInclude>
    <ClInclude Include="Ui\Elem\UiCursor.h">
      <Filter>ui\elem</Filter>
    </ClInclude>
    <ClInclude Include="Ui\Elem\UiImage.h">
      <Filter>ui\elem</Filter>
    </ClInclude>
    <ClInclude Include="Ui\Elem\WndBorder.h">
      <Filter>ui\elem</Filter>
    </ClInclude>
    <ClInclude Include="Ui\Elem\WndButton.h">
      <Filter>ui\elem</Filter>
    </ClInclude>
    <ClInclude Include="Ui\Elem\WndEdit.h">
      <Filter>ui\elem</Filter>
    </ClInclude>
    <ClInclude Include="Ui\Elem\WndGameSpace.h">
      <Filter>ui\elem</Filter>
    </ClInclude>
    <ClInclude Include="Ui\Elem\WndImage.h">
      <Filter>ui\elem</Filter>
    </ClInclude>
    <ClInclude Include="Ui\Elem\WndImagePart.h">
      <Filter>ui\elem</Filter>
    </ClInclude>
    <ClInclude Include="Ui\Elem\WndLabeledButton.h">
      <Filter>ui\elem</Filter>
    </ClInclude>
    <ClInclude Include="Ui\Elem\WndList.h">
      <Filter>ui\elem</Filter>
    </ClInclude>
    <ClInclude Include="Ui\Elem\WndList2.h">
      <Filter>ui\elem</Filter>
    </ClInclude>
    <ClInclude Include="Ui\Elem\WndMessage.h">
      <Filter>ui\elem</Filter>
    </ClInclude>
    <ClInclude Include="Ui\Elem\WndMessageListBox.h">
      <Filter>ui\elem</Filter>
    </ClInclude>
    <ClInclude Include="Ui\Elem\WndMovingImage.h">
      <Filter>ui\elem</Filter>
    </ClInclude>
    <ClInclude Include="Ui\Elem\WndObjContainer.h">
      <Filter>ui\elem</Filter>
    </ClInclude>
    <ClInclude Include="Ui\Elem\WndPage.h">
      <Filter>ui\elem</Filter>
    </ClInclude>
    <ClInclude Include="Ui\Elem\WndPureTextBtn.h">
      <Filter>ui\elem</Filter>
    </ClInclude>
    <ClInclude Include="Ui\Elem\Wnds.h">
      <Filter>ui\elem</Filter>
    </ClInclude>
    <ClInclude Include="Ui\Elem\WndScrollBar.h">
      <Filter>ui\elem</Filter>
    </ClInclude>
    <ClInclude Include="Ui\elem\WndShadow.h">
      <Filter>ui\elem</Filter>
    </ClInclude>
    <ClInclude Include="Ui\Elem\WndShowAnimate.h">
      <Filter>ui\elem</Filter>
    </ClInclude>
    <ClInclude Include="Ui\Elem\WndText.h">
      <Filter>ui\elem</Filter>
    </ClInclude>
    <ClInclude Include="Ui\Elem\WndToolBar.h">
      <Filter>ui\elem</Filter>
    </ClInclude>
    <ClInclude Include="Ui\Elem\WndValueImage.h">
      <Filter>ui\elem</Filter>
    </ClInclude>
    <ClInclude Include="Ui\Elem\WndWindow.h">
      <Filter>ui\elem</Filter>
    </ClInclude>
    <ClInclude Include="Ui\UiCase\UiConnectInfo.h">
      <Filter>ui\uicase\µÇÂ½½çĂæ</Filter>
    </ClInclude>
    <ClInclude Include="Ui\UiCase\UiLogin.h">
      <Filter>ui\uicase\µÇÂ½½çĂæ</Filter>
    </ClInclude>
    <ClInclude Include="Ui\UiCase\UiLoginBg.h">
      <Filter>ui\uicase\µÇÂ½½çĂæ</Filter>
    </ClInclude>
    <ClInclude Include="Ui\UiCase\UiNotice.h">
      <Filter>ui\uicase\µÇÂ½½çĂæ</Filter>
    </ClInclude>
    <ClInclude Include="Ui\UiCase\UiSelServer.h">
      <Filter>ui\uicase\µÇÂ½½çĂæ</Filter>
    </ClInclude>
    <ClInclude Include="Ui\UiCase\UiChooseFace.h">
      <Filter>ui\uicase\Ö÷½ÇĐÅÏ¢,×´̀¬½çĂæ</Filter>
    </ClInclude>
    <ClInclude Include="Ui\UiCase\UiContainer.h">
      <Filter>ui\uicase\Ö÷½ÇĐÅÏ¢,×´̀¬½çĂæ</Filter>
    </ClInclude>
    <ClInclude Include="Ui\UiCase\UiFaceSelector.h">
      <Filter>ui\uicase\Ö÷½ÇĐÅÏ¢,×´̀¬½çĂæ</Filter>
    </ClInclude>
    <ClInclude Include="Ui\UiCase\UiPlayerBar.h">
      <Filter>ui\uicase\Ö÷½ÇĐÅÏ¢,×´̀¬½çĂæ</Filter>
    </ClInclude>
    <ClInclude Include="Ui\UiCase\UiPlayerControlBar.h">
      <Filter>ui\uicase\Ö÷½ÇĐÅÏ¢,×´̀¬½çĂæ</Filter>
    </ClInclude>
    <ClInclude Include="Ui\UiCase\UiStatus.h">
      <Filter>ui\uicase\Ö÷½ÇĐÅÏ¢,×´̀¬½çĂæ</Filter>
    </ClInclude>
    <ClInclude Include="Ui\UiCase\UiOptions.h">
      <Filter>ui\uicase\Ñ¡Ïî½çĂæ</Filter>
    </ClInclude>
    <ClInclude Include="Ui\UiCase\UiInit.h">
      <Filter>ui\uicase\³ơÊ¼½çĂæ</Filter>
    </ClInclude>
    <ClInclude Include="Ui\UiCase\UiAutoPlay.h">
      <Filter>ui\uicase\µÀ¾ß,×°±¸,¼¼ÄÜ½çĂæ¡¢´¢ÎïÏä</Filter>
    </ClInclude>
    <ClInclude Include="Ui\UiCase\UiPlayerLock.h">
      <Filter>ui\uicase\µÀ¾ß,×°±¸,¼¼ÄÜ½çĂæ¡¢´¢ÎïÏä</Filter>
    </ClInclude>
    <ClInclude Include="Ui\UiCase\UiBreakItem.h">
      <Filter>ui\uicase\µÀ¾ß,×°±¸,¼¼ÄÜ½çĂæ¡¢´¢ÎïÏä</Filter>
    </ClInclude>
    <ClInclude Include="Ui\UiCase\UiExpandItem.h">
      <Filter>ui\uicase\µÀ¾ß,×°±¸,¼¼ÄÜ½çĂæ¡¢´¢ÎïÏä</Filter>
    </ClInclude>
    <ClInclude Include="Ui\UiCase\UiGetMoney.h">
      <Filter>ui\uicase\µÀ¾ß,×°±¸,¼¼ÄÜ½çĂæ¡¢´¢ÎïÏä</Filter>
    </ClInclude>
    <ClInclude Include="Ui\UiCase\UiGive.h">
      <Filter>ui\uicase\µÀ¾ß,×°±¸,¼¼ÄÜ½çĂæ¡¢´¢ÎïÏä</Filter>
    </ClInclude>
    <ClInclude Include="Ui\UiCase\UiItem.h">
      <Filter>ui\uicase\µÀ¾ß,×°±¸,¼¼ÄÜ½çĂæ¡¢´¢ÎïÏä</Filter>
    </ClInclude>
    <ClInclude Include="Ui\UiCase\UiSkills.h">
      <Filter>ui\uicase\µÀ¾ß,×°±¸,¼¼ÄÜ½çĂæ¡¢´¢ÎïÏä</Filter>
    </ClInclude>
    <ClInclude Include="Ui\UiCase\UiSkillTree.h">
      <Filter>ui\uicase\µÀ¾ß,×°±¸,¼¼ÄÜ½çĂæ¡¢´¢ÎïÏä</Filter>
    </ClInclude>
    <ClInclude Include="Ui\uicase\UiStoreBox.h">
      <Filter>ui\uicase\µÀ¾ß,×°±¸,¼¼ÄÜ½çĂæ¡¢´¢ÎïÏä</Filter>
    </ClInclude>
    <ClInclude Include="Ui\UiCase\UiESCDlg.h">
      <Filter>ui\uicase\ÆäËû½çĂæ</Filter>
    </ClInclude>
    <ClInclude Include="Ui\UiCase\UiGetString.h">
      <Filter>ui\uicase\ÆäËû½çĂæ</Filter>
    </ClInclude>
    <ClInclude Include="Ui\UiCase\UiMsgSel.h">
      <Filter>ui\uicase\ÆäËû½çĂæ</Filter>
    </ClInclude>
    <ClInclude Include="Ui\UiCase\UiParadeItem.h">
      <Filter>ui\uicase\ÆäËû½çĂæ</Filter>
    </ClInclude>
    <ClInclude Include="Ui\UiCase\UiSelPlayerNearby.h">
      <Filter>ui\uicase\ÆäËû½çĂæ</Filter>
    </ClInclude>
    <ClInclude Include="Ui\UiCase\UiNewPlayer.h">
      <Filter>ui\uicase\½ÇÉ«Ñ¡Ôñ</Filter>
    </ClInclude>
    <ClInclude Include="Ui\UiCase\UiSelNativePlace.h">
      <Filter>ui\uicase\½ÇÉ«Ñ¡Ôñ</Filter>
    </ClInclude>
    <ClInclude Include="Ui\UiCase\UiSelPlayer.h">
      <Filter>ui\uicase\½ÇÉ«Ñ¡Ôñ</Filter>
    </ClInclude>
    <ClInclude Include="Ui\UiCase\UiChatCentre.h">
      <Filter>ui\uicase\ÁÄ̀́</Filter>
    </ClInclude>
    <ClInclude Include="Ui\UiChatPhrase.h">
      <Filter>ui\uicase\ÁÄ̀́</Filter>
    </ClInclude>
    <ClInclude Include="Ui\UiCase\UiChatRoom.h">
      <Filter>ui\uicase\ÁÄ̀́</Filter>
    </ClInclude>
    <ClInclude Include="Ui\UiCase\UiInformation.h">
      <Filter>ui\uicase\̀áÊ¾´°¿Ú</Filter>
    </ClInclude>
    <ClInclude Include="Ui\UiCase\UiInformation1.h">
      <Filter>ui\uicase\̀áÊ¾´°¿Ú</Filter>
    </ClInclude>
    <ClInclude Include="Ui\UiCase\UiInformation2.h">
      <Filter>ui\uicase\̀áÊ¾´°¿Ú</Filter>
    </ClInclude>
    <ClInclude Include="Ui\UiCase\UiTeamManage.h">
      <Filter>ui\uicase\team</Filter>
    </ClInclude>
    <ClInclude Include="Ui\UiCase\UiChatItem.h">
      <Filter>ui\uicase\ÆÁÄ»¶¥ĐĐ¿ØÖÆ̀ơÓëÏûÏ¢½çĂæ</Filter>
    </ClInclude>
    <ClInclude Include="Ui\UiCase\UiHeaderControlBar.h">
      <Filter>ui\uicase\ÆÁÄ»¶¥ĐĐ¿ØÖÆ̀ơÓëÏûÏ¢½çĂæ</Filter>
    </ClInclude>
    <ClInclude Include="Ui\UiCase\UiMsgCentrePad.h">
      <Filter>ui\uicase\ÆÁÄ»¶¥ĐĐ¿ØÖÆ̀ơÓëÏûÏ¢½çĂæ</Filter>
    </ClInclude>
    <ClInclude Include="Ui\UiCase\UiRankData.h">
      <Filter>ui\uicase\ÆÁÄ»¶¥ĐĐ¿ØÖÆ̀ơÓëÏûÏ¢½çĂæ</Filter>
    </ClInclude>
    <ClInclude Include="Ui\UiCase\UiToolsControlBar.h">
      <Filter>ui\uicase\ÆÁÄ»¶¥ĐĐ¿ØÖÆ̀ơÓëÏûÏ¢½çĂæ</Filter>
    </ClInclude>
    <ClInclude Include="Ui\UiCase\UiPlayerShop.h">
      <Filter>ui\uicase\½»̉×</Filter>
    </ClInclude>
    <ClInclude Include="Ui\UiCase\UiSetPrice.h">
      <Filter>ui\uicase\½»̉×</Filter>
    </ClInclude>
    <ClInclude Include="Ui\UIcase\UiShop.h">
      <Filter>ui\uicase\½»̉×</Filter>
    </ClInclude>
    <ClInclude Include="Ui\UiCase\UiSuperShop.h">
      <Filter>ui\uicase\½»̉×</Filter>
    </ClInclude>
    <ClInclude Include="Ui\UiCase\UiTrade.h">
      <Filter>ui\uicase\½»̉×</Filter>
    </ClInclude>
    <ClInclude Include="Ui\UiCase\UiTradeConfirmWnd.h">
      <Filter>ui\uicase\½»̉×</Filter>
    </ClInclude>
    <ClInclude Include="Ui\UiCase\UiGame.h">
      <Filter>ui\uicase\ÓÎÏ·ÊÀ½ç´°¿Ú</Filter>
    </ClInclude>
    <ClInclude Include="Ui\UiCase\UiSysMsgCentre.h">
      <Filter>ui\uicase\ÏµÍ³ÏûÏ¢</Filter>
    </ClInclude>
    <ClInclude Include="Ui\UiCase\UiCaveList.h">
      <Filter>ui\uicase\Đ¡µØÍ¼</Filter>
    </ClInclude>
    <ClInclude Include="Ui\uicase\UiMiniMap.h">
      <Filter>ui\uicase\Đ¡µØÍ¼</Filter>
    </ClInclude>
    <ClInclude Include="Ui\UiCase\UiSelColor.h">
      <Filter>ui\uicase\Ñ¡ÑƠÉ«</Filter>
    </ClInclude>
    <ClInclude Include="Ui\UiCase\UiNewPlayerStartMsg.h">
      <Filter>ui\uicase\ĐÂÊÖ½øÈë½çĂæ</Filter>
    </ClInclude>
    <ClInclude Include="Ui\UiCase\UiHelper.h">
      <Filter>ui\uicase\helper</Filter>
    </ClInclude>
    <ClInclude Include="Ui\UiCase\UiHelper2.h">
      <Filter>ui\uicase\helper</Filter>
    </ClInclude>
    <ClInclude Include="Ui\UiCase\UiReconnect.h">
      <Filter>ui\uicase\reconnect</Filter>
    </ClInclude>
    <ClInclude Include="Ui\UiCase\UiTaskDataFile.h">
      <Filter>ui\uicase\ÈÎÎñ¼ÇÂ¼</Filter>
    </ClInclude>
    <ClInclude Include="Ui\UiCase\UiTaskNote.h">
      <Filter>ui\uicase\ÈÎÎñ¼ÇÂ¼</Filter>
    </ClInclude>
    <ClInclude Include="Ui\UiCase\UiWorldMap.h">
      <Filter>ui\uicase\ÊÀ½çµØÍ¼</Filter>
    </ClInclude>
    <ClInclude Include="Ui\UiCase\UiNewsMessage.h">
      <Filter>ui\uicase\ĐÂÎÅ´°¿Ú</Filter>
    </ClInclude>
    <ClInclude Include="Ui\UiCase\UiNewsMessage2.h">
      <Filter>ui\uicase\ĐÂÎÅ´°¿Ú</Filter>
    </ClInclude>
    <ClInclude Include="Ui\UiCase\UiNewsSysMsg.h">
      <Filter>ui\uicase\ĐÂÎÅ´°¿Ú</Filter>
    </ClInclude>
    <ClInclude Include="Ui\UiCase\UiStrengthRank.h">
      <Filter>ui\uicase\ÅÅĂû½çĂæ</Filter>
    </ClInclude>
    <ClInclude Include="ui\uicase\UiTongAssignBox.h">
      <Filter>ui\uicase\tong</Filter>
    </ClInclude>
    <ClInclude Include="ui\uicase\UiTongCreateSheet.h">
      <Filter>ui\uicase\tong</Filter>
    </ClInclude>
    <ClInclude Include="Ui\UiCase\UiTongGetString.h">
      <Filter>ui\uicase\tong</Filter>
    </ClInclude>
    <ClInclude Include="ui\uicase\UiTongManager.h">
      <Filter>ui\uicase\tong</Filter>
    </ClInclude>
    <ClInclude Include="Ui\UiCase\UiEnchase.h">
      <Filter>ui\uicase\enchase</Filter>
    </ClInclude>
    <ClInclude Include="Ui\UiSoundSetting.h">
      <Filter>ui\sound</Filter>
    </ClInclude>
    <ClInclude Include="Ui\ShortcutKey.h">
      <Filter>ui</Filter>
    </ClInclude>
    <ClInclude Include="Ui\UiBase.h">
      <Filter>ui</Filter>
    </ClInclude>
    <ClInclude Include="Ui\UiShell.h">
      <Filter>ui</Filter>
    </ClInclude>
    <ClInclude Include="Ui\ChatFilter.h">
      <Filter>ÆäËü</Filter>
    </ClInclude>
    <ClInclude Include="Ui\FilterTextLib.h">
      <Filter>ÆäËü</Filter>
    </ClInclude>
    <ClInclude Include="S3Client.h">
      <Filter>ÆäËü</Filter>
    </ClInclude>
    <ClInclude Include="NetConnect\NetConnectAgent.h">
      <Filter>ÍøÂçÁ¬½Ó</Filter>
    </ClInclude>
    <ClInclude Include="NetConnect\NetMsgTargetObject.h">
      <Filter>ÍøÂçÁ¬½Ó</Filter>
    </ClInclude>
    <ClInclude Include="Login\Login.h">
      <Filter>login</Filter>
    </ClInclude>
    <ClInclude Include="Login\LoginDef.h">
      <Filter>login</Filter>
    </ClInclude>
    <ClInclude Include="TextCtrlCmd\TextCtrlCmd.h">
      <Filter>ÎÄ×ÖĂüÁî¿ØÖÆ</Filter>
    </ClInclude>
    <ClInclude Include="ErrorCode.h" />
    <ClInclude Include="resource.h" />
  </ItemGroup>
  <ItemGroup>
    <Library Include="..\..\Lib\debug\engine.lib">
      <Filter>lib\debug</Filter>
    </Library>
    <Library Include="..\..\Lib\debug\CoreClient.lib">
      <Filter>lib\debug</Filter>
    </Library>
    <Library Include="..\..\Lib\release\engine.lib">
      <Filter>lib\release</Filter>
    </Library>
    <Library Include="..\..\Lib\release\CoreClient.lib">
      <Filter>lib\release</Filter>
    </Library>
  </ItemGroup>
  <ItemGroup>
    <ResourceCompile Include="Script1.rc" />
  </ItemGroup>
  <ItemGroup>
    <CustomBuild Include="VLTK.ICO" />
  </ItemGroup>
</Project>