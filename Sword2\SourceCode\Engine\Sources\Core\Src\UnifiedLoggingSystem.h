//---------------------------------------------------------------------------
// Sword2 Unified Logging System (c) 2024
//
// File:	UnifiedLoggingSystem.h
// Date:	2024.07.02
// Code:	AI Assistant
// Desc:	Modern C++ unified logging system with multi-level, multi-format output
//---------------------------------------------------------------------------
#ifndef UNIFIED_LOGGING_SYSTEM_H
#define UNIFIED_LOGGING_SYSTEM_H

#include "ModernCpp.h"
#include <fstream>
#include <sstream>
#include <iomanip>
#include <chrono>
#include <thread>
#include <queue>
#include <mutex>
#include <condition_variable>
#include <atomic>

namespace sword2 {

// 日志级别
enum class LogLevel : uint8_t
{
    Trace = 0,      // 最详细的调试信息
    Debug,          // 调试信息
    Info,           // 一般信息
    Warning,        // 警告信息
    Error,          // 错误信息
    Critical,       // 严重错误
    Off             // 关闭日志
};

// 日志格式类型
enum class LogFormat
{
    Simple,         // 简单格式
    Detailed,       // 详细格式
    JSON,           // JSON格式
    XML,            // XML格式
    Custom          // 自定义格式
};

// 日志输出目标
enum class LogTarget
{
    Console,        // 控制台输出
    File,           // 文件输出
    Network,        // 网络输出
    Database,       // 数据库输出
    Memory          // 内存缓冲区
};

// 日志条目
struct LogEntry
{
    LogLevel level;
    std::string category;
    std::string message;
    std::string file;
    int line;
    std::string function;
    std::thread::id threadId;
    std::chrono::system_clock::time_point timestamp;
    std::unordered_map<std::string, std::string> metadata;
    
    LogEntry(LogLevel lvl, const std::string& cat, const std::string& msg,
             const std::string& f = "", int l = 0, const std::string& func = "")
        : level(lvl), category(cat), message(msg), file(f), line(l), function(func),
          threadId(std::this_thread::get_id()), timestamp(std::chrono::system_clock::now()) {}
};

// 日志格式化器接口
class ILogFormatter
{
public:
    virtual ~ILogFormatter() = default;
    virtual std::string Format(const LogEntry& entry) = 0;
    virtual std::string GetHeader() { return ""; }
    virtual std::string GetFooter() { return ""; }
};

// 简单格式化器
class SimpleFormatter : public ILogFormatter
{
public:
    std::string Format(const LogEntry& entry) override
    {
        auto time_t = std::chrono::system_clock::to_time_t(entry.timestamp);
        auto tm = *std::localtime(&time_t);
        
        std::ostringstream oss;
        oss << std::put_time(&tm, "%Y-%m-%d %H:%M:%S")
            << " [" << LogLevelToString(entry.level) << "]"
            << " [" << entry.category << "]"
            << " " << entry.message;
        
        return oss.str();
    }

private:
    std::string LogLevelToString(LogLevel level)
    {
        switch (level)
        {
        case LogLevel::Trace: return "TRACE";
        case LogLevel::Debug: return "DEBUG";
        case LogLevel::Info: return "INFO";
        case LogLevel::Warning: return "WARN";
        case LogLevel::Error: return "ERROR";
        case LogLevel::Critical: return "CRITICAL";
        default: return "UNKNOWN";
        }
    }
};

// 详细格式化器
class DetailedFormatter : public ILogFormatter
{
public:
    std::string Format(const LogEntry& entry) override
    {
        auto time_t = std::chrono::system_clock::to_time_t(entry.timestamp);
        auto tm = *std::localtime(&time_t);
        auto ms = std::chrono::duration_cast<std::chrono::milliseconds>(
            entry.timestamp.time_since_epoch()) % 1000;
        
        std::ostringstream oss;
        oss << std::put_time(&tm, "%Y-%m-%d %H:%M:%S")
            << "." << std::setfill('0') << std::setw(3) << ms.count()
            << " [" << LogLevelToString(entry.level) << "]"
            << " [" << entry.category << "]"
            << " [Thread:" << entry.threadId << "]";
        
        if (!entry.file.empty())
        {
            oss << " [" << entry.file << ":" << entry.line;
            if (!entry.function.empty())
                oss << " in " << entry.function << "()";
            oss << "]";
        }
        
        oss << " " << entry.message;
        
        // 添加元数据
        for (const auto& meta : entry.metadata)
        {
            oss << " {" << meta.first << "=" << meta.second << "}";
        }
        
        return oss.str();
    }

private:
    std::string LogLevelToString(LogLevel level)
    {
        switch (level)
        {
        case LogLevel::Trace: return "TRACE";
        case LogLevel::Debug: return "DEBUG";
        case LogLevel::Info: return "INFO ";
        case LogLevel::Warning: return "WARN ";
        case LogLevel::Error: return "ERROR";
        case LogLevel::Critical: return "CRIT ";
        default: return "UNKN ";
        }
    }
};

// JSON格式化器
class JSONFormatter : public ILogFormatter
{
public:
    std::string Format(const LogEntry& entry) override
    {
        auto time_t = std::chrono::system_clock::to_time_t(entry.timestamp);
        auto tm = *std::localtime(&time_t);
        auto ms = std::chrono::duration_cast<std::chrono::milliseconds>(
            entry.timestamp.time_since_epoch()) % 1000;
        
        std::ostringstream oss;
        oss << "{"
            << "\"timestamp\":\"" << std::put_time(&tm, "%Y-%m-%dT%H:%M:%S")
            << "." << std::setfill('0') << std::setw(3) << ms.count() << "Z\","
            << "\"level\":\"" << LogLevelToString(entry.level) << "\","
            << "\"category\":\"" << entry.category << "\","
            << "\"message\":\"" << EscapeJSON(entry.message) << "\","
            << "\"thread\":\"" << entry.threadId << "\"";
        
        if (!entry.file.empty())
        {
            oss << ",\"file\":\"" << entry.file << "\","
                << "\"line\":" << entry.line;
            if (!entry.function.empty())
                oss << ",\"function\":\"" << entry.function << "\"";
        }
        
        if (!entry.metadata.empty())
        {
            oss << ",\"metadata\":{";
            bool first = true;
            for (const auto& meta : entry.metadata)
            {
                if (!first) oss << ",";
                oss << "\"" << meta.first << "\":\"" << EscapeJSON(meta.second) << "\"";
                first = false;
            }
            oss << "}";
        }
        
        oss << "}";
        return oss.str();
    }

private:
    std::string LogLevelToString(LogLevel level)
    {
        switch (level)
        {
        case LogLevel::Trace: return "trace";
        case LogLevel::Debug: return "debug";
        case LogLevel::Info: return "info";
        case LogLevel::Warning: return "warning";
        case LogLevel::Error: return "error";
        case LogLevel::Critical: return "critical";
        default: return "unknown";
        }
    }
    
    std::string EscapeJSON(const std::string& str)
    {
        std::string result;
        for (char c : str)
        {
            switch (c)
            {
            case '"': result += "\\\""; break;
            case '\\': result += "\\\\"; break;
            case '\b': result += "\\b"; break;
            case '\f': result += "\\f"; break;
            case '\n': result += "\\n"; break;
            case '\r': result += "\\r"; break;
            case '\t': result += "\\t"; break;
            default: result += c; break;
            }
        }
        return result;
    }
};

// 日志输出器接口
class ILogAppender
{
public:
    virtual ~ILogAppender() = default;
    virtual void Append(const LogEntry& entry) = 0;
    virtual void Flush() {}
    virtual void Close() {}
    
    void SetFormatter(std::unique_ptr<ILogFormatter> formatter)
    {
        m_formatter = std::move(formatter);
    }
    
    void SetMinLevel(LogLevel level) { m_minLevel = level; }
    LogLevel GetMinLevel() const { return m_minLevel; }

protected:
    std::unique_ptr<ILogFormatter> m_formatter;
    LogLevel m_minLevel = LogLevel::Info;
    
    bool ShouldLog(LogLevel level) const
    {
        return level >= m_minLevel;
    }
};

// 控制台输出器
class ConsoleAppender : public ILogAppender
{
public:
    ConsoleAppender()
    {
        m_formatter = std::make_unique<SimpleFormatter>();
    }
    
    void Append(const LogEntry& entry) override
    {
        if (!ShouldLog(entry.level)) return;
        
        std::string formatted = m_formatter->Format(entry);
        
        // 根据日志级别使用不同颜色（Windows控制台）
        SetConsoleColor(entry.level);
        printf("%s\n", formatted.c_str());
        ResetConsoleColor();
    }

private:
    void SetConsoleColor(LogLevel level)
    {
#ifdef _WIN32
        HANDLE hConsole = GetStdHandle(STD_OUTPUT_HANDLE);
        switch (level)
        {
        case LogLevel::Error:
        case LogLevel::Critical:
            SetConsoleTextAttribute(hConsole, FOREGROUND_RED | FOREGROUND_INTENSITY);
            break;
        case LogLevel::Warning:
            SetConsoleTextAttribute(hConsole, FOREGROUND_RED | FOREGROUND_GREEN | FOREGROUND_INTENSITY);
            break;
        case LogLevel::Info:
            SetConsoleTextAttribute(hConsole, FOREGROUND_GREEN | FOREGROUND_INTENSITY);
            break;
        case LogLevel::Debug:
            SetConsoleTextAttribute(hConsole, FOREGROUND_BLUE | FOREGROUND_INTENSITY);
            break;
        default:
            SetConsoleTextAttribute(hConsole, FOREGROUND_RED | FOREGROUND_GREEN | FOREGROUND_BLUE);
            break;
        }
#endif
    }
    
    void ResetConsoleColor()
    {
#ifdef _WIN32
        HANDLE hConsole = GetStdHandle(STD_OUTPUT_HANDLE);
        SetConsoleTextAttribute(hConsole, FOREGROUND_RED | FOREGROUND_GREEN | FOREGROUND_BLUE);
#endif
    }
};

// 文件输出器
class FileAppender : public ILogAppender
{
public:
    explicit FileAppender(const std::string& filename, bool append = true)
        : m_filename(filename), m_maxFileSize(10 * 1024 * 1024), m_maxFiles(10)
    {
        m_formatter = std::make_unique<DetailedFormatter>();
        OpenFile(append);
    }
    
    ~FileAppender()
    {
        Close();
    }
    
    void Append(const LogEntry& entry) override
    {
        if (!ShouldLog(entry.level) || !m_file.is_open()) return;
        
        std::string formatted = m_formatter->Format(entry);
        
        std::lock_guard<std::mutex> lock(m_mutex);
        m_file << formatted << std::endl;
        
        // 检查文件大小并轮转
        if (m_file.tellp() > m_maxFileSize)
        {
            RotateFile();
        }
    }
    
    void Flush() override
    {
        std::lock_guard<std::mutex> lock(m_mutex);
        if (m_file.is_open())
        {
            m_file.flush();
        }
    }
    
    void Close() override
    {
        std::lock_guard<std::mutex> lock(m_mutex);
        if (m_file.is_open())
        {
            m_file.close();
        }
    }
    
    void SetMaxFileSize(size_t size) { m_maxFileSize = size; }
    void SetMaxFiles(int count) { m_maxFiles = count; }

private:
    std::string m_filename;
    std::ofstream m_file;
    std::mutex m_mutex;
    size_t m_maxFileSize;
    int m_maxFiles;
    
    void OpenFile(bool append)
    {
        auto mode = append ? (std::ios::out | std::ios::app) : std::ios::out;
        m_file.open(m_filename, mode);
    }
    
    void RotateFile()
    {
        m_file.close();
        
        // 轮转文件
        for (int i = m_maxFiles - 1; i > 0; --i)
        {
            std::string oldName = m_filename + "." + std::to_string(i);
            std::string newName = m_filename + "." + std::to_string(i + 1);
            
            if (i == m_maxFiles - 1)
            {
                std::remove(newName.c_str());
            }
            std::rename(oldName.c_str(), newName.c_str());
        }
        
        std::string backupName = m_filename + ".1";
        std::rename(m_filename.c_str(), backupName.c_str());
        
        OpenFile(false);
    }
};

// 异步日志管理器
class AsyncLogManager
{
public:
    AsyncLogManager() : m_running(false) {}

    ~AsyncLogManager()
    {
        Stop();
    }

    void Start()
    {
        if (m_running) return;

        m_running = true;
        m_workerThread = std::thread(&AsyncLogManager::ProcessLogs, this);
    }

    void Stop()
    {
        {
            std::lock_guard<std::mutex> lock(m_mutex);
            m_running = false;
        }
        m_condition.notify_all();

        if (m_workerThread.joinable())
        {
            m_workerThread.join();
        }

        // 处理剩余的日志
        ProcessRemainingLogs();
    }

    void AddAppender(std::unique_ptr<ILogAppender> appender)
    {
        std::lock_guard<std::mutex> lock(m_mutex);
        m_appenders.push_back(std::move(appender));
    }

    void Log(const LogEntry& entry)
    {
        std::lock_guard<std::mutex> lock(m_mutex);
        m_logQueue.push(entry);
        m_condition.notify_one();
    }

    void Flush()
    {
        // 等待队列清空
        std::unique_lock<std::mutex> lock(m_mutex);
        m_condition.wait(lock, [this] { return m_logQueue.empty(); });

        // 刷新所有输出器
        for (auto& appender : m_appenders)
        {
            appender->Flush();
        }
    }

private:
    std::vector<std::unique_ptr<ILogAppender>> m_appenders;
    std::queue<LogEntry> m_logQueue;
    std::thread m_workerThread;
    std::mutex m_mutex;
    std::condition_variable m_condition;
    std::atomic<bool> m_running;

    void ProcessLogs()
    {
        while (m_running)
        {
            std::unique_lock<std::mutex> lock(m_mutex);
            m_condition.wait(lock, [this] { return !m_logQueue.empty() || !m_running; });

            while (!m_logQueue.empty())
            {
                LogEntry entry = m_logQueue.front();
                m_logQueue.pop();
                lock.unlock();

                // 发送到所有输出器
                for (auto& appender : m_appenders)
                {
                    try
                    {
                        appender->Append(entry);
                    }
                    catch (const std::exception& e)
                    {
                        // 避免日志系统本身的错误导致程序崩溃
                        fprintf(stderr, "Log appender error: %s\n", e.what());
                    }
                }

                lock.lock();
            }
        }
    }

    void ProcessRemainingLogs()
    {
        while (!m_logQueue.empty())
        {
            LogEntry entry = m_logQueue.front();
            m_logQueue.pop();

            for (auto& appender : m_appenders)
            {
                try
                {
                    appender->Append(entry);
                }
                catch (...)
                {
                    // 忽略清理阶段的错误
                }
            }
        }

        for (auto& appender : m_appenders)
        {
            appender->Flush();
            appender->Close();
        }
    }
};

// 统一日志管理器
class UnifiedLogger : public Singleton<UnifiedLogger>
{
public:
    UnifiedLogger() : m_globalLevel(LogLevel::Info) {}

    ~UnifiedLogger()
    {
        Shutdown();
    }

    void Initialize()
    {
        // 默认添加控制台输出器
        auto consoleAppender = std::make_unique<ConsoleAppender>();
        consoleAppender->SetMinLevel(LogLevel::Info);
        m_asyncManager.AddAppender(std::move(consoleAppender));

        m_asyncManager.Start();
    }

    void Shutdown()
    {
        m_asyncManager.Stop();
    }

    void AddFileAppender(const std::string& filename, LogLevel minLevel = LogLevel::Info)
    {
        auto fileAppender = std::make_unique<FileAppender>(filename);
        fileAppender->SetMinLevel(minLevel);
        m_asyncManager.AddAppender(std::move(fileAppender));
    }

    void AddConsoleAppender(LogLevel minLevel = LogLevel::Info)
    {
        auto consoleAppender = std::make_unique<ConsoleAppender>();
        consoleAppender->SetMinLevel(minLevel);
        m_asyncManager.AddAppender(std::move(consoleAppender));
    }

    void SetGlobalLevel(LogLevel level) { m_globalLevel = level; }
    LogLevel GetGlobalLevel() const { return m_globalLevel; }

    void Log(LogLevel level, const std::string& category, const std::string& message,
             const std::string& file = "", int line = 0, const std::string& function = "")
    {
        if (level < m_globalLevel) return;

        LogEntry entry(level, category, message, file, line, function);
        m_asyncManager.Log(entry);
    }

    void LogWithMetadata(LogLevel level, const std::string& category, const std::string& message,
                        const std::unordered_map<std::string, std::string>& metadata,
                        const std::string& file = "", int line = 0, const std::string& function = "")
    {
        if (level < m_globalLevel) return;

        LogEntry entry(level, category, message, file, line, function);
        entry.metadata = metadata;
        m_asyncManager.Log(entry);
    }

    void Flush()
    {
        m_asyncManager.Flush();
    }

private:
    AsyncLogManager m_asyncManager;
    std::atomic<LogLevel> m_globalLevel;
};

} // namespace sword2

// 全局日志管理器访问
#define LOGGER() sword2::UnifiedLogger::getInstance()

// 便捷的日志宏
#define LOG_TRACE(category, message) \
    LOGGER().Log(sword2::LogLevel::Trace, category, message, __FILE__, __LINE__, __FUNCTION__)

#define LOG_DEBUG(category, message) \
    LOGGER().Log(sword2::LogLevel::Debug, category, message, __FILE__, __LINE__, __FUNCTION__)

#define LOG_INFO(category, message) \
    LOGGER().Log(sword2::LogLevel::Info, category, message, __FILE__, __LINE__, __FUNCTION__)

#define LOG_WARNING(category, message) \
    LOGGER().Log(sword2::LogLevel::Warning, category, message, __FILE__, __LINE__, __FUNCTION__)

#define LOG_ERROR(category, message) \
    LOGGER().Log(sword2::LogLevel::Error, category, message, __FILE__, __LINE__, __FUNCTION__)

#define LOG_CRITICAL(category, message) \
    LOGGER().Log(sword2::LogLevel::Critical, category, message, __FILE__, __LINE__, __FUNCTION__)

// 带格式化的日志宏
#define LOG_INFO_F(category, format, ...) \
    do { \
        char buffer[1024]; \
        snprintf(buffer, sizeof(buffer), format, __VA_ARGS__); \
        LOG_INFO(category, buffer); \
    } while(0)

#define LOG_ERROR_F(category, format, ...) \
    do { \
        char buffer[1024]; \
        snprintf(buffer, sizeof(buffer), format, __VA_ARGS__); \
        LOG_ERROR(category, buffer); \
    } while(0)

// 带元数据的日志宏
#define LOG_WITH_META(level, category, message, metadata) \
    LOGGER().LogWithMetadata(level, category, message, metadata, __FILE__, __LINE__, __FUNCTION__)

#endif // UNIFIED_LOGGING_SYSTEM_H
