//---------------------------------------------------------------------------
// Sword2 Social System Demo (c) 2024
//
// File:	SocialSystemDemo.cpp
// Date:	2024.07.02
// Code:	AI Assistant
// Desc:	Comprehensive demonstration of the social system
//---------------------------------------------------------------------------

#include "KCore.h"
#include "SocialSystem.h"
#include "SocialManager.h"
#include "MailManager.h"
#include "SocialScriptIntegration.h"
#include "PlayerSystem.h"
#include "PlayerManager.h"
#include "LuaScriptEngine.h"
#include "UnifiedLoggingSystem.h"

namespace sword2 {

// 社交系统演示类
class SocialSystemDemo
{
public:
    SocialSystemDemo()
    {
        m_initialized = false;
    }
    
    bool Initialize()
    {
        if (m_initialized) return true;
        
        printf("[SOCIAL_DEMO] Initializing social system demo...\n");
        
        // 初始化日志系统
        LOGGER().Initialize();
        LOGGER().AddConsoleAppender(LogLevel::Debug);
        
        // 启动玩家管理器
        if (!START_PLAYER_SYSTEM())
        {
            printf("[SOCIAL_DEMO] Failed to start player system\n");
            return false;
        }
        
        // 初始化脚本引擎
        if (!INIT_SCRIPT_ENGINE("../../../Server/script"))
        {
            printf("[SOCIAL_DEMO] Failed to initialize script engine\n");
            return false;
        }
        
        // 启动社交管理器
        if (!START_SOCIAL_SYSTEM())
        {
            printf("[SOCIAL_DEMO] Failed to start social system\n");
            return false;
        }
        
        // 初始化社交脚本集成
        if (!INIT_SOCIAL_SCRIPT_INTEGRATION())
        {
            printf("[SOCIAL_DEMO] Failed to initialize social script integration\n");
            return false;
        }
        
        m_initialized = true;
        LOG_INFO("SOCIAL_DEMO", "Social system demo initialized successfully");
        
        return true;
    }
    
    void RunDemo()
    {
        if (!m_initialized)
        {
            printf("[SOCIAL_DEMO] System not initialized\n");
            return;
        }
        
        LOG_INFO("SOCIAL_DEMO", "Starting social system demonstration...");
        
        // 演示各个功能
        DemoPlayerCreation();
        DemoFriendSystem();
        DemoChatSystem();
        DemoMailSystem();
        DemoSocialEvents();
        DemoMessageFiltering();
        DemoSocialScripting();
        DemoSocialStatistics();
        
        LOG_INFO("SOCIAL_DEMO", "Social system demonstration completed");
    }
    
    void Cleanup()
    {
        if (!m_initialized) return;
        
        LOG_INFO("SOCIAL_DEMO", "Cleaning up social system demo...");
        
        // 清理创建的玩家
        for (uint32_t playerId : m_createdPlayers)
        {
            PLAYER_LOGOUT(playerId);
        }
        m_createdPlayers.clear();
        
        // 停止系统
        STOP_SOCIAL_SYSTEM();
        SHUTDOWN_SCRIPT_ENGINE();
        STOP_PLAYER_SYSTEM();
        
        m_initialized = false;
        LOG_INFO("SOCIAL_DEMO", "Social system demo cleanup completed");
    }

private:
    bool m_initialized;
    std::vector<uint32_t> m_createdPlayers;
    
    void DemoPlayerCreation()
    {
        LOG_INFO("SOCIAL_DEMO", "=== Player Creation Demo ===");
        
        // 创建测试玩家
        std::vector<std::pair<std::string, std::string>> playerData = {
            {"张三", "zhangsan"},
            {"李四", "lisi"},
            {"王五", "wangwu"},
            {"赵六", "zhaoliu"},
            {"钱七", "qianqi"}
        };
        
        for (const auto& [name, account] : playerData)
        {
            PlayerCreationParams params(name, account);
            params.series = static_cast<PlayerSeries>(rand() % 5 + 1); // 随机门派
            
            uint32_t playerId = CREATE_PLAYER(params);
            if (playerId != 0)
            {
                m_createdPlayers.push_back(playerId);
                
                // 玩家登录
                uint32_t sessionId;
                PLAYER_LOGIN(playerId, "127.0.0.1", 8000 + playerId, sessionId);
                
                auto player = GET_ONLINE_PLAYER(playerId);
                if (player)
                {
                    player->level = 10 + rand() % 40; // 随机等级10-50
                    player->money = 1000 + rand() % 9000; // 随机金钱1000-10000
                    
                    // 通知社交系统玩家上线
                    NOTIFY_PLAYER_LOGIN(playerId, player->name);
                    
                    LOG_INFO("SOCIAL_DEMO", "Created player: " + player->name + 
                            " (ID: " + std::to_string(playerId) + 
                            ", Level: " + std::to_string(player->level) + ")");
                }
            }
        }
        
        LOG_INFO("SOCIAL_DEMO", "Created " + std::to_string(m_createdPlayers.size()) + " test players");
    }
    
    void DemoFriendSystem()
    {
        LOG_INFO("SOCIAL_DEMO", "=== Friend System Demo ===");
        
        if (m_createdPlayers.size() < 3) return;
        
        uint32_t player1 = m_createdPlayers[0];
        uint32_t player2 = m_createdPlayers[1];
        uint32_t player3 = m_createdPlayers[2];
        
        auto p1 = GET_ONLINE_PLAYER(player1);
        auto p2 = GET_ONLINE_PLAYER(player2);
        auto p3 = GET_ONLINE_PLAYER(player3);
        
        if (!p1 || !p2 || !p3) return;
        
        LOG_INFO("SOCIAL_DEMO", "Testing friend system with players: " + p1->name + ", " + p2->name + ", " + p3->name);
        
        // 添加好友
        SocialResult result1 = ADD_FRIEND(player1, player2, "好友");
        SocialResult result2 = ADD_FRIEND(player1, player3, "同门");
        SocialResult result3 = ADD_FRIEND(player2, player1, "好友");
        
        LOG_INFO("SOCIAL_DEMO", "Add friend results: " + 
                std::to_string(static_cast<int>(result1)) + ", " + 
                std::to_string(static_cast<int>(result2)) + ", " + 
                std::to_string(static_cast<int>(result3)));
        
        // 获取好友列表
        auto friendList1 = GET_FRIEND_LIST(player1, FriendshipType::Friend);
        LOG_INFO("SOCIAL_DEMO", p1->name + " has " + std::to_string(friendList1.size()) + " friends:");
        
        for (const auto& friendInfo : friendList1)
        {
            LOG_INFO("SOCIAL_DEMO", "  " + friendInfo.playerName + 
                    " (" + friendInfo.GetRelationshipDescription() + 
                    ", Group: " + friendInfo.groupName + 
                    ", Status: " + friendInfo.GetStatusDescription() + ")");
        }
        
        // 获取在线好友
        auto onlineFriends = GET_ONLINE_FRIENDS(player1);
        LOG_INFO("SOCIAL_DEMO", p1->name + " has " + std::to_string(onlineFriends.size()) + " online friends");
        
        // 检查好友关系
        bool isFriend = IS_FRIEND(player1, player2);
        LOG_INFO("SOCIAL_DEMO", p1->name + " and " + p2->name + " are friends: " + (isFriend ? "Yes" : "No"));
        
        // 设置好友分组
        SocialResult groupResult = SET_FRIEND_GROUP(player1, player2, "挚友");
        LOG_INFO("SOCIAL_DEMO", "Set friend group result: " + std::to_string(static_cast<int>(groupResult)));
        
        // 添加到黑名单
        if (m_createdPlayers.size() >= 4)
        {
            uint32_t player4 = m_createdPlayers[3];
            auto p4 = GET_ONLINE_PLAYER(player4);
            if (p4)
            {
                SocialResult blacklistResult = ADD_TO_BLACKLIST(player1, player4);
                LOG_INFO("SOCIAL_DEMO", "Add to blacklist result: " + std::to_string(static_cast<int>(blacklistResult)));
                
                bool isBlacklisted = IS_BLACKLISTED(player1, player4);
                LOG_INFO("SOCIAL_DEMO", p4->name + " is blacklisted by " + p1->name + ": " + (isBlacklisted ? "Yes" : "No"));
                
                // 获取黑名单
                auto blacklist = GET_FRIEND_LIST(player1, FriendshipType::Blacklist);
                LOG_INFO("SOCIAL_DEMO", p1->name + " has " + std::to_string(blacklist.size()) + " players in blacklist");
            }
        }
    }
    
    void DemoChatSystem()
    {
        LOG_INFO("SOCIAL_DEMO", "=== Chat System Demo ===");
        
        if (m_createdPlayers.size() < 2) return;
        
        uint32_t player1 = m_createdPlayers[0];
        uint32_t player2 = m_createdPlayers[1];
        
        auto p1 = GET_ONLINE_PLAYER(player1);
        auto p2 = GET_ONLINE_PLAYER(player2);
        
        if (!p1 || !p2) return;
        
        LOG_INFO("SOCIAL_DEMO", "Testing chat system between " + p1->name + " and " + p2->name);
        
        // 发送世界频道消息
        SocialResult worldResult = SEND_MESSAGE(player1, p1->name, "大家好，我是" + p1->name + "！", 
                                               ChatChannelType::World, 0, "");
        LOG_INFO("SOCIAL_DEMO", "World message result: " + std::to_string(static_cast<int>(worldResult)));
        
        // 发送私聊消息
        SocialResult privateResult1 = SEND_MESSAGE(player1, p1->name, "你好，" + p2->name + "！", 
                                                  ChatChannelType::Private, player2, p2->name);
        SocialResult privateResult2 = SEND_MESSAGE(player2, p2->name, "你好，" + p1->name + "！很高兴认识你。", 
                                                  ChatChannelType::Private, player1, p1->name);
        
        LOG_INFO("SOCIAL_DEMO", "Private message results: " + 
                std::to_string(static_cast<int>(privateResult1)) + ", " + 
                std::to_string(static_cast<int>(privateResult2)));
        
        // 获取私聊消息
        auto privateMessages = GET_PRIVATE_MESSAGES(player1, player2, 10);
        LOG_INFO("SOCIAL_DEMO", "Private messages between " + p1->name + " and " + p2->name + ": " + std::to_string(privateMessages.size()));
        
        for (const auto& message : privateMessages)
        {
            LOG_INFO("SOCIAL_DEMO", "  [" + message.GetChannelDescription() + "] " + 
                    message.senderName + ": " + message.content);
        }
        
        // 获取世界频道消息
        auto worldMessages = GET_CHANNEL_MESSAGES(ChatChannelType::World, 5);
        LOG_INFO("SOCIAL_DEMO", "World channel messages: " + std::to_string(worldMessages.size()));
        
        for (const auto& message : worldMessages)
        {
            LOG_INFO("SOCIAL_DEMO", "  [世界] " + message.senderName + ": " + message.content);
        }
        
        // 创建自定义频道
        uint32_t channelId = CREATE_CUSTOM_CHANNEL("测试频道", player1, p1->name, true, "");
        LOG_INFO("SOCIAL_DEMO", "Created custom channel: " + std::to_string(channelId));
        
        if (channelId != 0)
        {
            // 加入频道
            SocialResult joinResult = JOIN_CHANNEL(channelId, player2, "");
            LOG_INFO("SOCIAL_DEMO", "Join channel result: " + std::to_string(static_cast<int>(joinResult)));
            
            // 获取频道列表
            auto channelList = GET_CHANNEL_LIST(true);
            LOG_INFO("SOCIAL_DEMO", "Public channels: " + std::to_string(channelList.size()));
            
            for (const auto& channel : channelList)
            {
                LOG_INFO("SOCIAL_DEMO", "  Channel: " + channel.channelName + 
                        " (Owner: " + channel.ownerName + 
                        ", Members: " + std::to_string(channel.members.size()) + ")");
            }
        }
    }
    
    void DemoMailSystem()
    {
        LOG_INFO("SOCIAL_DEMO", "=== Mail System Demo ===");
        
        if (m_createdPlayers.size() < 2) return;
        
        uint32_t player1 = m_createdPlayers[0];
        uint32_t player2 = m_createdPlayers[1];
        
        auto p1 = GET_ONLINE_PLAYER(player1);
        auto p2 = GET_ONLINE_PLAYER(player2);
        
        if (!p1 || !p2) return;
        
        LOG_INFO("SOCIAL_DEMO", "Testing mail system between " + p1->name + " and " + p2->name);
        
        // 发送普通邮件
        SocialResult mailResult1 = SEND_MAIL(player1, p1->name, player2, p2->name, 
                                            "问候", "你好，这是一封测试邮件。", MailType::Personal);
        LOG_INFO("SOCIAL_DEMO", "Send mail result: " + std::to_string(static_cast<int>(mailResult1)));
        
        // 发送带物品的邮件
        SocialResult itemMailResult = SEND_ITEM_MAIL(player1, p1->name, player2, p2->name,
                                                    "礼物", "送你一些小礼物", 1001, 5, 100);
        LOG_INFO("SOCIAL_DEMO", "Send item mail result: " + std::to_string(static_cast<int>(itemMailResult)));
        
        // 发送系统邮件
        SocialResult systemMailResult = SEND_SYSTEM_MAIL(player2, p2->name, 
                                                        "系统奖励", "恭喜你获得登录奖励！", 2001, 3, 500);
        LOG_INFO("SOCIAL_DEMO", "Send system mail result: " + std::to_string(static_cast<int>(systemMailResult)));
        
        // 获取邮件列表
        auto mailList = GET_MAIL_LIST(player2, MailType::Personal, false);
        LOG_INFO("SOCIAL_DEMO", p2->name + " has " + std::to_string(mailList.size()) + " mails:");
        
        for (const auto& mail : mailList)
        {
            LOG_INFO("SOCIAL_DEMO", "  Mail: " + mail.subject + 
                    " (From: " + mail.senderName + 
                    ", Type: " + mail.GetTypeDescription() + 
                    ", Status: " + mail.GetStatusDescription() + 
                    ", Has Attachment: " + (mail.hasAttachment ? "Yes" : "No") + ")");
            
            // 读取邮件
            if (mail.status == MailStatus::Unread)
            {
                SocialResult readResult = READ_MAIL(player2, mail.mailId);
                LOG_INFO("SOCIAL_DEMO", "    Read mail result: " + std::to_string(static_cast<int>(readResult)));
            }
            
            // 收取附件
            if (mail.hasAttachment)
            {
                SocialResult collectResult = COLLECT_ALL_ATTACHMENTS(player2, mail.mailId);
                LOG_INFO("SOCIAL_DEMO", "    Collect attachments result: " + std::to_string(static_cast<int>(collectResult)));
            }
        }
        
        // 获取未读邮件数量
        uint32_t unreadCount = GET_UNREAD_MAIL_COUNT(player2);
        LOG_INFO("SOCIAL_DEMO", p2->name + " has " + std::to_string(unreadCount) + " unread mails");
        
        // 群发邮件
        std::vector<uint32_t> receivers;
        for (size_t i = 0; i < std::min(m_createdPlayers.size(), size_t(3)); ++i)
        {
            receivers.push_back(m_createdPlayers[i]);
        }
        
        SocialResult broadcastResult = SEND_BROADCAST_MAIL(receivers, "系统公告", "这是一条系统公告", MailType::System);
        LOG_INFO("SOCIAL_DEMO", "Broadcast mail result: " + std::to_string(static_cast<int>(broadcastResult)));
    }

    void DemoSocialEvents()
    {
        LOG_INFO("SOCIAL_DEMO", "=== Social Events Demo ===");

        // 注册自定义事件处理器
        auto& eventHandler = SOCIAL_EVENT_HANDLER();

        eventHandler.RegisterEventHandler(SocialEvent::FriendAdded, "demo_friend_added",
            [](const SocialEventHandler::SocialEventData& data) {
                LOG_INFO("SOCIAL_EVENT", "Custom handler - Friend added: Player " + std::to_string(data.playerId) +
                        " added " + std::to_string(data.targetId) + " as friend");
            });

        eventHandler.RegisterEventHandler(SocialEvent::MessageReceived, "demo_message_received",
            [](const SocialEventHandler::SocialEventData& data) {
                LOG_INFO("SOCIAL_EVENT", "Custom handler - Message received: Player " + std::to_string(data.targetId) +
                        " received message from " + std::to_string(data.playerId));
            });

        eventHandler.RegisterEventHandler(SocialEvent::MailReceived, "demo_mail_received",
            [](const SocialEventHandler::SocialEventData& data) {
                LOG_INFO("SOCIAL_EVENT", "Custom handler - Mail received: Player " + std::to_string(data.targetId) +
                        " received mail " + std::to_string(data.mailId) + " from " + std::to_string(data.playerId));
            });

        eventHandler.RegisterEventHandler(SocialEvent::FriendOnline, "demo_friend_online",
            [](const SocialEventHandler::SocialEventData& data) {
                LOG_INFO("SOCIAL_EVENT", "Custom handler - Friend online: Player " + std::to_string(data.playerId) + " came online");
            });

        // 触发一些测试事件
        if (m_createdPlayers.size() >= 2)
        {
            uint32_t player1 = m_createdPlayers[0];
            uint32_t player2 = m_createdPlayers[1];

            TRIGGER_SOCIAL_EVENT(SocialEvent::FriendAdded, player1, player2);
            TRIGGER_SOCIAL_EVENT(SocialEvent::MessageReceived, player1, player2);
            TRIGGER_SOCIAL_EVENT(SocialEvent::FriendOnline, player1, 0);
        }

        // 获取事件历史
        if (!m_createdPlayers.empty())
        {
            uint32_t playerId = m_createdPlayers[0];
            auto eventHistory = eventHandler.GetEventHistory(playerId);

            LOG_INFO("SOCIAL_DEMO", "Event history for player " + std::to_string(playerId) + ": " +
                    std::to_string(eventHistory.size()) + " events");

            for (const auto& event : eventHistory)
            {
                std::string eventName;
                switch (event.event)
                {
                case SocialEvent::FriendAdded: eventName = "Friend Added"; break;
                case SocialEvent::FriendRemoved: eventName = "Friend Removed"; break;
                case SocialEvent::FriendOnline: eventName = "Friend Online"; break;
                case SocialEvent::FriendOffline: eventName = "Friend Offline"; break;
                case SocialEvent::MessageSent: eventName = "Message Sent"; break;
                case SocialEvent::MessageReceived: eventName = "Message Received"; break;
                case SocialEvent::MailSent: eventName = "Mail Sent"; break;
                case SocialEvent::MailReceived: eventName = "Mail Received"; break;
                default: eventName = "Other"; break;
                }

                LOG_INFO("SOCIAL_DEMO", "  Event: " + eventName +
                        " - Player: " + std::to_string(event.playerId) +
                        " - Target: " + std::to_string(event.targetId));
            }
        }
    }

    void DemoMessageFiltering()
    {
        LOG_INFO("SOCIAL_DEMO", "=== Message Filtering Demo ===");

        auto& filterSystem = MESSAGE_FILTER_SYSTEM();

        // 添加过滤词
        filterSystem.AddFilterWord("垃圾");
        filterSystem.AddFilterWord("广告");
        filterSystem.AddFilterWord("外挂");
        filterSystem.AddFilterWord("代练");

        LOG_INFO("SOCIAL_DEMO", "Added filter words");

        // 测试消息过滤
        std::vector<std::string> testMessages = {
            "大家好，我是新手玩家",
            "有人要买垃圾装备吗？",
            "专业代练，价格便宜",
            "这个游戏真好玩！",
            "出售外挂软件，私聊",
            "求组队打副本",
            "广告：最强装备，点击购买"
        };

        LOG_INFO("SOCIAL_DEMO", "Testing message filtering:");

        for (const auto& message : testMessages)
        {
            std::string filtered = FILTER_MESSAGE(message);
            bool isSpam = IS_SPAM_MESSAGE(message);

            LOG_INFO("SOCIAL_DEMO", "  Original: \"" + message + "\"");
            LOG_INFO("SOCIAL_DEMO", "  Filtered: \"" + filtered + "\"");
            LOG_INFO("SOCIAL_DEMO", "  Is Spam: " + (isSpam ? "Yes" : "No"));
            LOG_INFO("SOCIAL_DEMO", "");
        }

        // 获取过滤词列表
        auto filterWords = filterSystem.GetFilterWords();
        LOG_INFO("SOCIAL_DEMO", "Current filter words: " + std::to_string(filterWords.size()));
        for (const auto& word : filterWords)
        {
            LOG_INFO("SOCIAL_DEMO", "  " + word);
        }
    }

    void DemoSocialScripting()
    {
        LOG_INFO("SOCIAL_DEMO", "=== Social Scripting Demo ===");

        // 创建测试脚本
        std::string testScript = R"(
            function social_test_function(playerId, targetId)
                WriteLog("Social script function called!")
                WriteLog("Player ID: " .. playerId)
                WriteLog("Target ID: " .. targetId)

                local friendCount = GetFriendCount(playerId)
                local onlineFriends = GetOnlineFriendCount(playerId)

                WriteLog("Friend count: " .. friendCount)
                WriteLog("Online friends: " .. onlineFriends)

                return true
            end

            function on_social_friend_added(playerId, targetId)
                WriteLog("Friend added event: " .. playerId .. " -> " .. targetId)

                local playerName = GetPlayerName(playerId)
                local targetName = GetPlayerName(targetId)

                WriteLog("Player " .. playerName .. " added " .. targetName .. " as friend")

                -- 发送欢迎消息
                local welcomeMsg = "欢迎 " .. targetName .. " 成为我的好友！"
                SendPrivateMessage(playerId, targetId, welcomeMsg)

                return true
            end

            function on_social_message_received(playerId, targetId)
                WriteLog("Message received event: " .. targetId .. " from " .. playerId)

                local playerLevel = GetPlayerLevel(playerId)
                local targetLevel = GetPlayerLevel(targetId)

                WriteLog("Sender level: " .. playerLevel)
                WriteLog("Receiver level: " .. targetLevel)

                -- 如果是新手玩家发消息给高级玩家，给予提示
                if playerLevel <= 10 and targetLevel >= 30 then
                    WriteLog("Newbie player messaging veteran player")
                    local helpMsg = "我看你是新手玩家，有什么问题可以问我！"
                    SendPrivateMessage(targetId, playerId, helpMsg)
                end

                return true
            end

            function check_social_permission(playerId, action, targetId)
                WriteLog("Checking social permission: " .. action)

                local playerLevel = GetPlayerLevel(playerId)
                local playerReputation = GetPlayerReputation(playerId)

                -- 等级限制
                if action == "send_mail" and playerLevel < 5 then
                    WriteLog("Player level too low to send mail")
                    return false
                end

                if action == "create_channel" and playerLevel < 20 then
                    WriteLog("Player level too low to create channel")
                    return false
                end

                -- 声望限制
                if playerReputation < -100 then
                    WriteLog("Player reputation too low")
                    return false
                end

                -- 检查是否在黑名单
                if targetId > 0 and IsBlacklisted(targetId, playerId) then
                    WriteLog("Player is blacklisted by target")
                    return false
                end

                return true
            end

            function calculate_intimacy_bonus(playerId, targetId, baseIntimacy)
                WriteLog("Calculating intimacy bonus")

                local playerLevel = GetPlayerLevel(playerId)
                local targetLevel = GetPlayerLevel(targetId)
                local levelDiff = math.abs(playerLevel - targetLevel)

                -- 等级相近的玩家亲密度增长更快
                local bonus = baseIntimacy
                if levelDiff <= 5 then
                    bonus = bonus * 1.5
                elseif levelDiff <= 10 then
                    bonus = bonus * 1.2
                end

                -- 同门派亲密度增长更快
                local playerSeries = GetPlayerSeries(playerId)
                local targetSeries = GetPlayerSeries(targetId)
                if playerSeries == targetSeries then
                    bonus = bonus * 1.3
                end

                WriteLog("Final intimacy bonus: " .. bonus)
                return math.floor(bonus)
            end

            function filter_custom_message(message, playerId)
                WriteLog("Custom message filtering")

                local playerLevel = GetPlayerLevel(playerId)

                -- 新手玩家的消息更严格过滤
                if playerLevel <= 10 then
                    -- 检查是否包含数字（可能是广告）
                    if string.match(message, "%d+") then
                        WriteLog("Newbie player message contains numbers, might be spam")
                        return string.gsub(message, "%d", "*")
                    end
                end

                -- 检查重复字符
                local repeatedChar = string.match(message, "(.)\1\1\1+")
                if repeatedChar then
                    WriteLog("Message contains repeated characters")
                    return string.gsub(message, repeatedChar .. "+", repeatedChar .. repeatedChar)
                end

                return message
            end
        )";

        // 执行测试脚本
        auto context = std::make_unique<LuaScriptContext>();
        context->Initialize();

        ScriptResult result = context->ExecuteString(testScript);
        if (result == ScriptResult::Success)
        {
            LOG_INFO("SOCIAL_DEMO", "Successfully loaded social test script");

            if (m_createdPlayers.size() >= 2)
            {
                uint32_t playerId = m_createdPlayers[0];
                uint32_t targetId = m_createdPlayers[1];

                // 测试脚本函数调用
                std::vector<LuaValue> args = {
                    LuaValue(static_cast<double>(playerId)),
                    LuaValue(static_cast<double>(targetId))
                };

                result = context->CallFunction("social_test_function", args);
                if (result == ScriptResult::Success)
                {
                    LOG_INFO("SOCIAL_DEMO", "Successfully executed social script function");
                }

                // 测试事件处理函数
                context->CallFunction("on_social_friend_added", args);
                context->CallFunction("on_social_message_received", args);

                // 测试权限检查函数
                std::vector<LuaValue> permissionArgs = {
                    LuaValue(static_cast<double>(playerId)),
                    LuaValue("send_mail"),
                    LuaValue(static_cast<double>(targetId))
                };
                context->CallFunction("check_social_permission", permissionArgs);

                // 测试亲密度计算
                std::vector<LuaValue> intimacyArgs = {
                    LuaValue(static_cast<double>(playerId)),
                    LuaValue(static_cast<double>(targetId)),
                    LuaValue(static_cast<double>(10))
                };
                context->CallFunction("calculate_intimacy_bonus", intimacyArgs);

                // 测试消息过滤
                std::vector<LuaValue> filterArgs = {
                    LuaValue("这是一条测试消息1234"),
                    LuaValue(static_cast<double>(playerId))
                };
                context->CallFunction("filter_custom_message", filterArgs);
            }
        }
        else
        {
            LOG_ERROR("SOCIAL_DEMO", "Failed to load social test script");
        }
    }

    void DemoSocialStatistics()
    {
        LOG_INFO("SOCIAL_DEMO", "=== Social Statistics Demo ===");

        // 显示所有玩家的社交统计
        for (uint32_t playerId : m_createdPlayers)
        {
            auto player = GET_ONLINE_PLAYER(playerId);
            if (!player) continue;

            // 获取好友统计
            auto friendList = GET_FRIEND_LIST(playerId, FriendshipType::Friend);
            auto onlineFriends = GET_ONLINE_FRIENDS(playerId);
            auto blacklist = GET_FRIEND_LIST(playerId, FriendshipType::Blacklist);

            // 获取邮件统计
            uint32_t totalMails = GET_MAIL_LIST(playerId, MailType::Personal, false).size();
            uint32_t unreadMails = GET_UNREAD_MAIL_COUNT(playerId);

            LOG_INFO("SOCIAL_DEMO", "Player " + player->name + " (" + std::to_string(playerId) + ") social statistics:");
            LOG_INFO("SOCIAL_DEMO", "  Level: " + std::to_string(player->level));
            LOG_INFO("SOCIAL_DEMO", "  Series: " + std::to_string(static_cast<int>(player->series)));
            LOG_INFO("SOCIAL_DEMO", "  Total friends: " + std::to_string(friendList.size()));
            LOG_INFO("SOCIAL_DEMO", "  Online friends: " + std::to_string(onlineFriends.size()));
            LOG_INFO("SOCIAL_DEMO", "  Blacklisted players: " + std::to_string(blacklist.size()));
            LOG_INFO("SOCIAL_DEMO", "  Total mails: " + std::to_string(totalMails));
            LOG_INFO("SOCIAL_DEMO", "  Unread mails: " + std::to_string(unreadMails));

            // 显示好友详情
            if (!friendList.empty())
            {
                LOG_INFO("SOCIAL_DEMO", "  Friend details:");
                for (const auto& friendInfo : friendList)
                {
                    LOG_INFO("SOCIAL_DEMO", "    " + friendInfo.playerName +
                            " (Level: " + std::to_string(friendInfo.level) +
                            ", Status: " + friendInfo.GetStatusDescription() +
                            ", Intimacy: " + std::to_string(friendInfo.intimacy) +
                            ", Group: " + friendInfo.groupName + ")");
                }
            }

            LOG_INFO("SOCIAL_DEMO", "");
        }

        // 计算总体统计
        uint32_t totalFriendships = 0;
        uint32_t totalMessages = 0;
        uint32_t totalMails = 0;

        for (uint32_t playerId : m_createdPlayers)
        {
            auto friendList = GET_FRIEND_LIST(playerId, FriendshipType::Friend);
            totalFriendships += friendList.size();

            auto mailList = GET_MAIL_LIST(playerId, MailType::Personal, false);
            totalMails += mailList.size();
        }

        LOG_INFO("SOCIAL_DEMO", "Overall social statistics:");
        LOG_INFO("SOCIAL_DEMO", "  Total players: " + std::to_string(m_createdPlayers.size()));
        LOG_INFO("SOCIAL_DEMO", "  Total friendships: " + std::to_string(totalFriendships));
        LOG_INFO("SOCIAL_DEMO", "  Average friends per player: " +
                std::to_string(m_createdPlayers.empty() ? 0 : totalFriendships / m_createdPlayers.size()));
        LOG_INFO("SOCIAL_DEMO", "  Total mails: " + std::to_string(totalMails));
        LOG_INFO("SOCIAL_DEMO", "  Average mails per player: " +
                std::to_string(m_createdPlayers.empty() ? 0 : totalMails / m_createdPlayers.size()));

        // 显示最受欢迎的玩家
        uint32_t mostPopularPlayer = 0;
        uint32_t maxFriends = 0;

        for (uint32_t playerId : m_createdPlayers)
        {
            auto friendList = GET_FRIEND_LIST(playerId, FriendshipType::Friend);
            if (friendList.size() > maxFriends)
            {
                maxFriends = friendList.size();
                mostPopularPlayer = playerId;
            }
        }

        if (mostPopularPlayer != 0)
        {
            auto popularPlayer = GET_ONLINE_PLAYER(mostPopularPlayer);
            if (popularPlayer)
            {
                LOG_INFO("SOCIAL_DEMO", "Most popular player: " + popularPlayer->name +
                        " with " + std::to_string(maxFriends) + " friends");
            }
        }
    }
};

} // namespace sword2

// 全局社交系统演示实例
sword2::SocialSystemDemo g_SocialDemo;

// 初始化社交系统
bool InitializeSocialSystem()
{
    return g_SocialDemo.Initialize();
}

// 运行社交系统演示
void RunSocialSystemDemo()
{
    g_SocialDemo.RunDemo();
}

// 清理社交系统
void CleanupSocialSystem()
{
    g_SocialDemo.Cleanup();
}
