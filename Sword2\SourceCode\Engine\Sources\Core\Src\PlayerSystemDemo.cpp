//---------------------------------------------------------------------------
// Sword2 Player System Demo (c) 2024
//
// File:	PlayerSystemDemo.cpp
// Date:	2024.07.02
// Code:	AI Assistant
// Desc:	Comprehensive demonstration of the player system
//---------------------------------------------------------------------------

#include "KCore.h"
#include "PlayerSystem.h"
#include "PlayerManager.h"
#include "PlayerDataPersistence.h"
#include "GameDataSystem.h"
#include "UnifiedLoggingSystem.h"
#include "BusinessMonitor.h"

namespace sword2 {

// 玩家系统演示类
class PlayerSystemDemo
{
public:
    PlayerSystemDemo()
    {
        m_initialized = false;
    }
    
    bool Initialize()
    {
        if (m_initialized) return true;
        
        printf("[PLAYER_DEMO] Initializing player system demo...\n");
        
        // 初始化日志系统
        LOGGER().Initialize();
        LOGGER().AddConsoleAppender(LogLevel::Debug);
        
        // 启动业务监控
        START_BUSINESS_MONITORING();
        
        // 启动玩家管理器
        if (!START_PLAYER_SYSTEM())
        {
            printf("[PLAYER_DEMO] Failed to start player system\n");
            return false;
        }
        
        // 启动玩家数据持久化
        if (!START_PLAYER_PERSISTENCE("demo_playerdata"))
        {
            printf("[PLAYER_DEMO] Failed to start player persistence\n");
            return false;
        }
        
        // 设置自动保存
        PLAYER_PERSISTENCE().SetAutoSave(true, std::chrono::seconds(30));
        
        m_initialized = true;
        LOG_INFO("PLAYER_DEMO", "Player system demo initialized successfully");
        
        return true;
    }
    
    void RunDemo()
    {
        if (!m_initialized)
        {
            printf("[PLAYER_DEMO] System not initialized\n");
            return;
        }
        
        LOG_INFO("PLAYER_DEMO", "Starting player system demonstration...");
        
        // 演示各个功能
        DemoPlayerCreation();
        DemoPlayerLogin();
        DemoPlayerAttributes();
        DemoPlayerEquipment();
        DemoPlayerPersistence();
        DemoPlayerManagement();
        DemoPlayerStatistics();
        
        LOG_INFO("PLAYER_DEMO", "Player system demonstration completed");
    }
    
    void Cleanup()
    {
        if (!m_initialized) return;
        
        LOG_INFO("PLAYER_DEMO", "Cleaning up player system demo...");
        
        // 登出所有玩家
        auto onlinePlayers = PLAYER_MANAGER().GetOnlinePlayerList();
        for (const auto& player : onlinePlayers)
        {
            PLAYER_LOGOUT(player->playerId);
        }
        
        // 停止系统
        STOP_PLAYER_PERSISTENCE();
        STOP_PLAYER_SYSTEM();
        STOP_BUSINESS_MONITORING();
        
        m_initialized = false;
        LOG_INFO("PLAYER_DEMO", "Player system demo cleanup completed");
    }

private:
    bool m_initialized;
    std::vector<uint32_t> m_demoPlayerIds;
    
    void DemoPlayerCreation()
    {
        LOG_INFO("PLAYER_DEMO", "=== Player Creation Demo ===");
        
        // 创建不同门派的玩家
        std::vector<PlayerCreationParams> playerParams = {
            {"张三丰", "account001", PlayerGender::Male, PlayerSeries::Wudang},
            {"小龙女", "account002", PlayerGender::Female, PlayerSeries::Emei},
            {"乔峰", "account003", PlayerGender::Male, PlayerSeries::Beggar},
            {"黄蓉", "account004", PlayerGender::Female, PlayerSeries::Tangmen},
            {"令狐冲", "account005", PlayerGender::Male, PlayerSeries::Shaolin}
        };
        
        for (auto& params : playerParams)
        {
            params.startMapId = 100; // 新手村
            params.startX = 1000 + rand() % 100;
            params.startY = 1000 + rand() % 100;
            
            uint32_t playerId = CREATE_PLAYER(params);
            if (playerId != 0)
            {
                m_demoPlayerIds.push_back(playerId);
                LOG_INFO("PLAYER_DEMO", "Created player: " + params.playerName + 
                        " (ID: " + std::to_string(playerId) + ", Series: " + 
                        GetSeriesName(params.series) + ")");
            }
            else
            {
                LOG_ERROR("PLAYER_DEMO", "Failed to create player: " + params.playerName);
            }
        }
        
        LOG_INFO("PLAYER_DEMO", "Created " + std::to_string(m_demoPlayerIds.size()) + " demo players");
    }
    
    void DemoPlayerLogin()
    {
        LOG_INFO("PLAYER_DEMO", "=== Player Login Demo ===");
        
        // 模拟玩家登录
        for (size_t i = 0; i < m_demoPlayerIds.size(); ++i)
        {
            uint32_t playerId = m_demoPlayerIds[i];
            std::string ipAddress = "192.168.1." + std::to_string(100 + i);
            uint32_t connectionId = 1000 + i;
            uint32_t sessionId = 0;
            
            LoginResult result = PLAYER_LOGIN(playerId, ipAddress, connectionId, sessionId);
            
            if (result == LoginResult::Success)
            {
                auto player = GET_ONLINE_PLAYER(playerId);
                if (player)
                {
                    LOG_INFO("PLAYER_DEMO", "Player logged in: " + player->playerName + 
                            " (Session: " + std::to_string(sessionId) + ")");
                    
                    // 更新玩家状态为游戏中
                    player->status = PlayerStatus::InGame;
                }
            }
            else
            {
                LOG_ERROR("PLAYER_DEMO", "Login failed for player " + std::to_string(playerId) + 
                         ", result: " + std::to_string(static_cast<int>(result)));
            }
        }
        
        LOG_INFO("PLAYER_DEMO", "Online players: " + std::to_string(GET_ONLINE_PLAYER_COUNT()));
    }
    
    void DemoPlayerAttributes()
    {
        LOG_INFO("PLAYER_DEMO", "=== Player Attributes Demo ===");
        
        if (m_demoPlayerIds.empty()) return;
        
        uint32_t playerId = m_demoPlayerIds[0];
        auto player = GET_ONLINE_PLAYER(playerId);
        if (!player) return;
        
        LOG_INFO("PLAYER_DEMO", "Player: " + player->playerName);
        LOG_INFO("PLAYER_DEMO", "  Level: " + std::to_string(player->level));
        LOG_INFO("PLAYER_DEMO", "  Experience: " + std::to_string(player->experience) + 
                "/" + std::to_string(player->nextLevelExp));
        LOG_INFO("PLAYER_DEMO", "  Life: " + std::to_string(player->attributes.currentLife) + 
                "/" + std::to_string(player->attributes.maxLife));
        LOG_INFO("PLAYER_DEMO", "  Mana: " + std::to_string(player->attributes.currentMana) + 
                "/" + std::to_string(player->attributes.maxMana));
        LOG_INFO("PLAYER_DEMO", "  Attack: " + std::to_string(player->attributes.minAttack) + 
                "-" + std::to_string(player->attributes.maxAttack));
        LOG_INFO("PLAYER_DEMO", "  Defense: " + std::to_string(player->attributes.defense));
        
        // 演示升级
        LOG_INFO("PLAYER_DEMO", "Adding experience to trigger level up...");
        bool leveledUp = player->AddExperience(500);
        if (leveledUp)
        {
            LOG_INFO("PLAYER_DEMO", "Player leveled up! New level: " + std::to_string(player->level));
            LOG_INFO("PLAYER_DEMO", "  New Life: " + std::to_string(player->attributes.maxLife));
            LOG_INFO("PLAYER_DEMO", "  New Attack: " + std::to_string(player->attributes.minAttack) + 
                    "-" + std::to_string(player->attributes.maxAttack));
        }
        
        // 演示属性修改
        player->attributes.strength += 5;
        player->attributes.agility += 3;
        player->RecalculateAttributes();
        
        LOG_INFO("PLAYER_DEMO", "After attribute boost:");
        LOG_INFO("PLAYER_DEMO", "  Strength: " + std::to_string(player->attributes.strength));
        LOG_INFO("PLAYER_DEMO", "  Agility: " + std::to_string(player->attributes.agility));
        LOG_INFO("PLAYER_DEMO", "  New Attack: " + std::to_string(player->attributes.minAttack) + 
                "-" + std::to_string(player->attributes.maxAttack));
    }
    
    void DemoPlayerEquipment()
    {
        LOG_INFO("PLAYER_DEMO", "=== Player Equipment Demo ===");
        
        if (m_demoPlayerIds.empty()) return;
        
        uint32_t playerId = m_demoPlayerIds[0];
        auto player = GET_ONLINE_PLAYER(playerId);
        if (!player) return;
        
        // 创建一些装备
        auto weapon = std::make_unique<EquipmentItem>(1001, "青钢剑", EquipSlot::Weapon);
        weapon->bonusAttributes.minAttack = 10;
        weapon->bonusAttributes.maxAttack = 15;
        weapon->durability = 100;
        weapon->maxDurability = 100;
        
        auto armor = std::make_unique<EquipmentItem>(2001, "布衣", EquipSlot::Armor);
        armor->bonusAttributes.defense = 5;
        armor->bonusAttributes.maxLife = 20;
        
        LOG_INFO("PLAYER_DEMO", "Before equipment:");
        LOG_INFO("PLAYER_DEMO", "  Attack: " + std::to_string(player->attributes.minAttack) + 
                "-" + std::to_string(player->attributes.maxAttack));
        LOG_INFO("PLAYER_DEMO", "  Defense: " + std::to_string(player->attributes.defense));
        
        // 装备物品
        if (player->EquipItem(std::move(weapon)))
        {
            LOG_INFO("PLAYER_DEMO", "Equipped weapon: 青钢剑");
        }
        
        if (player->EquipItem(std::move(armor)))
        {
            LOG_INFO("PLAYER_DEMO", "Equipped armor: 布衣");
        }
        
        LOG_INFO("PLAYER_DEMO", "After equipment:");
        LOG_INFO("PLAYER_DEMO", "  Attack: " + std::to_string(player->attributes.minAttack) + 
                "-" + std::to_string(player->attributes.maxAttack));
        LOG_INFO("PLAYER_DEMO", "  Defense: " + std::to_string(player->attributes.defense));
        LOG_INFO("PLAYER_DEMO", "  Life: " + std::to_string(player->attributes.maxLife));
        
        // 检查装备
        const auto* equippedWeapon = player->GetEquipment(EquipSlot::Weapon);
        if (equippedWeapon)
        {
            LOG_INFO("PLAYER_DEMO", "Equipped weapon: " + equippedWeapon->itemName + 
                    " (Durability: " + std::to_string(equippedWeapon->durability) + 
                    "/" + std::to_string(equippedWeapon->maxDurability) + ")");
        }
    }
    
    void DemoPlayerPersistence()
    {
        LOG_INFO("PLAYER_DEMO", "=== Player Persistence Demo ===");
        
        if (m_demoPlayerIds.empty()) return;
        
        // 保存所有玩家数据
        for (uint32_t playerId : m_demoPlayerIds)
        {
            auto player = GET_PLAYER(playerId);
            if (player)
            {
                if (SAVE_PLAYER(*player))
                {
                    LOG_INFO("PLAYER_DEMO", "Saved player: " + player->playerName);
                }
                else
                {
                    LOG_ERROR("PLAYER_DEMO", "Failed to save player: " + player->playerName);
                }
            }
        }
        
        // 演示加载玩家数据
        if (!m_demoPlayerIds.empty())
        {
            uint32_t playerId = m_demoPlayerIds[0];
            Player loadedPlayer;
            
            if (LOAD_PLAYER(playerId, loadedPlayer))
            {
                LOG_INFO("PLAYER_DEMO", "Loaded player from file:");
                LOG_INFO("PLAYER_DEMO", "  Name: " + loadedPlayer.playerName);
                LOG_INFO("PLAYER_DEMO", "  Level: " + std::to_string(loadedPlayer.level));
                LOG_INFO("PLAYER_DEMO", "  Experience: " + std::to_string(loadedPlayer.experience));
                LOG_INFO("PLAYER_DEMO", "  Money: " + std::to_string(loadedPlayer.money));
            }
            else
            {
                LOG_ERROR("PLAYER_DEMO", "Failed to load player: " + std::to_string(playerId));
            }
        }
        
        // 显示存储统计
        auto stats = PLAYER_PERSISTENCE().GetStorageStatistics();
        LOG_INFO("PLAYER_DEMO", "Storage statistics:");
        LOG_INFO("PLAYER_DEMO", "  Total players: " + std::to_string(stats.totalPlayers));
        LOG_INFO("PLAYER_DEMO", "  Total file size: " + std::to_string(stats.totalFileSize) + " bytes");
        LOG_INFO("PLAYER_DEMO", "  Data directory: " + stats.dataDirectory);
    }
    
    void DemoPlayerManagement()
    {
        LOG_INFO("PLAYER_DEMO", "=== Player Management Demo ===");
        
        // 演示玩家查找
        if (!m_demoPlayerIds.empty())
        {
            uint32_t playerId = m_demoPlayerIds[0];
            auto player = GET_ONLINE_PLAYER(playerId);
            if (player)
            {
                // 移动玩家
                player->MoveTo(1100, 1200);
                LOG_INFO("PLAYER_DEMO", "Moved player " + player->playerName + 
                        " to (" + std::to_string(player->position.x) + 
                        ", " + std::to_string(player->position.y) + ")");
                
                // 传送玩家
                player->TeleportTo(101, 2000, 2000);
                LOG_INFO("PLAYER_DEMO", "Teleported player " + player->playerName + 
                        " to map " + std::to_string(player->position.mapId));
            }
        }
        
        // 演示范围查询
        PlayerPosition center(101, 2000, 2000);
        auto nearbyPlayers = PLAYER_MANAGER().GetPlayersInRange(center, 100.0);
        LOG_INFO("PLAYER_DEMO", "Found " + std::to_string(nearbyPlayers.size()) + 
                " players within 100 units of center");
        
        // 演示地图查询
        auto playersInMap = PLAYER_MANAGER().GetPlayersInMap(101);
        LOG_INFO("PLAYER_DEMO", "Found " + std::to_string(playersInMap.size()) + 
                " players in map 101");
        
        // 演示广播消息
        BROADCAST_MESSAGE("Welcome to Sword2 demo server!");
    }
    
    void DemoPlayerStatistics()
    {
        LOG_INFO("PLAYER_DEMO", "=== Player Statistics Demo ===");
        
        auto stats = PLAYER_MANAGER().GetPlayerStatistics();
        
        LOG_INFO("PLAYER_DEMO", "Player statistics:");
        LOG_INFO("PLAYER_DEMO", "  Total players: " + std::to_string(stats.totalPlayers));
        LOG_INFO("PLAYER_DEMO", "  Online players: " + std::to_string(stats.onlinePlayers));
        LOG_INFO("PLAYER_DEMO", "  Active sessions: " + std::to_string(stats.activeSessions));
        
        LOG_INFO("PLAYER_DEMO", "Players by series:");
        for (const auto& [series, count] : stats.playersBySeries)
        {
            LOG_INFO("PLAYER_DEMO", "  " + GetSeriesName(series) + ": " + std::to_string(count));
        }
        
        LOG_INFO("PLAYER_DEMO", "Players by level range:");
        for (const auto& [levelRange, count] : stats.playersPerLevel)
        {
            LOG_INFO("PLAYER_DEMO", "  Level " + std::to_string(levelRange) + "-" + 
                    std::to_string(levelRange + 9) + ": " + std::to_string(count));
        }
        
        LOG_INFO("PLAYER_DEMO", "Players by map:");
        for (const auto& [mapId, count] : stats.playersPerMap)
        {
            LOG_INFO("PLAYER_DEMO", "  Map " + std::to_string(mapId) + ": " + std::to_string(count));
        }
    }
    
    std::string GetSeriesName(PlayerSeries series)
    {
        switch (series)
        {
        case PlayerSeries::Shaolin: return "少林";
        case PlayerSeries::Wudang: return "武当";
        case PlayerSeries::Emei: return "峨眉";
        case PlayerSeries::Beggar: return "丐帮";
        case PlayerSeries::Tangmen: return "唐门";
        case PlayerSeries::Wudu: return "五毒";
        case PlayerSeries::Kunlun: return "昆仑";
        case PlayerSeries::Mingjiao: return "明教";
        case PlayerSeries::Cuiyan: return "翠烟";
        case PlayerSeries::Cangjian: return "藏剑";
        default: return "无门派";
        }
    }
};

} // namespace sword2

// 全局玩家系统演示实例
sword2::PlayerSystemDemo g_PlayerDemo;

// 初始化玩家系统
bool InitializePlayerSystem()
{
    return g_PlayerDemo.Initialize();
}

// 运行玩家系统演示
void RunPlayerSystemDemo()
{
    g_PlayerDemo.RunDemo();
}

// 清理玩家系统
void CleanupPlayerSystem()
{
    g_PlayerDemo.Cleanup();
}
