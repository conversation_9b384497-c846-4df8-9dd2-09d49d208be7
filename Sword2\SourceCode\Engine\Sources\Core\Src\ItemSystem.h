//---------------------------------------------------------------------------
// Sword2 Item System (c) 2024
//
// File:	ItemSystem.h
// Date:	2024.07.02
// Code:	AI Assistant
// Desc:	Comprehensive item and equipment system for Sword2, compatible with existing code
//---------------------------------------------------------------------------
#ifndef ITEM_SYSTEM_H
#define ITEM_SYSTEM_H

#include "ModernCpp.h"
#include "UnifiedLoggingSystem.h"
#include "PlayerSystem.h"
#include <string>
#include <unordered_map>
#include <vector>
#include <memory>
#include <chrono>
#include <atomic>
#include <mutex>
#include <functional>

namespace sword2 {

// 物品类型（对应原有的ITEMGENRE）
enum class ItemGenre : uint8_t
{
    Equipment = 0,      // 装备
    Medicine,           // 药品
    Event,              // 事件物品
    Materials,          // 材料
    Task,               // 任务物品
    TownPortal,         // 传送符
    MagicScript,        // 魔法脚本
    Number              // 数量
};

// 装备详细类型（对应原有的EQUIPDETAILTYPE）
enum class EquipDetailType : uint8_t
{
    MeleeWeapon = 0,    // 近战武器
    RangeWeapon,        // 远程武器
    Armor,              // 护甲
    Ring,               // 戒指
    Amulet,             // 护身符
    Boots,              // 靴子
    Belt,               // 腰带
    Helm,               // 头盔
    Cuff,               // 护腕
    Pendant,            // 吊坠
    Horse,              // 坐骑
    Mask,               // 面具
    Mantle,             // 斗篷
    Signet,             // 印章
    Shipin,             // 饰品
    Hoods,              // 兜帽
    Cloak,              // 披风
    DetailNum           // 数量
};

// 装备品质（对应原有的EQUIPNATURE）
enum class EquipNature : uint8_t
{
    Normal = 0,         // 普通（白色）
    Violet,             // 紫色
    Gold,               // 金色
    Platina,            // 白金
    Number              // 数量
};

// 装备等级（对应原有的EQUIPLEVEL）
enum class EquipLevel : uint8_t
{
    Normal = 0,         // 普通
    Magic,              // 魔法
    Damage,             // 损坏
    Violet,             // 紫色
    Gold,               // 金色
    Platina,            // 白金
    Number              // 数量
};

// 物品位置（对应原有的ITEM_POSITION）
enum class ItemPosition : uint8_t
{
    Hand = 1,           // 手中
    Equip,              // 装备栏
    EquipRoom,          // 装备仓库
    EquipRoomEx,        // 扩展装备仓库
    RepositoryRoom,     // 仓库
    RepositoryRoom1,    // 仓库1
    RepositoryRoom2,    // 仓库2
    RepositoryRoom3,    // 仓库3
    RepositoryRoom4,    // 仓库4
    RepositoryRoom5,    // 仓库5
    TradeRoom,          // 交易栏
    Trade1,             // 交易1
    Immediacy,          // 即时
    Give,               // 给予
    Compound,           // 合成
    CompoundRoom,       // 合成仓库
    Number              // 数量
};

// 装备槽位（对应原有的ITEM_PART）
enum class ItemPart : uint8_t
{
    Head = 0,           // 头部
    Body,               // 身体
    Belt,               // 腰带
    Weapon,             // 武器
    Foot,               // 脚部
    Cuff,               // 护腕
    Amulet,             // 护身符
    Ring1,              // 戒指1
    Ring2,              // 戒指2
    Pendant,            // 吊坠
    Horse,              // 坐骑
    Mask,               // 面具
    Mantle,             // 斗篷
    Signet,             // 印章
    Shipin,             // 饰品
    Hoods,              // 兜帽
    Cloak,              // 披风
    Number              // 数量
};

// 物品属性类型
enum class ItemAttributeType : uint8_t
{
    None = 0,           // 无属性
    Damage,             // 伤害
    Defense,            // 防御
    Health,             // 生命值
    Mana,               // 内力
    Strength,           // 力量
    Agility,            // 敏捷
    Vitality,           // 体质
    Energy,             // 精力
    Lucky,              // 幸运
    AttackSpeed,        // 攻击速度
    MoveSpeed,          // 移动速度
    CriticalRate,       // 暴击率
    CriticalDamage,     // 暴击伤害
    HitRate,            // 命中率
    DodgeRate,          // 闪避率
    BlockRate,          // 格挡率
    ResistFire,         // 火抗
    ResistIce,          // 冰抗
    ResistLightning,    // 雷抗
    ResistPoison,       // 毒抗
    Number              // 数量
};

// 物品属性
struct ItemAttribute
{
    ItemAttributeType type = ItemAttributeType::None;
    int32_t value = 0;
    int32_t minValue = 0;
    int32_t maxValue = 0;
    bool isPercentage = false;  // 是否为百分比
    
    ItemAttribute() = default;
    ItemAttribute(ItemAttributeType attrType, int32_t attrValue, bool percentage = false)
        : type(attrType), value(attrValue), isPercentage(percentage)
    {
        minValue = maxValue = attrValue;
    }
    
    ItemAttribute(ItemAttributeType attrType, int32_t minVal, int32_t maxVal, bool percentage = false)
        : type(attrType), minValue(minVal), maxValue(maxVal), isPercentage(percentage)
    {
        value = (minVal + maxVal) / 2;
    }
    
    // 获取属性描述
    std::string GetDescription() const
    {
        std::string desc;
        switch (type)
        {
        case ItemAttributeType::Damage: desc = "伤害"; break;
        case ItemAttributeType::Defense: desc = "防御"; break;
        case ItemAttributeType::Health: desc = "生命值"; break;
        case ItemAttributeType::Mana: desc = "内力"; break;
        case ItemAttributeType::Strength: desc = "力量"; break;
        case ItemAttributeType::Agility: desc = "敏捷"; break;
        case ItemAttributeType::Vitality: desc = "体质"; break;
        case ItemAttributeType::Energy: desc = "精力"; break;
        case ItemAttributeType::Lucky: desc = "幸运"; break;
        case ItemAttributeType::AttackSpeed: desc = "攻击速度"; break;
        case ItemAttributeType::MoveSpeed: desc = "移动速度"; break;
        case ItemAttributeType::CriticalRate: desc = "暴击率"; break;
        case ItemAttributeType::CriticalDamage: desc = "暴击伤害"; break;
        case ItemAttributeType::HitRate: desc = "命中率"; break;
        case ItemAttributeType::DodgeRate: desc = "闪避率"; break;
        case ItemAttributeType::BlockRate: desc = "格挡率"; break;
        default: desc = "未知属性"; break;
        }
        
        if (isPercentage)
        {
            desc += " +" + std::to_string(value) + "%";
        }
        else
        {
            desc += " +" + std::to_string(value);
        }
        
        return desc;
    }
};

// 物品需求
struct ItemRequirement
{
    uint32_t requiredLevel = 1;         // 需求等级
    PlayerSeries requiredSeries = PlayerSeries::None; // 需求门派
    uint32_t requiredStrength = 0;      // 需求力量
    uint32_t requiredAgility = 0;       // 需求敏捷
    uint32_t requiredVitality = 0;      // 需求体质
    uint32_t requiredEnergy = 0;        // 需求精力
    
    ItemRequirement() = default;
    
    // 检查是否满足需求
    bool CheckRequirement(const Player& player) const
    {
        if (player.level < requiredLevel)
            return false;
        
        if (requiredSeries != PlayerSeries::None && player.series != requiredSeries)
            return false;
        
        if (player.attributes.strength < requiredStrength)
            return false;
        
        if (player.attributes.agility < requiredAgility)
            return false;
        
        if (player.attributes.vitality < requiredVitality)
            return false;
        
        if (player.attributes.energy < requiredEnergy)
            return false;
        
        return true;
    }
};

// 物品模板
class ItemTemplate
{
public:
    uint32_t itemId = 0;            // 物品ID
    std::string name;               // 物品名称
    std::string description;        // 物品描述
    std::string iconPath;           // 图标路径
    std::string scriptPath;         // 脚本路径
    
    ItemGenre genre = ItemGenre::Equipment;
    EquipDetailType detailType = EquipDetailType::MeleeWeapon;
    EquipNature nature = EquipNature::Normal;
    EquipLevel equipLevel = EquipLevel::Normal;
    ItemPart equipPart = ItemPart::Weapon;
    
    uint32_t level = 1;             // 物品等级
    uint32_t price = 0;             // 价格
    uint32_t weight = 1;            // 重量
    uint32_t width = 1;             // 宽度
    uint32_t height = 1;            // 高度
    uint32_t maxStack = 1;          // 最大堆叠数
    uint32_t maxDurability = 100;   // 最大耐久度
    
    PlayerSeries series = PlayerSeries::None; // 五行系列
    ItemRequirement requirement;    // 使用需求
    std::vector<ItemAttribute> baseAttributes; // 基础属性
    std::vector<ItemAttribute> magicAttributes; // 魔法属性
    
    ItemTemplate() = default;
    ItemTemplate(uint32_t id, const std::string& itemName, ItemGenre itemGenre)
        : itemId(id), name(itemName), genre(itemGenre) {}
    
    // 检查是否可以装备
    bool CanEquip(const Player& player) const
    {
        if (genre != ItemGenre::Equipment)
            return false;
        
        return requirement.CheckRequirement(player);
    }
    
    // 获取品质描述
    std::string GetNatureDescription() const
    {
        switch (nature)
        {
        case EquipNature::Normal: return "普通";
        case EquipNature::Violet: return "紫色";
        case EquipNature::Gold: return "金色";
        case EquipNature::Platina: return "白金";
        default: return "未知";
        }
    }
    
    // 获取类型描述
    std::string GetDetailTypeDescription() const
    {
        switch (detailType)
        {
        case EquipDetailType::MeleeWeapon: return "近战武器";
        case EquipDetailType::RangeWeapon: return "远程武器";
        case EquipDetailType::Armor: return "护甲";
        case EquipDetailType::Ring: return "戒指";
        case EquipDetailType::Amulet: return "护身符";
        case EquipDetailType::Boots: return "靴子";
        case EquipDetailType::Belt: return "腰带";
        case EquipDetailType::Helm: return "头盔";
        case EquipDetailType::Cuff: return "护腕";
        case EquipDetailType::Pendant: return "吊坠";
        case EquipDetailType::Horse: return "坐骑";
        default: return "未知装备";
        }
    }
};

// 物品实例
class ItemInstance
{
public:
    uint32_t instanceId = 0;        // 实例ID
    uint32_t templateId = 0;        // 模板ID
    uint32_t ownerId = 0;           // 拥有者ID
    
    ItemPosition position = ItemPosition::Hand;
    uint32_t stackCount = 1;        // 堆叠数量
    uint32_t durability = 100;      // 当前耐久度
    uint32_t enhanceLevel = 0;      // 强化等级
    
    std::vector<ItemAttribute> randomAttributes; // 随机属性
    std::vector<uint32_t> socketGems;  // 镶嵌宝石
    std::chrono::system_clock::time_point createTime; // 创建时间
    std::chrono::system_clock::time_point expireTime; // 过期时间
    
    bool isBound = false;           // 是否绑定
    bool isLocked = false;          // 是否锁定
    bool canTrade = true;           // 是否可交易
    bool canDrop = true;            // 是否可丢弃
    bool canSell = true;            // 是否可出售
    
    ItemInstance() = default;
    ItemInstance(uint32_t instId, uint32_t templId, uint32_t owner)
        : instanceId(instId), templateId(templId), ownerId(owner)
    {
        createTime = std::chrono::system_clock::now();
    }
    
    // 检查是否损坏
    bool IsBroken() const
    {
        return durability == 0;
    }
    
    // 修理物品
    void Repair(uint32_t amount = UINT32_MAX)
    {
        // 这里应该从模板获取最大耐久度
        uint32_t maxDur = 100; // 暂时硬编码
        
        if (amount == UINT32_MAX)
        {
            durability = maxDur;
        }
        else
        {
            durability = std::min(durability + amount, maxDur);
        }
    }
    
    // 减少耐久度
    void ReduceDurability(uint32_t amount = 1)
    {
        if (durability >= amount)
            durability -= amount;
        else
            durability = 0;
    }
    
    // 强化物品
    bool Enhance()
    {
        if (enhanceLevel >= 15) // 最大强化等级
            return false;
        
        enhanceLevel++;
        return true;
    }
    
    // 镶嵌宝石
    bool SocketGem(uint32_t gemId)
    {
        if (socketGems.size() >= 3) // 最多3个孔
            return false;
        
        socketGems.push_back(gemId);
        return true;
    }
    
    // 移除宝石
    bool RemoveGem(size_t index)
    {
        if (index >= socketGems.size())
            return false;
        
        socketGems.erase(socketGems.begin() + index);
        return true;
    }
    
    // 检查是否过期
    bool IsExpired() const
    {
        if (expireTime == std::chrono::system_clock::time_point{})
            return false; // 永久物品
        
        return std::chrono::system_clock::now() > expireTime;
    }
    
    // 设置过期时间
    void SetExpireTime(uint32_t seconds)
    {
        if (seconds == 0)
        {
            expireTime = std::chrono::system_clock::time_point{}; // 永久
        }
        else
        {
            expireTime = std::chrono::system_clock::now() + std::chrono::seconds(seconds);
        }
    }
    
    // 获取剩余时间
    uint32_t GetRemainingTime() const
    {
        if (expireTime == std::chrono::system_clock::time_point{})
            return UINT32_MAX; // 永久
        
        auto now = std::chrono::system_clock::now();
        if (now >= expireTime)
            return 0;
        
        auto remaining = std::chrono::duration_cast<std::chrono::seconds>(expireTime - now);
        return static_cast<uint32_t>(remaining.count());
    }
};

// 背包格子
struct InventorySlot
{
    std::unique_ptr<ItemInstance> item; // 物品实例
    uint32_t x = 0;                     // X坐标
    uint32_t y = 0;                     // Y坐标
    
    InventorySlot() = default;
    InventorySlot(uint32_t posX, uint32_t posY) : x(posX), y(posY) {}
    
    // 检查是否为空
    bool IsEmpty() const
    {
        return item == nullptr;
    }
    
    // 放置物品
    bool PlaceItem(std::unique_ptr<ItemInstance> newItem)
    {
        if (item != nullptr)
            return false; // 已有物品
        
        item = std::move(newItem);
        return true;
    }
    
    // 取出物品
    std::unique_ptr<ItemInstance> TakeItem()
    {
        return std::move(item);
    }
};

// 背包容器
class InventoryContainer
{
public:
    uint32_t width = 6;             // 宽度
    uint32_t height = 10;           // 高度
    uint32_t maxWeight = 1000;      // 最大重量
    uint32_t currentWeight = 0;     // 当前重量
    
    std::vector<std::vector<InventorySlot>> slots; // 格子数组
    
    InventoryContainer() = default;
    InventoryContainer(uint32_t w, uint32_t h, uint32_t maxW)
        : width(w), height(h), maxWeight(maxW)
    {
        InitializeSlots();
    }
    
    // 初始化格子
    void InitializeSlots()
    {
        slots.clear();
        slots.resize(height);
        for (uint32_t y = 0; y < height; ++y)
        {
            slots[y].resize(width);
            for (uint32_t x = 0; x < width; ++x)
            {
                slots[y][x] = InventorySlot(x, y);
            }
        }
    }
    
    // 检查位置是否有效
    bool IsValidPosition(uint32_t x, uint32_t y) const
    {
        return x < width && y < height;
    }
    
    // 获取格子
    InventorySlot* GetSlot(uint32_t x, uint32_t y)
    {
        if (!IsValidPosition(x, y))
            return nullptr;
        
        return &slots[y][x];
    }
    
    // 检查是否可以放置物品
    bool CanPlaceItem(uint32_t x, uint32_t y, uint32_t itemWidth, uint32_t itemHeight) const
    {
        if (x + itemWidth > width || y + itemHeight > height)
            return false;
        
        for (uint32_t dy = 0; dy < itemHeight; ++dy)
        {
            for (uint32_t dx = 0; dx < itemWidth; ++dx)
            {
                if (!slots[y + dy][x + dx].IsEmpty())
                    return false;
            }
        }
        
        return true;
    }
    
    // 放置物品
    bool PlaceItem(std::unique_ptr<ItemInstance> item, uint32_t x, uint32_t y)
    {
        if (!item)
            return false;
        
        // 这里应该从模板获取物品尺寸
        uint32_t itemWidth = 1;  // 暂时硬编码
        uint32_t itemHeight = 1;
        
        if (!CanPlaceItem(x, y, itemWidth, itemHeight))
            return false;
        
        // 检查重量限制
        uint32_t itemWeight = 1; // 暂时硬编码
        if (currentWeight + itemWeight > maxWeight)
            return false;
        
        // 放置物品
        slots[y][x].PlaceItem(std::move(item));
        currentWeight += itemWeight;
        
        return true;
    }
    
    // 移除物品
    std::unique_ptr<ItemInstance> RemoveItem(uint32_t x, uint32_t y)
    {
        if (!IsValidPosition(x, y))
            return nullptr;
        
        auto item = slots[y][x].TakeItem();
        if (item)
        {
            uint32_t itemWeight = 1; // 暂时硬编码
            if (currentWeight >= itemWeight)
                currentWeight -= itemWeight;
        }
        
        return item;
    }
    
    // 查找空位
    std::pair<uint32_t, uint32_t> FindEmptySlot(uint32_t itemWidth = 1, uint32_t itemHeight = 1) const
    {
        for (uint32_t y = 0; y <= height - itemHeight; ++y)
        {
            for (uint32_t x = 0; x <= width - itemWidth; ++x)
            {
                if (CanPlaceItem(x, y, itemWidth, itemHeight))
                {
                    return {x, y};
                }
            }
        }
        
        return {UINT32_MAX, UINT32_MAX}; // 没有空位
    }
    
    // 获取空闲格子数
    uint32_t GetFreeSlotCount() const
    {
        uint32_t count = 0;
        for (uint32_t y = 0; y < height; ++y)
        {
            for (uint32_t x = 0; x < width; ++x)
            {
                if (slots[y][x].IsEmpty())
                    count++;
            }
        }
        return count;
    }
    
    // 获取已使用格子数
    uint32_t GetUsedSlotCount() const
    {
        return width * height - GetFreeSlotCount();
    }
    
    // 整理背包
    void SortItems()
    {
        // 收集所有物品
        std::vector<std::unique_ptr<ItemInstance>> items;
        for (uint32_t y = 0; y < height; ++y)
        {
            for (uint32_t x = 0; x < width; ++x)
            {
                if (!slots[y][x].IsEmpty())
                {
                    items.push_back(slots[y][x].TakeItem());
                }
            }
        }
        
        // 重新放置物品
        currentWeight = 0;
        for (auto& item : items)
        {
            auto [x, y] = FindEmptySlot();
            if (x != UINT32_MAX && y != UINT32_MAX)
            {
                PlaceItem(std::move(item), x, y);
            }
        }
    }
};

} // namespace sword2

#endif // ITEM_SYSTEM_H
