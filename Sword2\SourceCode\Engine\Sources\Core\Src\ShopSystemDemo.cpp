//---------------------------------------------------------------------------
// Sword2 Shop System Demo (c) 2024
//
// File:	ShopSystemDemo.cpp
// Date:	2024.07.02
// Code:	AI Assistant
// Desc:	Comprehensive demonstration of the shop system
//---------------------------------------------------------------------------

#include "KCore.h"
#include "ShopSystem.h"
#include "ShopManager.h"
#include "AuctionManager.h"
#include "ShopScriptIntegration.h"
#include "PlayerSystem.h"
#include "PlayerManager.h"
#include "ItemSystem.h"
#include "ItemManager.h"
#include "InventoryManager.h"
#include "NPCSystem.h"
#include "NPCManager.h"
#include "LuaScriptEngine.h"
#include "UnifiedLoggingSystem.h"

namespace sword2 {

// 商店系统演示类
class ShopSystemDemo
{
public:
    ShopSystemDemo()
    {
        m_initialized = false;
    }
    
    bool Initialize()
    {
        if (m_initialized) return true;
        
        printf("[SHOP_DEMO] Initializing shop system demo...\n");
        
        // 初始化日志系统
        LOGGER().Initialize();
        LOGGER().AddConsoleAppender(LogLevel::Debug);
        
        // 启动玩家管理器
        if (!START_PLAYER_SYSTEM())
        {
            printf("[SHOP_DEMO] Failed to start player system\n");
            return false;
        }
        
        // 启动物品管理器
        if (!START_ITEM_SYSTEM())
        {
            printf("[SHOP_DEMO] Failed to start item system\n");
            return false;
        }
        
        // 启动NPC管理器
        if (!START_NPC_SYSTEM())
        {
            printf("[SHOP_DEMO] Failed to start NPC system\n");
            return false;
        }
        
        // 初始化脚本引擎
        if (!INIT_SCRIPT_ENGINE("../../../Server/script"))
        {
            printf("[SHOP_DEMO] Failed to initialize script engine\n");
            return false;
        }
        
        // 启动商店管理器
        if (!START_SHOP_SYSTEM())
        {
            printf("[SHOP_DEMO] Failed to start shop system\n");
            return false;
        }
        
        // 启动拍卖行管理器
        if (!START_AUCTION_SYSTEM())
        {
            printf("[SHOP_DEMO] Failed to start auction system\n");
            return false;
        }
        
        // 初始化商店脚本集成
        if (!INIT_SHOP_SCRIPT_INTEGRATION())
        {
            printf("[SHOP_DEMO] Failed to initialize shop script integration\n");
            return false;
        }
        
        m_initialized = true;
        LOG_INFO("SHOP_DEMO", "Shop system demo initialized successfully");
        
        return true;
    }
    
    void RunDemo()
    {
        if (!m_initialized)
        {
            printf("[SHOP_DEMO] System not initialized\n");
            return;
        }
        
        LOG_INFO("SHOP_DEMO", "Starting shop system demonstration...");
        
        // 演示各个功能
        DemoShopCreation();
        DemoNPCShop();
        DemoPlayerShop();
        DemoShoppingCart();
        DemoAuctionHouse();
        DemoDynamicPricing();
        DemoShopEvents();
        DemoShopScripting();
        DemoShopStatistics();
        
        LOG_INFO("SHOP_DEMO", "Shop system demonstration completed");
    }
    
    void Cleanup()
    {
        if (!m_initialized) return;
        
        LOG_INFO("SHOP_DEMO", "Cleaning up shop system demo...");
        
        // 清理创建的玩家
        for (uint32_t playerId : m_createdPlayers)
        {
            REMOVE_PLAYER_INVENTORY(playerId);
            PLAYER_LOGOUT(playerId);
        }
        m_createdPlayers.clear();
        
        // 清理创建的NPC
        for (uint32_t npcId : m_createdNPCs)
        {
            REMOVE_NPC(npcId);
        }
        m_createdNPCs.clear();
        
        // 停止系统
        STOP_AUCTION_SYSTEM();
        STOP_SHOP_SYSTEM();
        STOP_NPC_SYSTEM();
        STOP_ITEM_SYSTEM();
        SHUTDOWN_SCRIPT_ENGINE();
        STOP_PLAYER_SYSTEM();
        
        m_initialized = false;
        LOG_INFO("SHOP_DEMO", "Shop system demo cleanup completed");
    }

private:
    bool m_initialized;
    std::vector<uint32_t> m_createdPlayers;
    std::vector<uint32_t> m_createdNPCs;
    std::vector<uint32_t> m_createdShops;
    
    void DemoShopCreation()
    {
        LOG_INFO("SHOP_DEMO", "=== Shop Creation Demo ===");
        
        // 创建不同类型的商店
        
        // 1. NPC武器商店
        ShopConfig weaponShopConfig("武器商店", ShopType::NPC);
        weaponShopConfig.description = "专门出售各种武器的商店";
        weaponShopConfig.maxItems = 50;
        weaponShopConfig.taxRate = 0.05f; // 5%税率
        weaponShopConfig.autoRestock = true;
        weaponShopConfig.dynamicPricing = false;
        
        uint32_t weaponShopId = CREATE_SHOP(weaponShopConfig);
        m_createdShops.push_back(weaponShopId);
        
        // 2. NPC药品商店
        ShopConfig potionShopConfig("药品商店", ShopType::NPC);
        potionShopConfig.description = "出售各种药品和消耗品";
        potionShopConfig.maxItems = 30;
        potionShopConfig.autoRestock = true;
        potionShopConfig.dynamicPricing = true; // 启用动态定价
        
        uint32_t potionShopId = CREATE_SHOP(potionShopConfig);
        m_createdShops.push_back(potionShopId);
        
        // 3. 系统商店
        ShopConfig systemShopConfig("系统商店", ShopType::System);
        systemShopConfig.description = "系统特殊物品商店";
        systemShopConfig.alwaysOpen = true;
        systemShopConfig.publicAccess = true;
        systemShopConfig.maxItems = 100;
        
        uint32_t systemShopId = CREATE_SHOP(systemShopConfig);
        m_createdShops.push_back(systemShopId);
        
        // 4. 帮会商店
        ShopConfig guildShopConfig("帮会商店", ShopType::Guild);
        guildShopConfig.description = "帮会专属商店";
        guildShopConfig.publicAccess = false;
        guildShopConfig.minLevel = 20;
        guildShopConfig.allowedSeries = {PlayerSeries::Shaolin, PlayerSeries::Wudang};
        
        uint32_t guildShopId = CREATE_SHOP(guildShopConfig);
        m_createdShops.push_back(guildShopId);
        
        LOG_INFO("SHOP_DEMO", "Created shops:");
        LOG_INFO("SHOP_DEMO", "  Weapon Shop ID: " + std::to_string(weaponShopId));
        LOG_INFO("SHOP_DEMO", "  Potion Shop ID: " + std::to_string(potionShopId));
        LOG_INFO("SHOP_DEMO", "  System Shop ID: " + std::to_string(systemShopId));
        LOG_INFO("SHOP_DEMO", "  Guild Shop ID: " + std::to_string(guildShopId));
        
        // 为商店添加物品
        auto weaponShop = GET_SHOP(weaponShopId);
        if (weaponShop)
        {
            // 添加武器
            ShopItem sword(1001, 100, CurrencyType::Gold);
            sword.stock = 10;
            sword.maxStock = 20;
            sword.isLimited = true;
            sword.restockInterval = 3600; // 1小时补货
            sword.restockAmount = 5;
            sword.minLevel = 10;
            weaponShop->AddItem(sword);
            
            ShopItem bow(1002, 150, CurrencyType::Gold);
            bow.stock = 8;
            bow.maxStock = 15;
            bow.isLimited = true;
            bow.minLevel = 15;
            weaponShop->AddItem(bow);
            
            ShopItem staff(1003, 200, CurrencyType::Gold);
            staff.stock = 5;
            staff.maxStock = 10;
            staff.isLimited = true;
            staff.minLevel = 20;
            staff.requiredSeries = PlayerSeries::Emei;
            weaponShop->AddItem(staff);
        }
        
        auto potionShop = GET_SHOP(potionShopId);
        if (potionShop)
        {
            // 添加药品
            ShopItem healthPotion(2001, 10, CurrencyType::Gold);
            healthPotion.stock = 100;
            healthPotion.maxStock = 200;
            healthPotion.isLimited = true;
            healthPotion.restockInterval = 1800; // 30分钟补货
            healthPotion.restockAmount = 50;
            potionShop->AddItem(healthPotion);
            
            ShopItem manaPotion(2002, 15, CurrencyType::Gold);
            manaPotion.stock = 80;
            manaPotion.maxStock = 150;
            manaPotion.isLimited = true;
            potionShop->AddItem(manaPotion);
            
            ShopItem antidote(2003, 25, CurrencyType::Gold);
            antidote.stock = 30;
            antidote.maxStock = 50;
            antidote.isLimited = true;
            potionShop->AddItem(antidote);
        }
        
        auto systemShop = GET_SHOP(systemShopId);
        if (systemShop)
        {
            // 添加特殊物品
            ShopItem teleportScroll(3001, 50, CurrencyType::Gold);
            teleportScroll.isLimited = false; // 无限库存
            systemShop->AddItem(teleportScroll);
            
            ShopItem expBook(3002, 100, CurrencyType::Reputation);
            expBook.stock = 10;
            expBook.isLimited = true;
            expBook.minLevel = 30;
            expBook.requiredReputation = 1000;
            systemShop->AddItem(expBook);
        }
        
        auto guildShop = GET_SHOP(guildShopId);
        if (guildShop)
        {
            // 添加帮会物品
            ShopItem guildBanner(4001, 500, CurrencyType::Contribution);
            guildBanner.stock = 1;
            guildBanner.isUnique = true;
            guildBanner.minLevel = 25;
            guildShop->AddItem(guildBanner);
            
            ShopItem guildSkillBook(4002, 200, CurrencyType::Contribution);
            guildSkillBook.stock = 5;
            guildSkillBook.isLimited = true;
            guildSkillBook.minLevel = 30;
            guildShop->AddItem(guildSkillBook);
        }
        
        LOG_INFO("SHOP_DEMO", "Added items to all shops");
    }
    
    void DemoNPCShop()
    {
        LOG_INFO("SHOP_DEMO", "=== NPC Shop Demo ===");
        
        // 创建商人NPC
        NPCTemplate merchantTemplate(2001, "武器商人老李", NPCType::Merchant);
        merchantTemplate.title = "武器专家";
        merchantTemplate.description = "经验丰富的武器商人";
        merchantTemplate.behavior = NPCBehavior::Static;
        merchantTemplate.baseAttributes.level = 50;
        merchantTemplate.baseAttributes.maxLife = 1000;
        merchantTemplate.baseAttributes.currentLife = 1000;
        
        uint32_t merchantId = CREATE_NPC(merchantTemplate);
        if (merchantId != 0)
        {
            m_createdNPCs.push_back(merchantId);
            
            // 设置NPC位置
            auto merchant = GET_NPC(merchantId);
            if (merchant)
            {
                merchant->position.x = 100;
                merchant->position.y = 100;
                merchant->position.mapId = 1;
                
                // 关联商店
                if (!m_createdShops.empty())
                {
                    auto weaponShop = GET_SHOP(m_createdShops[0]);
                    if (weaponShop)
                    {
                        weaponShop->config.ownerId = merchantId;
                    }
                }
            }
            
            LOG_INFO("SHOP_DEMO", "Created merchant NPC: " + std::to_string(merchantId));
        }
        
        // 创建测试玩家
        PlayerCreationParams params("购物者小王", "shopper_wang");
        params.series = PlayerSeries::Shaolin;
        uint32_t playerId = CREATE_PLAYER(params);
        
        if (playerId != 0)
        {
            m_createdPlayers.push_back(playerId);
            
            // 玩家登录
            uint32_t sessionId;
            PLAYER_LOGIN(playerId, "127.0.0.1", 7001, sessionId);
            
            auto player = GET_ONLINE_PLAYER(playerId);
            if (player)
            {
                player->level = 15;
                player->money = 1000; // 给玩家一些金钱
                player->reputation = 500;
                
                // 创建玩家背包
                CREATE_PLAYER_INVENTORY(playerId, 50, 200);
                
                LOG_INFO("SHOP_DEMO", "Created test player: " + player->name + 
                        " (Level: " + std::to_string(player->level) + 
                        ", Money: " + std::to_string(player->money) + ")");
                
                // 打开商店
                if (!m_createdShops.empty())
                {
                    uint32_t shopSessionId = OPEN_SHOP(m_createdShops[0], playerId);
                    
                    if (shopSessionId != 0)
                    {
                        LOG_INFO("SHOP_DEMO", "Opened shop session: " + std::to_string(shopSessionId));
                        
                        // 查看商店物品
                        auto shop = GET_SHOP(m_createdShops[0]);
                        if (shop)
                        {
                            auto availableItems = shop->GetAvailableItems(*player);
                            LOG_INFO("SHOP_DEMO", "Available items for player: " + std::to_string(availableItems.size()));
                            
                            for (const auto& item : availableItems)
                            {
                                LOG_INFO("SHOP_DEMO", "  Item " + std::to_string(item.itemId) + 
                                        ": " + item.GetDescription());
                            }
                            
                            // 购买物品
                            if (!availableItems.empty())
                            {
                                uint32_t itemToBuy = availableItems[0].itemId;
                                ShopResult buyResult = BUY_ITEM(shopSessionId, itemToBuy, 1);
                                
                                LOG_INFO("SHOP_DEMO", "Buy result for item " + std::to_string(itemToBuy) + 
                                        ": " + std::to_string(static_cast<int>(buyResult)));
                                
                                if (buyResult == ShopResult::Success)
                                {
                                    LOG_INFO("SHOP_DEMO", "Successfully bought item!");
                                    LOG_INFO("SHOP_DEMO", "Player money after purchase: " + std::to_string(player->money));
                                }
                            }
                        }
                        
                        // 关闭商店
                        CLOSE_SHOP(shopSessionId);
                        LOG_INFO("SHOP_DEMO", "Closed shop session");
                    }
                }
            }
        }
    }
    
    void DemoPlayerShop()
    {
        LOG_INFO("SHOP_DEMO", "=== Player Shop Demo ===");
        
        // 创建玩家商店
        PlayerCreationParams params("商人小张", "merchant_zhang");
        params.series = PlayerSeries::Wudang;
        uint32_t merchantPlayerId = CREATE_PLAYER(params);
        
        if (merchantPlayerId != 0)
        {
            m_createdPlayers.push_back(merchantPlayerId);
            
            // 玩家登录
            uint32_t sessionId;
            PLAYER_LOGIN(merchantPlayerId, "127.0.0.1", 7002, sessionId);
            
            auto merchantPlayer = GET_ONLINE_PLAYER(merchantPlayerId);
            if (merchantPlayer)
            {
                merchantPlayer->level = 25;
                merchantPlayer->money = 2000;
                
                // 创建玩家商店
                ShopConfig playerShopConfig("小张的杂货铺", ShopType::Player);
                playerShopConfig.description = "玩家经营的杂货店";
                playerShopConfig.ownerId = merchantPlayerId;
                playerShopConfig.maxItems = 20;
                playerShopConfig.publicAccess = true;
                playerShopConfig.openHour = 8;
                playerShopConfig.closeHour = 20; // 8:00-20:00营业
                playerShopConfig.alwaysOpen = false;
                
                uint32_t playerShopId = CREATE_SHOP(playerShopConfig);
                m_createdShops.push_back(playerShopId);
                
                auto playerShop = GET_SHOP(playerShopId);
                if (playerShop)
                {
                    // 添加玩家出售的物品
                    ShopItem playerItem1(5001, 80, CurrencyType::Gold);
                    playerItem1.stock = 3;
                    playerItem1.isLimited = true;
                    playerShop->AddItem(playerItem1);
                    
                    ShopItem playerItem2(5002, 120, CurrencyType::Gold);
                    playerItem2.stock = 2;
                    playerItem2.isLimited = true;
                    playerItem2.discount = 0.9f; // 9折优惠
                    playerItem2.discountExpire = std::chrono::system_clock::now() + std::chrono::hours(24);
                    playerShop->AddItem(playerItem2);
                    
                    LOG_INFO("SHOP_DEMO", "Created player shop: " + std::to_string(playerShopId));
                    LOG_INFO("SHOP_DEMO", "Shop owner: " + merchantPlayer->name);
                    LOG_INFO("SHOP_DEMO", "Shop items: " + std::to_string(playerShop->items.size()));
                    
                    // 其他玩家购买
                    if (!m_createdPlayers.empty())
                    {
                        uint32_t buyerId = m_createdPlayers[0]; // 使用之前创建的玩家
                        auto buyer = GET_ONLINE_PLAYER(buyerId);
                        
                        if (buyer && playerShop->CanAccess(*buyer))
                        {
                            uint32_t buySessionId = OPEN_SHOP(playerShopId, buyerId);
                            
                            if (buySessionId != 0)
                            {
                                LOG_INFO("SHOP_DEMO", "Buyer opened player shop");
                                
                                // 购买打折物品
                                ShopResult buyResult = BUY_ITEM(buySessionId, 5002, 1);
                                LOG_INFO("SHOP_DEMO", "Buy discounted item result: " + std::to_string(static_cast<int>(buyResult)));
                                
                                CLOSE_SHOP(buySessionId);
                            }
                        }
                    }
                }
            }
        }
    }
    
    void DemoShoppingCart()
    {
        LOG_INFO("SHOP_DEMO", "=== Shopping Cart Demo ===");
        
        if (m_createdPlayers.empty() || m_createdShops.empty()) return;
        
        uint32_t playerId = m_createdPlayers[0];
        uint32_t shopId = m_createdShops[0];
        
        auto player = GET_ONLINE_PLAYER(playerId);
        if (!player) return;
        
        // 给玩家更多金钱用于购物车演示
        player->money = 5000;
        
        uint32_t sessionId = OPEN_SHOP(shopId, playerId);
        if (sessionId == 0) return;
        
        LOG_INFO("SHOP_DEMO", "Testing shopping cart functionality...");
        
        // 添加多个物品到购物车
        bool addResult1 = ADD_TO_CART(sessionId, 1001, 2); // 2把剑
        bool addResult2 = ADD_TO_CART(sessionId, 1002, 1); // 1把弓
        bool addResult3 = ADD_TO_CART(sessionId, 2001, 5); // 5瓶生命药水
        
        LOG_INFO("SHOP_DEMO", "Add to cart results: " + 
                std::to_string(addResult1) + ", " + 
                std::to_string(addResult2) + ", " + 
                std::to_string(addResult3));
        
        auto session = SHOP_MANAGER().GetSession(sessionId);
        if (session)
        {
            LOG_INFO("SHOP_DEMO", "Cart items: " + std::to_string(session->cart.size()));
            LOG_INFO("SHOP_DEMO", "Total cart items: " + std::to_string(session->GetCartItemCount()));
            
            for (const auto& [itemId, quantity] : session->cart)
            {
                LOG_INFO("SHOP_DEMO", "  Item " + std::to_string(itemId) + " x" + std::to_string(quantity));
            }
            
            // 从购物车移除一个物品
            bool removeResult = REMOVE_FROM_CART(sessionId, 1002, 1);
            LOG_INFO("SHOP_DEMO", "Remove from cart result: " + std::to_string(removeResult));
            
            // 购买购物车中的所有物品
            ShopResult buyCartResult = BUY_CART(sessionId);
            LOG_INFO("SHOP_DEMO", "Buy cart result: " + std::to_string(static_cast<int>(buyCartResult)));
            
            if (buyCartResult == ShopResult::Success)
            {
                LOG_INFO("SHOP_DEMO", "Successfully bought all cart items!");
                LOG_INFO("SHOP_DEMO", "Player money after cart purchase: " + std::to_string(player->money));
                LOG_INFO("SHOP_DEMO", "Cart items after purchase: " + std::to_string(session->cart.size()));
            }
        }
        
        CLOSE_SHOP(sessionId);
    }

    void DemoAuctionHouse()
    {
        LOG_INFO("SHOP_DEMO", "=== Auction House Demo ===");

        if (m_createdPlayers.size() < 2) return;

        uint32_t sellerId = m_createdPlayers[0];
        uint32_t bidderId = m_createdPlayers[1];

        auto seller = GET_ONLINE_PLAYER(sellerId);
        auto bidder = GET_ONLINE_PLAYER(bidderId);

        if (!seller || !bidder) return;

        // 给玩家一些物品用于拍卖
        seller->money = 3000;
        bidder->money = 2000;

        LOG_INFO("SHOP_DEMO", "Testing auction house functionality...");

        // 创建拍卖（模拟物品实例）
        uint32_t itemInstanceId = 10001; // 模拟物品实例ID
        AuctionResult createResult = CREATE_AUCTION(sellerId, itemInstanceId, 100, 200, AuctionType::Bidding, 3600);

        LOG_INFO("SHOP_DEMO", "Create auction result: " + std::to_string(static_cast<int>(createResult)));

        if (createResult == AuctionResult::Success)
        {
            // 搜索拍卖
            AuctionSearchCriteria criteria;
            criteria.activeOnly = true;
            criteria.minPrice = 50;
            criteria.maxPrice = 300;
            criteria.sortBy = AuctionSearchCriteria::SortBy::Price;
            criteria.ascending = true;

            auto searchResults = SEARCH_AUCTIONS(criteria);
            LOG_INFO("SHOP_DEMO", "Found auctions: " + std::to_string(searchResults.size()));

            for (const auto& auction : searchResults)
            {
                LOG_INFO("SHOP_DEMO", "  Auction " + std::to_string(auction.auctionId) +
                        ": Item " + std::to_string(auction.itemTemplateId) +
                        ", Price: " + std::to_string(auction.currentPrice) +
                        ", Time left: " + std::to_string(auction.GetRemainingTime()) + "s");

                // 竞价
                if (auction.CanBid())
                {
                    uint32_t bidAmount = auction.GetNextBidAmount();
                    AuctionResult bidResult = PLACE_BID(bidderId, auction.auctionId, bidAmount);
                    LOG_INFO("SHOP_DEMO", "Bid result: " + std::to_string(static_cast<int>(bidResult)));

                    if (bidResult == AuctionResult::Success)
                    {
                        LOG_INFO("SHOP_DEMO", "Successfully placed bid of " + std::to_string(bidAmount));

                        // 再次竞价（更高价格）
                        uint32_t higherBid = bidAmount + 20;
                        AuctionResult higherBidResult = PLACE_BID(bidderId, auction.auctionId, higherBid);
                        LOG_INFO("SHOP_DEMO", "Higher bid result: " + std::to_string(static_cast<int>(higherBidResult)));

                        // 测试一口价购买
                        if (auction.CanBuyout())
                        {
                            AuctionResult buyoutResult = BUYOUT_AUCTION(bidderId, auction.auctionId);
                            LOG_INFO("SHOP_DEMO", "Buyout result: " + std::to_string(static_cast<int>(buyoutResult)));
                        }
                    }
                }
            }

            // 获取玩家的拍卖
            auto sellerAuctions = GET_PLAYER_AUCTIONS(sellerId, true);
            LOG_INFO("SHOP_DEMO", "Seller's active auctions: " + std::to_string(sellerAuctions.size()));

            auto bidderBids = GET_PLAYER_BIDS(bidderId, true);
            LOG_INFO("SHOP_DEMO", "Bidder's active bids: " + std::to_string(bidderBids.size()));
        }

        // 获取拍卖统计
        auto auctionStats = GET_AUCTION_STATISTICS();
        LOG_INFO("SHOP_DEMO", "Auction statistics:");
        LOG_INFO("SHOP_DEMO", "  Total auctions: " + std::to_string(auctionStats.totalAuctions));
        LOG_INFO("SHOP_DEMO", "  Active auctions: " + std::to_string(auctionStats.activeAuctions));
        LOG_INFO("SHOP_DEMO", "  Completed auctions: " + std::to_string(auctionStats.completedAuctions));
        LOG_INFO("SHOP_DEMO", "  Total volume: " + std::to_string(auctionStats.totalVolume));
        LOG_INFO("SHOP_DEMO", "  Daily volume: " + std::to_string(auctionStats.dailyVolume));
    }

    void DemoDynamicPricing()
    {
        LOG_INFO("SHOP_DEMO", "=== Dynamic Pricing Demo ===");

        if (m_createdShops.size() < 2) return;

        uint32_t shopId = m_createdShops[1]; // 使用药品商店（启用了动态定价）
        auto shop = GET_SHOP(shopId);
        if (!shop) return;

        LOG_INFO("SHOP_DEMO", "Testing dynamic pricing system...");

        auto& pricingSystem = DYNAMIC_PRICING_SYSTEM();

        // 模拟销售数据更新价格
        uint32_t itemId = 2001; // 生命药水
        uint32_t basePrice = 10;

        // 初始价格
        uint32_t initialPrice = pricingSystem.GetCurrentPrice(itemId);
        LOG_INFO("SHOP_DEMO", "Initial price for item " + std::to_string(itemId) + ": " + std::to_string(initialPrice));

        // 模拟高销量，低库存（价格应该上涨）
        pricingSystem.UpdatePrice(itemId, basePrice, 50, 5); // 销售50个，库存5个
        uint32_t highDemandPrice = pricingSystem.GetCurrentPrice(itemId);
        LOG_INFO("SHOP_DEMO", "High demand price: " + std::to_string(highDemandPrice));

        // 模拟低销量，高库存（价格应该下降）
        pricingSystem.UpdatePrice(itemId, basePrice, 5, 100); // 销售5个，库存100个
        uint32_t lowDemandPrice = pricingSystem.GetCurrentPrice(itemId);
        LOG_INFO("SHOP_DEMO", "Low demand price: " + std::to_string(lowDemandPrice));

        // 获取价格数据
        auto priceData = pricingSystem.GetPriceData(itemId);
        LOG_INFO("SHOP_DEMO", "Price data for item " + std::to_string(itemId) + ":");
        LOG_INFO("SHOP_DEMO", "  Base price: " + std::to_string(priceData.basePrice));
        LOG_INFO("SHOP_DEMO", "  Current price: " + std::to_string(priceData.currentPrice));
        LOG_INFO("SHOP_DEMO", "  Demand factor: " + std::to_string(priceData.demandFactor));
        LOG_INFO("SHOP_DEMO", "  Supply factor: " + std::to_string(priceData.supplyFactor));
        LOG_INFO("SHOP_DEMO", "  Season factor: " + std::to_string(priceData.seasonFactor));

        // 测试脚本计算的动态价格
        uint32_t scriptPrice = CALCULATE_DYNAMIC_PRICE(itemId, basePrice, shopId);
        LOG_INFO("SHOP_DEMO", "Script calculated price: " + std::to_string(scriptPrice));

        // 测试折扣计算
        if (!m_createdPlayers.empty())
        {
            uint32_t playerId = m_createdPlayers[0];
            uint32_t discountPrice = CALCULATE_DISCOUNT_PRICE(basePrice, 0.2f, playerId); // 8折
            LOG_INFO("SHOP_DEMO", "Discount price (20% off): " + std::to_string(discountPrice));
        }
    }

    void DemoShopEvents()
    {
        LOG_INFO("SHOP_DEMO", "=== Shop Events Demo ===");

        // 注册自定义事件处理器
        auto& eventHandler = SHOP_EVENT_HANDLER();

        eventHandler.RegisterEventHandler(ShopEvent::ItemBought, "demo_item_bought",
            [](const ShopEventHandler::ShopEventData& data) {
                LOG_INFO("SHOP_EVENT", "Custom handler - Item bought: Item " + std::to_string(data.itemId) +
                        " x" + std::to_string(data.quantity) +
                        " by player " + std::to_string(data.playerId) +
                        " for " + std::to_string(data.price) + " gold");
            });

        eventHandler.RegisterEventHandler(ShopEvent::ItemSold, "demo_item_sold",
            [](const ShopEventHandler::ShopEventData& data) {
                LOG_INFO("SHOP_EVENT", "Custom handler - Item sold: Item " + std::to_string(data.itemId) +
                        " x" + std::to_string(data.quantity) +
                        " by player " + std::to_string(data.playerId));
            });

        eventHandler.RegisterEventHandler(ShopEvent::PriceChanged, "demo_price_changed",
            [](const ShopEventHandler::ShopEventData& data) {
                LOG_INFO("SHOP_EVENT", "Custom handler - Price changed: Item " + std::to_string(data.itemId) +
                        " new price: " + std::to_string(data.price));
            });

        eventHandler.RegisterEventHandler(ShopEvent::AuctionCreated, "demo_auction_created",
            [](const ShopEventHandler::ShopEventData& data) {
                LOG_INFO("SHOP_EVENT", "Custom handler - Auction created: " + std::to_string(data.auctionId) +
                        " by player " + std::to_string(data.playerId));
            });

        // 触发一些测试事件
        if (!m_createdShops.empty() && !m_createdPlayers.empty())
        {
            uint32_t shopId = m_createdShops[0];
            uint32_t playerId = m_createdPlayers[0];

            TRIGGER_SHOP_EVENT(ShopEvent::ShopOpened, shopId, playerId);
            TRIGGER_SHOP_EVENT(ShopEvent::ItemBought, shopId, playerId);
            TRIGGER_SHOP_EVENT(ShopEvent::PriceChanged, shopId, 0);
        }

        // 获取事件历史
        if (!m_createdShops.empty())
        {
            uint32_t shopId = m_createdShops[0];
            auto eventHistory = eventHandler.GetEventHistory(shopId);

            LOG_INFO("SHOP_DEMO", "Event history for shop " + std::to_string(shopId) + ": " +
                    std::to_string(eventHistory.size()) + " events");

            for (const auto& event : eventHistory)
            {
                std::string eventName;
                switch (event.event)
                {
                case ShopEvent::ShopCreated: eventName = "Shop Created"; break;
                case ShopEvent::ShopOpened: eventName = "Shop Opened"; break;
                case ShopEvent::ItemBought: eventName = "Item Bought"; break;
                case ShopEvent::ItemSold: eventName = "Item Sold"; break;
                case ShopEvent::PriceChanged: eventName = "Price Changed"; break;
                case ShopEvent::AuctionCreated: eventName = "Auction Created"; break;
                default: eventName = "Other"; break;
                }

                LOG_INFO("SHOP_DEMO", "  Event: " + eventName +
                        " - Shop: " + std::to_string(event.shopId) +
                        " - Player: " + std::to_string(event.playerId) +
                        " - Item: " + std::to_string(event.itemId));
            }
        }
    }

    void DemoShopScripting()
    {
        LOG_INFO("SHOP_DEMO", "=== Shop Scripting Demo ===");

        // 创建测试脚本
        std::string testScript = R"(
            function shop_test_function(shopId, playerId)
                WriteLog("Shop script function called!")
                WriteLog("Shop ID: " .. shopId)
                WriteLog("Player ID: " .. playerId)

                local shopInfo = GetShopInfo(shopId)
                if shopInfo then
                    WriteLog("Shop name: " .. shopInfo.name)
                    WriteLog("Shop type: " .. shopInfo.type)
                    WriteLog("Shop owner: " .. shopInfo.ownerId)
                end

                return true
            end

            function on_shop_item_bought(shopId, playerId)
                WriteLog("Item bought event: Shop " .. shopId .. " by player " .. playerId)

                local playerMoney = GetPlayerMoney(playerId)
                WriteLog("Player money after purchase: " .. playerMoney)

                -- 检查是否给予购买奖励
                if playerMoney > 1000 then
                    WriteLog("Player is a VIP customer, giving bonus!")
                    -- 这里可以给予额外奖励
                end

                return true
            end

            function calculate_discount_price(basePrice, discountRate, playerId)
                WriteLog("Calculating discount price")
                WriteLog("Base price: " .. basePrice)
                WriteLog("Discount rate: " .. discountRate)

                local playerLevel = GetPlayerLevel(playerId)
                local playerMoney = GetPlayerMoney(playerId)

                -- 根据玩家等级给予额外折扣
                local levelDiscount = 0
                if playerLevel >= 30 then
                    levelDiscount = 0.1  -- 30级以上额外9折
                elseif playerLevel >= 20 then
                    levelDiscount = 0.05 -- 20级以上额外95折
                end

                -- 根据玩家财富给予VIP折扣
                local vipDiscount = 0
                if playerMoney >= 5000 then
                    vipDiscount = 0.05  -- 富豪额外95折
                end

                local totalDiscount = discountRate + levelDiscount + vipDiscount
                totalDiscount = math.min(totalDiscount, 0.5) -- 最大5折

                local finalPrice = math.floor(basePrice * (1.0 - totalDiscount))
                WriteLog("Final discounted price: " .. finalPrice)

                return finalPrice
            end

            function check_purchase_condition(playerId, itemId, quantity, shopId)
                WriteLog("Checking purchase condition")

                local playerLevel = GetPlayerLevel(playerId)
                local playerMoney = GetPlayerMoney(playerId)

                -- 检查特殊物品的购买条件
                if itemId == 3002 then -- 经验书
                    if playerLevel < 30 then
                        WriteLog("Player level too low for experience book")
                        return false
                    end
                end

                -- 检查批量购买限制
                if quantity > 10 then
                    WriteLog("Cannot buy more than 10 items at once")
                    return false
                end

                -- 检查玩家信誉
                local reputation = GetPlayerReputation(playerId)
                if reputation < 0 then
                    WriteLog("Player reputation too low")
                    return false
                end

                return true
            end

            function on_shop_price_changed(shopId, itemId)
                WriteLog("Price changed for item " .. itemId .. " in shop " .. shopId)

                local newPrice = GetItemPrice(shopId, itemId)
                local marketPrice = GetMarketPrice(itemId)

                WriteLog("New price: " .. newPrice)
                WriteLog("Market price: " .. marketPrice)

                -- 价格预警
                if newPrice > marketPrice * 1.5 then
                    WriteLog("WARNING: Price is 50% above market price!")
                elseif newPrice < marketPrice * 0.5 then
                    WriteLog("NOTICE: Great deal! Price is 50% below market price!")
                end

                return true
            end

            function calculate_auction_fee(startPrice, duration)
                WriteLog("Calculating auction listing fee")

                local baseFee = math.floor(startPrice * 0.05) -- 5%基础费用
                local durationFee = math.floor(duration / 3600) * 10 -- 每小时10金币

                local totalFee = baseFee + durationFee
                totalFee = math.max(totalFee, 1) -- 最少1金币

                WriteLog("Auction fee: " .. totalFee)
                return totalFee
            end
        )";

        // 执行测试脚本
        auto context = std::make_unique<LuaScriptContext>();
        context->Initialize();

        ScriptResult result = context->ExecuteString(testScript);
        if (result == ScriptResult::Success)
        {
            LOG_INFO("SHOP_DEMO", "Successfully loaded shop test script");

            if (!m_createdShops.empty() && !m_createdPlayers.empty())
            {
                uint32_t shopId = m_createdShops[0];
                uint32_t playerId = m_createdPlayers[0];

                // 测试脚本函数调用
                std::vector<LuaValue> args = {
                    LuaValue(static_cast<double>(shopId)),
                    LuaValue(static_cast<double>(playerId))
                };

                result = context->CallFunction("shop_test_function", args);
                if (result == ScriptResult::Success)
                {
                    LOG_INFO("SHOP_DEMO", "Successfully executed shop script function");
                }

                // 测试事件处理函数
                context->CallFunction("on_shop_item_bought", args);

                // 测试折扣计算函数
                std::vector<LuaValue> discountArgs = {
                    LuaValue(static_cast<double>(100)),
                    LuaValue(static_cast<double>(0.2)),
                    LuaValue(static_cast<double>(playerId))
                };
                context->CallFunction("calculate_discount_price", discountArgs);

                // 测试购买条件检查
                std::vector<LuaValue> conditionArgs = {
                    LuaValue(static_cast<double>(playerId)),
                    LuaValue(static_cast<double>(3002)),
                    LuaValue(static_cast<double>(1)),
                    LuaValue(static_cast<double>(shopId))
                };
                context->CallFunction("check_purchase_condition", conditionArgs);

                // 测试拍卖费用计算
                std::vector<LuaValue> feeArgs = {
                    LuaValue(static_cast<double>(100)),
                    LuaValue(static_cast<double>(3600))
                };
                context->CallFunction("calculate_auction_fee", feeArgs);
            }
        }
        else
        {
            LOG_ERROR("SHOP_DEMO", "Failed to load shop test script");
        }
    }

    void DemoShopStatistics()
    {
        LOG_INFO("SHOP_DEMO", "=== Shop Statistics Demo ===");

        // 显示所有商店的统计信息
        for (uint32_t shopId : m_createdShops)
        {
            auto shop = GET_SHOP(shopId);
            if (shop)
            {
                auto [totalSales, dailySales] = shop->GetSalesStats();

                LOG_INFO("SHOP_DEMO", "Shop " + std::to_string(shopId) + " (" + shop->config.name + ") statistics:");
                LOG_INFO("SHOP_DEMO", "  Type: " + std::to_string(static_cast<int>(shop->config.type)));
                LOG_INFO("SHOP_DEMO", "  Owner ID: " + std::to_string(shop->config.ownerId));
                LOG_INFO("SHOP_DEMO", "  Total items: " + std::to_string(shop->items.size()));
                LOG_INFO("SHOP_DEMO", "  Total customers: " + std::to_string(shop->totalCustomers));
                LOG_INFO("SHOP_DEMO", "  Total sales: " + std::to_string(totalSales));
                LOG_INFO("SHOP_DEMO", "  Daily sales: " + std::to_string(dailySales));
                LOG_INFO("SHOP_DEMO", "  Purchase history: " + std::to_string(shop->history.size()));

                // 显示热门物品
                std::unordered_map<uint32_t, uint32_t> itemSales;
                for (const auto& record : shop->history)
                {
                    itemSales[record.itemId] += record.quantity;
                }

                if (!itemSales.empty())
                {
                    LOG_INFO("SHOP_DEMO", "  Popular items:");
                    for (const auto& [itemId, sales] : itemSales)
                    {
                        LOG_INFO("SHOP_DEMO", "    Item " + std::to_string(itemId) + ": " + std::to_string(sales) + " sold");
                    }
                }

                LOG_INFO("SHOP_DEMO", "");
            }
        }

        // 显示拍卖行统计
        auto auctionStats = GET_AUCTION_STATISTICS();
        LOG_INFO("SHOP_DEMO", "Auction house statistics:");
        LOG_INFO("SHOP_DEMO", "  Total auctions: " + std::to_string(auctionStats.totalAuctions));
        LOG_INFO("SHOP_DEMO", "  Active auctions: " + std::to_string(auctionStats.activeAuctions));
        LOG_INFO("SHOP_DEMO", "  Completed auctions: " + std::to_string(auctionStats.completedAuctions));
        LOG_INFO("SHOP_DEMO", "  Total volume: " + std::to_string(auctionStats.totalVolume));
        LOG_INFO("SHOP_DEMO", "  Daily volume: " + std::to_string(auctionStats.dailyVolume));
        LOG_INFO("SHOP_DEMO", "  Total commission: " + std::to_string(auctionStats.totalCommission));
        LOG_INFO("SHOP_DEMO", "  Daily commission: " + std::to_string(auctionStats.dailyCommission));

        if (!auctionStats.popularItems.empty())
        {
            LOG_INFO("SHOP_DEMO", "  Popular auction items:");
            for (const auto& [itemId, count] : auctionStats.popularItems)
            {
                LOG_INFO("SHOP_DEMO", "    Item " + std::to_string(itemId) + ": " + std::to_string(count) + " auctions");
            }
        }

        // 显示玩家购买统计
        for (uint32_t playerId : m_createdPlayers)
        {
            auto player = GET_ONLINE_PLAYER(playerId);
            if (player)
            {
                uint32_t playerPurchases = 0;
                uint32_t playerSpending = 0;

                for (uint32_t shopId : m_createdShops)
                {
                    auto shop = GET_SHOP(shopId);
                    if (shop)
                    {
                        for (const auto& record : shop->history)
                        {
                            if (record.playerId == playerId)
                            {
                                playerPurchases++;
                                playerSpending += record.totalPrice;
                            }
                        }
                    }
                }

                LOG_INFO("SHOP_DEMO", "Player " + player->name + " shopping statistics:");
                LOG_INFO("SHOP_DEMO", "  Total purchases: " + std::to_string(playerPurchases));
                LOG_INFO("SHOP_DEMO", "  Total spending: " + std::to_string(playerSpending));
                LOG_INFO("SHOP_DEMO", "  Current money: " + std::to_string(player->money));
                LOG_INFO("SHOP_DEMO", "");
            }
        }
    }
};

} // namespace sword2

// 全局商店系统演示实例
sword2::ShopSystemDemo g_ShopDemo;

// 初始化商店系统
bool InitializeShopSystem()
{
    return g_ShopDemo.Initialize();
}

// 运行商店系统演示
void RunShopSystemDemo()
{
    g_ShopDemo.RunDemo();
}

// 清理商店系统
void CleanupShopSystem()
{
    g_ShopDemo.Cleanup();
}
